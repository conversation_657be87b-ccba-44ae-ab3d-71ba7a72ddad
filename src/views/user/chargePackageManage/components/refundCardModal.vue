<template>
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="630px"
      @close="handleCancel"
    >
        <el-form :model="form" :rules="rules" ref="ruleForm"  label-position="top">
            <el-row :gutter="20">
                <el-col :span="24">
                    <div class="info-bg">
                        <div class="info-title">
                            充电包套餐名称：
                            <div class="info-title-detail">
                                {{ info.mealName }}
                            </div>
                        </div>

                        <div class="info-wrap">
                            <div style="flex: 1">充电包单价(元)</div>
                            <div style="flex: 1">可退卡数量</div>
                        </div>

                        <div class="info-detail">
                            <div style="flex: 1">{{ info.mealPrice }}</div>
                            <div style="flex: 1">{{ info.canRefundNumber }}</div>
                        </div>
                    </div>
                </el-col>
            </el-row>

            <el-row :gutter="20">

                <el-col :span="24">
                    <el-form-item
                        label="充电包退卡数量："
                        prop="refundNumber"
                        :label-width="formLabelWidth"
                    >
                        <el-input-number
                            v-model="form.refundNumber"
                            :min="0"
                            :max="Number(info.canRefundNumber)"
                            :precision="0"
                            :step="1"
                            :controls="false"
                        ></el-input-number>
                    </el-form-item>
                </el-col>

                <el-col :span="24">
                    <el-form-item
                        label="充电包购买人："
                        prop="refundMethod"
                        :label-width="formLabelWidth"
                    >
                        <el-select v-model="form.refundMethod" style="width: 100%;" > 
                            <el-option
                                v-for="item in refundMethodList"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            ></el-option>                            
                        </el-select>  
                    </el-form-item>
                </el-col>

                <el-col :span="24">
                    <el-form-item
                        label="充电包交易金额："
                        prop="amount"
                        :label-width="formLabelWidth"
                    >
                        <el-input
                            v-model="form.amount"
                            placeholder="自动计算"
                            disabled
                        ></el-input>
                    </el-form-item>
                </el-col>

              

            </el-row>
           

        </el-form>

        <div slot="footer" class="dialog-footer">
            <el-button @click="handleCancel">取 消</el-button>
            <el-button type="primary" @click="handleConfirm">确 定</el-button>
        </div>
    </el-dialog>
  </template>
  <script>  

import {
    editPileControl
} from '@/api/pile/index';


import moment from 'moment';

import _ from 'lodash';

  export default {
    props: {
        dialogTitle:{
            type: String,
            default: '退卡'
        }
    },
    components: {
  
    },
    dicts: [
        'ls_charging_status', // 是否状态
        'ls_charging_adjustable_type', // 可调控类型
        'ls_charging_station_control', // 站点调节方式
    ],
    data() {
        return {
            dialogVisible: false,
            pileId: '',
            form: {
                refundNumber: '',
                refundMethod: '',
            },
            rules: {
                refundNumber: [
                    { required: true, message: '请输入退款数量', trigger: 'change' },
                ],
                refundMethod: [
                    { required: true, message: '请选择退款方式', trigger: 'change' },
                ],
               

            },
            formLabelWidth: '120px',

            mealPrice: '', // 套餐价格

            refundMethodList: [
                {  label: '原路退回',   value: '1' },
            ],

            info: {
                mealName: '',
                mealPrice: '',
                canRefundNumber: '',
            }
        };
    },
    computed: {},
    mounted() {},
    methods: {
       
        calculateAmount() {
            const price = Number(this.info.mealPrice) || 0;
            const quantity = Number(this.form.refundNumber) || 0;
            this.form.amount = (price * quantity).toFixed(2);
        },

        resetForm() {
            Object.keys(this.form).forEach((key) => {
                this.form[key] = '';
                
            });
        },

        handleCancel() {
            this.$refs.ruleForm.resetFields();
            this.resetForm();
            console.log(this.form, 'this.form')
            this.dialogVisible = false;
        },



        // 新增按钮防抖        
        handleConfirm: _.debounce(function() {
            this.$refs.ruleForm.validate(async (valid) => {
                if (valid) {
                    console.log(this.form, 'this.form')
                    const {
                        controlType,
                        maxDownCapability,
                        maxUpwardCapability
                    } = this.form;
             
                         const params = {
                            pileId: this.pileId,
                            controlType,
                            maxDownCapability,
                            maxUpwardCapability
                        }

                        console.log('params', params)
                        const [err, res] = await editPileControl(params);
                        if (err) return;
                    
                        this.$message.success('编辑成功');
                        this.dialogVisible = false;
                        this.pileId  = '';
                        this.$emit('loadData');
                    
                }
            });
        }, 300) ,


    },
    watch: {
        'form.refundNumber': {
            handler() {
                this.calculateAmount();
            },
            immediate: true
        }
    }
  };
  </script>
  <style lang="scss" scoped>
::v-deep .el-form-item__content{
    display: flex !important;
}

::v-deep .el-input-number {
    width: 100% !important;
}


.info-bg {
    width: 100%;
    height: 120px;
    background-image: url('~@/assets/user/refund-bg.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    margin-bottom: 12px;
    padding: 12px 16px;
    box-sizing: border-box;
    .info-title {
        height: 24px;
        display: flex;
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 20px;
        color: #292B33;
        .info-title-detail {
            font-family: PingFang SC;
            font-weight: 500;
            font-size: 20px;
            color: #12151A

        }
    }
    .info-wrap {
        display: flex;
        height: 16px;
        margin: 24px 0 8px 0;
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 16px;
        color: #505363;
    }
    .info-detail {
        display: flex;
        height: 24px;
        font-family: Oswald Regular;
        font-weight: 400;
        font-size: 20px;
        color: #217AFF;

    }
}


.info-wrap {
    display: flex;
    height: 20px;
    margin-bottom: 24px;
    align-items: center;
    .info-title {
        font-weight: 400;
        font-size: 16px;
        line-height: 16px;
        color: #505363;
      }
      .info-detail {
        font-weight: 400;
        font-size: 16px;
        line-height: 16px;
        color: #292B33;
      }
}

.price-wrap {
    width: 100%;
    height: 40px;
    display: flex;
    align-items: center;
    background-color: #EBF3FF;
    border-radius: 2px;
    margin-bottom: 12px;
    font-weight: 400;
    font-size: 16px;
    padding-left: 16px;
    box-sizing: border-box;
    .price {
        font-family: Oswald Regular;
        color: #217AFF;
    }

}
  </style>
  