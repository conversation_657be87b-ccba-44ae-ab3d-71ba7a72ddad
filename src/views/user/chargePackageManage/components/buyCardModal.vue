<template>
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="630px"
      @close="handleCancel"
    >
        <el-form :model="form" :rules="rules" ref="ruleForm"  label-position="top">
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item
                        label="充电宝套餐名称："
                        prop="mealName"
                        :label-width="formLabelWidth"
                    >
                        <el-select v-model="form.mealName" style="width: 100%;" @change="handleValidityChange"> 
                            <el-option
                                v-for="item in mealNameList"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            ></el-option>                            
                        </el-select>  
                    </el-form-item>
                </el-col>

                <el-col :span="24" v-if="mealPrice">
                    <div class="price-wrap">
                        充电包单价(元)：
                        <div class="price">{{ mealPrice }}</div>
                    </div>
                </el-col>

                <el-col :span="24">
                    <el-form-item
                        label="充电包购买数量："
                        prop="buyNumber"
                        :label-width="formLabelWidth"
                    >
                        <el-input-number
                            v-model="form.buyNumber"
                            :min="0"
                            :precision="0"
                            :step="1"
                            :controls="false"
                        ></el-input-number>
                    </el-form-item>
                </el-col>

                <el-col :span="24">
                    <el-form-item
                        label="充电包购买人："
                        prop="buyUser"
                        :label-width="formLabelWidth"
                    >
                        <el-select v-model="form.buyUser" style="width: 100%;" > 
                            <el-option
                                v-for="item in buyUserList"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            ></el-option>                            
                        </el-select>  
                    </el-form-item>
                </el-col>

                <el-col :span="24">
                    <el-form-item
                        label="充电包支付方式："
                        prop="payMethod"
                        :label-width="formLabelWidth"
                    >
                        <el-select v-model="form.payMethod" style="width: 100%;" > 
                            <el-option
                                v-for="item in payMethodList"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            ></el-option>                            
                        </el-select>  
                    </el-form-item>
                </el-col>

                <el-col :span="24">
                    <el-form-item
                        label="充电包交易金额："
                        prop="amount"
                        :label-width="formLabelWidth"
                    >
                        <el-input
                            v-model="form.amount"
                            placeholder="自动计算"
                            disabled
                        ></el-input>
                    </el-form-item>
                </el-col>

            </el-row>
           

        </el-form>

        <div slot="footer" class="dialog-footer">
            <el-button @click="handleCancel">取 消</el-button>
            <el-button type="primary" @click="handleConfirm">确 定</el-button>
        </div>
    </el-dialog>
  </template>
  <script>  

import {
    editPileControl
} from '@/api/pile/index';


import moment from 'moment';

import _ from 'lodash';

  export default {
    props: {
        dialogTitle:{
            type: String,
            default: '购卡'
        }
    },
    components: {
  
    },
    dicts: [
        'ls_charging_status', // 是否状态
        'ls_charging_adjustable_type', // 可调控类型
        'ls_charging_station_control', // 站点调节方式
    ],
    data() {
        return {
            dialogVisible: false,
            pileId: '',
            form: {
                mealName: '',
                buyNumber: '',
                buyUser: '',
                payMethod: '',
                amount: '',
            },
            rules: {
                mealName: [
                    { required: true, message: '请选择充电宝套餐名称', trigger: 'change' },
                ],
                buyNumber: [
                    { required: true, message: '请输入充电包购买数量', trigger: 'blur' },
                ],
                buyUser: [
                    { required: true, message: '请选择购买人', trigger: 'blur' },
                ],
                payMethod: [
                    { required: true, message: '请选择支付方式', trigger: 'change' },
                ]

            },
            formLabelWidth: '120px',

            mealPrice: '', // 套餐价格

            mealNameList: [
                {
                    label: '套餐1',
                    value: '1'
                },
                {
                    label: '套餐2',
                    value: '2'
                },
                {
                    label: '套餐3',
                    value: '3'
                }
            ],
            buyUserList: [
                {
                    label: '用户1',
                    value: '1'
                },
                {
                    label: '用户2',
                    value: '2'
                },
                {
                    label: '用户3',
                    value: '3'
                },
            ],
            payMethodList: [
                {
                    label: '企业1',
                    value: '1'
                },
                {
                    label: '企业2',
                    value: '2'
                },
            ]
        };
    },
    computed: {},
    mounted() {},
    methods: {
        handleValidityChange() {
            this.mealPrice  = '120'
        },

        calculateAmount() {
            const price = Number(this.mealPrice) || 0;
            const quantity = Number(this.form.buyNumber) || 0;
            this.form.amount = (price * quantity).toFixed(2);
        },

        resetForm() {
            this.pileId = '';

            Object.keys(this.form).forEach((key) => {
                this.form[key] = '';
                
            });
        },

        handleCancel() {
            this.$refs.ruleForm.resetFields();
            this.resetForm();
            console.log(this.form, 'this.form')
            this.dialogVisible = false;
        },



        // 新增按钮防抖        
        handleConfirm: _.debounce(function() {
            this.$refs.ruleForm.validate(async (valid) => {
                if (valid) {
                    console.log(this.form, 'this.form')
                    const {
                        controlType,
                        maxDownCapability,
                        maxUpwardCapability
                    } = this.form;
             
                         const params = {
                            pileId: this.pileId,
                            controlType,
                            maxDownCapability,
                            maxUpwardCapability
                        }

                        console.log('params', params)
                        const [err, res] = await editPileControl(params);
                        if (err) return;
                    
                        this.$message.success('编辑成功');
                        this.dialogVisible = false;
                        this.pileId  = '';
                        this.$emit('loadData');
                    
                }
            });
        }, 300) ,


    },
    watch: {
        'form.buyNumber': {
            handler() {
                this.calculateAmount();
            },
            immediate: true
        }
    }
  };
  </script>
  <style lang="scss" scoped>
::v-deep .el-form-item__content{
    display: flex !important;
}

::v-deep .el-input-number {
    width: 100% !important;
}



.info-wrap {
    display: flex;
    height: 20px;
    margin-bottom: 24px;
    align-items: center;
    .info-title {
        font-weight: 400;
        font-size: 16px;
        line-height: 16px;
        color: #505363;
      }
      .info-detail {
        font-weight: 400;
        font-size: 16px;
        line-height: 16px;
        color: #292B33;
      }
}

.price-wrap {
    width: 100%;
    height: 40px;
    display: flex;
    align-items: center;
    background-color: #EBF3FF;
    border-radius: 2px;
    margin-bottom: 12px;
    font-weight: 400;
    font-size: 16px;
    padding-left: 16px;
    box-sizing: border-box;
    .price {
        font-family: Oswald Regular;
        color: #217AFF;
    }

}
  </style>
  