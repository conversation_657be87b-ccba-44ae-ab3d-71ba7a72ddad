<template>
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="630px"
      @close="handleCancel"
    >
        <el-form :model="form" :rules="rules" ref="ruleForm"  label-position="top">
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item
                        label="绑定用户："
                        prop="mobile"
                        :label-width="formLabelWidth"
                    >
                        <el-input
                            v-model="form.mobile"
                            placeholder="请输入用户手机号"
                           
                        ></el-input>
                    </el-form-item>
                </el-col>

            </el-row>
           

        </el-form>

        <div slot="footer" class="dialog-footer">
            <el-button @click="handleCancel">取 消</el-button>
            <el-button type="primary" @click="handleConfirm">确 定</el-button>
        </div>
    </el-dialog>
  </template>
  <script>  

import {
    editPileControl
} from '@/api/pile/index';


import moment from 'moment';

import _ from 'lodash';

  export default {
    props: {
        dialogTitle:{
            type: String,
            default: '绑定用户'
        }
    },
    components: {
  
    },
    dicts: [
        
    ],
    data() {
        return {
            dialogVisible: false,
            pileId: '',
            form: {
                mobile: '',
            },
            rules: {
                mobile: [
                    { required: true, message: '请输入用户手机号', trigger: 'blur' },
                ],
    
            },
            formLabelWidth: '120px',

        };
    },
    computed: {},
    mounted() {},
    methods: {


        resetForm() {
            this.pileId = '';

            Object.keys(this.form).forEach((key) => {
                this.form[key] = '';
                
            });
        },

        handleCancel() {
            this.$refs.ruleForm.resetFields();
            this.resetForm();
            console.log(this.form, 'this.form')
            this.dialogVisible = false;
        },



        // 新增按钮防抖        
        handleConfirm: _.debounce(function() {
            this.$refs.ruleForm.validate(async (valid) => {
                if (valid) {
                    console.log(this.form, 'this.form')
                    const {
                        controlType,
                        maxDownCapability,
                        maxUpwardCapability
                    } = this.form;
             
                         const params = {
                            pileId: this.pileId,
                            controlType,
                            maxDownCapability,
                            maxUpwardCapability
                        }

                        console.log('params', params)
                        const [err, res] = await editPileControl(params);
                        if (err) return;
                    
                        this.$message.success('编辑成功');
                        this.dialogVisible = false;
                        this.pileId  = '';
                        this.$emit('loadData');
                    
                }
            });
        }, 300) ,


    },
    
  };
  </script>
  <style lang="scss" scoped>
::v-deep .el-form-item__content{
    display: flex !important;
}

::v-deep .el-input-number {
    width: 100% !important;
}



.info-wrap {
    display: flex;
    height: 20px;
    margin-bottom: 24px;
    align-items: center;
    .info-title {
        font-weight: 400;
        font-size: 16px;
        line-height: 16px;
        color: #505363;
      }
      .info-detail {
        font-weight: 400;
        font-size: 16px;
        line-height: 16px;
        color: #292B33;
      }
}

.price-wrap {
    width: 100%;
    height: 40px;
    display: flex;
    align-items: center;
    background-color: #EBF3FF;
    border-radius: 2px;
    margin-bottom: 12px;
    font-weight: 400;
    font-size: 16px;
    padding-left: 16px;
    box-sizing: border-box;
    .price {
        font-family: Oswald Regular;
        color: #217AFF;
    }

}
  </style>
  