<template>
    <div class="container container-float " style="padding: 0 0 100px 0;">
        <div class="info-card">
            <div class="card-head">
                <div class="before-icon"></div>
                <div class="card-head-text">基础信息</div>
            </div>
            <div class="card-head-split"></div>
            <div class="form-wrap">
                <el-form
                    :model="baseInfo.form"
                    :rules="baseInfo.rules"
                    ref="baseInfoForm"
                    label-position="top"
                >
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item label="充电包套餐名称" prop="mealName">
                                <el-input 
                                    v-model="baseInfo.form.mealName"
                                    placeholder="请输入充电包套餐名称"
                                ></el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="8">
                            <el-form-item label="充电包套餐面值" prop="mealValue">
                                <el-input 
                                    v-model="baseInfo.form.mealValue"
                                    placeholder="请输入充电包套餐面值"
                                ></el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="8"> 
                            <el-form-item label="充电包套餐优惠方式" prop="discountMethod">
                                <div style="display: flex;align-items: center; height: 28px;">
                                    <el-select v-model="baseInfo.form.discountType" style="width: 20%;" @change="handleValidityChange"> 
                                        <el-option
                                            v-for="item in discountTypeList"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value"
                                        ></el-option>
                                    </el-select>  

                                    <div style="display: flex; align-items: center; height: 28px; width: 80%;" v-if="baseInfo.form.discountType === '1'">
                                        <div  style="margin-left: 8px;">满：</div>
                                        <el-input 
                                            v-model="baseInfo.form.full"
                                            placeholder="金额"
                                        ></el-input>
                                        <div style="margin-left: 8px;">减：</div>
                                        <el-input 
                                            v-model="baseInfo.form.reduce"
                                            placeholder="金额"
                                        ></el-input>
                                    </div>

                                    <div style="display: flex; align-items: center; height: 28px; width: 80%;" v-if="baseInfo.form.discountType === '2'">
                                        <div  style="margin-left: 4px;">满：</div>
                                        <el-input 
                                            v-model="baseInfo.form.discountFull"
                                            placeholder="金额"
                                        ></el-input>
                                        <div style="margin-left: 4px;text-wrap: nowrap;">折扣：</div>
                                        <el-input 
                                            v-model="baseInfo.form.discount"
                                            placeholder="折扣力度"
                                        ></el-input>
                                        <div style="margin-left: 4px; text-wrap: nowrap;">最高减：</div>
                                        <el-input
                                            v-model="baseInfo.form.maxReduce"
                                            placeholder="金额"
                                        ></el-input>
                                    </div>

                                </div>
                            </el-form-item>
                        </el-col>

                       
                    </el-row>

                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item label="充电包套餐售价" prop="mealPrice">
                                <el-input 
                                    v-model="baseInfo.form.mealPrice"
                                    placeholder="请输入充电包套餐售价"
                                ></el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="8">
                            <el-form-item label="是否生成卡密" prop="isPassword">
                                <el-select 
                                    v-model="baseInfo.form.isPassword"
                                    placeholder="请选择是否生成卡密"
                                     style="width: 100%"
                                >
                                    <el-option
                                        v-for="item in isPasswordList"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="8">
                            <el-form-item label="使用站点范围" prop="stationList">
                                <el-select 
                                    v-model="baseInfo.form.stationList"
                                    placeholder="请选择使用站点范围"
                                    style="width: 100%"
                                    multiple
                                >
                                    <el-option
                                        v-for="item in stationList"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item label="成本单位" prop="costUnit">
                                <el-select 
                                    v-model="baseInfo.form.costUnit"
                                    placeholder="请选择成本单位"
                                     style="width: 100%"
                                >
                                    <el-option
                                        v-for="item in costUnitList"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row :gutter="20">
                        <el-col :span="24">
                            <el-form-item label="使用说明" prop="description">
                                <el-input
                                    v-model="baseInfo.form.description"
                                    type="textarea"
                                    :rows="3"
                                    placeholder="请输入"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row :gutter="20">
                        <el-col :span="24">
                            <el-form-item label="充电包套餐生效时间" prop="effectTimeType">
                                <div style="display: flex;align-items: center; height: 28px;">
                                    <el-radio-group
                                        v-model="baseInfo.form.effectTimeType"
                                    >
                                        <el-radio
                                            v-for="dict in effectTimeTypeList"
                                            :key="dict.value"
                                            :label="dict.value"
                                        >
                                            {{ dict.label }}
                                        </el-radio>
                                    </el-radio-group>


                                    <el-date-picker
                                        v-if="baseInfo.form.effectTimeType === '2'"
                                        style="margin-left: 16px;"
                                        type="datetime"
                                        v-model="baseInfo.form.effectTime" 
                                        value-format="yyyy-MM-dd HH:mm:ss"
                                        placeholder="请选择"
                                    >

                                    </el-date-picker>
                                </div>
                                
                            </el-form-item>
                        </el-col>
                    </el-row>
                    
                </el-form>
            </div>
        </div>

        <div class="bottom-wrap">
            <div>
                <el-button @click="handleCancel">取消</el-button>
                <el-button @click="handleSave()" type="primary">保存</el-button>

            </div>

        </div>

    </div>
    
  </template>
  
  <script>
  
    export default {
    components: {
        
    },
    dicts: [],
    data() {
        return {
            baseInfo: {
                form: {
                    mealName: '',
                    mealValue: '',
                    discountType: '',
                    full:'',
                    reduce: '',
                    discountFull:'',
                    discount:'',
                    maxReduce: '',
                    mealPrice: '',
                    isPassword: '',
                    stationList: [],
                    costUnit: '',
                    description: '',
                    effectTimeType: '',
                    effectTime: '',
                },
                rules: {
                    mealName: [
                        { required: true, message: '请输入充电包套餐名称', trigger: 'blur' },
                    ],
                    mealValue: [
                        { required: true, message: '请输入充电包套餐面值', trigger: 'blur' },
                    ],
                    discountMethod: [
                        {
                            required: true, 
                            validator: (rule, value, callback) => {
                                if(!this.baseInfo.form.discountType) {
                                    callback(new Error('请选择优惠方式')); 
                                } else {
                                    if(this.baseInfo.form.discountType == '1' && (!this.baseInfo.form.full || !this.baseInfo.form.reduce)){
                                        callback(new Error('请输入满减方式'));
                                    } else if(this.baseInfo.form.discountType == '2' && (!this.baseInfo.form.discountFull || !this.baseInfo.form.discount || !this.baseInfo.form.maxReduce)){
                                        callback(new Error('请输入折扣方式'));
                                    } else {
                                        callback();
                                    }
                                }
                            },
                            trigger: 'blur'
                        }
                    ],
                    mealPrice: [
                        { required: true, message: '请输入充电包套餐售价', trigger: 'blur' },
                    ],
                    description: [
                        { required: true, message: '请输入使用说明', trigger: 'blur' },
                    ],
                    effectTimeType: [
                        { 
                            required: true, 
                            validator: (rule, value, callback) => {
                                if(!this.baseInfo.form.effectTimeType) {
                                    callback(new Error('请选择生效时间')); 
                                } else {
                                    if(this.baseInfo.form.effectTimeType == '2' && !this.baseInfo.form.effectTime) {
                                        callback(new Error('请选择生效时间'));
                                    }  else {
                                        callback();
                                    }
                                }
                            },
                            trigger: 'blur',
                        },
                    ]


                }
            },

            discountTypeList: [
                { label: '满减券', value: '1' },
                { label: '折扣券', value: '2' },
            ],
            isPasswordList: [
                { label: '是', value: '1' },
                { label: '否', value: '2' },
            ],
            stationList: [
                {  label: '站点1', value: '1' },
                {   label: '站点2', value: '2' },
                {   label: '站点3', value: '3' },
            ],
            costUnitList: [
                { label: '单位1', value: '1' },
                { label: '单位2', value: '2' },
                { label: '单位3', value: '3' },
            ],
            effectTimeTypeList: [
                { label: '立即生效', value: '1' },
                { label: '延迟生效', value: '2' },
            ]
        };
    },

    computed: {
    },
    mounted() {

    },
    methods: {
        handleValidityChange() {
            // 切换时清除相关字段的校验状态
            this.$nextTick(() => {
                this.$refs.baseInfoForm.clearValidate(['discountMethod',]);
            });
        },


        handleCancel() {
            this.$router.back();
        },
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .container-full {
    background-color: rgba(244, 246, 249, 1);
    padding-bottom: 16px;
    min-height: calc(100% - 80px);
    overflow-y: auto;
  }
   
    

  .info-card {
  margin: 16px;
  border-radius: 5px;
  border: 1px solid #fff;
  background-color: #fff;

  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;

    .before-icon {
      width: 3px;
      height: 16px;
      background-image: url('~@/assets/station/consno-before.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin-right: 8px;
    }

    .card-head-text {
      font-weight: 500;
      font-size: 16px;
      color: #12151a;
    }
  }

  .card-head-split {
    height: 1.5px;
    background-color: #F9F9FB;
  }

  .form-wrap {
    padding: 16px;
  }

  ::v-deep .el-form-item__label {
    display: flex;
  }

  .protocol-label {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .btn-wrap {
      display: flex;
      align-items: center;
      color: #409eff;
      cursor: pointer;
      font-size: 14px;
      font-weight: 400;

      .icon-add {
        width: 16px;
        height: 16px;
        margin-right: 8px;
      }
    }
  }
}

.container {
      position: relative;
      padding-bottom: 100px;
      box-sizing: border-box;
      .bottom-wrap {
        z-index: 100;
          position: fixed;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 86px;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          background-color: #FFFFFF;
          padding-right: 32px;
          box-sizing: border-box;
      }
 
    }
  </style>
  