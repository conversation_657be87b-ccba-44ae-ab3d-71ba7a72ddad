<template>
  <div class="container container-float" style="padding: 0">
    <el-tabs v-model="activeName" @tab-click="handleClickTab">
      <el-tab-pane label="充电包套餐管理" name="meal">
        <div class="table-wrap">
          <BuseCrud
            ref="meal"
            :loading="mealLoading"
            :filterOptions="mealFilterOptions"
            :tablePage="mealTablePage"
            :tableColumn="mealTableColumn"
            :tableData="mealTableData"
            :modalConfig="modalConfig"
            class="buse-wrap-user"
            @loadData="loadMealData"
          >
            <template slot="defaultHeader">
              <div>
                <div class="card-head">
                  <div class="card-head-text">充电包套餐管理列表</div>

                  <div class="top-button-wrap">
                    <el-button
                      class="set-btn"
                      type="primary"
                      @click="onClickMealExport()"
                    >
                      <svg-icon iconClass="a-export-black"></svg-icon>
                      导出
                    </el-button>

                    <el-button type="primary" @click="() => handleAddMeal()">
                      <svg-icon iconClass="a-add"></svg-icon>
                      新增
                    </el-button>
                  </div>
                </div>
              </div>
            </template>

            <template slot="operate" slot-scope="{ row }">
              <el-button class="button-border" @click="handleEdit(row)">
                编辑
              </el-button>

              <el-button class="button-border" @click="handleDetail(row)">
                详情
              </el-button>

              <el-button class="button-border" @click="handleStop(row)">
                停用
              </el-button>
            </template>
          </BuseCrud>
        </div>
      </el-tab-pane>

      <el-tab-pane label="充电包购买记录" name="purchase">
        <div class="table-wrap">
          <BuseCrud
            ref="purchase"
            :loading="purchaseLoading"
            :filterOptions="purchaseFilterOptions"
            :tablePage="purchaseTablePage"
            :tableColumn="purchaseTableColumn"
            :tableData="purchaseTableData"
            :modalConfig="modalConfig"
            class="buse-wrap-user"
            @loadData="loadPurchaseData"
          >
            <template slot="defaultHeader">
              <div>
                <div class="card-head">
                  <div class="card-head-text">充电包购买记录列表</div>

                  <div class="top-button-wrap">
                    <el-button
                      class="set-btn"
                      type="primary"
                      @click="onClickPurchaseExport()"
                    >
                      <svg-icon iconClass="a-export-black"></svg-icon>
                      导出
                    </el-button>

                    <el-button type="primary" @click="() => handleBuyCard()">
                      <svg-icon iconClass="a-buy-card"></svg-icon>
                      购卡
                    </el-button>
                  </div>
                </div>
              </div>
            </template>

            <template slot="operate" slot-scope="{ row }">
              <el-button class="button-border" @click="handleRefundCard(row)">
                退卡
              </el-button>
            </template>
          </BuseCrud>
        </div>
      </el-tab-pane>

      <el-tab-pane label="充电包退款记录" name="Refund">
        <div class="table-wrap">
          <BuseCrud
            ref="refund"
            :loading="refundLoading"
            :filterOptions="refundFilterOptions"
            :tablePage="refundTablePage"
            :tableColumn="refundTableColumn"
            :tableData="refundTableData"
            :modalConfig="modalConfig"
            class="buse-wrap-user"
            @loadData="loadRefundData"
          >
            <template slot="defaultHeader">
              <div>
                <div class="card-head">
                  <div class="card-head-text">充电包退款记录列表</div>

                  <div class="top-button-wrap">
                    <el-button
                      class="set-btn"
                      type="primary"
                      @click="onClickRefundExport()"
                    >
                      <svg-icon iconClass="a-export-black"></svg-icon>
                      导出
                    </el-button>
                  </div>
                </div>
              </div>
            </template>
          </BuseCrud>
        </div>
      </el-tab-pane>

      <el-tab-pane label="充电包管理" name="Manage">
        <div class="table-wrap">
          <BuseCrud
            ref="manage"
            :loading="manageLoading"
            :filterOptions="manageFilterOptions"
            :tablePage="manageTablePage"
            :tableColumn="manageTableColumn"
            :tableData="manageTableData"
            :modalConfig="modalConfig"
            class="buse-wrap-user"
            @loadData="loadManageData"
          >
            <template slot="defaultHeader">
              <div>
                <div class="card-head">
                  <div class="card-head-text">充电包管理列表</div>

                  <div class="top-button-wrap">
                    <el-button
                      class="set-btn"
                      type="primary"
                      @click="onClickManageExport()"
                    >
                      <svg-icon iconClass="a-export-black"></svg-icon>
                      导出
                    </el-button>
                  </div>
                </div>
              </div>
            </template>

            <template slot="operate" slot-scope="{ row }">
              <el-button class="button-border" @click="handleBindUser(row)">
                绑定用户
              </el-button>
              <el-button class="button-border" @click="handleUnbindUser(row)">
                解绑用户
              </el-button>
              <el-button class="button-border" @click="handleRefundCard(row)">
                退卡
              </el-button>

              <el-button class="button-border" @click="handleFlow(row)">
                流水
              </el-button>
            </template>
          </BuseCrud>
        </div>
      </el-tab-pane>
    </el-tabs>

    <BuyCardModal ref="buyCardModal" />

    <RefundCardModal ref="refundCardModal" />

    <BindUserModal ref="bindUserModal" />

    <FlowModal ref="flowModal" />
  </div>
</template>

<script>
import BuyCardModal from './components/buyCardModal.vue';
import RefundCardModal from './components/refundCardModal.vue';
import BindUserModal from './components/bindUserModal.vue';
import FlowModal from './components/flowModal.vue';

export default {
  components: {
    BuyCardModal,
    RefundCardModal,
    BindUserModal,
    FlowModal,
  },
  dicts: [],
  data() {
    return {
      activeName: 'meal',
      mealLoading: false,
      mealParams: {
        mealId: '',
        mealName: '',
        mealStatus: '',
        isPassword: '',
        discountType: '',
      },
      mealTablePage: { total: 0, currentPage: 1, pageSize: 10 },
      mealTableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
          fixed: 'left',
        },
        {
          field: 'mealId',
          title: '充电包套餐ID',
          minWidth: 150,
        },
        {
          field: 'mealName',
          title: '充电包套餐',
          minWidth: 150,
        },
        {
          field: 'mealAmount',
          title: '充电包套餐面值',
          minWidth: 180,
        },
        {
          field: 'mealPrice',
          title: '充电包套餐售价',
          minWidth: 180,
        },
        {
          field: 'discountType',
          title: '充电包套餐优惠方式',
          minWidth: 200,
        },
        {
          field: 'isPassword',
          title: '是否生成卡密',
          minWidth: 120,
        },
        {
          field: 'useScope',
          title: '使用范围',
          minWidth: 120,
        },
        {
          field: 'costUnit',
          title: '成本单位',
          minWidth: 120,
        },
        {
          field: 'remark',
          title: '使用说明',
          minWidth: 100,
        },
        {
          field: 'effectTime',
          title: '充电包套餐生效时间',
          minWidth: 180,
        },
        {
          field: 'status',
          title: '充电包状态',
          minWidth: 120,
          fixed: 'right',
        },
        {
          title: '操作',
          slots: { default: 'operate' },
          width: 250,
          align: 'center',
          fixed: 'right',
        },
      ],
      mealTableData: [],

      purchaseLoading: false,
      purchaseParams: {
        businessId: '',
        tradeNo: '',
        tradeTime: [],
        mealName: '',
        tradeType: '',
        tradeStatus: '',
        creator: '',
      },
      purchaseTablePage: { total: 0, currentPage: 1, pageSize: 10 },
      purchaseTableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
          fixed: 'left',
        },
        {
          field: 'businessId',
          title: '业务订单号',
          minWidth: 150,
        },
        {
          field: 'tradeNo',
          title: '交易流水号',
          minWidth: 150,
        },
        {
          field: 'mealName',
          title: '套餐名称',
          minWidth: 180,
        },
        {
          field: 'buyNumber',
          title: '购买数量',
          minWidth: 180,
        },
        {
          field: 'useNumber',
          title: '使用数量',
          minWidth: 200,
        },
        {
          field: 'buyer',
          title: '购买人',
          minWidth: 120,
        },
        {
          field: 'tradeAmount',
          title: '交易金额',
          minWidth: 120,
        },
        {
          field: 'tradeTime',
          title: '交易时间',
          minWidth: 120,
        },
        {
          field: 'payMethod',
          title: '支付方式',
          minWidth: 100,
        },
        {
          field: 'accountId',
          title: '账户ID',
          minWidth: 180,
        },
        {
          field: 'creator',
          title: '操作人',
          minWidth: 120,
        },
        {
          field: 'tradeStatus',
          title: '交易状态',
          minWidth: 120,
          fixed: 'right',
        },
        {
          title: '操作',
          slots: { default: 'operate' },
          width: 100,
          align: 'center',
          fixed: 'right',
        },
      ],
      purchaseTableData: [],

      refundLoading: false,
      refundParams: {
        businessId: '',
        tradeNo: '',
        tradeTime: [],
        mealName: '',
        refundNo: '',
        tradeStatus: '',
        creator: '',
      },
      refundTablePage: { total: 0, currentPage: 1, pageSize: 10 },
      refundTableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
          fixed: 'left',
        },
        {
          field: 'refundNo',
          title: '退款单号',
          minWidth: 150,
        },
        {
          field: 'businessId',
          title: '业务订单号',
          minWidth: 150,
        },
        {
          field: 'tradeNo',
          title: '交易流水号',
          minWidth: 150,
        },
        {
          field: 'mealName',
          title: '充电包套餐名称',
          minWidth: 180,
        },
        {
          field: 'buyNumber',
          title: '购买数量',
          minWidth: 180,
        },
        {
          field: 'useNumber',
          title: '使用数量',
          minWidth: 200,
        },
        {
          field: 'refundNumber',
          title: '退卡数量',
          minWidth: 200,
        },
        {
          field: 'refundMethod',
          title: '退款方式',
          minWidth: 120,
        },
        {
          field: 'tradeAmount',
          title: '交易金额',
          minWidth: 120,
        },
        {
          field: 'tradeTime',
          title: '交易时间',
          minWidth: 120,
        },

        {
          field: 'refundCompany',
          title: '充电包退款企业',
          minWidth: 180,
        },
        {
          field: 'creator',
          title: '操作人',
          minWidth: 120,
        },
        {
          field: 'tradeStatus',
          title: '交易状态',
          minWidth: 120,
          fixed: 'right',
        },
      ],
      refundTableData: [],

      manageLoading: false,
      manageParams: {
        packageId: '',
        bindUserId: '',
        bindTime: [],
        bindStatus: '',
        operator: '',
        company: '',
      },
      manageTablePage: { total: 0, currentPage: 1, pageSize: 10 },
      manageTableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
          fixed: 'left',
        },
        {
          field: 'packageId',
          title: '充电包ID',
          minWidth: 150,
        },
        {
          field: 'packageBalance',
          title: '充电包余额(元)',
          minWidth: 150,
        },
        {
          field: 'bindUser',
          title: '充电包绑定用户/机构',
          minWidth: 180,
        },
        {
          field: 'packagePassword',
          title: '充电包卡密',
          minWidth: 180,
        },
        {
          field: 'bindTime',
          title: '充电包绑定时间',
          minWidth: 200,
        },
        {
          field: 'operator',
          title: '操作人',
          minWidth: 120,
        },
        {
          field: 'company',
          title: '所属企业',
          minWidth: 120,
        },

        {
          field: 'bindStatus',
          title: '充电包绑定状态',
          minWidth: 120,
          fixed: 'right',
        },
        {
          title: '操作',
          slots: { default: 'operate' },
          width: 300,
          align: 'center',
          fixed: 'right',
        },
      ],
      manageTableData: [],
    };
  },

  computed: {
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
    mealFilterOptions() {
      return {
        config: [
          {
            field: 'mealId',
            title: '充电包套餐ID',
            element: 'el-input',
          },
          {
            field: 'mealName',
            title: '充电包套餐名称',
            element: 'el-input',
          },
          {
            field: 'mealStatus',
            title: '充电包套餐状态',
            element: 'el-select',
            props: {
              placeholder: '请选择充电包套餐状态',
              options: [],
            },
          },
          {
            field: 'isPassword',
            title: '是否生成卡密',
            element: 'el-select',
            props: {
              placeholder: '请选择是否生成卡密',
              options: [],
            },
          },
          {
            field: 'discountType',
            title: '优惠方式',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
        ],
        params: this.mealParams,
      };
    },
    purchaseFilterOptions() {
      return {
        config: [
          {
            field: 'businessId',
            title: '业务订单号',
            element: 'el-input',
          },
          {
            field: 'tradeNo',
            title: '交易流水号',
            element: 'el-input',
          },
          {
            field: 'tradeTime',
            title: '交易时间',
            element: 'el-date-picker',
            props: {
              type: 'datetimerange',
              options: [],
              valueFormat: 'yyyy-MM-dd HH:mm:ss',
              defaultTime: ['00:00:00', '23:59:59'],
            },
          },
          {
            field: 'mealName',
            title: '套餐名称',
            element: 'el-input',
          },
          {
            field: 'tradeType',
            title: '交易类型',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
          {
            field: 'tradeStatus',
            title: '交易状态',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
          {
            field: 'creator',
            title: '操作人',
            element: 'el-input',
          },
        ],
        params: this.purchaseParams,
      };
    },

    refundFilterOptions() {
      return {
        config: [
          {
            field: 'businessId',
            title: '业务订单号',
            element: 'el-input',
          },
          {
            field: 'tradeNo',
            title: '交易流水号',
            element: 'el-input',
          },
          {
            field: 'tradeTime',
            title: '交易时间',
            element: 'el-date-picker',
            props: {
              type: 'datetimerange',
              options: [],
              valueFormat: 'yyyy-MM-dd HH:mm:ss',
              defaultTime: ['00:00:00', '23:59:59'],
            },
          },
          {
            field: 'mealName',
            title: '套餐名称',
            element: 'el-input',
          },
          {
            field: 'refundNo',
            title: '退款单号',
            element: 'el-input',
          },
          {
            field: 'tradeStatus',
            title: '交易状态',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
          {
            field: 'creator',
            title: '操作人',
            element: 'el-input',
          },
        ],
        params: this.refundParams,
      };
    },

    manageFilterOptions() {
      return {
        config: [
          {
            field: 'packageId',
            title: '充电包ID',
            element: 'el-input',
          },
          {
            field: 'bindUserId',
            title: '绑定用户',
            element: 'el-input',
          },
          {
            field: 'bindTime',
            title: '交易时间',
            element: 'el-date-picker',
            props: {
              type: 'datetimerange',
              options: [],
              valueFormat: 'yyyy-MM-dd HH:mm:ss',
              defaultTime: ['00:00:00', '23:59:59'],
            },
          },
          {
            field: 'bindStatus',
            title: '绑定状态',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
          {
            field: 'operator',
            title: '操作人',
            element: 'el-input',
          },
          {
            field: 'company',
            title: '所属企业',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
        ],
        params: this.manageParams,
      };
    },
  },
  mounted() {
    this.loadMealData();
  },
  methods: {
    async handleClickTab({ index }) {
      if (index === '0') {
        this.mealTablePage = {
          total: 0,
          currentPage: 1,
          pageSize: 10,
        };

        this.mealTableData = [];
        this.loadMealData();
      } else if (index === '1') {
        this.purchaseTablePage = {
          total: 0,
          currentPage: 1,
          pageSize: 10,
        };

        this.purchaseTableData = [];
        this.loadPurchaseData();
      } else if (index === '2') {
        this.refundTablePage = {
          total: 0,
          currentPage: 1,
          pageSize: 10,
        };

        this.refundTableData = [];
        this.loadRefundData();
      } else if (index === '3') {
        this.manageTablePage = {
          total: 0,
          currentPage: 1,
          pageSize: 10,
        };

        this.manageTableData = [];
        this.loadManageData();
      }
    },

    async loadMealData() {
      this.mealTableData = [
        {
          mealId: 'PKG001',
          mealName: '经济充电套餐',
          mealAmount: 100,
          mealPrice: 90,
          discountType: '立减10元',
          isPassword: '是',
          useScope: '全国通用',
          costUnit: '元',
          remark: '适用于快充站',
          effectTime: '2023-01-01 至 2023-12-31',
          status: '生效中',
          action: '编辑｜下架',
        },
        {
          mealId: 'PKG002',
          mealName: '标准充电套餐',
          mealAmount: 200,
          mealPrice: 180,
          discountType: '9折优惠',
          isPassword: '否',
          useScope: '指定合作站点',
          costUnit: '元',
          remark: '仅限夜间使用',
          effectTime: '2023-06-01 至 2024-05-31',
          status: '生效中',
          action: '编辑｜下架',
        },
        {
          mealId: 'PKG003',
          mealName: 'VIP尊享套餐',
          mealAmount: 500,
          mealPrice: 450,
          discountType: '赠送50积分',
          isPassword: '是',
          useScope: '全国通用',
          costUnit: '元',
          remark: '含优先充电权益',
          effectTime: '2023-03-15 至 2023-12-31',
          status: '已下架',
          action: '编辑｜上架',
        },
      ];
    },

    // 新增套餐
    handleAddMeal() {
      this.$router.push({
        path: '/v2g-charging/baseInfo/userManage/chargePackageManage/createMeal',
      });
    },

    async loadPurchaseData() {
      this.purchaseTableData = [
        {
          businessId: 'BID202307280001',
          tradeNo: 'TRADE20230728123456',
          mealName: '经济充电套餐',
          buyNumber: 2,
          useNumber: 1,
          buyer: '张三',
          tradeAmount: 180.0,
          tradeTime: '2023-07-28 14:30:45',
          payMethod: '支付宝',
          accountId: 'USER_1001',
          creator: '系统自动',
          tradeStatus: '支付成功',
        },
        {
          businessId: 'BID202307280002',
          tradeNo: 'TRADE20230728987654',
          mealName: 'VIP尊享套餐',
          buyNumber: 1,
          useNumber: 0,
          buyer: '李四',
          tradeAmount: 450.0,
          tradeTime: '2023-07-28 15:12:33',
          payMethod: '微信支付',
          accountId: 'CORP_2001',
          creator: '管理员',
          tradeStatus: '已完成',
        },
        {
          businessId: 'BID202307280003',
          tradeNo: 'TRADE20230728555555',
          mealName: '标准充电套餐',
          buyNumber: 5,
          useNumber: 5,
          buyer: '王五',
          tradeAmount: 900.0,
          tradeTime: '2023-07-28 09:45:21',
          payMethod: '企业转账',
          accountId: 'VIP_3001',
          creator: '财务部',
          tradeStatus: '已退款',
        },
        {
          businessId: 'BID202307280004',
          tradeNo: 'TRADE20230728111111',
          mealName: '夜间特惠套餐',
          buyNumber: 3,
          useNumber: 2,
          buyer: '赵六',
          tradeAmount: 270.0,
          tradeTime: '2023-07-28 22:15:59',
          payMethod: '银联卡',
          accountId: 'NIGHT_4001',
          creator: '客服代操作',
          tradeStatus: '部分退款',
        },
        {
          businessId: 'BID202307280005',
          tradeNo: 'TRADE20230728333333',
          mealName: '企业定制套餐',
          buyNumber: 10,
          useNumber: 8,
          buyer: 'XX科技有限公司',
          tradeAmount: 8000.0,
          tradeTime: '2023-07-28 11:03:17',
          payMethod: '对公转账',
          accountId: 'ENT_5001',
          creator: '企业管理员',
          tradeStatus: '待确认',
        },
      ];
    },

    // 购卡
    handleBuyCard() {
      this.$refs.buyCardModal.dialogVisible = true;
    },

    // 退卡
    handleRefundCard() {
      this.$refs.refundCardModal.info = {
        mealName: '100元充电包',
        mealPrice: 100.0,
        canRefundNumber: 50,
      };
      this.$refs.refundCardModal.dialogVisible = true;
    },

    async loadRefundData() {
      this.refundTableData = [
        {
          refundNo: 'RF202310230001',
          businessId: 'ORD202310230001',
          tradeNo: 'TR202310230001',
          mealName: '经济型充电包（30天）',
          buyNumber: 2,
          useNumber: 1,
          refundNumber: 1,
          refundMethod: '原路退回',
          tradeAmount: 500.0,
          tradeTime: '2023-10-23 09:25:47',
          refundCompany: '深圳充电科技有限公司',
          creator: '张三',
          tradeStatus: '已完成',
        },
        {
          refundNo: 'RF202310230002',
          businessId: 'ORD202310230045',
          tradeNo: 'TR202310230087',
          mealName: '超级快充套餐（90天）',
          buyNumber: 1,
          useNumber: 0,
          refundNumber: 1,
          refundMethod: '线下转账',
          tradeAmount: 899.0,
          tradeTime: '2023-10-23 11:12:33',
          refundCompany: '北京新能源集团',
          creator: '李四',
          tradeStatus: '处理中',
        },
        {
          refundNo: 'RF202310230003',
          businessId: 'ORD202310230112',
          tradeNo: 'TR202310230156',
          mealName: '夜间专用充电包',
          buyNumber: 3,
          useNumber: 2,
          refundNumber: 1,
          refundMethod: '原路退回',
          tradeAmount: 360.0,
          tradeTime: '2023-10-23 14:30:00',
          refundCompany: '上海电力服务有限公司',
          creator: '王五',
          tradeStatus: '已关闭',
        },
        {
          refundNo: 'RF202310230010',
          businessId: 'ORD202310230999',
          tradeNo: 'TR202310230888',
          mealName: '企业定制充电包',
          buyNumber: 10,
          useNumber: 8,
          refundNumber: 2,
          refundMethod: '优惠券返还',
          tradeAmount: 2000.0,
          tradeTime: '2023-10-23 16:55:21',
          refundCompany: '杭州智能充电集团',
          creator: '赵六',
          tradeStatus: '部分退款',
        },
      ];
    },

    async loadManageData() {
      this.manageTableData = [
        {
          packageId: 'PKG-2024001',
          packageBalance: 150.5,
          bindUser: '张三 / 机构A',
          packagePassword: 'A1B2C3D4',
          bindTime: '2024-03-15 10:00:00',
          operator: '管理员',
          company: 'XX科技有限公司',
          bindStatus: '已绑定',
        },
        {
          packageId: 'PKG-2024002',
          packageBalance: 200.0,
          bindUser: '李四 / 机构B',
          packagePassword: 'E5F6G7H8',
          bindTime: '2024-03-16 14:30:00',
          operator: '客服人员',
          company: 'YY新能源公司',
          bindStatus: '未绑定',
        },
        {
          packageId: 'PKG-2024003',
          packageBalance: 99.99,
          bindUser: '王五 / 机构C',
          packagePassword: 'I9J0K1L2',
          bindTime: '2024-03-17 09:15:00',
          operator: '系统自动',
          company: 'ZZ电力集团',
          bindStatus: '已绑定',
        },
        {
          packageId: 'PKG-2024010',
          packageBalance: 300.0,
          bindUser: '赵六 / 机构D',
          packagePassword: 'M3N4O5P6',
          bindTime: '2024-03-20 16:45:00',
          operator: '管理员',
          company: 'AA科技发展',
          bindStatus: '已绑定',
        },
      ];
    },

    // 绑定用户
    handleBindUser(row) {
      this.$refs.bindUserModal.dialogVisible = true;
    },

    // 解绑用户
    handleUnbindUser(row) {},

    // 流水
    handleFlow(row) {
      this.$refs.flowModal.dialogVisible = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.container-full {
  background-color: rgba(244, 246, 249, 1);
  padding-bottom: 16px;
  min-height: calc(100% - 80px);
  overflow-y: auto;
}

::v-deep .el-tabs {
  margin-top: 16px;
  background-color: #f5f6f9;
  .el-tabs__header {
    padding-left: 0;
    display: flex;
    justify-content: center;
    text-align: center;
    margin-bottom: 1px;
    .el-tabs__item {
      padding: 0;
      width: 164px;
      font-size: 18px;
      font-weight: 400;
      background-color: #fff;
    }
    .el-tabs__item.is-active {
      background-color: #1677fe;
      color: #fff;
    }
    .el-tabs__nav-scroll {
      border-radius: 25px;
      border: solid 1px #dfe1e5;
    }
    .el-tabs__active-bar {
      display: none;
    }
    .el-tabs__nav-wrap::after {
      width: 0;
    }
  }
}

.table-wrap {
  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }
  margin: 16px;

  .card-head {
    // position: relative;
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    margin-top: -20px;
    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }
  .info-wrap {
    margin-top: 16px;
    display: flex;
    flex-wrap: wrap;
    gap: 16px;

    .info-item {
      background-color: #fafbfc;
      flex: 1 1 0;
      // min-width: 180px;

      border-radius: 5px;
      padding: 8px 24px;
      box-sizing: border-box;
      // margin-right: 16px;
      display: flex;
      .info-icon {
        width: 42px;
        height: 42px;
      }
      .info-right-wrap {
        flex: 1;
        margin-left: 8px;
        .info-title {
          font-weight: 400;
          font-size: 14px;
          line-height: 14px;
          margin-bottom: 8px;
        }
        .info-number {
          font-size: 20px;
          font-weight: 500;
          .info-unit {
            font-size: 14px;
            font-weight: 400;
          }
        }
      }
    }
    .info-item:last-child {
      margin-right: 0;
    }
  }

  .top-button-wrap {
    display: flex;
    margin: 16px 0;
    .set-btn {
      background-color: #ffffff;
      color: #292b33;
      border-color: #dfe1e5;
    }
  }
}

.button-border {
  border: 0.01rem solid #217aff;
  color: #217aff;
  background-color: #fff;
}
</style>
