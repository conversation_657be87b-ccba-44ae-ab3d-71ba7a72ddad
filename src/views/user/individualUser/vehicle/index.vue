<template>
    <div class="container container-float ">
        <div class="table-wrap">
            <BuseCrud
                ref="crud"
                :loading="loading"
                :filterOptions="filterOptions"
                :tablePage="tablePage"
                :tableColumn="tableColumn"
                :tableData="tableData"
                :pagerProps="pagerProps"
                :modalConfig="modalConfig"
                class="buse-wrap-station"
                @loadData="loadData"
            >
            <template slot="defaultHeader">
                <div>
                    <div class="card-head">
                        <div class="card-head-text">个人车辆列表</div>

                        
                    </div>
                    

                    
                </div>
                
            </template>

                <template slot="operate" slot-scope="{ row }">
                    <div class="menu-box">
                        <el-button
                            type="primary"
                            plain
                            v-if="row.status === '1'"
                            @click="hanleUnbind(row)"
                        >
                            解绑
                        </el-button>
                    
                    </div>
                
                </template>

            </BuseCrud>
        </div>

    </div>
    
  </template>
  
  <script>
  
  
import {getUserVehicleList, unbindVehicle} from '@/api/user/vehicle';

    export default {
    components: {
        
    },
    dicts: [
        'ls_charging_license_color', // 车牌类型
    ],
    data() {
      return {
        loading: false,
        tablePage: { total: 0, currentPage: 1, pageSize: 10 },
        tableColumn:[
            {
                type: 'seq',
                title: '序号',
                width: 60,
                minWidth: 60, 
            },
            {
                field: 'licenseNo',
                title: '车牌号',
                minWidth: 120, 
            },
            {
                field: 'vinCode',
                title: '车架号',
                minWidth: 160,
            },
            {
                field: 'ownership',
                title: '车辆归属类型',
                minWidth: 130,
            },
            {
                field: 'ownerName',
                title: '车主名称',
                minWidth: 120,
            },
            {
                field: 'mobile',
                title: '车主手机号码',
                minWidth: 140,
            },
            {
                field: 'licenseColor',
                title: '车牌类型',
                minWidth: 120,
                formatter: ({ cellValue }) => {
                    return this.selectDictLabel(
                        this.dict.type.ls_charging_license_color,
                        cellValue
                    );
                },
            },
            {
                field: 'brand',
                title: '车辆品牌',
                minWidth: 120,
            },
            {
                field: 'carName',
                title: '车辆型号',
                minWidth: 180,
            },
            {
                field: 'remark',
                title: '备注',
                minWidth: 120,
            },
            {
                field: 'plugChargeLabel',
                title: '即插即充状态',
                minWidth: 140,
            },
            {
                field: 'statusLabel',
                title: '车辆状态',
                minWidth: 120,
                fixed: 'right',
            },
            {
                title: '操作',
                slots: { default: 'operate' },
                width: 100,
                align: 'center',
                fixed: 'right',
            },
  
        ],
        tableData: [],
        pagerProps: {
          layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
        },
        params: {
            licensePlate: '',
            vin: '',      
            ownerName: '',
            ownerPhone: '',
            jcjcStatus: '',
            vehicleStatus: '',
        },
      };
    },

    computed: {
        filterOptions() {
        return {
            config: [
                {
                    title: '车牌号',
                    field: 'licensePlate',
                    element: 'el-input',
                },
                {
                    title: '车架号',
                    field: 'vin',
                    element: 'el-input',
                },
                {
                    title: '车主名称',
                    field: 'ownerName',
                    element: 'el-input',

                },
                {
                    title: '车主手机号码',
                    field: 'ownerPhone',
                    element: 'el-input',
                },
                {
                    field: 'jcjcStatus',
                    title: '即插即充状态',
                    element: 'el-select',
                    props: {
                        placeholder: '请选择',
                        options: [
                            {
                                label: '全部',
                                value: ''
                            },
                            {
                                label: '启用',
                                value: '1'
                            },
                            {
                                label: '禁用',
                                value: '0'
                            },
                        ]  
                    }
                },
                {
                    field: 'vehicleStatus',
                    title: '车辆状态',
                    element: 'el-select',
                    props: {
                        placeholder: '请选择',
                        options: [
                            {
                                label: '全部',
                                value: ''
                            },
                            {
                                label: '已绑定',
                                value: '1'
                            },
                            {
                                label: '未绑定',
                                value: '0'
                            },
                        ]  
                    }
                },
            ],
          params: this.params,
        };
      },
      modalConfig() {
        return {
            addBtn: false,
            viewBtn: false,
            menu: false,
            editBtn: false,
            delBtn: false,
        }
      },
    },
    mounted() {
        this.loadData();
    },
    methods: {
        async loadData() {
            const {
                licensePlate,
                vin,      
                ownerName,
                ownerPhone,
                jcjcStatus,
                vehicleStatus,
            } = this.filterOptions.params;

            this.loading = true;
            const [err, res] = await getUserVehicleList({
                licenseNo: licensePlate,
                vinCode: vin,      
                ownerName,
                mobile: ownerPhone,
                plugCharge: jcjcStatus,
                status: vehicleStatus,
                pageNum: this.tablePage.currentPage,
                pageSize: this.tablePage.pageSize,
            });
            this.loading = false;

            if (err) {
                return this.$message.error(err.message);
            }
            const { data, total } = res;


            this.tableData = data;
            this.tablePage.total = total;


            // this.tableData =  [
            //     {
            //         licenseNo: '鄂A12345',
            //         vinCode: 'LVTDB11B8ND******',
            //         ownership: '个人',
            //         ownerName: '张三',
            //         mobile: '131****5678',
            //         licenseColor: '蓝牌',
            //         brand: '宝马',
            //         carName: '宝马X7 2023款-3.0T',
            //         remark: '领导的车',
            //         plugCharge: '未开通',
            //         status: '已绑定'
            //     },
            //     {
            //         licenseNo: '鄂A12345',
            //         vinCode: 'LVTDB11B8ND******',
            //         ownership: '企业',
            //         ownerName: 'AAA公司',
            //         mobile: '131****5678',
            //         licenseColor: '蓝牌',
            //         brand: '宝马',
            //         carName: '720223款-3.0T',
            //         remark: '公司用车',
            //         plugCharge: '已开通',
            //         status: '已开通'
            //     },
            //     {
            //         licenseNo: '鄂A12345',
            //         vinCode: 'LVTDB11B8ND******',
            //         ownership: '个人',
            //         ownerName: '--',
            //         mobile: '131****5678',
            //         licenseColor: '蓝牌',
            //         brand: '宝马X7',
            //         carName: '宝马X7 2023款-3.0T',
            //         remark: '待分配',
            //         plugCharge: '已开通',
            //         status: '已绑定'
            //     },
            //     {
            //         licenseNo: '鄂A12345',
            //         vinCode: 'LVTDB11B8ND******',
            //         ownership: '租赁',
            //         ownerName: '--',
            //         mobile: '131****5678',
            //         licenseColor: '蓝牌',
            //         brand: '宝马',
            //         carName: '宝马X7 2023款-3.0T',
            //         remark: '共享车辆',
            //         plugCharge: '已开通',
            //         status: '已开通'
            //     },
            //     {
            //         licenseNo: '鄂A12345',
            //         vinCode: 'LVTDB11B8ND******',
            //         ownership: '个人',
            //         ownerName: '李四',
            //         mobile: '131****5678',
            //         licenseColor: '蓝牌',
            //         brand: '宝马',
            //         carName: '宝马X7 2023款-3.0T',
            //         remark: '待审核',
            //         plugCharge: '已开通',
            //         status: '已开通'
            //     }
            // ]
        },

        hanleUnbind(row) {
            const {
                plugCharge,
                id
            } = row
            if (plugCharge === '1') {
                // 已开通即插即冲
                this.$confirm('车辆已开通【即插即冲】，确认解绑该车辆吗？', '温馨提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                    }).then(() => {
                        this.unbindVehicle(id)
                        // this.$message({
                        //     type: 'success',
                        //     message: '解绑成功!'
                        // });
                    });
            } else {
                // 未开通即插即冲
                this.$confirm('确认解绑该车辆吗？', '温馨提示', {
                    confirmButtonText: '确定',
                    cancelButtonText: '取消',
                    type: 'warning'
                    }).then(() => {
                        this.unbindVehicle(id)
                        // this.$message({
                        //     type: 'success',
                        //     message: '解绑成功!'
                        // });
                    });
            }
        },

        async unbindVehicle(id) {
            const [err, res] = await unbindVehicle(
                {
                    id: id
                }
            )
            if (err) {
                return this.$message.error(err.message);
            }

            this.$message({
                type: 'success',
                message: '解绑成功!'
            })

            this.loadData();

        }
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .container-full {
    background-color: rgba(244, 246, 249, 1);
    padding-bottom: 16px;
    min-height: calc(100% - 80px);
    overflow-y: auto;
  }

  .table-wrap {
    ::v-deep .bd3001-table-select-box {
        display: none;
    }
    ::v-deep .bd3001-header  {
        display: block;
    }
    ::v-deep .bd3001-button {
        display: block !important;
    }
    
    .card-head {
        // position: relative; 
        height: 56px;
        padding: 0 16px;
        display: flex;
        align-items: center;
        margin-top: -20px;
        .card-head-text {
        flex:1;
        width: 520px;
            height: 26px;
            background-image: url('~@/assets/images/bg-title.png');
            background-size: 520px 26px;
            background-repeat: no-repeat;
            font-size: 20px;
            font-style: normal;
            font-weight: 500;
            line-height: 16px;
            padding-left: 36px;
            color: #21252e;
        &::before {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            left: -3px; /* 调整这个值来改变边框的宽度 */
            width: 0;
            border-top: 3px solid transparent;
            border-bottom: 3px solid transparent;
            border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
        }
        }   

    }

    .card-head-after {
        width: 100%;
        height: 1px;
        background-color: #DCDEE2;
        margin-bottom: 16px;
    }
    .info-wrap {
        margin-top: 16px;
        display: flex;
        flex-wrap: wrap;
        gap: 16px;

        .info-item {
            background-color: #FAFBFC;
            flex: 1 1 0;
            // min-width: 180px;
            
            border-radius: 5px;
            padding: 8px 24px;
            box-sizing: border-box;
            // margin-right: 16px;
            display: flex;
            .info-icon {
                width: 42px;
                height: 42px;
            }
            .info-right-wrap {
                flex:1;
                margin-left: 8px;
                .info-title {
                    font-weight: 400;
                    font-size: 14px;
                    line-height: 14px;
                    margin-bottom: 8px;
                }
                .info-number {
                    font-size: 20px;
                    font-weight: 500;
                    .info-unit {
                        font-size: 14px;
                        font-weight: 400;
                    }
                }
            }
        }
        .info-item:last-child {
            margin-right: 0;
        }
    }

    .top-button-wrap {
        display:flex;
        margin: 16px 0;
    }
}

   
 
  </style>
  