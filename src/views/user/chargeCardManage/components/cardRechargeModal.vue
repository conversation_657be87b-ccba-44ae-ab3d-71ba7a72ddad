<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    width="630px"
    @close="handleCancel"
  >
    <el-form :model="form" :rules="rules" ref="ruleForm" label-position="top">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="所属企业：" :label-width="formLabelWidth">
            <el-input v-model="info.enterpriseName" :disabled="true"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="绑定用户：" :label-width="formLabelWidth">
            <el-input
              v-model="info.cardBindUserName"
              :disabled="true"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="充电卡ID：" :label-width="formLabelWidth">
            <el-input v-model="info.cardNo" :disabled="true"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item
            label="充电卡充值金额："
            prop="transactionAmount"
            :label-width="formLabelWidth"
          >
            <el-input-number
              v-model="form.transactionAmount"
              :min="1"
              :precision="0"
              :step="1"
            ></el-input-number>
          </el-form-item>
        </el-col>
        <el-col :span="24" v-if="allAmount">
          <div class="price-wrap">
            充电卡充值后余额（元）：
            <div class="price">{{ allAmount }}</div>
          </div>
        </el-col>
      </el-row>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" :loading="submitLoading" @click="handleConfirm">
        确 定
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import { cardRecharge } from '@/api/user/chargeCardManage';
import Decimal from 'decimal.js';

export default {
  props: {
    dialogTitle: {
      type: String,
      default: '充值',
    },
  },
  components: {},
  dicts: [],
  data() {
    return {
      dialogVisible: false,
      form: {
        transactionAmount: '',
      },
      rules: {
        cardNo: [
          { required: true, message: '请输入充电卡购买数量', trigger: 'blur' },
        ],
        enterpriseId: [
          { required: true, message: '请选择购买人', trigger: 'blur' },
        ],
        cardBindUserId: [
          { required: true, message: '请选择支付方式', trigger: 'change' },
        ],
        transactionAmount: [
          {
            required: true,
            message: '请输入充电卡工本费',
            trigger: 'change',
          },
        ],
      },
      formLabelWidth: '120px',
      cardBindUserList: [
        {
          label: '企业1',
          value: '1',
        },
        {
          label: '企业2',
          value: '2',
        },
      ],
      info: {
        cardId: '',
        enterpriseId: '',
        enterpriseName: '',
        cardBindUserId: '',
        cardBindUserName: '',
        cardNo: '',
        instruction: '',
      },
      submitLoading: false,
    };
  },
  computed: {
    allAmount() {
      return new Decimal(this.form.transactionAmount || 0)
        .add(this.info.instruction || 0)
        .toFixed(2);
    },
  },
  mounted() {},
  methods: {
    resetForm() {
      Object.keys(this.form).forEach((key) => {
        this.form[key] = '';
      });
    },

    handleCancel() {
      this.$refs.ruleForm.resetFields();
      this.resetForm();
      console.log(this.form, 'this.form');
      this.dialogVisible = false;
    },

    // 新增按钮防抖
    handleConfirm() {
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          console.log(this.form, 'this.form');
          this.submitLoading = true;
          const params = {
            ...this.form,
            ...this.info,
          };

          console.log('params', params);
          const [err, res] = await cardRecharge(params);
          this.submitLoading = false;
          if (err) return;

          this.$message.success('充值成功');
          this.dialogVisible = false;
          this.$emit('loadData');
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-form-item__content {
  display: flex !important;
}

::v-deep .el-input-number {
  width: 100% !important;
}

.info-wrap {
  display: flex;
  height: 20px;
  margin-bottom: 24px;
  align-items: center;
  .info-title {
    font-weight: 400;
    font-size: 16px;
    line-height: 16px;
    color: #505363;
  }
  .info-detail {
    font-weight: 400;
    font-size: 16px;
    line-height: 16px;
    color: #292b33;
  }
}

.price-wrap {
  width: 100%;
  height: 40px;
  display: flex;
  align-items: center;
  background-color: #ebf3ff;
  border-radius: 2px;
  margin-bottom: 12px;
  font-weight: 400;
  font-size: 16px;
  padding-left: 16px;
  box-sizing: border-box;
  .price {
    font-family: Oswald Regular;
    color: #217aff;
  }
}
</style>
