<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    width="630px"
    @close="handleCancel"
  >
    <el-form :model="form" :rules="rules" ref="ruleForm" label-position="top">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="所属企业：" :label-width="formLabelWidth">
            <el-input v-model="info.enterpriseName" :disabled="true"></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="绑定用户：" :label-width="formLabelWidth">
            <el-input
              v-model="info.cardBindUserName"
              :disabled="true"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="充电卡ID：" :label-width="formLabelWidth">
            <el-input v-model="info.cardNo" :disabled="true"></el-input>
          </el-form-item>
        </el-col>

        <el-col :span="24">
          <div class="price-wrap">
            充电卡余额（元）：
            <div class="price">{{ info.cardAmount || 0 }}</div>
          </div>
        </el-col>
        <el-col :span="24">
          <el-form-item
            label="挂失说明："
            prop="instruction"
            :label-width="formLabelWidth"
          >
            <el-input
              type="textarea"
              :rows="3"
              v-model="form.instruction"
              placeholder="请输入挂失说明"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" :loading="submitLoading" @click="handleConfirm">
        确 定
      </el-button>
    </div>
  </el-dialog>
</template>
<script>
import { cardLoss } from '@/api/user/chargeCardManage';

export default {
  props: {
    dialogTitle: {
      type: String,
      default: '挂失',
    },
  },
  components: {},
  dicts: [],
  data() {
    return {
      dialogVisible: false,
      form: {
        instruction: '',
      },
      rules: {
        instruction: [
          {
            required: true,
            message: '请输入挂失说明',
            trigger: 'change',
          },
        ],
      },
      formLabelWidth: '120px',
      info: {
        cardId: '',
        enterpriseId: '',
        enterpriseName: '',
        cardBindUserId: '',
        cardBindUserName: '',
        cardNo: '',
        cardAmount: '',
      },
      submitLoading: false,
    };
  },
  computed: {},
  mounted() {},
  methods: {
    resetForm() {
      Object.keys(this.form).forEach((key) => {
        this.form[key] = '';
      });
    },

    handleCancel() {
      this.$refs.ruleForm.resetFields();
      this.resetForm();
      console.log(this.form, 'this.form');
      this.dialogVisible = false;
    },

    // 新增按钮防抖
    handleConfirm() {
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          console.log(this.form, 'this.form');
          this.submitLoading = true;
          const params = {
            ...this.form,
            ...this.info,
          };

          console.log('params', params);
          const [err, res] = await cardLoss(params);
          this.submitLoading = false;

          if (err) return;

          this.$message.success('挂失成功');
          this.dialogVisible = false;
          this.$emit('loadData');
        }
      });
    },
  },
  watch: {},
};
</script>
<style lang="scss" scoped>
::v-deep .el-form-item__content {
  display: flex !important;
}

::v-deep .el-input-number {
  width: 100% !important;
}

.info-wrap {
  display: flex;
  height: 20px;
  margin-bottom: 24px;
  align-items: center;
  .info-title {
    font-weight: 400;
    font-size: 16px;
    line-height: 16px;
    color: #505363;
  }
  .info-detail {
    font-weight: 400;
    font-size: 16px;
    line-height: 16px;
    color: #292b33;
  }
}

.price-wrap {
  width: 100%;
  height: 40px;
  display: flex;
  align-items: center;
  background-color: #ebf3ff;
  border-radius: 2px;
  margin-bottom: 12px;
  font-weight: 400;
  font-size: 16px;
  padding-left: 16px;
  box-sizing: border-box;
  .price {
    font-family: Oswald Regular;
    color: #217aff;
  }
}
</style>
