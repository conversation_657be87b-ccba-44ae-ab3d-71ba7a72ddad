<template>
  <div class="container container-float" style="padding: 0 0 100px 0">
    <el-tabs v-model="activeName" @tab-click="handleClickTab">
      <el-tab-pane label="充电卡购卡记录" name="meal">
        <div class="table-wrap">
          <BuseCrud
            ref="meal"
            :loading="mealLoading"
            :filterOptions="mealFilterOptions"
            :tablePage="mealTablePage"
            :tableColumn="mealTableColumn"
            :tableData="mealTableData"
            :modalConfig="modalConfig"
            @loadData="loadMealData"
          >
            <template slot="defaultHeader">
              <div>
                <div class="card-head">
                  <div class="card-head-text">充电卡购卡记录列表</div>

                  <div class="top-button-wrap">
                    <el-button
                      class="set-btn"
                      type="primary"
                      @click="onClickMealExport()"
                    >
                      <svg-icon iconClass="a-export-black"></svg-icon>
                      导出
                    </el-button>

                    <el-button type="primary" @click="() => handleBuyCard()">
                      <svg-icon iconClass="a-add"></svg-icon>
                      购卡
                    </el-button>
                  </div>
                </div>
              </div>
            </template>
            <template slot="operate" slot-scope="{ row }">
              <el-button class="button-border" @click="handleRefundCard(row)">
                退卡
              </el-button>
            </template>
          </BuseCrud>
        </div>
      </el-tab-pane>

      <el-tab-pane label="充电卡退卡记录" name="purchase">
        <div class="table-wrap">
          <BuseCrud
            ref="purchase"
            :loading="purchaseLoading"
            :filterOptions="purchaseFilterOptions"
            :tablePage="purchaseTablePage"
            :tableColumn="purchaseTableColumn"
            :tableData="purchaseTableData"
            :modalConfig="modalConfig"
            @loadData="loadPurchaseData"
          >
            <template slot="defaultHeader">
              <div>
                <div class="card-head">
                  <div class="card-head-text">充电卡退卡记录列表</div>

                  <div class="top-button-wrap">
                    <el-button
                      class="set-btn"
                      type="primary"
                      @click="onClickPurchaseExport()"
                    >
                      <svg-icon iconClass="a-export-black"></svg-icon>
                      导出
                    </el-button>
                  </div>
                </div>
              </div>
            </template>
          </BuseCrud>
        </div>
      </el-tab-pane>

      <el-tab-pane label="充电卡管理" name="Refund">
        <div class="table-wrap">
          <BuseCrud
            ref="refund"
            :loading="refundLoading"
            :filterOptions="refundFilterOptions"
            :tablePage="refundTablePage"
            :tableColumn="refundTableColumn"
            :tableData="refundTableData"
            :modalConfig="modalConfig"
            @loadData="loadRefundData"
          >
            <template slot="defaultHeader">
              <div>
                <div class="card-head">
                  <div class="card-head-text">充电卡管理列表</div>

                  <div class="top-button-wrap">
                    <el-button
                      class="set-btn"
                      type="primary"
                      @click="onClickRefundExport()"
                    >
                      <svg-icon iconClass="a-export-black"></svg-icon>
                      导出
                    </el-button>
                  </div>
                </div>
              </div>
            </template>

            <template slot="operate" slot-scope="{ row }">
              <el-button class="button-border" @click="bindUser(row)">
                绑定用户
              </el-button>
              <el-button class="button-border" @click="cardRecharge(row)">
                充值
              </el-button>
              <el-button class="button-border" @click="handleCardRefund(row)">
                退卡
              </el-button>
              <el-button class="button-border" @click="handleRefundCard(row)">
                流水
              </el-button>
              <el-button class="button-border" @click="releaseUser(row)">
                解除用户
              </el-button>
              <el-button class="button-border" @click="handleRefundCard(row)">
                补卡
              </el-button>
              <el-button class="button-border">挂失</el-button>
              <el-button class="button-border" @click="cancelLossReport(row)">
                取消挂失
              </el-button>
              <el-button class="button-border" @click="handleRefundCard(row)">
                注销
              </el-button>
            </template>
          </BuseCrud>
        </div>
      </el-tab-pane>

      <el-tab-pane label="充电卡业务记录" name="manage">
        <div class="table-wrap">
          <BuseCrud
            ref="purchase"
            :loading="businessLoading"
            :filterOptions="businessFilterOptions"
            :tablePage="businessTablePage"
            :tableColumn="businessTableColumn"
            :tableData="businessTableData"
            :modalConfig="modalConfig"
            @loadData="loadBusinessData"
          >
            <template slot="defaultHeader">
              <div>
                <div class="card-head">
                  <div class="card-head-text">充电卡业务记录列表</div>

                  <div class="top-button-wrap">
                    <el-button
                      class="set-btn"
                      type="primary"
                      @click="onClickPurchaseExport()"
                    >
                      <svg-icon iconClass="a-export-black"></svg-icon>
                      导出
                    </el-button>
                  </div>
                </div>
              </div>
            </template>
          </BuseCrud>
        </div>
      </el-tab-pane>
    </el-tabs>

    <BuyCardModal ref="buyCardModal" />

    <RefundCardModal ref="refundCardModal" />

    <CardRefundModal ref="cardRefundModal" />

    <BindUserModal ref="bindUserModal" />

    <CardRechargeModal ref="cardRechargeModal" />

    <CancelLossReportModal ref="cancelLossReportModal" />
  </div>
</template>

<script>
import BuyCardModal from './components/buyCardModal.vue';
import RefundCardModal from './components/refundCardModal.vue';
import CardRefundModal from './components/cardRefundModal.vue';
import BindUserModal from './components/bindUserModal.vue';
import CardRechargeModal from './components/cardRechargeModal.vue';
import CancelLossReportModal from './components/cancelLossReportModal.vue';

export default {
  components: {
    BuyCardModal,
    RefundCardModal,
    CardRefundModal,
    BindUserModal,
    CardRechargeModal,
    CancelLossReportModal,
  },
  dicts: [],
  data() {
    return {
      activeName: 'meal',
      mealLoading: false,
      mealParams: {
        mealId: '',
        mealName: '',
        mealStatus: '',
        isPassword: '',
        discountType: '',
      },
      mealTablePage: { total: 0, currentPage: 1, pageSize: 10 },
      mealTableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
          fixed: 'left',
        },
        {
          field: 'mealId',
          title: '业务订单号',
          minWidth: 150,
        },
        {
          field: 'mealName',
          title: '交易流水',
          minWidth: 150,
        },
        {
          field: 'mealAmount',
          title: '充电卡购买数量',
          minWidth: 180,
        },
        {
          field: 'mealPrice',
          title: '充电卡绑定员工数量',
          minWidth: 180,
        },
        {
          field: 'discountType',
          title: '充电卡购卡人',
          minWidth: 150,
        },
        {
          field: 'isPassword',
          title: '充电卡工本费',
          minWidth: 120,
        },
        {
          field: 'useScope',
          title: '支付方式',
          minWidth: 120,
        },
        {
          field: 'costUnit',
          title: '交易金额',
          minWidth: 120,
        },
        {
          field: 'remark',
          title: '交易时间',
          minWidth: 180,
        },
        {
          field: 'effectTime',
          title: '操作人',
          minWidth: 120,
        },
        {
          field: 'status',
          title: '交易状态',
          minWidth: 120,
          fixed: 'right',
          align: 'center',
        },
        {
          title: '操作',
          slots: { default: 'operate' },
          width: 120,
          align: 'center',
          fixed: 'right',
        },
      ],
      mealTableData: [],

      purchaseLoading: false,
      purchaseParams: {
        businessId: '',
        tradeNo: '',
        tradeTime: [],
        mealName: '',
        tradeType: '',
        tradeStatus: '',
        creator: '',
      },
      purchaseTablePage: { total: 0, currentPage: 1, pageSize: 10 },
      purchaseTableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
          fixed: 'left',
        },
        {
          field: 'mealName',
          title: '退款单号',
          minWidth: 180,
        },
        {
          field: 'businessId',
          title: '业务订单号',
          minWidth: 150,
        },
        {
          field: 'tradeNo',
          title: '交易流水号',
          minWidth: 150,
        },

        {
          field: 'buyNumber',
          title: '充电卡可退数量',
          minWidth: 180,
        },
        {
          field: 'useNumber',
          title: '充电卡退卡数量',
          minWidth: 200,
        },
        {
          field: 'buyer',
          title: '充电卡退卡企业',
          minWidth: 120,
        },
        {
          field: 'tradeAmount',
          title: '退款方式',
          minWidth: 120,
        },
        {
          field: 'tradeAmount',
          title: '交易金额',
          minWidth: 120,
        },
        {
          field: 'tradeTime',
          title: '交易时间',
          minWidth: 120,
        },

        {
          field: 'tradeStatus',
          title: '交易状态',
          minWidth: 120,
          fixed: 'right',
        },
        {
          field: 'creator',
          title: '操作人',
          minWidth: 120,
        },
      ],
      purchaseTableData: [],

      refundLoading: false,
      refundParams: {
        businessId: '',
        tradeNo: '',
        tradeTime: [],
        mealName: '',
        refundNo: '',
        tradeStatus: '',
        creator: '',
      },
      refundTablePage: { total: 0, currentPage: 1, pageSize: 10 },
      refundTableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
          fixed: 'left',
        },
        {
          field: 'refundNo',
          title: '充电卡ID',
          minWidth: 150,
        },
        {
          field: 'businessId',
          title: '充电卡余额（元）',
          minWidth: 150,
        },
        {
          field: 'tradeNo',
          title: '充电卡绑定状态',
          minWidth: 150,
        },
        {
          field: 'mealName',
          title: '充电卡绑定用户',
          minWidth: 180,
        },
        {
          field: 'buyNumber',
          title: '充电卡绑定时间',
          minWidth: 180,
        },
        {
          field: 'useNumber',
          title: '操作人',
          minWidth: 200,
        },
        {
          field: 'refundNumber',
          title: '充电卡状态',
          minWidth: 120,
          fixed: 'right',
          align: 'center',
        },
        {
          field: 'refundMethod',
          title: '所属企业',
          minWidth: 120,
        },
        {
          title: '操作',
          slots: { default: 'operate' },
          width: 350,
          align: 'center',
          fixed: 'right',
        },
      ],
      refundTableData: [],
      businessLoading: false,

      businessTablePage: { total: 0, currentPage: 1, pageSize: 10 },
      businessTableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
          fixed: 'left',
        },
        {
          field: 'refundMethod',
          title: '业务类型',
          minWidth: 120,
        },
        {
          field: 'tradeTime',
          title: '操作时间',
          minWidth: 120,
        },
        {
          field: 'creator',
          title: '操作员',
          minWidth: 120,
        },
        {
          field: 'tradeStatus',
          title: '操作说明',
          minWidth: 120,
        },
      ],
      businessTableData: [],
    };
  },

  computed: {
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
    mealFilterOptions() {
      return {
        config: [
          {
            field: 'mealId',
            title: '业务订单号',
            element: 'el-input',
          },
          {
            field: 'mealName',
            title: '交易流水号',
            element: 'el-input',
          },
          {
            field: 'mealStatus',
            title: '交易时间',
            element: 'el-date-picker',
            props: {
              type: 'daterange',
              placeholder: '开始日期 - 自定日期',
              startPlaceholder: '开始日期',
              endPlaceholder: '结束日期',
              valueFormat: 'yyyy-MM-dd',
            },
          },
          {
            field: 'transactionType',
            title: '交易类型',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
          {
            field: 'discountType',
            title: '操作人',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
        ],
        params: this.mealParams,
      };
    },
    purchaseFilterOptions() {
      return {
        config: [
          {
            field: 'businessId',
            title: '业务订单号',
            element: 'el-input',
          },
          {
            field: 'tradeNo',
            title: '交易流水号',
            element: 'el-input',
          },
          {
            field: 'tradeTime',
            title: '交易时间',
            element: 'el-date-picker',
            props: {
              type: 'datetimerange',
              options: [],
              valueFormat: 'yyyy-MM-dd HH:mm:ss',
            },
          },

          {
            field: 'tradeStatus',
            title: '交易状态',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
          {
            field: 'mealName',
            title: '退款单号',
            element: 'el-input',
          },

          {
            field: 'creator',
            title: '操作人',
            element: 'el-input',
          },
        ],
        params: this.purchaseParams,
      };
    },

    refundFilterOptions() {
      return {
        config: [
          {
            field: 'businessId',
            title: '充电卡ID',
            element: 'el-input',
          },
          {
            field: 'tradeNo',
            title: '绑定用户',
            element: 'el-input',
          },
          {
            field: 'tradeTime',
            title: '绑定时间',
            element: 'el-date-picker',
            props: {
              type: 'daterange',
              placeholder: '开始日期 - 自定日期',
              startPlaceholder: '开始日期',
              endPlaceholder: '结束日期',
              valueFormat: 'yyyy-MM-dd',
            },
          },

          {
            field: 'tradeStatus',
            title: '绑定类型',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
          {
            field: 'creator',
            title: '操作人',
            element: 'el-input',
          },
          {
            field: 'tradeStatus',
            title: '所属企业',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
          {
            field: 'tradeStatus',
            title: '充电卡状态',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
        ],
        params: this.refundParams,
      };
    },

    businessFilterOptions() {
      return {
        config: [
          {
            field: 'tradeTime',
            title: '操作时间',
            element: 'el-date-picker',
            props: {
              type: 'daterange',
              placeholder: '开始日期 - 自定日期',
              startPlaceholder: '开始日期',
              endPlaceholder: '结束日期',
              valueFormat: 'yyyy-MM-dd',
            },
          },
          {
            field: 'creator',
            title: '操作员',
            element: 'el-input',
          },
          {
            field: 'tradeStatus',
            title: '业务状态',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
        ],
        params: this.refundParams,
      };
    },
  },
  mounted() {
    this.loadMealData();
  },
  methods: {
    // 取消挂失
    cancelLossReport(row) {
      this.$refs.cancelLossReportModal.dialogVisible = true;
    },

    // 充值
    cardRecharge(row) {
      this.$refs.cardRechargeModal.dialogVisible = true;
    },

    // 绑定用户
    bindUser(row) {
      this.$refs.bindUserModal.dialogVisible = true;
    },

    // 解绑用户
    releaseUser(row) {
      this.$confirm(`确定解绑用户吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        this.$message({
          type: 'success',
          message: '解绑成功!',
        });
      });
    },
    // 退卡
    handleCardRefund(row) {
      this.$refs.cardRefundModal.dialogVisible = true;
    },
    async handleClickTab({ index }) {
      if (index === '0') {
        this.mealTablePage = {
          total: 0,
          currentPage: 1,
          pageSize: 10,
        };

        this.mealTableData = [];
        this.loadMealData();
      } else if (index === '1') {
        this.purchaseTablePage = {
          total: 0,
          currentPage: 1,
          pageSize: 10,
        };

        this.purchaseTableData = [];
        this.loadPurchaseData();
      } else if (index === '2') {
        this.refundTablePage = {
          total: 0,
          currentPage: 1,
          pageSize: 10,
        };

        this.refundTableData = [];
        this.loadRefundData();
      } else if (index === '3') {
        this.businessTablePage = {
          total: 0,
          currentPage: 1,
          pageSize: 10,
        };

        this.businessTableData = [];
        this.loadBusinessData();
      }
    },

    async loadMealData() {
      this.mealTableData = [
        {
          mealId: 'PKG001',
          mealName: '经济充电套餐',
          mealAmount: 100,
          mealPrice: 90,
          discountType: '立减10元',
          isPassword: '是',
          useScope: '全国通用',
          costUnit: '元',
          remark: '适用于快充站',
          effectTime: '2023-01-01 至 2023-12-31',
          status: '生效中',
          action: '编辑｜下架',
        },
        {
          mealId: 'PKG002',
          mealName: '标准充电套餐',
          mealAmount: 200,
          mealPrice: 180,
          discountType: '9折优惠',
          isPassword: '否',
          useScope: '指定合作站点',
          costUnit: '元',
          remark: '仅限夜间使用',
          effectTime: '2023-06-01 至 2024-05-31',
          status: '生效中',
          action: '编辑｜下架',
        },
        {
          mealId: 'PKG003',
          mealName: 'VIP尊享套餐',
          mealAmount: 500,
          mealPrice: 450,
          discountType: '赠送50积分',
          isPassword: '是',
          useScope: '全国通用',
          costUnit: '元',
          remark: '含优先充电权益',
          effectTime: '2023-03-15 至 2023-12-31',
          status: '已下架',
          action: '编辑｜上架',
        },
      ];
    },

    // 新增套餐
    handleAddMeal() {
      this.$router.push({
        path: '/v2g-charging-web/baseInfo/userManage/chargePackageManage/createMeal',
      });
    },

    async loadPurchaseData() {
      this.purchaseTableData = [
        {
          businessId: 'BID202307280001',
          tradeNo: 'TRADE20230728123456',
          mealName: '经济充电套餐',
          buyNumber: 2,
          useNumber: 1,
          buyer: '张三',
          tradeAmount: 180.0,
          tradeTime: '2023-07-28 14:30:45',
          payMethod: '支付宝',
          accountId: 'USER_1001',
          creator: '系统自动',
          tradeStatus: '支付成功',
        },
        {
          businessId: 'BID202307280002',
          tradeNo: 'TRADE20230728987654',
          mealName: 'VIP尊享套餐',
          buyNumber: 1,
          useNumber: 0,
          buyer: '李四',
          tradeAmount: 450.0,
          tradeTime: '2023-07-28 15:12:33',
          payMethod: '微信支付',
          accountId: 'CORP_2001',
          creator: '管理员',
          tradeStatus: '已完成',
        },
        {
          businessId: 'BID202307280003',
          tradeNo: 'TRADE20230728555555',
          mealName: '标准充电套餐',
          buyNumber: 5,
          useNumber: 5,
          buyer: '王五',
          tradeAmount: 900.0,
          tradeTime: '2023-07-28 09:45:21',
          payMethod: '企业转账',
          accountId: 'VIP_3001',
          creator: '财务部',
          tradeStatus: '已退款',
        },
        {
          businessId: 'BID202307280004',
          tradeNo: 'TRADE20230728111111',
          mealName: '夜间特惠套餐',
          buyNumber: 3,
          useNumber: 2,
          buyer: '赵六',
          tradeAmount: 270.0,
          tradeTime: '2023-07-28 22:15:59',
          payMethod: '银联卡',
          accountId: 'NIGHT_4001',
          creator: '客服代操作',
          tradeStatus: '部分退款',
        },
        {
          businessId: 'BID202307280005',
          tradeNo: 'TRADE20230728333333',
          mealName: '企业定制套餐',
          buyNumber: 10,
          useNumber: 8,
          buyer: 'XX科技有限公司',
          tradeAmount: 8000.0,
          tradeTime: '2023-07-28 11:03:17',
          payMethod: '对公转账',
          accountId: 'ENT_5001',
          creator: '企业管理员',
          tradeStatus: '待确认',
        },
      ];
    },

    // 购卡
    handleBuyCard() {
      this.$refs.buyCardModal.dialogVisible = true;
    },

    // 退卡
    handleRefundCard() {
      this.$refs.refundCardModal.info = {
        mealName: '100元充电包',
        mealPrice: 100.0,
        canRefundNumber: 50,
      };
      this.$refs.refundCardModal.dialogVisible = true;
    },

    async loadRefundData() {
      this.refundTableData = [
        {
          refundNo: 'RF202310230001',
          businessId: 'ORD202310230001',
          tradeNo: 'TR202310230001',
          mealName: '经济型充电包（30天）',
          buyNumber: 2,
          useNumber: 1,
          refundNumber: 1,
          refundMethod: '原路退回',
          tradeAmount: 500.0,
          tradeTime: '2023-10-23 09:25:47',
          refundCompany: '深圳充电科技有限公司',
          creator: '张三',
          tradeStatus: '已完成',
        },
        {
          refundNo: 'RF202310230002',
          businessId: 'ORD202310230045',
          tradeNo: 'TR202310230087',
          mealName: '超级快充套餐（90天）',
          buyNumber: 1,
          useNumber: 0,
          refundNumber: 1,
          refundMethod: '线下转账',
          tradeAmount: 899.0,
          tradeTime: '2023-10-23 11:12:33',
          refundCompany: '北京新能源集团',
          creator: '李四',
          tradeStatus: '处理中',
        },
        {
          refundNo: 'RF202310230003',
          businessId: 'ORD202310230112',
          tradeNo: 'TR202310230156',
          mealName: '夜间专用充电包',
          buyNumber: 3,
          useNumber: 2,
          refundNumber: 1,
          refundMethod: '原路退回',
          tradeAmount: 360.0,
          tradeTime: '2023-10-23 14:30:00',
          refundCompany: '上海电力服务有限公司',
          creator: '王五',
          tradeStatus: '已关闭',
        },
        {
          refundNo: 'RF202310230010',
          businessId: 'ORD202310230999',
          tradeNo: 'TR202310230888',
          mealName: '企业定制充电包',
          buyNumber: 10,
          useNumber: 8,
          refundNumber: 2,
          refundMethod: '优惠券返还',
          tradeAmount: 2000.0,
          tradeTime: '2023-10-23 16:55:21',
          refundCompany: '杭州智能充电集团',
          creator: '赵六',
          tradeStatus: '部分退款',
        },
      ];
    },

    async loadBusinessData() {
      this.businessTableData = [
        {
          refundNo: 'RF202310230001',
          businessId: 'ORD202310230001',
          tradeNo: 'TR202310230001',
          mealName: '经济型充电包（30天）',
          buyNumber: 2,
          useNumber: 1,
          refundNumber: 1,
          refundMethod: '原路退回',
          tradeAmount: 500.0,
          tradeTime: '2023-10-23 09:25:47',
          refundCompany: '深圳充电科技有限公司',
          creator: '张三',
          tradeStatus: '已完成',
        },
        {
          refundNo: 'RF202310230002',
          businessId: 'ORD202310230045',
          tradeNo: 'TR202310230087',
          mealName: '超级快充套餐（90天）',
          buyNumber: 1,
          useNumber: 0,
          refundNumber: 1,
          refundMethod: '线下转账',
          tradeAmount: 899.0,
          tradeTime: '2023-10-23 11:12:33',
          refundCompany: '北京新能源集团',
          creator: '李四',
          tradeStatus: '处理中',
        },
        {
          refundNo: 'RF202310230003',
          businessId: 'ORD202310230112',
          tradeNo: 'TR202310230156',
          mealName: '夜间专用充电包',
          buyNumber: 3,
          useNumber: 2,
          refundNumber: 1,
          refundMethod: '原路退回',
          tradeAmount: 360.0,
          tradeTime: '2023-10-23 14:30:00',
          refundCompany: '上海电力服务有限公司',
          creator: '王五',
          tradeStatus: '已关闭',
        },
        {
          refundNo: 'RF202310230010',
          businessId: 'ORD202310230999',
          tradeNo: 'TR202310230888',
          mealName: '企业定制充电包',
          buyNumber: 10,
          useNumber: 8,
          refundNumber: 2,
          refundMethod: '优惠券返还',
          tradeAmount: 2000.0,
          tradeTime: '2023-10-23 16:55:21',
          refundCompany: '杭州智能充电集团',
          creator: '赵六',
          tradeStatus: '部分退款',
        },
      ];
    },
  },
};
</script>

<style lang="scss" scoped>
.container-full {
  background-color: rgba(244, 246, 249, 1);
  padding-bottom: 16px;
  min-height: calc(100% - 80px);
  overflow-y: auto;
}

::v-deep .el-tabs {
  margin-top: 16px;
  background-color: #f5f6f9;
  .el-tabs__header {
    padding-left: 0;
    display: flex;
    justify-content: center;
    text-align: center;
    margin-bottom: 1px;
    .el-tabs__item {
      padding: 0;
      width: 164px;
      font-size: 18px;
      font-weight: 400;
      background-color: #fff;
    }
    .el-tabs__item.is-active {
      background-color: #1677fe;
      color: #fff;
    }
    .el-tabs__nav-scroll {
      border-radius: 25px;
      border: solid 1px #dfe1e5;
    }
    .el-tabs__active-bar {
      display: none;
    }
    .el-tabs__nav-wrap::after {
      width: 0;
    }
  }
}

.table-wrap {
  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }
  margin: 16px;

  .card-head {
    // position: relative;
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    margin-top: -20px;
    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }
  .info-wrap {
    margin-top: 16px;
    display: flex;
    flex-wrap: wrap;
    gap: 16px;

    .info-item {
      background-color: #fafbfc;
      flex: 1 1 0;
      // min-width: 180px;

      border-radius: 5px;
      padding: 8px 24px;
      box-sizing: border-box;
      // margin-right: 16px;
      display: flex;
      .info-icon {
        width: 42px;
        height: 42px;
      }
      .info-right-wrap {
        flex: 1;
        margin-left: 8px;
        .info-title {
          font-weight: 400;
          font-size: 14px;
          line-height: 14px;
          margin-bottom: 8px;
        }
        .info-number {
          font-size: 20px;
          font-weight: 500;
          .info-unit {
            font-size: 14px;
            font-weight: 400;
          }
        }
      }
    }
    .info-item:last-child {
      margin-right: 0;
    }
  }

  .top-button-wrap {
    display: flex;
    margin: 16px 0;
    .set-btn {
      background-color: #ffffff;
      color: #292b33;
      border-color: #dfe1e5;
    }
  }
}

.button-border {
  border: 0.01rem solid #217aff;
  color: #217aff;
  background-color: #fff;
}
</style>
