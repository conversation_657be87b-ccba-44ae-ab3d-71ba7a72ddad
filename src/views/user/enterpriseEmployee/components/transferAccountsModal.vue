<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    @close="handleCancel"
    width="532px"
    :destroy-on-close="true"
  >
    <div>
      <el-form :model="form" :rules="rules" ref="ruleForm" label-position="top">
        <div class="form-item-wrap">
          <div class="label-text">{{ userName }} 当前账户余额（元）：</div>
          <div class="value-text">{{ currentBalance }}</div>
        </div>

        <el-form-item label="企业/机构：" prop="organization" required>
          <el-select
            v-model="form.organization"
            placeholder="请选择"
            style="width: 100%"
            disabled
            @change="handleSelectOrganization"
          >
            <el-option
              v-for="item in organizationOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <div class="blue-wrap">
          <div class="form-item-wrap">
            <div class="label-text">{{provinceName}} 账户余额（元）：</div>
            <div class="value-text">{{ provinceBalance }}</div>
          </div>
        </div>
       

        <el-form-item :label="this.type === 'in'?'转入金额（元）：': '转出金额（元）：'" prop="amount" required>
          <div class="amount-input-wrap">
            <el-input
              v-model="form.amount"
              placeholder="请输入数字"
              class="amount-input"
              type="number"
            ></el-input>
            <div class="unit">元</div>
          </div>
        </el-form-item>

        <div class="blue-wrap">
          <div class="form-item-wrap" style="margin-bottom: 0;">
            <div class="label-text" v-if="dialogTitle == '账户转入'">
              转入后{{provinceName}}账户余额（元）：
            </div>
            <div class="label-text" v-if="dialogTitle == '账户转出'">
              转出后{{provinceName}}账户余额（元）：
            </div>
            <div class="value-text">{{ afterProvinceBalance }}</div>
          </div>

          <div class="form-item-wrap" >
            <div class="label-text" v-if="dialogTitle == '账户转入'">
              转入后{{userName}}账户余额（元）：
            </div>
            <div class="label-text" v-if="dialogTitle == '账户转出'">
              转出后{{userName}}账户余额（元）：
            </div>
            <div class="value-text">{{ afterDepartmentBalance }}</div>
          </div>
        </div>

       

        <el-form-item label="备注：" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            :rows="4"
            placeholder="请输入"
          ></el-input>
        </el-form-item>
      </el-form>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" @click="handleConfirm">确 定</el-button>
    </div>
  </el-dialog>
</template>
<script>

import {
  queryEnterpriseList,
  transferAccount,
} from '@/api/user/enterpriseEmployee'

import _ from 'lodash';


export default {
  props: {
    dialogTitle: {
      type: String,
      default: '充电站选择',
    },
  },
  components: {},
  data() {
    return {
      dialogVisible: false,
      type: '',
      userName: '',
      userId: '',
      currentBalance: '',
      provinceName: '',
      provinceBalance: '',
      afterProvinceBalance: '',
      afterDepartmentBalance: '',
      form: {
        organization: '',
        amount: '',
        remark: '',
      },
      rules: {
        organization: [
          { required: true, message: '请选择企业/机构', trigger: 'change' },
        ],
        amount: [
          { required: true, message: '请输入金额', trigger: 'blur' },
          {
            pattern: /^[0-9]+(\.[0-9]{1,2})?$/,
            message: '请输入正确的金额格式',
            trigger: 'blur',
          },
        ],
      },
      organizationOptions: [],
    };
  },
  watch: {
    'form.amount': {
      handler(val) {
        if (val && !isNaN(val)) {
          const amount = parseFloat(val);
          const currentBalance = parseFloat(this.currentBalance);
          const provinceBalance = parseFloat(this.provinceBalance);
          if(this.type === 'in') {
            this.afterProvinceBalance = (provinceBalance + amount).toFixed(2);
            this.afterDepartmentBalance = (currentBalance + amount).toFixed(2);
          } else {
            this.afterProvinceBalance = (provinceBalance  - amount).toFixed(2);
            this.afterDepartmentBalance = (currentBalance - amount).toFixed(2);
          }
         
        } else {
          this.afterProvinceBalance = this.provinceBalance;
          this.afterDepartmentBalance = this.currentBalance;
        }
      },
      immediate: true,
    },
  },
  mounted() {
    this.loadData();
  },
  methods: {
    async loadData() {
      const [err, res] = await queryEnterpriseList({})

      if(err)  return 

      const {
        data
      } = res

      const list = []

      data.forEach(element => {
        list.push({
          value: element.userId,
          label: element.enterpriseName,
          amount: element.accountBalance
        })
      });

      this.organizationOptions = list
    },
    // 机构变化
    handleSelectOrganization() {
      this.organizationOptions.forEach(element => {
        if(element.value === this.form.organization) {
          this.provinceBalance = element.amount
          this.provinceName = element.label
        }
      });
    },

    // 关闭弹窗
    handleCancel() {
      this.resetForm();
      this.dialogVisible = false;
    },
    resetForm() {
      this.type = '',
      this.userName = '',
      this.currentBalance ='',
      this.provinceBalance ='',
      this.afterProvinceBalance = '',
      this.afterDepartmentBalance = '',
      this.form = {
        organization: '',
        amount: '',
        remark: '',
      }
    },
    handleConfirm: _.debounce(function() {
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          // 提交表单逻辑
          const {
            organization,
            amount,
            remark,
          } = this.form;
            const params = {
              employeeUserId: this.userId,
              enterpriseUserId: organization,
              amt: amount,
              type: this.type === 'in' ? 'IN' : 'OUT',
              remark,
            }
            const [err, res] = await transferAccount(params);
            if (err) return;
                  
            this.$message.success(this.type === 'in' ? '转入成功' : '转出成功') ;
            this.dialogVisible = false;
            this.resetForm()
            this.$emit('loadData');
        } else {
          return false;
        }
      });
    },300)
  },
};
</script>
<style lang="scss" scoped>
.transfer-form {
  padding: 0 20px;
}

.form-item-wrap {
  display: flex;
  margin-bottom: 22px;
  line-height: 40px;

  .label-text {
    width: 280px;
    // text-align: right;
    padding-right: 12px;
    box-sizing: border-box;
    color: #606266;
  }

  .value-text {
    flex: 1;
    color: #409eff;
    font-weight: bold;
    font-size: 20px;
  }
}

.amount-input-wrap {
  display: flex;
  align-items: center;

  .amount-input {
    flex: 1;
  }

  .unit {
    margin-left: 10px;
    padding: 0 10px;
    // background-color: #f5f7fa;
    color: #606266;
    height: 40px;
    line-height: 40px;
    // border-radius: 0 4px 4px 0;
    // border: 1px solid #dcdfe6;
    // border-left: none;
  }
}

.el-form-item.is-required .el-form-item__label:before {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
}

.blue-wrap {
  padding:  0 16px;
  box-sizing: border-box;
  background-color: #EBF3FF;
  border-radius: 2px;
}
</style>
