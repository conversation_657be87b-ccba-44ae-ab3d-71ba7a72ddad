<template>
  <div class="container">
    <div class="info-card" style="margin-top: 0px">
      <div class="card-head" style="margin-top: 0">
        <div class="before-icon"></div>
        <div class="card-head-text">基础信息</div>
      </div>
      <div class="card-head-after"></div>
      <div class="form-wrap">
        <el-form
          :model="basicInforForm"
          :rules="basicInfoRrules"
          ref="basicInforForm"
          label-position="top"
        >
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="所属企业机构" prop="enterpriseId">
                <el-select
                  v-model="basicInforForm.enterpriseId"
                  placeholder="请选择"
                  style="width: 100%"
                  :disabled="isDetail"
                >
                  <el-option
                    v-for="item in enterpriseOption"
                    :key="item.enterpriseId"
                    :label="item.enterpriseName"
                    :value="item.enterpriseId"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="用户名" prop="fullName">
                <el-input
                  v-model="basicInforForm.fullName"
                  placeholder="请输入"
                  :disabled="isDetail"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="手机号" prop="mobile">
                <el-input
                  v-model="basicInforForm.mobile"
                  placeholder="请输入"
                  type="number"
                  @input="handlePhoneInput"
                  :disabled="isDetail"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <!-- <el-col :span="8">
              <el-form-item label="所属机构" prop="institution">
                <el-select
                  v-model="basicInforForm.institution"
                  placeholder="请选择"
                  style="width: 100%"
                  :disabled="isDetail"
                >
                  <el-option
                    v-for="item in institutionOption"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col> -->
            <el-col :span="8">
              <el-form-item label="岗位角色" prop="role">
                <el-select
                  v-model="basicInforForm.role"
                  placeholder="请选择"
                  style="width: 100%"
                  :disabled="isDetail"
                >
                  <el-option
                    v-for="item in roleOption"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="用户状态" prop="status">
                <el-select
                  v-model="basicInforForm.status"
                  placeholder="请选择"
                  style="width: 100%"
                  :disabled="isDetail"
                >
                  <el-option
                    v-for="item in statusOption"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <div class="info-card">
      <div class="card-head" style="margin-top: 0">
        <div class="before-icon"></div>
        <div class="card-head-text">使用限制配置</div>
      </div>
      <div class="card-head-after"></div>
      <div class="form-wrap">
        <el-form
          :model="limitationsForm"
          :rules="limitationsRrules"
          ref="limitationsForm"
          label-position="top"
        >
          <el-row :gutter="20">
            <!-- 次数限制 -->
            <el-col :span="2">次数限制</el-col>
            <el-col :span="8">
              <el-form-item prop="dayLimit">
                <el-input
                  v-model="limitationsForm.dayLimit"
                  placeholder="请输入"
                  :disabled="isDetail"
                >
                  <template slot="prepend">单日次数</template>
                  <template slot="append">次</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="totalLimit">
                <el-input
                  v-model="limitationsForm.totalLimit"
                  placeholder="请输入"
                  :disabled="isDetail"
                >
                  <template slot="prepend">总量可用</template>
                  <template slot="append">次</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6" style="color: #909399; font-size: 12px">
              <i class="el-icon-question"></i>
              自然日内可用充电次数，如超出则下次不可启动
              
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <!-- 电量限制 -->
            <el-col :span="2">电量限制</el-col>
            <el-col :span="8">
              <el-form-item prop="dayPqLimit">
                <el-input
                  v-model="limitationsForm.dayPqLimit"
                  placeholder="请输入"
                  :disabled="isDetail"
                >
                  <template slot="prepend">单日电量</template>
                  <template slot="append">kWh</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="totalPqLimit">
                <el-input
                  v-model="limitationsForm.totalPqLimit"
                  placeholder="请输入"
                  :disabled="isDetail"
                >
                  <template slot="prepend">总量可用</template>
                  <template slot="append">kWh</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6" style="color: #909399; font-size: 12px">
              <i class="el-icon-question"></i>
              自然日内可用充电电量，如超出则下次不可启动
              
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <!-- 金额限制 -->
            <el-col :span="2">金额限制</el-col>
            <el-col :span="8">
              <el-form-item prop="dayAmtLimit">
                <el-input
                  v-model="limitationsForm.dayAmtLimit"
                  placeholder="请输入"
                  :disabled="isDetail"
                >
                  <template slot="prepend">单日金额</template>
                  <template slot="append">元</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item prop="totalAmtLimit">
                <el-input
                  v-model="limitationsForm.totalAmtLimit"
                  placeholder="请输入"
                  :disabled="isDetail"
                >
                  <template slot="prepend">总量可用</template>
                  <template slot="append">元</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6" style="color: #909399; font-size: 12px">
              <i class="el-icon-question"></i>
              自然日内可用充电金额，如超出则下次不可启动
              
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <div class="info-card" v-if="isDetail">
      <div class="card-head" style="margin-top: 0">
        <div class="before-icon"></div>
        <div class="card-head-text">车辆基础信息</div>
      </div>
      <div class="card-head-after"></div>
      <div class="form-wrap">
        <el-table :data="vehicleTableData" style="width: 100%">
          <el-table-column
            type="index"
            label="序号"
            width="60"
          ></el-table-column>
          <el-table-column prop="plateNumber" label="车牌号"></el-table-column>
          <el-table-column prop="frameNumber" label="车架号"></el-table-column>
          <el-table-column
            prop="vehicleType"
            label="车辆所属类型"
          ></el-table-column>
          <el-table-column prop="plateType" label="车牌类型"></el-table-column>
          <el-table-column
            prop="vehicleBrand"
            label="车辆品牌"
          ></el-table-column>
          <el-table-column
            prop="vehicleModel"
            label="车辆型号"
          ></el-table-column>
          <el-table-column label="车辆状态">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.vehicleStatus"
                :disabled="isDetail"
                :active-value="1"
                :inactive-value="0"
              ></el-switch>
            </template>
          </el-table-column>
          <el-table-column label="即插即充状态">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.plugAndChargeStatus"
                :disabled="isDetail"
                :active-value="1"
                :inactive-value="0"
              ></el-switch>
            </template>
          </el-table-column>
          <el-table-column
            prop="chargingLimit"
            label="充电限额（元）"
          ></el-table-column>
          <el-table-column prop="updateTime" label="更新时间"></el-table-column>
        </el-table>
      </div>
    </div>

    <div class="bottom-wrap">
      <el-button @click="() => handleCancel()">
        <span v-if="!isDetail">取消</span>
        <span v-if="isDetail">返回</span>
      </el-button>
      <el-button
        type="primary"
        :loading="submitLoading"
        @click="() => handleConfirm()"
        v-if="!isDetail"
      >
        提交
      </el-button>
    </div>
  </div>
</template>

<script>
import {
  queryEnterpriseList,
  enterpriseUserAdd,
  enterpriseUserEdit,
  queryDetail,
} from '@/api/user/enterpriseEmployee';

export default {
  data() {
    return {
      basicInforForm: {
        enterpriseId: '',
        fullName: '',
        mobile: '',
        institution: '',
        role: '',
        status: '',
      },
      basicInfoRrules: {
        enterpriseId: [
          { required: true, message: '请选择所属企业机构', trigger: 'change' },
        ],
        fullName: [
          { required: true, message: '请输入用户名', trigger: 'blur' },
        ],
        mobile: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          {
            pattern: /^1[3-9]\d{9}$/,
            message: '请输入正确的手机号',
            trigger: 'blur',
          },
        ],
        institution: [
          { required: true, message: '请选择所属机构', trigger: 'change' },
        ],
        role: [
          { required: true, message: '请选择岗位角色', trigger: 'change' },
        ],
        status: [
          { required: true, message: '请选择用户状态', trigger: 'change' },
        ],
      },
      enterpriseOption: [],
      institutionOption: [
        { label: 'XX二级机构', value: '1' },
        { label: 'XXX二级机构', value: '2' },
      ],
      roleOption: [{ label: '经理', value: '经理' }],
      statusOption: [
        { label: '启用', value: 'ENABLE' },
        { label: '禁用', value: 'DISABLE' },
      ],
      limitationsForm: {
        dayLimit: '',
        totalLimit: '',
        dayPqLimit: '',
        totalPqLimit: '',
        dayAmtLimit: '',
        totalAmtLimit: '',
      },
      limitationsRrules: {
        // dayLimit: [
        //   { required: true, message: '请输入单日次数', trigger: 'blur' },
        // ],
        // totalLimit: [
        //   { required: true, message: '请输入总量可用次数', trigger: 'blur' },
        // ],
        // dayPqLimit: [
        //   { required: true, message: '请输入单日电量', trigger: 'blur' },
        // ],
        // totalPqLimit: [
        //   { required: true, message: '请输入总量可用电量', trigger: 'blur' },
        // ],
        // dayAmtLimit: [
        //   { required: true, message: '请输入单日金额', trigger: 'blur' },
        // ],
        // totalAmtLimit: [
        //   { required: true, message: '请输入总量可用金额', trigger: 'blur' },
        // ],
      },
      isDetail: false,
      vehicleTableData: [
        {
          plateNumber: '京A12345',
          frameNumber: 'LFV3BXXXXXX000001',
          vehicleType: '轿车',
          plateType: '蓝牌',
          vehicleBrand: '奥迪',
          vehicleModel: 'A6L',
          vehicleStatus: '1',
          plugAndChargeStatus: '1',
          chargingLimit: '500',
          updateTime: '2023-10-01 12:00:00',
        },
      ],
      submitLoading: false,
    };
  },
  mounted() {
    console.log('query', this.$route.query);
    if (this.$route.query.isDetail) {
      this.isDetail = true;
    }
    if (this.$route.query.userId) {
      this.getUserDetail();
    }
    this.getEnterpriseList();
  },
  methods: {
    async getEnterpriseList() {
      const [err, res] = await queryEnterpriseList({});

      if (err) {
        return this.$message.error(err.message);
      }
      this.enterpriseOption = res?.data || [];
    },
    async getUserDetail() {
      const [err, res] = await queryDetail({
        userId: this.$route.query.userId,
      });

      if (err) {
        return this.$message.error(err.message);
      }
      const {
        fullName,
        enterpriseId,
        mobile,
        role,
        status,
        dayLimit,
        dayPqLimit,
        dayAmtLimit,
        totalLimit,
        totalPqLimit,
        totalAmtLimit,
      } = res?.data || {};
      this.basicInforForm = {
        fullName,
        enterpriseId,
        mobile,
        role,
        status,
      };
      this.limitationsForm = {
        dayLimit,
        dayPqLimit,
        dayAmtLimit,
        totalLimit,
        totalPqLimit,
        totalAmtLimit,
      };
    },
    handlePhoneInput(value) {
      if (value) {
        this.basicInforForm.mobile = value.slice(0, 11);
      }
    },
    // 取消
    handleCancel() {
      this.$router.back();
    },
    // 提交
    handleConfirm() {
      this.$refs.basicInforForm.validate(async (valid) => {
        if (valid) {
          this.submitLoading = true;
          console.log('basicInforForm', this.basicInforForm);
          console.log('limitationsForm', this.limitationsForm);
          const params = {
            ...this.basicInforForm,
            ...this.limitationsForm,
            enterpriseName:
              this.enterpriseOption.find(
                (item) => item.enterpriseId === this.basicInforForm.enterpriseId
              )?.enterpriseName || '',
          };
          if (this.$route.query.userId) {
            const [err, res] = await enterpriseUserEdit({
              ...params,
              userId: this.$route.query.userId,
            });
            if (err) {
              this.submitLoading = false;
              return this.$message.error(err.message);
            }
            this.$message.success('编辑成功');
            setTimeout(() => {
              this.$router.back();
              this.submitLoading = false;
            }, 1200);
          } else {
            const [err, res] = await enterpriseUserAdd({
              ...params,
            });
            if (err) {
              this.submitLoading = false;
              return this.$message.error(err.message);
            }
            this.$message.success('新增成功');
            setTimeout(() => {
              this.$router.back();
              this.submitLoading = false;
            }, 1200);
          }
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  position: relative;
  padding-bottom: 100px;
  box-sizing: border-box;
  .card-head {
    // position: relative;
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    margin-top: -20px;
    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }

  .info-card {
    margin: 16px 0 16px 0;
    border-radius: 5px;
    border: 1px solid #fff;
    overflow: hidden;
    background-color: #fff;
    .form-wrap {
      padding: 0 16px 16px 16px;
      .custom-header {
        background: -webkit-gradient(
            linear,
            left top,
            left bottom,
            from(rgba(0, 149, 255, 0.5)),
            to(rgba(87, 152, 255, 0))
          ),
          #f5faff;
        background: linear-gradient(
            180deg,
            rgba(0, 149, 255, 0.5) 0%,
            rgba(87, 152, 255, 0) 100%
          ),
          #f5faff;
        background-repeat: no-repeat;
      }
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }
  .bottom-wrap {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 86px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background-color: #ffffff;
    padding-right: 32px;
    box-sizing: border-box;
    z-index: 5;
  }
  .el-select:focus {
    // 自定义 :focus 状态下的样式
    border-color: #dcdfe6 !important;
    box-shadow: none !important;
  }
}
</style>
