<template>
  <div class="container">
    <div class="table-wrap">
      <BuseCrud
        ref="crud"
        :loading="loading"
        :filterOptions="filterOptions"
        :tablePage="tablePage"
        :tableColumn="tableColumn"
        :tableData="tableData"
        :pagerProps="pagerProps"
        :modalConfig="modalConfig"
        @loadData="loadData"
      >
        <template slot="defaultHeader">
          <div class="card-head">
            <div class="card-head-text">场站SOC配置</div>
            <div class="top-button-wrap">
              <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
                新增车辆
              </el-button>
              <el-button
                type="primary"
                icon="el-icon-upload2"
                @click="handleInput"
              >
                批量导入
              </el-button>
            </div>
          </div>
          <div class="card-head-after"></div>
        </template>
        <template slot="vehicleStatus" slot-scope="{ row }">
          <el-switch
            v-model="row.vehicleStatus"
            active-value="1"
            inactive-value="0"
            @change="(newValue) => handleVehicleStatusChange(row, newValue)"
          />
        </template>
        <template slot="plugAndChargeStatus" slot-scope="{ row }">
          <el-switch
            v-model="row.plugAndChargeStatus"
            active-value="1"
            inactive-value="0"
            @change="
              (newValue) => handlePlugAndChargeStatusChange(row, newValue)
            "
          />
        </template>
        <template slot="operate" slot-scope="{ row }">
          <div class="menu-box">
            <el-button
              class="button-border"
              type="primary"
              plain
              @click="handleDetail(row)"
            >
              详情
            </el-button>

            <el-button
              class="button-border"
              type="primary"
              plain
              @click="handleedit(row)"
              v-if="row.vehicleStatus === '1'"
            >
              编辑
            </el-button>
          </div>
        </template>
      </BuseCrud>
    </div>
    <el-dialog
      :title="upload.title"
      :visible.sync="upload.open"
      width="760px"
      height="398px"
      append-to-body
      @close="handleCancel"
    >
      <div>模板下载：</div>
      <div class="box link-box">
        <el-link
          type="primary"
          :underline="false"
          style="font-size: 16px; vertical-align: baseline"
          @click="downloadTemplate"
        >
          导入模板.xlsx
        </el-link>
      </div>
      <div>上传文件：</div>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :auto-upload="false"
        :data="upload.data"
        :on-exceed="handleExceed"
        :http-request="customUpload"
      >
        <div class="box el-upload__text">
          <img
            class="upload-icon"
            src="@/assets/icons/responseAndRegulation/upload.png"
          />
          选择要导入上传的文件
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth';

export default {
  data() {
    return {
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableColumn: [
        {
          type: 'seq',
          title: '序号',
          minWidth: 60, // 最小宽度
        },
        {
          field: '',
          title: '车牌号',
          minWidth: 120, // 最小宽度
        },
        {
          field: '',
          title: '车架号',
          minWidth: 120, // 最小宽度
        },
        {
          field: '',
          title: '车辆所属类型',
          minWidth: 120, // 最小宽度
        },
        {
          field: '',
          title: '企业名称',
          minWidth: 120, // 最小宽度
        },
        {
          field: '',
          title: '车主名称',
          minWidth: 120, // 最小宽度
        },
        {
          field: '',
          title: '车主手机号',
          minWidth: 120, // 最小宽度
        },
        {
          field: '',
          title: '车牌类型',
          minWidth: 120, // 最小宽度
        },
        {
          field: '',
          title: '车辆品牌',
          minWidth: 120, // 最小宽度
        },
        {
          field: '',
          title: '车辆型号',
          minWidth: 120, // 最小宽度
        },
        {
          field: '',
          title: '备注',
          minWidth: 120, // 最小宽度
        },
        {
          field: 'vehicleStatus',
          title: '车辆状态',
          minWidth: 120, // 最小宽度
          slots: {
            default: 'vehicleStatus',
          },
        },
        {
          field: 'plugAndChargeStatus',
          title: '即插即充状态',
          minWidth: 120, // 最小宽度
          slots: {
            default: 'plugAndChargeStatus',
          },
        },
        {
          field: '',
          title: '充电限额(元)',
          minWidth: 120, // 最小宽度
        },

        {
          field: '',
          title: '创建人',
          minWidth: 120, // 最小宽度
        },
        {
          field: '',
          title: '创建时间',
          minWidth: 120, // 最小宽度
        },
        {
          title: '操作',
          slots: { default: 'operate' },
          width: 250,
          align: 'center',
          fixed: 'right',
        },
      ],
      tableData: [
        {
          vehicleStatus: '1',
          plugAndChargeStatus: '1',
        },
      ],
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      params: {},
      // 导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: '',
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: 'Bearer ' + getToken() },
        // 上传的地址
        url:
          process.env.VUE_APP_BASE_API +
          '/vehicle-charging-admin/insp/soc/import',
        data: {
          requireId: '',
        },
      },
      // 导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: '',
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: 'Bearer ' + getToken() },
        // 上传的地址
        url:
          process.env.VUE_APP_BASE_API +
          '/vehicle-charging-admin/insp/soc/import',
        data: {
          requireId: '',
        },
      },
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'plateNumber',
            title: '车牌号',
            element: 'el-input',
          },
          {
            field: 'frameNumber',
            title: '车架号',
            element: 'el-input',
          },
          {
            field: 'vehicleType',
            title: '车辆所属类型',
            element: 'el-select',
            props: {
              options: [
                {
                  label: '员工',
                  value: 0,
                },
                {
                  label: '企业',
                  value: 1,
                },
              ],
            },
          },
          {
            field: 'enterpriseName',
            title: '企业名称',
            element: 'el-input',
          },
          {
            field: 'carOwnerName',
            title: '车主名称',
            element: 'el-input',
          },
          {
            field: 'carOwnerNumber',
            title: '车主手机号',
            element: 'el-input',
          },
          {
            field: 'vehicleStatus',
            title: '车辆状态',
            element: 'el-select',
            props: {
              options: [
                {
                  label: '启用',
                  value: 0,
                },
                {
                  label: '禁用',
                  value: 1,
                },
              ],
            },
          },
          {
            field: 'plugAndChargeStatus',
            title: '即插即充状态',
            element: 'el-select',
            props: {
              options: [
                {
                  label: '启用',
                  value: 0,
                },
                {
                  label: '禁用',
                  value: 1,
                },
              ],
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  mounted() {
    this.loadData();
  },
  methods: {
    loadData() {},
    handleVehicleStatusChange(row, newValue) {
      let oldStatus = '0';
      if (row.vehicleStatus == '0') {
        oldStatus = '1';
      } else if (row.vehicleStatus == '1') {
        oldStatus = '0';
      }
      this.$confirm('确定变更车辆状态吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          row.vehicleStatus = newValue;
          this.$message({
            type: 'success',
            message: '变更成功',
          });
        })
        .catch(() => {
          row.vehicleStatus = oldStatus;
          this.$message({
            type: 'info',
            message: '已取消变更',
          });
        });
    },
    handlePlugAndChargeStatusChange(row, newValue) {
      let oldStatus = '0';
      if (row.plugAndChargeStatus == '0') {
        oldStatus = '1';
      } else if (row.plugAndChargeStatus == '1') {
        oldStatus = '0';
      }
      this.$confirm('确定变更即充状态吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          row.plugAndChargeStatus = newValue;
          this.$message({
            type: 'success',
            message: '变更成功',
          });
        })
        .catch(() => {
          row.plugAndChargeStatus = oldStatus;
          this.$message({
            type: 'info',
            message: '已取消变更',
          });
        });
    },
    // 详情
    handleDetail(row) {},
    // 编辑
    handleedit(row) {},
    // 添加
    handleAdd() {
      this.$router.push({
        path: '/v2g-charging-web/baseInfo/userManage/employeeVehicles/addOrEdit',
      });
    },
    // 导入
    handleInput() {
      this.upload.title = '导入';
      this.upload.open = true;
    },
    // 导入关闭
    handleCancel() {
      this.upload.open = false;
      this.$refs.upload.clearFiles();
    },
    // 下载模板
    downloadTemplate() {
      this.download(
        '/vehicle-charging-admin/insp/soc/import/template',
        {},
        `导入模板.xlsx`
      );
    },
    // 用户选择的文件数量超过 limit 限制
    handleExceed(files, fileList) {
      console.log(files, fileList);
      // 清空已选文件列表
      this.$refs.upload.clearFiles();
      // 手动添加新选择的文件（首个文件）
      this.$refs.upload.handleStart(files[0]);
    },
    async customUpload({ action, file, data }) {
      this.upload.isUploading = true;
      this.upload.open = false;
      const formData = new FormData();
      formData.append('file', file);
      try {
        const [err, res] = await uploadClearingResult(formData);
        if (err) return;
        if (res.success) {
          // console.log('导入res', res);
          this.$message.success('导入成功');
          this.handleCancel();
          this.loadData();
        } else {
          this.$message.error(res.subMsg || '导入失败');
        }
      } finally {
        this.upload.isUploading = false;
      }
    },
    // 提交上传文件
    submitFileForm() {
      console.log('upload data', this.upload.data);
      this.$refs.upload.submit();
    },
  },
};
</script>

<style lang="scss" scoped>
.table-wrap {
  background: #fff;
  padding: 20px;
  border-radius: 4px;

  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }

  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }
}
</style>
