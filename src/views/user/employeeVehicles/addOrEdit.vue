<template>
  <div
    class="container container-float"
    style="padding: 0 0 100px 0; position: relative"
  >
    <div class="info-card">
      <div class="card-head">
        <div class="before-icon"></div>
        <div class="card-head-text">车辆基础信息</div>
      </div>
      <div class="form-wrap">
        <el-form :model="form" :rules="rules" ref="form" label-position="top">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="车辆所属类型" prop="vehicleType">
                <el-select v-model="form.vehicleType" style="width: 100%">
                  <el-option
                    v-for="item in vehicleTypeOptions"
                    :key="item.value"
                    :value="item.label"
                  >
                    {{ item.label }}
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="车牌号" prop="plateNumber">
                <el-input
                  v-model="form.plateNumber"
                  placeholder="请输入车牌号"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="车架号" prop="frameNumber">
                <el-input
                  v-model="form.frameNumber"
                  placeholder="请输入车架号"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="车牌类型" prop="plateType">
                <el-select v-model="form.plateType" style="width: 100%">
                  <el-option
                    v-for="item in plateTypeOptions"
                    :key="item.value"
                    :value="item.label"
                  >
                    {{ item.label }}
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="车辆品牌" prop="vehicleBrand">
                <el-input
                  v-model="form.vehicleBrand"
                  placeholder="请输入车辆品牌"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="车辆型号" prop="vehicleModel">
                <el-input
                  v-model="form.vehicleModel"
                  placeholder="请输入车辆型号"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="企业名称" prop="enterpriseName">
                <el-select v-model="form.enterpriseName" style="width: 100%">
                  <el-option
                    v-for="item in enterpriseNameOptions"
                    :key="item.value"
                    :value="item.label"
                  >
                    {{ item.label }}
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="车主名称" prop="carOwnerName">
                <el-input
                  v-model="form.carOwnerName"
                  placeholder="请输入车主名称"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="车主手机号" prop="carOwnerPhone">
                <el-input
                  v-model="form.carOwnerPhone"
                  placeholder="请输入车主手机号"
                  type="number"
                  @input="handleCarOwnerPhoneInput"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="车辆状态" prop="vehicleStatus">
                <el-select v-model="form.vehicleStatus" style="width: 100%">
                  <el-option
                    v-for="item in vehicleStatusOptions"
                    :key="item.value"
                    :value="item.label"
                  >
                    {{ item.label }}
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="是否启用即插即充" prop="plugAndChargeStatus">
                <el-select
                  v-model="form.plugAndChargeStatus"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in plugAndChargeStatusOptions"
                    :key="item.value"
                    :value="item.label"
                  >
                    {{ item.label }}
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="充电限额(元)" prop="chargingLimit">
                <el-input
                  v-model="form.chargingLimit"
                  placeholder="请输入充电限额"
                  type="number"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
    <div class="bottom-wrap">
      <el-button @click="() => handleCancel()">取消</el-button>
      <el-button type="primary" @click="() => handleConfirm()">保存</el-button>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      form: {
        vehicleType: '',
        plateNumber: '',
        frameNumber: '',
        plateType: '',
        vehicleBrand: '',
        vehicleModel: '',
        carOwnerName: '',
        carOwnerPhone: '',
        vehicleStatus: '',
        plugAndChargeStatus: '',
        chargingLimit: '',
      },
      rules: {
        vehicleType: [
          { required: true, message: '请选择车辆所属类型', trigger: 'blur' },
        ],
        plateNumber: [
          { required: true, message: '请输入车牌号', trigger: 'blur' },
        ],
        frameNumber: [
          { required: true, message: '请输入车架号', trigger: 'blur' },
        ],
        carOwnerName: [
          { required: true, message: '请输入车主名称', trigger: 'blur' },
        ],
        carOwnerPhone: [
          { required: true, message: '请输入车主手机号', trigger: 'blur' },
          {
            pattern: /^1[3-9]\d{9}$/,
            message: '请输入正确的手机号',
            trigger: 'blur',
          },
        ],
        vehicleStatus: [
          { required: true, message: '请选择车辆状态', trigger: 'blur' },
        ],
        plugAndChargeStatus: [
          {
            required: true,
            message: '请选择是否启用即插即充',
            trigger: 'blur',
          },
        ],
        chargingLimit: [
          { required: true, message: '请输入充电限额', trigger: 'blur' },
        ],
      },
      vehicleTypeOptions: [
        {
          label: '员工',
          value: 0,
        },
        {
          label: '企业',
          value: 1,
        },
      ],
      plateTypeOptions: [
        {
          label: '蓝牌',
          value: 0,
        },
        {
          label: '绿牌',
          value: 1,
        },
      ],
      enterpriseNameOptions: [
        {
          label: '企业',
          value: 0,
        },
        {
          label: '员工',
          value: 1,
        },
      ],
      vehicleStatusOptions: [
        {
          label: '已绑定',
          value: 0,
        },
        {
          label: '未绑定',
          value: 1,
        },
      ],
      plugAndChargeStatusOptions: [
        {
          label: '是',
          value: 0,
        },
        {
          label: '否',
          value: 1,
        },
      ],
    };
  },
  mounted() {},
  methods: {
    handleCarOwnerPhoneInput(value) {
      if (value) {
        this.form.carOwnerPhone = value.slice(0, 11);
      }
    },
    // 取消
    handleCancel() {
      this.$router.back();
    },
    // 确定
    handleConfirm() {
      console.log('rules', this.rules);
      this.$refs.form.validate(async (valid) => {
        if (valid) {
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  position: relative;
  padding-bottom: 100px;
  box-sizing: border-box;
  .info-card {
    margin: 16px;
    border-radius: 5px;
    border: 1px solid #fff;
    background-color: #fff;

    .card-head {
      height: 56px;
      padding: 0 16px;
      display: flex;
      align-items: center;

      .before-icon {
        width: 3px;
        height: 16px;
        background-image: url('~@/assets/station/consno-before.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        margin-right: 8px;
      }

      .card-head-text {
        font-weight: 500;
        font-size: 16px;
        color: #12151a;
      }
    }

    .form-wrap {
      padding: 16px;
    }
  }

  .bottom-wrap {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 86px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background-color: #ffffff;
    padding-right: 32px;
    box-sizing: border-box;
  }
}
</style>
