<template>
  <div
    class="container container-float"
    style="padding: 0 0 100px 0; position: relative"
  >
    <div class="info-card">
      <div class="card-head">
        <div class="before-icon"></div>
        <div class="card-head-text">车辆基础信息</div>
      </div>
      <div class="form-wrap">
        <el-form :model="form" :rules="rules" ref="form" label-position="top">
          <el-row
            :gutter="20"
            type="flex"
            justify="start"
            style="flex-wrap: wrap; flex-direction: row"
          >
            <el-col :span="8">
              <el-form-item label="车辆所属类型" prop="ownership">
                <el-select
                  v-model="form.ownership"
                  style="width: 100%"
                  :disabled="isDetail"
                  @change="changeOwnership"
                >
                  <el-option
                    v-for="item in ownershipOptions"
                    :key="item.value"
                    :value="item.label"
                  >
                    {{ item.label }}
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="车牌号" prop="licenseNo">
                <el-input
                  v-model="form.licenseNo"
                  placeholder="请输入车牌号"
                  :disabled="isDetail"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="车架号" prop="vin">
                <el-input
                  v-model="form.vin"
                  placeholder="请输入车架号"
                  :disabled="isDetail"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="车牌类型" prop="licenseColor">
                <el-select
                  v-model="form.licenseColor"
                  style="width: 100%"
                  :disabled="isDetail"
                >
                  <el-option
                    v-for="item in licenseColorOptions"
                    :key="item.value"
                    :value="item.label"
                  >
                    {{ item.label }}
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="车辆品牌" prop="make">
                <!-- <el-input
                  v-model="form.make"
                  placeholder="请输入车辆品牌"
                  :disabled="isDetail"
                ></el-input> -->
                <el-select
                  v-model="form.make"
                  style="width: 100%"
                  :disabled="isDetail"
                  @change="changeMake"
                >
                  <el-option
                    v-for="item in makeOptions"
                    :key="item.brandId"
                    :value="item.brand"
                  >
                    {{ item.brand }}
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="车辆型号" prop="model">
                <!-- <el-input
                  v-model="form.model"
                  placeholder="请输入车辆型号"
                  :disabled="isDetail"
                ></el-input> -->
                <el-select
                  v-model="form.model"
                  style="width: 100%"
                  :disabled="isDetail"
                >
                  <el-option
                    v-for="item in modelOptions"
                    :key="item.seriesId"
                    :value="item.seriesName"
                  >
                    {{ item.seriesName }}
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="企业名称" prop="enterpriseId">
                <el-select
                  v-model="form.enterpriseId"
                  style="width: 100%"
                  placeholder="请选择企业名称"
                  :disabled="isDetail"
                  @change="changeEnterpriseId"
                >
                  <el-option
                    v-for="item in enterpriseOptions"
                    :key="item.enterpriseId"
                    :value="item.enterpriseName"
                  >
                    {{ item.enterpriseName }}
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8" v-if="form.ownership != '企业'">
              <el-form-item label="车主名称" prop="ownerName">
                <el-select
                  v-model="form.ownerName"
                  style="width: 100%"
                  placeholder="请选择车主名称"
                  :disabled="isDetail"
                  @change="changeOwnerName"
                >
                  <el-option
                    v-for="item in ownerNameOptions"
                    :key="item.userId"
                    :value="item.fullName"
                  >
                    {{ item.fullName }}
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="车主手机号" prop="mobile">
                <el-input
                  v-model="form.mobile"
                  placeholder="请输入车主手机号"
                  type="number"
                  @input="handleCarOwnerPhoneInput"
                  disabled
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="车辆状态" prop="status">
                <el-select
                  v-model="form.status"
                  style="width: 100%"
                  :disabled="isDetail"
                >
                  <el-option
                    v-for="item in statusOptions"
                    :key="item.value"
                    :value="item.label"
                  >
                    {{ item.label }}
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="是否启用即插即充" prop="plugChargeStatus">
                <el-select
                  v-model="form.plugChargeStatus"
                  style="width: 100%"
                  :disabled="isDetail"
                >
                  <el-option
                    v-for="item in plugChargeStatusOptions"
                    :key="item.value"
                    :value="item.label"
                  >
                    {{ item.label }}
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="充电限额(元)" prop="chargeLimit">
                <el-input
                  v-model="form.chargeLimit"
                  placeholder="请输入充电限额"
                  type="number"
                  :disabled="isDetail"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
    <div class="bottom-wrap">
      <el-button @click="() => handleCancel()">
        <span v-if="isDetail">返回</span>
        <span v-if="!isDetail">取消</span>
      </el-button>
      <el-button type="primary" @click="() => handleConfirm()" v-if="!isDetail">
        保存
      </el-button>
    </div>
  </div>
</template>

<script>
import {
  addVehicle,
  enterpriseList,
  getVehicleDetail,
  editVehicle,
  queryList,
  carBrandList,
  carSeriesList,
} from '@/api/user/employeeVehicles';

export default {
  data() {
    return {
      form: {
        ownership: '',
        licenseNo: '',
        vin: '',
        licenseColor: '',
        make: '',
        model: '',
        enterpriseId: '',
        ownerName: '',
        ownerId: '',
        mobile: '',
        status: '',
        plugChargeStatus: '',
        chargeLimit: '',
      },
      rules: {
        ownership: [
          { required: true, message: '请选择车辆所属类型', trigger: 'blur' },
        ],
        licenseNo: [
          { required: true, message: '请输入车牌号', trigger: 'blur' },
        ],
        vin: [{ required: true, message: '请输入车架号', trigger: 'blur' }],
        enterpriseId: [
          { required: true, message: '请输入车架号', trigger: 'blur' },
        ],
        ownerName: [
          { required: true, message: '请输入车主名称', trigger: 'blur' },
        ],
        mobile: [
          { required: true, message: '请输入车主手机号', trigger: 'blur' },
          {
            pattern: /^1[3-9]\d{9}$/,
            message: '请输入正确的手机号',
            trigger: 'blur',
          },
        ],
        status: [
          { required: true, message: '请选择车辆状态', trigger: 'blur' },
        ],
        plugChargeStatus: [
          {
            required: true,
            message: '请选择是否启用即插即充',
            trigger: 'blur',
          },
        ],
        chargeLimit: [
          { required: true, message: '请输入充电限额', trigger: 'blur' },
        ],
      },
      ownershipOptions: [
        {
          label: '企业',
          value: 'ENP',
        },
        {
          label: '企业员工',
          value: 'ENP_E',
        },
      ],
      licenseColorOptions: [
        {
          label: '白色',
          value: '0',
        },
        {
          label: '黄色',
          value: '1',
        },
        {
          label: '蓝色',
          value: '2',
        },
        {
          label: '黑色',
          value: '3',
        },
        {
          label: '黄绿',
          value: '4',
        },
        {
          label: '绿色',
          value: '5',
        },
        {
          label: '其他',
          value: '99',
        },
      ],
      enterpriseOptions: [],
      statusOptions: [
        {
          label: '启用',
          value: 'ENABLE',
        },
        {
          label: '禁用',
          value: 'DISABLE',
        },
      ],
      plugChargeStatusOptions: [
        {
          label: '否',
          value: '0',
        },
        {
          label: '是',
          value: '1',
        },
      ],
      dataId: '',
      isDetail: false,
      ownerNameOptions: [],
      makeOptions: [],
      modelOptions: [],
    };
  },
  async mounted() {
    await this.getEnterpriseOptions();
    await this.getMakeOptions();
    console.log('quey', this.$route.query);
    if (this.$route.query && this.$route.query.id) {
      this.dataId = this.$route.query.id;
      await this.loadData();
    }
    if (this.$route.query && this.$route.query.isDetail) {
      this.isDetail = this.$route.query.isDetail;
    }
  },
  methods: {
    // 企业名称数据源
    async getEnterpriseOptions() {
      let params = {};
      const [err, res] = await enterpriseList(params);
      if (err) return;
      // console.log('res', res);
      this.enterpriseOptions = res.data;
    },
    // 车辆品牌数据源
    async getMakeOptions() {
      let params = {};
      const [err, res] = await carBrandList(params);
      if (err) return;
      // console.log('车辆品牌', res);
      this.makeOptions = [];
      res.data.forEach((item) => {
        this.makeOptions = [...this.makeOptions, ...item.list];
      });
      // console.log('车辆品牌', this.makeOptions);
    },
    // 获取详情数据
    async loadData() {
      let params = {
        id: this.dataId,
      };
      const [err, res] = await getVehicleDetail(params);
      if (err) return;
      console.log('res', res);
      this.form = res.data;
      this.ownershipOptions.forEach((item) => {
        if (item.value == this.form.ownership) {
          this.form.ownership = item.label;
        }
      });
      this.licenseColorOptions.forEach((item) => {
        if (item.value == this.form.licenseColor) {
          this.form.licenseColor = item.label;
        }
      });
      this.enterpriseOptions.forEach((item) => {
        if (item.enterpriseId == this.form.enterpriseId) {
          this.form.enterpriseId = item.enterpriseName;
        }
      });
      this.statusOptions.forEach((item) => {
        if (item.value == this.form.status) {
          this.form.status = item.label;
        }
      });
      this.plugChargeStatusOptions.forEach((item) => {
        if (item.value == this.form.plugChargeStatus) {
          this.form.plugChargeStatus = item.label;
        }
      });
    },
    handleCarOwnerPhoneInput(value) {
      if (value) {
        this.form.mobile = value.slice(0, 11);
      }
    },
    // 取消
    handleCancel() {
      this.$router.back();
    },
    // 确定
    async handleConfirm() {
      // console.log(this.form, 'form');
      // console.log('rules', this.rules);
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          let params = { ...this.form };

          this.ownershipOptions.forEach((item) => {
            if (item.label === params.ownership) {
              params.ownership = item.value;
            }
          });
          this.licenseColorOptions.forEach((item) => {
            if (item.label === params.licenseColor) {
              params.licenseColor = item.value;
            }
          });
          this.enterpriseOptions.forEach((item) => {
            if (item.enterpriseName === params.enterpriseId) {
              params.enterpriseId = item.enterpriseId;
              params.enterpriseName = item.enterpriseName;
            }
          });
          this.statusOptions.forEach((item) => {
            if (item.label === params.status) {
              params.status = item.value;
            }
          });
          this.plugChargeStatusOptions.forEach((item) => {
            if (item.label === params.plugChargeStatus) {
              params.plugChargeStatus = item.value;
            }
          });
          // console.log(params, 'params');
          if (this.dataId) {
            params.id = this.dataId;
            const [err, res] = await editVehicle(params);
            if (err) return;
            if (res.code == '10000') {
              this.$message.success('修改成功');
              this.handleCancel();
            }
          } else {
            const [err, res] = await addVehicle(params);
            if (err) return;
            // console.log('res', res);
            if (res.code == '10000') {
              this.$message.success('新增成功');
              this.handleCancel();
            }
          }
        }
      });
    },
    // 修改车辆所属类型
    changeOwnership() {
      console.log(this.form.ownership);
      // this.form.ownership
      if (this.form.ownership == '企业') {
        this.rules.ownerName[0].required = false;
      } else {
        this.rules.ownerName[0].required = true;
      }
      this.form.ownerName = '';
      this.form.mobile = '';
      this.changeEnterpriseId();
    },
    // 修改所属企业
    async changeEnterpriseId() {
      console.log(this.form.enterpriseId);
      if (this.form.ownership == '企业') {
        this.enterpriseOptions.forEach(async (item) => {
          if (item.enterpriseName == this.form.enterpriseId) {
            // console.log(item.adminMobile);
            this.form.mobile = item.adminMobile;
          }
        });
      } else if (this.form.ownership == '企业员工') {
        this.form.ownerName = '';
        this.form.mobile = '';
        this.enterpriseOptions.forEach(async (item) => {
          if (item.enterpriseName == this.form.enterpriseId) {
            // console.log(item.enterpriseId);
            const [err, res] = await queryList({
              enterpriseId: item.enterpriseId,
            });
            if (err) return;
            // console.log('res', res);
            this.ownerNameOptions = res.data;
          }
        });
      }
    },
    // 选择车主名称
    changeOwnerName() {
      console.log(this.form.ownerName);
      this.ownerNameOptions.forEach((item) => {
        if (item.fullName == this.form.ownerName) {
          this.form.ownerId = item.userId;
          this.form.mobile = item.mobile;
        }
      });
    },
    // 选择车辆品牌
    async changeMake() {
      // console.log(this.form.make);
      let brandId = '';
      this.form.model = '';
      this.makeOptions.forEach((item) => {
        if (item.brand == this.form.make) {
          brandId = item.brandId;
        }
      });
      const [err, res] = await carSeriesList({ brandId: brandId });
      if (err) return;
      // console.log('车辆品牌', res.data);
      this.modelOptions = res.data;
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  position: relative;
  padding-bottom: 100px;
  box-sizing: border-box;
  .info-card {
    margin: 16px;
    border-radius: 5px;
    border: 1px solid #fff;
    background-color: #fff;

    .card-head {
      height: 56px;
      padding: 0 16px;
      display: flex;
      align-items: center;

      .before-icon {
        width: 3px;
        height: 16px;
        background-image: url('~@/assets/station/consno-before.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        margin-right: 8px;
      }

      .card-head-text {
        font-weight: 500;
        font-size: 16px;
        color: #12151a;
      }
    }

    .form-wrap {
      padding: 16px;
    }
  }

  .bottom-wrap {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 86px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background-color: #ffffff;
    padding-right: 32px;
    box-sizing: border-box;
  }
}
</style>
