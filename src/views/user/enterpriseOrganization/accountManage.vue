<template>
  <div class="container container-float" style="padding: 0 0 100px 0">
    <div class="device-head">
      <img
        src="@/assets/order/abnormal-detail-icon.png"
        class="device-head-icon"
      />
      <div class="device-info-wrap">
        <div class="device-title-wrap">
          <div class="device-title">账户编码：{{ accountCode }}</div>
          <div class="device-status">{{ status }}</div>
        </div>
        <div class="device-detail-wrap">
          <el-row>
            <el-col :span="6">
              <span class="label">可用余额：</span>
              <span class="value">{{ availableBalance }}</span>
            </el-col>
          </el-row>
        </div>
      </div>
      <div class="button-wrap">
        <el-button type="primary" @click="openModal('in')">
          <svg-icon iconClass="a-recharge"></svg-icon>
          转入
        </el-button>
        <el-button type="primary" class="set-btn" @click="openModal('out')">
          <svg-icon iconClass="a-withdrawal"></svg-icon>
          转出
        </el-button>
      </div>
    </div>

    <div class="table-wrap">
      <div class="card-head">
        <div class="card-head-text">账户详情</div>
      </div>
      <div class="info-wrap">
        <div class="info-item" v-for="item in infoList" :key="item.name">
          <img :src="item.icon" class="info-icon" />
          <div class="info-right-wrap">
            <div class="info-title">{{ item.name }}</div>
            <div class="info-number">
              {{ item.value }}
              <span class="info-unit">{{ item.unit }}</span>
            </div>
          </div>
        </div>
      </div>
      <BuseCrud
        ref="crud"
        :loading="loading"
        :filterOptions="filterOptions"
        :tablePage="tablePage"
        :tableColumn="tableColumn"
        :tableData="tableData"
        :pagerProps="pagerProps"
        :modalConfig="modalConfig"
        @loadData="loadData"
      >
        <template slot="defaultHeader">
          <el-button
            style="margin-bottom: 16px"
            class="export-btn"
            type="primary"
            @click="handleExport"
          >
            <svg-icon iconClass="a-export-blue"></svg-icon>
            导出
          </el-button>
        </template>
      </BuseCrud>
    </div>

    <TransferAccountsModal
      ref="transferAccountsModal"
      @confirm="handleTransferAccountsConfirm"
      :dialogTitle="dialogTitle"
    />
  </div>
</template>

<script>
import icon1 from '@/assets/enterprise/icon-1.png';
import icon2 from '@/assets/enterprise/icon-2.png';
import icon5 from '@/assets/enterprise/icon-5.png';
import icon6 from '@/assets/enterprise/icon-6.png';
import TransferAccountsModal from './components/transferAccountsModal';

export default {
  components: {
    TransferAccountsModal,
  },
  data() {
    return {
      accountCode: '********',
      status: '启用',
      availableBalance: '212312.11',
      infoList: [
        {
          name: '消费次数',
          value: '1401',
          unit: '次',
          icon: icon1,
        },
        {
          name: '退款次数',
          value: '2212',
          unit: '次',
          icon: icon2,
        },
        {
          name: '转入次数',
          value: '1',
          unit: '次',
          icon: icon5,
        },
        {
          name: '转出次数',
          value: '11',
          unit: '次',
          icon: icon6,
        },
      ],
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [
        {
          serialNumber: 'SN20231010001',
          operationType: '转入',
          accountId: 'ACC123456',
          accountType: '企业账户',
          businessOrderNo: 'BON20231010001',
          tradeTime: '2023-10-10 10:00:00',
          tradeStatus: '成功',
          paymentAmount: '5000.00',
          accountBalance: '217312.11',
          operator: '张三',
        },
      ],
      tableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
        },
        {
          field: 'serialNumber',
          title: '流水号',
          minWidth: 120,
        },
        {
          field: 'operationType',
          title: '操作类型',
          minWidth: 120,
        },
        {
          field: 'accountId',
          title: '转入账户ID',
          minWidth: 150,
        },
        {
          field: 'accountType',
          title: '转入账户类型',
          minWidth: 150,
        },
        {
          field: 'businessOrderNo',
          title: '业务订单号',
          minWidth: 180,
        },
        {
          field: 'tradeTime',
          title: '交易时间',
          minWidth: 160,
        },
        {
          field: 'tradeStatus',
          title: '交易状态',
          minWidth: 120,
        },
        {
          field: 'paymentAmount',
          title: '支付金额',
          minWidth: 120,
        },
        {
          field: 'accountBalance',
          title: '账户余额',
          minWidth: 120,
        },
        {
          field: 'operator',
          title: '操作员',
          minWidth: 120,
        },
      ],
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      params: {
        serialNumber: '',
        orderNo: '',
        operator: '',
        operationType: '',
        tradeTime: [],
        status: '',
      },
      dialogTitle: '',
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'serialNumber',
            title: '流水号',
            element: 'el-input',
            placeholder: '请输入',
          },
          {
            field: 'orderNo',
            title: '订单编号',
            element: 'el-input',
            placeholder: '请输入',
          },
          {
            field: 'operator',
            title: '操作员',
            element: 'el-select',
            props: {
              options: [],
            },
          },
          {
            field: 'operationType',
            title: '操作类型',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
          {
            field: 'tradeTime',
            title: '交易时间',
            element: 'el-date-picker',
            props: {
              type: 'daterange',
              startPlaceholder: '开始日期',
              endPlaceholder: '结束日期',
            },
          },
          {
            field: 'status',
            title: '交易状态',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  mounted() {
    this.loadData();
  },
  methods: {
    async loadData() {},
    // 转入转出
    openModal(str) {
      if (str == 'in') {
        this.dialogTitle = '账户转入';
      } else if (str == 'out') {
        this.dialogTitle = '账户转出';
      }
      this.$refs.transferAccountsModal.loadData();
      this.$refs.transferAccountsModal.dialogVisible = true;
    },
    // 导出
    handleExport() {},
    handleTransferAccountsConfirm() {
      this.loadData();
    },
  },
};
</script>

<style lang="scss" scoped>
.device-head {
  background-color: #fff;
  display: flex;
  height: 112px;
  display: flex;
  align-items: center;
  padding: 0 24px;
  box-sizing: border-box;
  .device-head-icon {
    width: 48px;
    height: 48px;
    margin-right: 24px;
  }
  .device-info-wrap {
    flex: 1;
    .device-title-wrap {
      height: 32px;
      display: flex;
      align-items: center;
      .device-title {
        font-weight: 500;
        font-size: 24px;
        color: #12151a;
      }
      .device-status {
        // width: 50px;
        padding: 0 10px;
        height: 24px;
        border-radius: 10px 0 10px 0;
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;
        text-align: center;
        color: #fff;
        background: linear-gradient(321.01deg, #217aff 8.79%, #21c8ff 100.27%);
        margin-left: 12px;
      }
    }
    .device-detail-wrap {
      height: 16px;
      margin-top: 16px;
      font-size: 16px;
      font-weight: 400;
      color: #292b33;

      .label {
        font-weight: 400;
        font-size: 16px;
        color: #505363;
      }
      .value {
        font-weight: 400;
        font-size: 20px;
        color: #292b33;
      }
    }
  }
  .button-wrap {
    display: flex;
    align-items: center;
    .set-btn {
      background-color: #ffffff;
      color: #292b33;
      border-color: #dfe1e5;
    }
  }
}

.table-wrap {
  background-color: #ffffff;
  border-radius: 5px;
  margin: 16px;
  .card-head {
    // position: relative;
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    // margin-top: -20px;
    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }

  .info-wrap {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;

    .info-item {
      // background-color: #FAFBFC;
      flex: 1 1 0;
      // min-width: 180px;

      border-radius: 5px;
      padding: 8px 24px;
      box-sizing: border-box;
      // margin-right: 16px;
      display: flex;
      .info-icon {
        width: 42px;
        height: 42px;
      }
      .info-right-wrap {
        flex: 1;
        margin-left: 24px;
        .info-title {
          font-weight: 400;
          font-size: 14px;
          line-height: 14px;
          margin-bottom: 8px;
        }
        .info-number {
          font-size: 20px;
          font-weight: 500;
          .info-unit {
            font-size: 14px;
            font-weight: 400;
          }
        }
      }
    }
    .info-item:last-child {
      margin-right: 0;
    }
  }
}

::v-deep .bd3001-auto-filters-container {
  margin-bottom: 0px !important;
}

::v-deep .bd3001-auto-filters-container {
  box-shadow: none !important;
}

::v-deep .bd3001-content {
  padding-top: 0px !important;
}

.export-btn {
  background-color: #ffffff;
  color: #217aff;
  border-color: #217aff;
}
</style>
