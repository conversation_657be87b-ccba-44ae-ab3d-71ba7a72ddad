<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    @close="handleCancel"
    width="60%"
    :destroy-on-close="true"
  >
    <div>
      <el-form :model="form" :rules="rules" ref="ruleForm" label-position="top">
        <div class="form-item-wrap">
          <div class="label-text">省电动长沙事业部当前账户余额（元）：</div>
          <div class="value-text">{{ currentBalance }}</div>
        </div>

        <el-form-item label="转入对象类型：" prop="targetType" required>
          <el-radio-group v-model="form.targetType">
            <el-radio label="organization">机构</el-radio>
            <el-radio label="employee">员工</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="企业/机构：" prop="organization" required>
          <el-select
            v-model="form.organization"
            placeholder="请选择"
            style="width: 100%"
          >
            <el-option
              v-for="item in organizationOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>

        <div class="form-item-wrap">
          <div class="label-text">省电动账户余额（元）：</div>
          <div class="value-text">{{ provinceBalance }}</div>
        </div>

        <el-form-item label="转入金额（元）：" prop="amount" required>
          <div class="amount-input-wrap">
            <el-input
              v-model="form.amount"
              placeholder="请输入数字"
              class="amount-input"
              type="number"
            ></el-input>
            <div class="unit">元</div>
          </div>
        </el-form-item>

        <div class="form-item-wrap">
          <div class="label-text" v-if="dialogTitle == '账户转入'">
            转入后省电动账户余额（元）：
          </div>
          <div class="label-text" v-if="dialogTitle == '账户转出'">
            转出后省电动账户余额（元）：
          </div>
          <div class="value-text">{{ afterProvinceBalance }}</div>
        </div>

        <div class="form-item-wrap">
          <div class="label-text" v-if="dialogTitle == '账户转入'">
            转入后省电动长沙事业部账户余额（元）：
          </div>
          <div class="label-text" v-if="dialogTitle == '账户转出'">
            转出后省电动长沙事业部账户余额（元）：
          </div>
          <div class="value-text">{{ afterDepartmentBalance }}</div>
        </div>

        <el-form-item label="备注：" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            :rows="4"
            placeholder="请输入"
          ></el-input>
        </el-form-item>
      </el-form>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" @click="handleConfirm">确 定</el-button>
    </div>
  </el-dialog>
</template>
<script>
export default {
  props: {
    dialogTitle: {
      type: String,
      default: '',
    },
  },
  components: {},
  data() {
    return {
      dialogVisible: false,
      currentBalance: '13000.00',
      provinceBalance: '13000.00',
      afterProvinceBalance: '13000.00',
      afterDepartmentBalance: '13000.00',
      form: {
        targetType: 'organization',
        organization: '',
        amount: '',
        remark: '',
      },
      rules: {
        targetType: [
          { required: true, message: '请选择转入对象类型', trigger: 'change' },
        ],
        organization: [
          { required: true, message: '请选择企业/机构', trigger: 'change' },
        ],
        amount: [
          { required: true, message: '请输入转入金额', trigger: 'blur' },
          {
            pattern: /^[0-9]+(\.[0-9]{1,2})?$/,
            message: '请输入正确的金额格式',
            trigger: 'blur',
          },
        ],
      },
      organizationOptions: [
        { value: 'org1', label: '长沙分公司' },
        { value: 'org2', label: '株洲分公司' },
        { value: 'org3', label: '湘潭分公司' },
      ],
    };
  },
  watch: {
    'form.amount': {
      handler(val) {
        if (val && !isNaN(val)) {
          const amount = parseFloat(val);
          const currentBalance = parseFloat(this.currentBalance);
          this.afterProvinceBalance = (currentBalance - amount).toFixed(2);
          this.afterDepartmentBalance = (currentBalance - amount).toFixed(2);
        } else {
          this.afterProvinceBalance = this.provinceBalance;
          this.afterDepartmentBalance = this.currentBalance;
        }
      },
      immediate: true,
    },
  },
  mounted() {
    this.loadData();
  },
  methods: {
    async loadData() {},
    // 关闭弹窗
    handleCancel() {
      this.dialogVisible = false;
    },
    handleConfirm() {
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          // 提交表单逻辑
          this.$modal.msgSuccess('操作成功');
          this.handleCancel();
        } else {
          return false;
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.transfer-form {
  padding: 0 20px;
}

.form-item-wrap {
  display: flex;
  margin-bottom: 22px;
  line-height: 40px;

  .label-text {
    width: 280px;
    text-align: right;
    padding-right: 12px;
    box-sizing: border-box;
    color: #606266;
  }

  .value-text {
    flex: 1;
    color: #409eff;
    font-weight: bold;
  }
}

.amount-input-wrap {
  display: flex;
  align-items: center;

  .amount-input {
    flex: 1;
  }

  .unit {
    margin-left: 10px;
    padding: 0 10px;
    background-color: #f5f7fa;
    color: #606266;
    height: 40px;
    line-height: 40px;
    border-radius: 0 4px 4px 0;
    border: 1px solid #dcdfe6;
    border-left: none;
  }
}

.el-form-item.is-required .el-form-item__label:before {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
}
</style>
