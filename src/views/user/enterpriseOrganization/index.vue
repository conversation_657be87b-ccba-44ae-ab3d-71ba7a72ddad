<template>
  <div class="container container-region">
    <OrganizationLeft
      ref="OrganizationLeft"
      @nodeClick="nodeClick"
      @modalConfirm="modalConfirm"
    ></OrganizationLeft>
    <div class="container-info">
      <div class="table-wrap">
        <BuseCrud
          ref="crud"
          :loading="loading"
          :filterOptions="filterOptions"
          :tablePage="tablePage"
          :tableColumn="tableColumn"
          :tableData="tableData"
          :pagerProps="pagerProps"
          :modalConfig="modalConfig"
          :tableOn="{
            'checkbox-change': handleCheckboxChange,
            'checkbox-all': handleCheckboxChange,
          }"
          class="buse-wrap-organization"
          @loadData="loadData"
        >
          <template slot="defaultHeader">
            <div class="card-head">
              <div class="card-head-text">企业机构管理</div>
              <div class="top-button-wrap">
                <el-button type="primary" plain @click="handleEnable">
                  批量启用
                </el-button>
                <el-button type="primary" plain @click="handleDisable">
                  批量禁用
                </el-button>
                <el-button type="primary" @click="handleOutput">
                  批量导出
                </el-button>
                <!-- TODO缺少导入接口 -->
                <!-- <el-button type="primary" @click="handleInput">
                  批量导入
                </el-button> -->
              </div>
            </div>
            <div class="card-head-after"></div>
          </template>
          <template slot="status" slot-scope="{ row }">
            <el-switch
              v-model="row.status"
              active-value="ENABLE"
              inactive-value="DISABLE"
              @change="(newValue) => changeStatus(row, newValue)"
            ></el-switch>
          </template>
          <template slot="operate" slot-scope="{ row }">
            <el-button type="primary" plain @click="handleEdit(row)">
              编辑
            </el-button>
            <!-- <el-button type="primary" plain @click="handleAccount(row)">
              账户管理
            </el-button> -->
            <el-button type="danger" plain @click="handleDelete(row)">
              删除
            </el-button>
          </template>
        </BuseCrud>
      </div>
    </div>
    <!-- 导入对话框 -->
    <el-dialog
      :title="upload.title"
      :visible.sync="upload.open"
      width="760px"
      height="398px"
      append-to-body
      @close="handleCancel"
    >
      <div>模板下载：</div>
      <div class="box link-box">
        <el-link
          type="primary"
          :underline="false"
          style="font-size: 16px; vertical-align: baseline"
          @click="downloadTemplate"
        >
          导入模板.xlsx
        </el-link>
      </div>
      <div>上传文件：</div>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :auto-upload="false"
        :data="upload.data"
        :on-exceed="handleExceed"
        :on-error="handleError"
        :on-success="handleSuccess"
        :http-request="customUpload"
      >
        <div class="box el-upload__text">
          <img
            class="upload-icon"
            src="@/assets/icons/responseAndRegulation/upload.png"
          />
          选择要导入上传的文件
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import OrganizationLeft from '@/components/Business/OrganizationLeft';
import {
  queryPage,
  batchModifyStatus,
  queryDetail,
} from '@/api/user/enterpriseOrganization';
import { getToken } from '@/utils/auth';

export default {
  components: {
    OrganizationLeft,
  },
  data() {
    return {
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [],
      tableColumn: [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
        },
        { type: 'seq', title: '序号', width: 60 }, // 序号列
        { field: 'departmentId', title: '机构编号', minWidth: 200 }, // 机构编号
        { field: 'name', title: '机构名称', minWidth: 160 }, // 机构名称
        { field: 'address', title: '机构地址', minWidth: 200 }, // 机构地址
        {
          title: '机构状态',
          minWidth: 100,
          slots: { default: 'status' },
        }, // 机构状态
        { field: 'usingNum', title: '在用人员数量', minWidth: 120 }, // 在用人员数量
        { field: 'unUsedNum', title: '停用人员数量', minWidth: 120 }, // 停用人员数量
        { field: 'adminName', title: '管理员名称', minWidth: 120 }, // 管理员名称
        { field: 'adminMobile', title: '管理员联系方式', minWidth: 150 }, // 管理员联系方式
        { field: 'adminEmail', title: '管理员邮箱', minWidth: 180 }, // 管理员邮箱
        {
          slots: { default: 'operate' },
          title: '操作',
          width: 200,
          align: 'center',
          fixed: 'right',
        }, // 操作列
      ],
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      params: {
        name: '', // 机构名称
        departmentId: '', // 机构编号
        status: '', // 机构状态
      },
      selectList: [],
      // 机构状态选项
      orgStatusOptions: [
        { label: '启用', value: 'ENABLE' },
        { label: '禁用', value: 'DISABLE' },
      ],
      leftTreeData: {},
      // 导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: '',
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: 'Bearer ' + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '',
        data: {},
      },
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'name',
            title: '机构名称',
            element: 'el-input',
            props: {
              placeholder: '请输入机构名称',
            },
          },
          {
            field: 'departmentId',
            title: '机构编号',
            element: 'el-input',
            props: {
              placeholder: '请输入机构编号',
            },
          },
          {
            field: 'status',
            title: '机构状态',
            element: 'el-select',
            props: {
              placeholder: '请选择机构状态',
              options: this.orgStatusOptions,
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  async mounted() {
    await this.loadData();
  },
  methods: {
    async loadData() {
      this.loading = true;
      let params = {
        ...this.params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
        parentDepartmentId: '',
      };
      if (this.leftTreeData && this.leftTreeData.departmentId) {
        params.parentDepartmentId = this.leftTreeData.departmentId;
      } else if (this.leftTreeData && this.leftTreeData.departmentId) {
        params.parentDepartmentId = this.leftTreeData.departmentId;
      } else {
        params.parentDepartmentId = '';
      }
      const [err, res] = await queryPage(params);
      if (err) return;
      // console.log('企业机构分页查询', res);
      const { data, total } = res;
      this.tableData = data;
      this.tablePage.total = total;
      this.loading = false;
    },
    nodeClick(node) {
      console.log('树状图选择数据', node);
      this.leftTreeData = node;
    },
    modalConfirm(data) {
      console.log(data);
    },
    // 启用
    handleEnable() {
      if (this.selectList.length == 0) {
        this.$message({
          message: '请选择要操作的记录',
          type: 'warning',
        });
        return;
      }
      this.$confirm('确定启用这些企业吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        console.log(this.selectList);
        let params = {
          departmentIds: [],
          type: 'ENABLE',
        };
        this.selectList.forEach((item) => {
          params.departmentIds.push(item.departmentId);
        });
        const [err, res] = await batchModifyStatus(params);
        if (err) return;
        this.$message({
          type: 'success',
          message: '变更成功',
        });
        await this.loadData();
      });
    },
    // 禁用
    handleDisable() {
      if (this.selectList.length == 0) {
        this.$message({
          message: '请选择要操作的记录',
          type: 'warning',
        });
        return;
      }
      this.$confirm('确定禁用这些企业吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        console.log(this.selectList);
        let params = {
          departmentIds: [],
          type: 'DISABLE',
        };
        this.selectList.forEach((item) => {
          params.departmentIds.push(item.departmentId);
        });
        const [err, res] = await batchModifyStatus(params);
        if (err) return;
        this.$message({
          type: 'success',
          message: '变更成功',
        });
        await this.loadData();
      });
    },
    // 批量导出
    handleOutput() {
      this.download(
        '/vehicle-charging-admin/enterpriseDepartment/export',
        {},
        `企业机构.xlsx`
      );
    },
    // 批量导入
    handleInput() {
      this.upload.title = '导入';
      this.upload.open = true;
    },
    handleExceed(files, fileList) {
      console.log(files, fileList);
      // 清空已选文件列表
      this.$refs.upload.clearFiles();
      // 手动添加新选择的文件（首个文件）
      this.$refs.upload.handleStart(files[0]);
    },
    handleError(err, file, fileList) {
      console.log('文件上传失败', err);
      // this.$message.error('接口请求失败');
    },
    // 文件上传成功
    handleSuccess(res, file, fileList) {
      console.log('文件上传成功', res, file, fileList);
      // if (res.code == 10000) {
      //   this.$message.success('导入成功');
      // } else {
      //   this.$message.error(res.subMsg);
      // }
    },
    async customUpload({ action, file, data }) {
      this.upload.isUploading = true;
      this.upload.open = false;
      const formData = new FormData();
      console.log(file, data, 8888);
      formData.append('file', file);
      try {
        // TODO 接口待定
        const [err, res] = await batchImport(formData);
        if (err) {
          return this.$message.error(err.message);
        }
        // this.$message.success('导入成功');
        this.$message.success(
          `导入成功${res.data.succeedNum}条，导入失败${res.data.failNum}条`
        );
        if (res.data.failNum > 0) {
          // TODO 接口待定
          this.download(
            `/vehicle-charging-admin/enterpriseUser/export/${res.data.failInfoId}`,
            {},
            `企业用户导入失败数据.xlsx`,
            'getXlsx'
          );
          this.$message.success(`导入失败文件下载成功`);
        }
        this.handleCancel();
        this.loadData();
      } finally {
        this.upload.isUploading = false;
      }
    },
    // 导入关闭
    handleCancel() {
      this.upload.open = false;
      this.$refs.upload.clearFiles();
    },
    // 下载模板
    downloadTemplate() {
      // TODO 接口待定
      this.download('/vehicle-charging-admin/', {}, `企业机构导入模版.xlsx`);
    },
    // 提交上传文件
    submitFileForm() {
      console.log('upload data', this.upload.data);
      this.$refs.upload.submit();
    },
    // 行勾选
    handleCheckboxChange({ records }) {
      this.selectList = records;
    },
    // 修改状态
    changeStatus(row, newValue) {
      let oldStatus = 'ENABLE';
      if (row.status == 'ENABLE') {
        oldStatus = 'DISABLE';
      } else if (row.status == 'DISABLE') {
        oldStatus = 'ENABLE';
      }
      this.$confirm('确定变更当前企业状态吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          let params = {
            departmentIds: [row.departmentId],
            type: row.status,
          };
          const [err, res] = await batchModifyStatus(params);
          if (err) return;
          this.$message({
            type: 'success',
            message: '变更成功',
          });
          await this.loadData();
        })
        .catch(() => {
          row.status = oldStatus;
          this.$message({
            type: 'info',
            message: '已取消变更',
          });
        });
    },
    // 编辑
    async handleEdit(row) {
      let params = {
        departmentId: row.departmentId,
      };
      const [err, res] = await queryDetail(params);
      if (err) return;
      console.log('企业用户明细', res);
      this.$refs.OrganizationLeft.modalData.title = '企业机构新增';
      this.$refs.OrganizationLeft.modalData.form = res.data;
      this.$refs.OrganizationLeft.modalData.id = row.departmentId;
      this.$refs.OrganizationLeft.modalData.open = true;
    },
    // // 账号管理
    // handleAccount(row) {
    //   console.log(row);
    //   this.$router.push({
    //     path: '/v2g-charging/baseInfo/userManage/enterpriseOrganization/accountManage',
    //   });
    // },
    // 删除
    handleDelete(row) {
      console.log(row);
      this.$confirm(`确认删除这条数据吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        this.$message.success('删除成功');
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.table-wrap {
  // background: #fff;
  // padding: 20px;
  // border-radius: 4px;

  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }

  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }
}

::v-deep .el-upload {
  text-align: left;
  font-size: 16px;
}
.box {
  margin-top: 4px;
  width: 688px;
  height: 36px;
  line-height: 36px;
  border-radius: 2px;
  text-align: center;
}
.link-box {
  margin-bottom: 38px;
  border: 1px solid #dfe1e5;
}
::v-deep .el-upload__text {
  border: 1px dashed #dfe1e5;
}
.upload-icon {
  width: 14px;
  height: 14px;
  margin-right: 6px;
}
</style>
