<template>
  <div class="container container-float" style="padding: 0 0 100px 0">
    <el-tabs v-model="activeName" @tab-click="handleClickTab">
      <!-- 企业管理标签栏内容 -->
      <el-tab-pane label="企业管理" name="manage">
        <div class="table-wrap">
          <BuseCrud
            ref="crud"
            :loading="manageLoading"
            :filterOptions="manageFilterOptions"
            :tablePage="manageTablePage"
            :tableColumn="manageTableColumn"
            :tableData="manageTableData"
            :modalConfig="modalConfig"
            :tableOn="{
              'checkbox-change': handleCheckboxChange,
              'checkbox-all': handleCheckboxChange,
            }"
            @loadData="loadManageData"
          >
            <template slot="defaultHeader">
              <div>
                <div class="card-head">
                  <div class="card-head-text">企业管理列表</div>

                  <div class="top-button-wrap">
                    <el-button
                      class="button-border"
                      type="primary"
                      plain
                      @click="onClickAdd({})"
                    >
                      企业注册
                    </el-button>

                    <el-button
                      class="button-border"
                      type="primary"
                      plain
                      @click="() => handleStatus('ENABLE')"
                    >
                      批量启用
                    </el-button>

                    <el-button
                      class="button-danger"
                      type="danger"
                      plain
                      @click="() => handleStatus('DISABLE')"
                    >
                      批量禁用
                    </el-button>

                    <el-button type="primary" @click="() => handleExport()">
                      批量导出
                    </el-button>
                  </div>
                </div>
              </div>
            </template>
            <template slot="status" slot-scope="{ row }">
              <el-switch
                v-model="row.status"
                @change="changeEnterpriseStatus(row)"
              ></el-switch>
            </template>

            <template slot="operate" slot-scope="{ row }">
              <!-- <div class="menu-box">
                <el-button
                  class="button-border"
                  type="primary"
                  plain
                  @click="handleDetail(row)"
                >
                  详情
                </el-button>

                <el-button
                  class="button-border"
                  type="primary"
                  plain
                  v-if="row.status"
                  @click="handleAccount(row)"
                >
                  企业账户管理
                </el-button>

                <el-button
                  class="button-border"
                  type="primary"
                  plain
                  v-if="row.status"
                  @click="handleDiscount(row)"
                >
                  企业优惠配置
                </el-button>

                <el-button
                  class="button-border"
                  type="primary"
                  plain
                  v-if="!row.status"
                  @click="onClickAdd(row)"
                >
                  编辑
                </el-button>

                <el-button
                  class="button-danger"
                  type="danger"
                  plain
                  v-if="!row.status"
                  @click="handleDelete(row)"
                >
                  删除
                </el-button>
              </div> -->

              <el-dropdown trigger="click">
                      <el-button
                        class="button-border"
                        type="primary"
                      >
                        操作
                      </el-button>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item >
                          <div   @click="handleDetail(row)">
                            详情
                          </div>
                        </el-dropdown-item>

                        <el-dropdown-item >
                          <div  v-if="row.status"   @click="handleAccount(row)">
                            企业账户管理
                          </div>
                        </el-dropdown-item>

                        <el-dropdown-item >
                          <div   v-if="row.status"   @click="handleDiscount(row)">
                            企业优惠配置
                          </div>
                        </el-dropdown-item>

                        <el-dropdown-item >
                          <div   v-if="!row.status"   @click="onClickAdd(row)">
                            编辑
                          </div>
                        </el-dropdown-item>

                        <el-dropdown-item >
                          <div   v-if="!row.status"   @click="handleDelete(row)">
                            删除
                          </div>
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </el-dropdown>
            </template>
          </BuseCrud>
        </div>
      </el-tab-pane>
      <!-- 企业注册管理标签栏内容 -->
      <el-tab-pane label="企业注册管理" name="register">
        <div class="table-wrap">
          <BuseCrud
            ref="register"
            :loading="registerLoading"
            :filterOptions="registerFilterOptions"
            :tablePage="registerTablePage"
            :tableColumn="registerTableColumn"
            :tableData="registerTableData"
            :modalConfig="modalConfig"
            :tableOn="{
              'checkbox-change': handleRegisterCheckboxChange,
              'checkbox-all': handleRegisterCheckboxChange,
            }"
            @loadData="loadRegisterData"
          >
            <template slot="defaultHeader">
              <div>
                <div class="card-head">
                  <div class="card-head-text">企业注册管理列表</div>

                  <div class="top-button-wrap">
                    <el-button type="primary" @click="() => handleBatchAudit()">
                      批量审核
                    </el-button>
                  </div>
                </div>
              </div>
            </template>
            <template slot="status" slot-scope="{ row }">
              <el-switch
                v-model="row.status"
                @change="changeEnterpriseStatus(row)"
              ></el-switch>
            </template>

            <template slot="operate" slot-scope="{ row }">
              <!-- <div class="menu-box">
                <el-button
                  class="button-border"
                  type="primary"
                  plain
                  @click="handleDetail(row)"
                >
                  详情
                </el-button>

                <el-button
                  v-if="
                    row.reviewStatus === 'WAITING_APPROVAL' ||
                    row.reviewStatus === 'IN_APPROVAL'
                  "
                  class="button-border"
                  type="primary"
                  plain
                  @click="handleAudit(row)"
                >
                  审核
                </el-button>
              </div> -->

              <el-dropdown trigger="click">
                      <el-button
                        class="button-border"
                        type="primary"
                      >
                        操作
                      </el-button>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item >
                          <div   @click="handleDetail(row)">
                            详情
                          </div>
                        </el-dropdown-item>

                        <el-dropdown-item >
                          <div  
                            v-if="
                            row.reviewStatus === 'WAITING_APPROVAL' ||
                            row.reviewStatus === 'IN_APPROVAL'
                          "
                            @click="handleAudit(row)">
                            审核
                          </div>
                        </el-dropdown-item>
                      </el-dropdown-menu>
              </el-dropdown>
            </template>
          </BuseCrud>
        </div>
      </el-tab-pane>
    </el-tabs>

    <BatchAuditModal
      ref="batchAuditModal"
      :enterpriseIds="chooseRegisterList"
      @loadData="loadRegisterData"
    />
  </div>
</template>

<script>
import BatchAuditModal from './components/batchAuditModal.vue';
import {
  getEnterprisePage,
  enterpriseStatus,
  enterpriseRemove,
} from '@/api/user/enterprise';

import StatusDot from '@/components/Business/StatusDot';


export default {
  components: {
    BatchAuditModal,
    StatusDot,
  },
  dicts: [],
  data() {
    return {
      activeName: 'manage',
      manageLoading: false,
      manageParams: {
        enterpriseName: '',
        enterpriseId: '',
        status: '',
        industryType: '',
      },
      manageTablePage: { total: 0, currentPage: 1, pageSize: 10 },
      manageTableColumn: [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
        },
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
          fixed: 'left',
        },
        {
          field: 'enterpriseId',
          title: '企业编号',
          minWidth: 160,
          slots: {
                  default: ({ row }) => [
                      <el-tooltip 
                          content={row.enterpriseId} 
                          placement="top" 
                          disabled={!row.enterpriseId || row.enterpriseId.length < 10}
                      >
                          <span class="ellipsis-text">{row.enterpriseId}</span>
                      </el-tooltip>
                  ]
              }
        },
        {
          field: 'enterpriseName',
          title: '企业名称',
          minWidth: 150,
        },
        {
          field: 'address',
          title: '企业地址',
          minWidth: 150,
        },
        {
          field: 'industryType',
          title: '行业分类',
          minWidth: 120,
        },
        {
          field: 'accountBalance',
          title: '账户余额（元）',
          minWidth: 150,
        },
        {
          field: 'chargePqSum',
          title: '累计充电电量',
          minWidth: 150,
        },
        {
          field: 'paidElecFeeSum',
          title: '累计实付电费',
          minWidth: 150,
        },
        {
          field: 'paidServiceFeeSum',
          title: '累计实付服务费（元）',
          minWidth: 180,
        },
        {
          field: 'paidFeeSum',
          title: '累计实付总价呢（元）',
          minWidth: 180,
        },
        {
          field: 'enableUserNum',
          title: '在用人员数量',
          minWidth: 150,
        },
        {
          field: 'disableOutUserNum',
          title: '停用人员数量',
          minWidth: 150,
        },
        {
          field: 'adminMobile',
          title: '管理员联系方式',
          minWidth: 180,
        },
        {
          field: 'adminEmail',
          title: '管理员邮箱',
          minWidth: 200,
        },
        {
          field: 'status',
          title: '企业状态',
          minWidth: 100,
          fixed: 'right',
          slots: { default: 'status' },
        },
        {
          field: 'action',
          title: '操作',
          slots: { default: 'operate' },
          width: 100,
          align: 'center',
          fixed: 'right',
        },
      ],
      manageTableData: [],
      chooseManageList: [],
      enterpriseStatusList: [
        { label: '启用', value: 'ENABLE' },
        { label: '禁用', value: 'DISABLE' },
      ],
      reviewStatusList: [
        { label: '待审核', value: 'WAITING_APPROVAL' },
        { label: '审核通过', value: 'PASS' },
        { label: '审核不通过', value: 'REJECT' },
        { label: '审核中', value: 'IN_APPROVAL' },
      ],
      industryTypeList: [{ label: '服务行业', value: '服务行业' }],
      registerLoading: false,
      registerParams: {
        enterpriseName: '',
        enterpriseId: '',
        industryType: '',
        reviewStatus: '',
      },
      registerTablePage: { total: 0, currentPage: 1, pageSize: 10 },
      registerTableColumn: [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
        },
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
          fixed: 'left',
        },
        {
          field: 'enterpriseId',
          title: '企业编号',
          minWidth: 150,
          slots: {
                  default: ({ row }) => [
                      <el-tooltip 
                          content={row.enterpriseId} 
                          placement="top" 
                          disabled={!row.enterpriseId || row.enterpriseId.length < 10}
                      >
                          <span class="ellipsis-text">{row.enterpriseId}</span>
                      </el-tooltip>
                  ]
              }
        },
        {
          field: 'enterpriseName',
          title: '企业名称',
          minWidth: 150,
        },
        {
          field: 'address',
          title: '企业地址',
          minWidth: 150,
        },
        {
          field: 'industryType',
          title: '行业分类',
          minWidth: 120,
        },
        {
          field: 'adminName',
          title: '管理员姓名',
          minWidth: 120,
        },
        {
          field: 'adminMobile',
          title: '管理员联系方式',
          minWidth: 180,
        },
        {
          field: 'adminEmail',
          title: '管理员邮箱',
          minWidth: 200,
        },
        {
          field: 'reviewStatus',
          title: '审核状态',
          minWidth: 120,
          fixed: 'right',
          // formatter: ({ cellValue }) => {
          //   return this.selectDictLabel(this.reviewStatusList, cellValue);
          // },
          slots: {
            // 自定义render函数
            default: ({ row }) => {
              return (
                <StatusDot
                  value={row.reviewStatus}
                  dictValue={this.reviewStatusList}
                  colors={['warning','success','danger','default']}
                ></StatusDot>
              );
            },
          },
        },
        {
          field: 'action',
          title: '操作',
          slots: { default: 'operate' },
          width: 100,
          align: 'center',
          fixed: 'right',
        },
      ],
      registerTableData: [],
      chooseRegisterList: [],
    };
  },

  computed: {
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
    manageFilterOptions() {
      return {
        config: [
          {
            field: 'enterpriseName',
            title: '企业名称',
            element: 'el-input',
          },
          {
            field: 'enterpriseId',
            title: '企业编号',
            element: 'el-input',
          },
          {
            field: 'status',
            title: '企业状态',
            element: 'el-select',
            props: {
              placeholder: '请选择企业状态',
              options: this.enterpriseStatusList,
            },
          },
          {
            field: 'industryType',
            title: '行业分类',
            element: 'el-select',
            props: {
              placeholder: '请选择行业分类',
              options: this.industryTypeList,
            },
          },
        ],
        params: this.manageParams,
      };
    },
    registerFilterOptions() {
      return {
        config: [
          {
            field: 'enterpriseName',
            title: '企业名称',
            element: 'el-input',
          },
          {
            field: 'enterpriseId',
            title: '企业编号',
            element: 'el-input',
          },
          {
            field: 'industryType',
            title: '行业分类',
            element: 'el-select',
            props: {
              placeholder: '请选择行业分类',
              options: this.industryTypeList,
            },
          },
          {
            field: 'reviewStatus',
            title: '审核状态',
            element: 'el-select',
            props: {
              placeholder: '请选择审核状态',
              options: this.reviewStatusList,
            },
          },
        ],
        params: this.registerParams,
      };
    },
  },
  mounted() {
    this.loadManageData();
  },
  methods: {
    async handleClickTab({ index }) {
      if (index === '0') {
        this.manageTablePage = {
          total: 0,
          currentPage: 1,
          pageSize: 10,
        };
        this.chooseManageList = [];
        this.manageTableData = [];
        this.loadManageData();
      } else {
        this.registerTablePage = {
          total: 0,
          currentPage: 1,
          pageSize: 10,
        };
        this.chooseRegisterList = [];
        this.registerTableData = [];
        this.loadRegisterData();
      }
    },

    // 企业管理列表
    async loadManageData() {
      this.chooseRegisterList = [];
      this.registerTableData = [];
      const { enterpriseName, enterpriseId, status, industryType } =
        this.manageParams;

      this.manageLoading = true;
      const [err, res] = await getEnterprisePage({
        enterpriseName,
        enterpriseId,
        status,
        industryType,
        pageNum: this.manageTablePage.currentPage,
        pageSize: this.manageTablePage.pageSize,
      });
      this.manageLoading = false;

      if (err) {
        return this.$message.error(err.message);
      }
      const { data, total } = res;

      this.manageTableData = data.map((item) => {
        return { ...item, status: item.status === 'ENABLE' ? true : false };
      });
      this.manageTablePage.total = total;
    },
    // 删除
    handleDelete(row) {
      const { enterpriseId, enterpriseName } = row;
      this.$confirm(`确定删除${enterpriseName}企业吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          const [err, res] = await enterpriseRemove({
            enterpriseId,
          });
          if (err) {
            return this.$message.error(err.message);
          }
          this.$message.success(`删除成功`);
          this.loadManageData();
        })
        .catch(() => {});
    },
    // 企业状态变更
    changeEnterpriseStatus(row) {
      const { status, enterpriseId, enterpriseName } = row;
      this.$confirm(
        `确定${!status ? '停用' : '启用'}${enterpriseName}企业吗？`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      )
        .then(async () => {
          const [err, res] = await enterpriseStatus({
            enterpriseIds: [enterpriseId],
            status: !status ? 'DISABLE' : 'ENABLE',
          });
          if (err) {
            return this.$message.error(err.message);
          }
          this.$message.success(`${!status ? '停用' : '启用'}成功`);
          this.loadManageData();
        })
        .catch(() => {
          row.status = !row.status;
        });
    },

    handleCheckboxChange({ records }) {
      this.chooseManageList = records;
    },

    // 批量启用/禁用
    handleStatus(status) {
      if (!this.chooseManageList.length) {
        this.$message.warning('请先选择要操作的项');
        return;
      }
      this.$confirm(
        `确定批量${status === 'ENABLE' ? '启用' : '禁用'}选中的企业吗？`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(async () => {
        const [err, res] = await enterpriseStatus({
          enterpriseIds: this.chooseManageList.map((item) => item.enterpriseId),
          status,
        });
        if (err) {
          return this.$message.error(err.message);
        }
        this.$message.success(
          `批量${status === 'ENABLE' ? '启用' : '禁用'}成功`
        );
        this.loadManageData();
      });
    },

    // 批量导出
    async handleExport() {
      this.download(
        '/vehicle-charging-admin/enterprise/enterpriseExport',
        {
          ...this.manageParams,
        },
        `企业管理列表.xlsx`
      );
    },
    // 企业注册
    onClickAdd(row) {
      this.$router.push({
        path: `/v2g-charging-web/baseInfo/userManage/enterpriseManage/create?enterpriseId=${
          row?.enterpriseId || ''
        }`,
      });
    },

    // 企业详情
    handleDetail(row) {
      const { enterpriseId } = row;
      this.$router.push({
        path: '/v2g-charging-web/baseInfo/userManage/enterpriseManage/detail',
        query: {
          enterpriseId,
        },
      });
    },

    // 企业审核
    handleAudit(row) {
      const { enterpriseId } = row;
      this.$router.push({
        path: '/v2g-charging-web/baseInfo/userManage/enterpriseManage/audit',
        query: {
          enterpriseId,
        },
      });
    },

    // 企业优惠配置
    handleDiscount(row) {
      const { enterpriseId } = row;
      this.$router.push({
        path: '/v2g-charging-web/baseInfo/userManage/enterpriseManage/discount',
        query: {
          enterpriseId,
        },
      });
    },

    // 企业账户管理
    handleAccount(row) {
      const { enterpriseId } = row;
      this.$router.push({
        path: '/v2g-charging-web/baseInfo/userManage/enterpriseManage/accountManage',
        query: {
          enterpriseId,
        },
      });
    },

    // 企业注册管理列表
    async loadRegisterData() {
      this.chooseRegisterList = [];
      this.registerTableData = [];
      const { enterpriseName, enterpriseId, industryType, reviewStatus } =
        this.registerParams;

      this.registerLoading = true;
      const [err, res] = await getEnterprisePage({
        enterpriseName,
        enterpriseId,
        industryType,
        reviewStatus,
        pageNum: this.registerTablePage.currentPage,
        pageSize: this.registerTablePage.pageSize,
      });
      this.registerLoading = false;

      if (err) {
        return this.$message.error(err.message);
      }
      const { data, total } = res;

      this.registerTableData = data;
      this.registerTablePage.total = total;
    },

    // 企业注册批量选择
    handleRegisterCheckboxChange({ records }) {
      this.chooseRegisterList = records;
    },

    // 批量审核
    handleBatchAudit() {
      if (!this.chooseRegisterList.length) {
        this.$message.warning('请选择企业');
        return;
      }

      this.$refs.batchAuditModal.dialogVisible = true;
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-tabs {
  margin-top: 16px;
  background-color: #f5f6f9;
  .el-tabs__header {
    padding-left: 0;
    display: flex;
    justify-content: center;
    text-align: center;
    margin-bottom: 1px;
    .el-tabs__item {
      padding: 0;
      width: 164px;
      font-size: 18px;
      font-weight: 400;
      background-color: #fff;
    }
    .el-tabs__item.is-active {
      background-color: #1677fe;
      color: #fff;
    }
    .el-tabs__nav-scroll {
      border-radius: 25px;
      border: solid 1px #dfe1e5;
    }
    .el-tabs__active-bar {
      display: none;
    }
    .el-tabs__nav-wrap::after {
      width: 0;
    }
  }
}

.table-wrap {
  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }
  margin: 16px;

  .card-head {
    // position: relative;
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    margin-top: -20px;
    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }
  .info-wrap {
    margin-top: 16px;
    display: flex;
    flex-wrap: wrap;
    gap: 16px;

    .info-item {
      background-color: #fafbfc;
      flex: 1 1 0;
      // min-width: 180px;

      border-radius: 5px;
      padding: 8px 24px;
      box-sizing: border-box;
      // margin-right: 16px;
      display: flex;
      .info-icon {
        width: 42px;
        height: 42px;
      }
      .info-right-wrap {
        flex: 1;
        margin-left: 8px;
        .info-title {
          font-weight: 400;
          font-size: 14px;
          line-height: 14px;
          margin-bottom: 8px;
        }
        .info-number {
          font-size: 20px;
          font-weight: 500;
          .info-unit {
            font-size: 14px;
            font-weight: 400;
          }
        }
      }
    }
    .info-item:last-child {
      margin-right: 0;
    }
  }

  .top-button-wrap {
    display: flex;
    margin: 16px 0;
  }
}

::v-deep .vxe-table--render-default .vxe-body--column.col--center {
  text-align: left !important;
}

.button-border {
  border: 0.01rem solid #217aff;
  color: #217aff;
  background-color: #fff;
}

.button-danger {
  border: 0.01rem solid #fc1e31;
  color: #fc1e31;
  background-color: #fff;
}

.ellipsis-text {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }
</style>
