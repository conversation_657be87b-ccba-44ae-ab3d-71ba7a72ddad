<template>
  <div class="container container-float" style="padding: 0">
    <div class="device-head">
      <img
        src="@/assets/order/abnormal-detail-icon.png"
        class="device-head-icon"
      />

      <div class="device-info-wrap">
        <div class="device-title-wrap">
          <div class="device-title">
            企业名称：{{ detailObj.enterpriseName || '' }}
          </div>
        </div>
        <div class="device-info-wrap">
          <el-row>
            <el-col :span="8">
              <span class="label">企业简称：</span>
              <span class="value">{{ detailObj.shortName || '' }}</span>
            </el-col>
            <el-col :span="8">
              <span class="label">行业分类：</span>
              <span class="value">{{ detailObj.industryType || '' }}</span>
            </el-col>
            <el-col :span="8">
              <span class="label">企业地址：</span>
              <span class="value">{{ detailObj.address || '' }}</span>
            </el-col>
          </el-row>
        </div>
      </div>

      <div class="device-status-wrap">
        <div class="device-status-item-wrap">
          <div class="device-status-item-title">企业状态</div>
          <div
            :class="{
              'device-status-success': true,
              'device-status': detailObj.status === 'ENABLE',
            }"
          >
            {{ detailObj.status === 'ENABLE' ? '启用' : '禁用' }}
          </div>
        </div>
      </div>
    </div>

    <div class="info-card">
      <div class="card-head" style="margin-bottom: 8px">
        <div class="before-icon"></div>
        <div class="card-head-text">基础信息</div>
      </div>

      <div class="form-wrap">
        <el-row :gutter="20">
          <el-col :span="8" style="margin-bottom: 24px">
            <div style="display: flex">
              <div class="info-title">管理员名称：</div>
              <div class="info-detail">{{ detailObj.adminName || '' }}</div>
            </div>
          </el-col>
          <el-col :span="8" style="margin-bottom: 24px">
            <div style="display: flex">
              <div class="info-title">管理员联系方式：</div>
              <div class="info-detail">{{ detailObj.adminMobile || '' }}</div>
            </div>
          </el-col>
          <el-col :span="8" style="margin-bottom: 24px">
            <div style="display: flex">
              <div class="info-title">管理员邮箱：</div>
              <div class="info-detail">{{ detailObj.adminEmail || '' }}</div>
            </div>
          </el-col>

          <el-col :span="8" style="margin-bottom: 24px">
            <div style="display: flex">
              <div class="info-title">法人信息：</div>
              <div class="info-detail">{{ detailObj.legalPerson || '' }}</div>
            </div>
          </el-col>

          <el-col :span="8" style="margin-bottom: 24px">
            <div style="display: flex">
              <div class="info-title">企业账号：</div>
              <div class="info-detail">
                {{ detailObj.enpLoginAccount || '' }}
              </div>
            </div>
          </el-col>
          <el-col :span="8" style="margin-bottom: 24px">
            <div style="display: flex">
              <div class="info-title">企业账号初始密码：</div>
              <div class="info-detail">
                {{ detailObj.password || '' }}
              </div>
            </div>
          </el-col>

          <el-col :span="8" style="margin-bottom: 24px">
            <div>
              <div class="info-title" style="margin-bottom: 8px">
                营业执照：
              </div>
              <Preview :src="detailObj.busiLicenseUrl"></Preview>
            </div>
          </el-col>

          <el-col :span="16" style="margin-bottom: 24px">
            <div>
              <div class="info-title" style="margin-bottom: 8px">
                法人证件：
              </div>

              <Preview
                style="margin-right: 8px"
                :src="detailObj.lpCertificateUrl"
              ></Preview>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <div class="info-card">
      <div class="card-head" style="margin-bottom: 8px">
        <div class="before-icon"></div>
        <div class="card-head-text">消息通知</div>
      </div>

      <div class="form-wrap">
        <el-row :gutter="20">
          <el-col :span="24" style="margin-bottom: 24px">
            <div style="display: flex">
              <div class="info-title">费用通知：</div>
              <div class="info-detail">{{ amountNotice }}</div>
            </div>
          </el-col>
          <el-col :span="24" style="margin-bottom: 24px">
            <div style="display: flex">
              <div class="info-title">企业账户余额通知：</div>
              <div class="info-detail">{{ balanceNotice }}</div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <div class="info-card">
      <div class="card-head" style="margin-bottom: 8px">
        <div class="before-icon"></div>
        <div class="card-head-text">站点配置</div>
      </div>
      <div class="form-wrap">
        <BuseCrud
          ref="crud"
          :loading="loading"
          :tablePage="tablePage"
          :tableColumn="tableColumn"
          :tableData="tableData"
          :modalConfig="modalConfig"
          @loadData="loadData"
        >
          <template slot="status" slot-scope="{ row }">
            <el-switch
              v-model="row.status"
              @change="changeStatus(row)"
            ></el-switch>
          </template>
          <template slot="operate" slot-scope="{ row }">
            <div class="menu-box">
              <el-button
                class="button-border"
                type="primary"
                plain
                @click="handleHistory(row)"
              >
                定价历史记录
              </el-button>
            </div>
          </template>
        </BuseCrud>
      </div>
    </div>

    <!-- <HistoryRecord ref="historyRecordModal" /> -->
  </div>
</template>

<script>
import { enterpriseDetail } from '@/api/user/enterprise';
import Preview from '@/components/Preview/index.vue';
import HistoryRecord from '../components/historyRecord.vue';
export default {
  components: {
    Preview,
    HistoryRecord,
  },
  dicts: [],
  data() {
    return {
      amountNotice: '每月 3 日，通知管理员账户余额情况',
      balanceNotice: '低于 300，通知账户余额情况，每日一次',
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
          fixed: 'left',
        },
        {
          field: 'stationName',
          title: '场站名称',
          minWidth: 150,
        },
        {
          title: '优惠方式',
          field: 'discountMethod',
          minWidth: 150,
        },
        {
          title: '原电价',
          field: 'originalPrice',
          minWidth: 150,
        },
        {
          title: '原服务费单价',
          field: 'originalServicePrice',
          minWidth: 150,
        },
        {
          title: '优惠后电价',
          field: 'discountedPrice',
          minWidth: 150,
        },
        {
          title: '优惠后服务费单价',
          field: 'discountedServicePrice',
          minWidth: 150,
        },
        {
          title: '生效时间',
          field: 'effectiveTime',
          minWidth: 150,
        },
        {
          title: '结束时间',
          field: 'endTime',
          minWidth: 150,
        },
        {
          field: 'status',
          title: '状态',
          minWidth: 100,
          fixed: 'right',
          slots: { default: 'status' },
        },
        {
          field: 'action',
          title: '操作',
          slots: { default: 'operate' },
          width: 150,
          align: 'center',
          fixed: 'right',
        },
      ],
      tableData: [
        {
          stationName: '高新充电站',
          discountMethod: '满100减20',
          originalPrice: 1.2,
          originalServicePrice: 0.5,
          discountedPrice: 1.0,
          discountedServicePrice: 0.45,
          effectiveTime: '2024-03-01',
          endTime: '2024-03-31',
          status: true,
        },
        {
          stationName: '滨江充电站',
          discountMethod: '夜间8折',
          originalPrice: 1.3,
          originalServicePrice: 0.55,
          discountedPrice: 1.04,
          discountedServicePrice: 0.55,
          effectiveTime: '2024-02-15',
          endTime: '2024-03-15',
          status: false,
        },
        {
          stationName: '西湖充电站',
          discountMethod: '套餐优惠包',
          originalPrice: 1.25,
          originalServicePrice: 0.48,
          discountedPrice: 1.1,
          discountedServicePrice: 0.4,
          effectiveTime: '2024-04-01',
          endTime: '2024-06-30',
          status: true,
        },
        {
          stationName: '钱江新城站',
          discountMethod: '新用户首充立减',
          originalPrice: 1.35,
          originalServicePrice: 0.6,
          discountedPrice: 1.15,
          discountedServicePrice: 0.5,
          effectiveTime: '2024-03-10',
          endTime: '2024-04-10',
          status: true,
        },
      ],
      detailObj: {},
      enterpriseId: '',
    };
  },

  computed: {
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  mounted() {
    this.enterpriseId = this.$route.query?.enterpriseId || '';
    if (this.enterpriseId) {
      this.queryEnterpriseDetail();
    }
  },
  methods: {
    async queryEnterpriseDetail() {
      const [err, res] = await enterpriseDetail({
        enterpriseId: this.enterpriseId,
      });
      if (err) return;
      this.detailObj = res?.data || {};
    },

    // 定价历史记录
    handleHistory(row) {
      const stationId = row.stationId;

      this.$refs.historyRecordModal.stationId = stationId;

      this.$refs.historyRecordModal.loadData();

      this.$refs.historyRecordModal.dialogVisible = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.container-full {
  background-color: rgba(244, 246, 249, 1);
  padding-bottom: 16px;
  min-height: calc(100% - 80px);
  overflow-y: auto;
}

.device-head {
  background-color: #fff;

  display: flex;
  height: 112px;
  display: flex;
  align-items: center;
  padding: 0 24px;
  box-sizing: border-box;
  .device-head-icon {
    width: 48px;
    height: 48px;
    margin-right: 24px;
  }
  .device-info-wrap {
    flex: 1;
    .device-title-wrap {
      height: 32px;
      display: flex;
      align-items: center;
      .device-title {
        font-weight: 500;
        font-size: 24px;
        color: #12151a;
      }
      .device-status {
        // width: 50px;
        padding: 0 10px;
        height: 24px;
        border-radius: 10px 0 10px 0;
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;
        text-align: center;
        color: #fff;
        background: linear-gradient(321.01deg, #ffb624 8.79%, #ff8d24 100.27%);
        margin-left: 12px;
      }
    }
    .device-info-wrap {
      height: 16px;
      margin-top: 16px;
      font-size: 16px;
      font-weight: 400;
      color: #292b33;
    }
  }
  .device-status-wrap {
    display: flex;
    align-items: center;
    .device-status-item-wrap {
      width: 150px;
      .device-status-item-title {
        font-weight: 400;
        font-size: 14px;
        line-height: 14px;
        color: #505363;
        margin: 0 auto 12px auto;
        text-align: center;
      }
      .device-status {
        width: 86px;
        height: 34px;
        border-radius: 4px;
        display: flex;
        margin: 0 auto;
        align-items: center;
        justify-content: center;
        background-color: #ebf3ff;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 18px;
        color: #217aff;
      }
      .device-status-success {
        width: 86px;
        height: 34px;
        border-radius: 4px;
        display: flex;
        margin: 0 auto;
        align-items: center;
        justify-content: center;
        background-color: #ebfff1;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 18px;
        color: #00c864;
      }
    }
    .device-status-split {
      width: 1px;
      height: 36px;
      background-color: #e9ebf0;
    }
  }
}

.info-card {
  margin: 16px;
  border-radius: 5px;
  border: 1px solid #fff;
  overflow: hidden;
  background-color: #fff;
  // min-height: 300px;
  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    background: linear-gradient(180deg, #e9f2ff 0%, #ffffff 100%);
    .before-icon {
      width: 3px;
      height: 16px;
      background-image: url('~@/assets/station/consno-before.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin-right: 8px;
    }
    .card-head-text {
      flex: 1;
      font-weight: 500;
      font-size: 16px;
      color: #12151a;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
    .button-wrap {
      display: flex;
      .invite-btn {
        background-color: #1ab2ff;
        border-color: #1ab2ff;
      }
      ::v-deep .el-button--small {
        font-size: 14px;
      }
      .distribution {
        margin-left: 24px;
        margin-right: 24px;
        display: flex;
        align-items: center;
      }
    }
  }

  .people-table-wrap {
    ::v-deep .bd3001-table-select-box {
      display: none;
    }
    ::v-deep .bd3001-button {
      width: 100%;
    }
  }
  .form-wrap {
    padding: 0 16px 16px 16px;
    .info-title {
      font-weight: 400;
      font-size: 16px;
      line-height: 16px;
      color: #505363;
    }
    .info-detail {
      font-weight: 400;
      font-size: 16px;
      line-height: 16px;
      color: #292b33;
    }
    .info-amount {
      height: 28px;
      background-color: #fff7e6;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 8px;
      font-weight: 400;
      font-size: 20px;
      color: #fe8921;
      margin-top: -6px;
      margin-right: 4px;
    }
    .info-img {
      width: 140px;
      height: 140px;
    }
  }
}

.button-border {
  border: 0.01rem solid #217aff;
  color: #217aff;
  background-color: #fff;
}

::v-deep .bd3001-content {
  padding: 0 !important;
}
</style>
