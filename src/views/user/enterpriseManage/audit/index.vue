<template>
  <div class="container container-float" style="padding: 0 0 100px 0">
    <div class="device-head">
      <img
        src="@/assets/order/abnormal-detail-icon.png"
        class="device-head-icon"
      />

      <div class="device-info-wrap">
        <div class="device-title-wrap">
          <div class="device-title">
            企业名称：{{ detailObj.enterpriseName || '' }}
          </div>
        </div>
        <div class="device-info-wrap">
          <el-row>
            <el-col :span="8">
              <span class="label">企业简称：</span>
              <span class="value">{{ detailObj.shortName || '' }}</span>
            </el-col>
            <el-col :span="8">
              <span class="label">行业分类：</span>
              <span class="value">{{ detailObj.industryType || '' }}</span>
            </el-col>
            <el-col :span="8">
              <span class="label">企业地址：</span>
              <span class="value">{{ detailObj.address || '' }}</span>
            </el-col>
          </el-row>
        </div>
      </div>

      <div class="device-status-wrap">
        <div class="device-status-item-wrap">
          <div class="device-status-item-title">企业状态</div>
          <div
            :class="{
              'device-status-success': true,
              'device-status': detailObj.status === 'ENABLE',
            }"
          >
            {{ detailObj.status === 'ENABLE' ? '启用' : '禁用' }}
          </div>
        </div>
      </div>

      <el-button type="primary" @click="drawer = true">审核轨迹</el-button>
    </div>

    <div class="info-card">
      <div class="card-head" style="margin-bottom: 8px">
        <div class="before-icon"></div>
        <div class="card-head-text">基础信息</div>
      </div>

      <div class="form-wrap">
        <el-row :gutter="20">
          <el-col :span="8" style="margin-bottom: 24px">
            <div style="display: flex">
              <div class="info-title">管理员名称：</div>
              <div class="info-detail">{{ detailObj.adminName || '' }}</div>
            </div>
          </el-col>
          <el-col :span="8" style="margin-bottom: 24px">
            <div style="display: flex">
              <div class="info-title">管理员联系方式：</div>
              <div class="info-detail">{{ detailObj.adminMobile || '' }}</div>
            </div>
          </el-col>
          <el-col :span="8" style="margin-bottom: 24px">
            <div style="display: flex">
              <div class="info-title">管理员邮箱：</div>
              <div class="info-detail">{{ detailObj.adminEmail || '' }}</div>
            </div>
          </el-col>

          <el-col :span="8" style="margin-bottom: 24px">
            <div style="display: flex">
              <div class="info-title">法人信息：</div>
              <div class="info-detail">{{ detailObj.legalPerson || '' }}</div>
            </div>
          </el-col>

          <el-col :span="8" style="margin-bottom: 24px">
            <div style="display: flex">
              <div class="info-title">企业账号：</div>
              <div class="info-detail">
                {{ detailObj.enpLoginAccount || '' }}
              </div>
            </div>
          </el-col>
          <el-col :span="8" style="margin-bottom: 24px">
            <div style="display: flex">
              <div class="info-title">企业账号初始密码：</div>
              <div class="info-detail">
                {{ detailObj.password || '' }}
              </div>
            </div>
          </el-col>

          <el-col :span="8" style="margin-bottom: 24px">
            <div>
              <div class="info-title" style="margin-bottom: 8px">
                营业执照：
              </div>
              <Preview :src="detailObj.busiLicenseUrl"></Preview>
            </div>
          </el-col>

          <el-col :span="16" style="margin-bottom: 24px">
            <div>
              <div class="info-title" style="margin-bottom: 8px">
                法人证件：
              </div>

              <Preview
                style="margin-right: 8px"
                :src="detailObj.lpCertificateUrl"
              ></Preview>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <div class="info-card">
      <div class="card-head" style="margin-bottom: 8px">
        <div class="before-icon"></div>
        <div class="card-head-text">审核信息</div>
      </div>

      <div class="form-wrap">
        <el-form :model="form" :rules="rules" ref="form" label-position="top">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                label="审核结果"
                prop="reviewStatus"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="form.reviewStatus"
                  placeholder="请选择审核结果"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in reviewStatusList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item
                label="审核意见"
                prop="reviewRemark"
                :label-width="formLabelWidth"
                :rules="[
                  {
                    required: form.reviewStatus === 'REJECT' ? true : false,
                    message: '请输入审核意见',
                    trigger: 'change',
                  },
                ]"
              >
                <el-input
                  v-model="form.reviewRemark"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入审核意见"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <div class="bottom-wrap">
      <el-button @click="() => handleCancel()">取消</el-button>
      <el-button
        type="primary"
        :loading="submitLoading"
        @click="() => handleConfirm()"
      >
        提交
      </el-button>
    </div>
  </div>
</template>

<script>
import Preview from '@/components/Preview/index.vue';
import { enterpriseDetail, batchApprove } from '@/api/user/enterprise';

export default {
  components: {
    Preview,
  },
  dicts: [],
  data() {
    return {
      form: {
        reviewStatus: '',
        reviewRemark: '',
      },
      rules: {
        reviewStatus: [
          { required: true, message: '请选择审核结果', trigger: 'blur' },
        ],
      },
      formLabelWidth: '120px',
      submitLoading: false,
      reviewStatusList: [
        { label: '审核通过', value: 'PASS' },
        { label: '审核不通过', value: 'REJECT' },
      ],
      detailObj: {},
      enterpriseId: '',
    };
  },

  computed: {},
  mounted() {
    this.enterpriseId = this.$route.query?.enterpriseId || '';
    if (this.enterpriseId) {
      this.queryEnterpriseDetail();
    }
  },
  methods: {
    async queryEnterpriseDetail() {
      const [err, res] = await enterpriseDetail({
        enterpriseId: this.enterpriseId,
      });
      if (err) return;
      this.detailObj = res?.data || {};
    },
    handleCancel() {
      this.$router.back();
    },
    handleConfirm() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.submitLoading = true;
          const params = {
            ...this.form,
            enterpriseIds: [this.enterpriseId],
          };
          const [err, res] = await batchApprove(params);
          if (err) {
            this.submitLoading = false;
            return this.$message.error(err.message || '审核失败');
          }
          this.$message({
            type: 'success',
            message: '审核成功!',
          });
          setTimeout(() => {
            this.$router.back();
            this.submitLoading = false;
          }, 2000);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.container-full {
  background-color: rgba(244, 246, 249, 1);
  padding-bottom: 16px;
  min-height: calc(100% - 80px);
  overflow-y: auto;
}

.device-head {
  background-color: #fff;

  display: flex;
  height: 112px;
  display: flex;
  align-items: center;
  padding: 0 24px;
  box-sizing: border-box;
  .device-head-icon {
    width: 48px;
    height: 48px;
    margin-right: 24px;
  }
  .device-info-wrap {
    flex: 1;
    .device-title-wrap {
      height: 32px;
      display: flex;
      align-items: center;
      .device-title {
        font-weight: 500;
        font-size: 24px;
        color: #12151a;
      }
      .device-status {
        // width: 50px;
        padding: 0 10px;
        height: 24px;
        border-radius: 10px 0 10px 0;
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;
        text-align: center;
        color: #fff;
        background: linear-gradient(321.01deg, #ffb624 8.79%, #ff8d24 100.27%);
        margin-left: 12px;
      }
    }
    .device-info-wrap {
      height: 16px;
      margin-top: 16px;
      font-size: 16px;
      font-weight: 400;
      color: #292b33;
    }
  }
  .device-status-wrap {
    display: flex;
    align-items: center;
    .device-status-item-wrap {
      width: 150px;
      .device-status-item-title {
        font-weight: 400;
        font-size: 14px;
        line-height: 14px;
        color: #505363;
        margin: 0 auto 12px auto;
        text-align: center;
      }
      .device-status {
        width: 86px;
        height: 34px;
        border-radius: 4px;
        display: flex;
        margin: 0 auto;
        align-items: center;
        justify-content: center;
        background-color: #ebf3ff;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 18px;
        color: #217aff;
      }
      .device-status-success {
        width: 86px;
        height: 34px;
        border-radius: 4px;
        display: flex;
        margin: 0 auto;
        align-items: center;
        justify-content: center;
        background-color: #ebfff1;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 18px;
        color: #00c864;
      }
    }
    .device-status-split {
      width: 1px;
      height: 36px;
      background-color: #e9ebf0;
    }
  }
}

.info-card {
  margin: 16px;
  border-radius: 5px;
  border: 1px solid #fff;
  overflow: hidden;
  background-color: #fff;
  // min-height: 300px;
  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    background: linear-gradient(180deg, #e9f2ff 0%, #ffffff 100%);
    .before-icon {
      width: 3px;
      height: 16px;
      background-image: url('~@/assets/station/consno-before.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin-right: 8px;
    }
    .card-head-text {
      flex: 1;
      font-weight: 500;
      font-size: 16px;
      color: #12151a;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
    .button-wrap {
      display: flex;
      .invite-btn {
        background-color: #1ab2ff;
        border-color: #1ab2ff;
      }
      ::v-deep .el-button--small {
        font-size: 14px;
      }
      .distribution {
        margin-left: 24px;
        margin-right: 24px;
        display: flex;
        align-items: center;
      }
    }
  }

  .people-table-wrap {
    ::v-deep .bd3001-table-select-box {
      display: none;
    }
    ::v-deep .bd3001-button {
      width: 100%;
    }
  }
  .form-wrap {
    padding: 0 16px 16px 16px;
    .info-title {
      font-weight: 400;
      font-size: 16px;
      line-height: 16px;
      color: #505363;
    }
    .info-detail {
      font-weight: 400;
      font-size: 16px;
      line-height: 16px;
      color: #292b33;
    }
    .info-amount {
      height: 28px;
      background-color: #fff7e6;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 8px;
      font-weight: 400;
      font-size: 20px;
      color: #fe8921;
      margin-top: -6px;
      margin-right: 4px;
    }
    .info-img {
      width: 140px;
      height: 140px;
    }
  }
}

.container {
  position: relative;
  padding-bottom: 100px;
  box-sizing: border-box;
  .bottom-wrap {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 86px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background-color: #ffffff;
    padding-right: 32px;
    box-sizing: border-box;
  }
}
</style>
