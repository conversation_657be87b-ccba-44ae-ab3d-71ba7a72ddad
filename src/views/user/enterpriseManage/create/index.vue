<template>
  <div class="container container-float" style="padding: 0">
    <div class="info-card">
      <div class="card-head">
        <div class="before-icon"></div>
        <div class="card-head-text">基础信息</div>
      </div>
      <div class="card-head-split"></div>
      <div class="form-wrap">
        <el-form
          :model="baseInfo.form"
          :rules="baseInfo.rules"
          ref="baseInfoForm"
          label-position="top"
        >
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="企业名称" prop="enterpriseName">
                <el-input v-model="baseInfo.form.enterpriseName"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="企业简称" prop="shortName">
                <el-input v-model="baseInfo.form.shortName"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="行业分类" prop="industryType">
                <el-select
                  v-model="baseInfo.form.industryType"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in industryTypeList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="企业地址" prop="address">
                <el-input v-model="baseInfo.form.address"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="管理员名称" prop="adminName">
                <el-input v-model="baseInfo.form.adminName"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="管理员联系方式" prop="adminMobile">
                <el-input v-model="baseInfo.form.adminMobile"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="管理员邮箱" prop="adminEmail">
                <el-input v-model="baseInfo.form.adminEmail"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="企业状态" prop="status">
                <el-select v-model="baseInfo.form.status" placeholder="请选择">
                  <el-option
                    v-for="item in enterpriseStatusList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="法人信息" prop="legalPerson">
                <el-input v-model="baseInfo.form.legalPerson"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="企业账号" prop="enpLoginAccount">
                <el-input v-model="baseInfo.form.enpLoginAccount"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="企业账号初始密码" prop="password">
                <el-input v-model="baseInfo.form.password"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="营业执照" prop="busiLicenseUrl">
                <Upload v-model="baseInfo.form.busiLicenseUrl" :limit="1" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item label="法人证件" prop="lpCertificateUrl">
                <Upload v-model="baseInfo.form.lpCertificateUrl" :limit="2" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
    <div class="bottom-button-wrap">
      <div>
        <el-button @click="handleCancel">取消</el-button>
        <el-button @click="handleSave" :loading="submitLoading" type="primary">
          提交
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import Upload from '@/components/Upload/index';
import {
  enterpriseCreate,
  enterpriseEdit,
  enterpriseDetail,
} from '@/api/user/enterprise';
import { Loading } from 'element-ui';
export default {
  components: {
    Upload,
  },
  dicts: ['ls_charging_status'],
  data() {
    return {
      industryTypeList: [{ label: '服务行业', value: '服务行业' }],
      enterpriseStatusList: [
        { label: '启用', value: 'ENABLE' },
        { label: '禁用', value: 'DISABLE' },
      ],
      enterpriseId: '',
      submitLoading: false,
      baseInfo: {
        form: {
          enterpriseName: '',
          shortName: '',
          industryType: '',
          address: '',
          adminName: '',
          adminMobile: '',
          adminEmail: '',
          status: '',
          legalPerson: '',
          enpLoginAccount: '',
          password: '',
          busiLicenseUrl: [],
          lpCertificateUrl: [],
        },
        rules: {
          enterpriseName: [
            { required: true, message: '请输入企业名称', trigger: 'blur' },
          ],
          industryType: [
            { required: true, message: '请选择行业类型', trigger: 'blur' },
          ],
          address: [
            { required: true, message: '请输入企业地址', trigger: 'blur' },
          ],
          adminName: [
            { required: true, message: '请输入管理员姓名', trigger: 'blur' },
          ],
          adminMobile: [
            {
              required: true,
              message: '请输入管理员联系方式',
              trigger: 'blur',
            },
          ],
          adminEmail: [
            { required: true, message: '请输入管理员邮箱', trigger: 'blur' },
          ],
          status: [
            { required: true, message: '请选择企业状态', trigger: 'blur' },
          ],
          legalPerson: [
            { required: true, message: '请输入法人信息', trigger: 'blur' },
          ],
          enpLoginAccount: [
            { required: true, message: '请输入企业账号', trigger: 'blur' },
          ],
          password: [
            {
              required: true,
              message: '请输入企业账号初始密码',
              trigger: 'blur',
            },
          ],
          busiLicenseUrl: [
            { required: true, message: '请上传营业执照', trigger: 'blur' },
          ],
          lpCertificateUrl: [
            { required: true, message: '请上传法人证件', trigger: 'blur' },
          ],
        },
      },
    };
  },

  computed: {},
  mounted() {
    this.enterpriseId = this.$route.query?.enterpriseId || '';
    if (this.enterpriseId) {
      this.queryEnterpriseDetail();
    }
  },
  methods: {
    async queryEnterpriseDetail() {
      const [err, res] = await enterpriseDetail({
        enterpriseId: this.enterpriseId,
      });
      if (err) return;
      const {
        enterpriseName,
        shortName,
        industryType,
        address,
        adminName,
        adminMobile,
        adminEmail,
        status,
        legalPerson,
        enpLoginAccount,
        password,
        busiLicenseUrl,
        lpCertificateUrl,
      } = res?.data || {};
      this.baseInfo.form = {
        enterpriseName,
        shortName,
        industryType,
        address,
        adminName,
        adminMobile,
        adminEmail,
        status,
        legalPerson,
        enpLoginAccount,
        password,
        busiLicenseUrl: [
          {
            url: busiLicenseUrl,
          },
        ],
        lpCertificateUrl: [
          {
            url: lpCertificateUrl,
          },
        ],
      };
    },
    handleCancel() {
      this.$router.back();
    },
    handleSave() {
      this.$refs.baseInfoForm.validate(async (valid) => {
        if (valid) {
          this.submitLoading = true;
          const params = {
            ...this.baseInfo.form,
          };
          if (!this.enterpriseId) {
            const [err, res] = await enterpriseCreate({
              ...params,
              busiLicenseUrl: params.busiLicenseUrl[0]?.url || '',
              lpCertificateUrl: params.lpCertificateUrl[0]?.url || '',
            });
            if (err) {
              this.submitLoading = false;
              return this.$message.error(err.message);
            }
            this.$message.success(`创建成功`);
            setTimeout(() => {
              this.submitLoading = false;
              this.$router.back();
            }, 2000);
          } else {
            const [err, res] = await enterpriseEdit({
              ...params,
              busiLicenseUrl: params.busiLicenseUrl[0]?.url || '',
              lpCertificateUrl: params.lpCertificateUrl[0]?.url || '',
              enterpriseId: this.enterpriseId,
            });
            if (err) {
              this.submitLoading = false;
              return this.$message.error(err.message);
            }
            this.$message.success(`编辑成功`);
            setTimeout(() => {
              this.submitLoading = false;
              this.$router.back();
            }, 2000);
          }
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.container-full {
  background-color: rgba(244, 246, 249, 1);
  // padding-bottom: 16px;
  min-height: calc(100% - 80px);
  overflow-y: auto;
}

.el-select {
  width: 100%;
}

.info-card {
  margin: 16px;
  border-radius: 5px;
  border: 1px solid #fff;
  background-color: #fff;

  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;

    .before-icon {
      width: 3px;
      height: 16px;
      background-image: url('~@/assets/station/consno-before.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin-right: 8px;
    }

    .card-head-text {
      font-weight: 500;
      font-size: 16px;
      color: #12151a;
    }
  }
  .card-head-split {
    height: 1px;
    background-color: #f9f9fb;
  }

  .form-wrap {
    padding: 16px;
  }

  ::v-deep .el-form-item__label {
    display: flex;
  }

  .protocol-label {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .btn-wrap {
      display: flex;
      align-items: center;
      color: #409eff;
      cursor: pointer;
      font-size: 14px;
      font-weight: 400;

      .icon-add {
        width: 16px;
        height: 16px;
        margin-right: 8px;
      }
    }
  }
}

.bottom-button-wrap {
  height: 86px;
  margin-top: 16px;
  width: 100%;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: 32px;
  box-sizing: border-box;
}
</style>
