<template>
  <el-dialog title="审核信息" :visible.sync="dialogVisible" width="630px">
    <el-form
      :model="form"
      :rules="rules"
      ref="abnormalForm"
      label-position="top"
    >
      <el-form-item
        label="审核类型"
        prop="reviewStatus"
        :label-width="formLabelWidth"
      >
        <el-radio-group v-model="form.reviewStatus">
          <el-radio :label="'PASS'">审核通过</el-radio>
          <el-radio :label="'REJECT'">审核不通过</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        label="审核意见"
        prop="reviewRemark"
        :label-width="formLabelWidth"
        :rules="[
          {
            required: form.reviewStatus === 'REJECT' ? true : false,
            message: '请输入审核意见',
            trigger: 'change',
          },
        ]"
      >
        <el-input
          v-model="form.reviewRemark"
          type="textarea"
          :rows="3"
          placeholder="请输入审核意见"
        ></el-input>

        <div class="remark-brief">注：审核不通过时，审核意见必填</div>
      </el-form-item>
    </el-form>

    <div class="bottom-button-wrap">
      <div>
        <el-button @click="handleCancel">取消</el-button>
        <el-button @click="handleSave" type="primary">确定</el-button>
      </div>
    </div>
  </el-dialog>
</template>
<script>
import { batchApprove } from '@/api/user/enterprise';

export default {
  props: {
    enterpriseIds: {
      type: Array,
      default: () => [],
    },
  },
  dicts: [],
  components: {},
  data() {
    return {
      dialogVisible: false,
      orderNo: '',
      form: {
        reviewStatus: '',
        reviewRemark: '',
      },
      rules: {
        reviewStatus: [
          { required: true, message: '请选择审核类型', trigger: 'change' },
        ],
      },
      formLabelWidth: '120px',
    };
  },
  watch: {},
  computed: {},
  mounted() {},
  methods: {
    handleSave() {
      this.$refs.abnormalForm.validate(async (valid) => {
        if (valid) {
          const { reviewStatus, reviewRemark } = this.form;
          const params = {
            reviewStatus,
            reviewRemark,
            enterpriseIds: this.enterpriseIds.map((item) => item.enterpriseId),
          };

          const [err, res] = await batchApprove(params);
          if (err) return;
          this.$message.success('批量审核成功');
          this.handleCancel();
          this.$emit('loadData');
        }
      });
    },
    handleCancel() {
      this.dialogVisible = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.bottom-button-wrap {
  height: 56px;
  margin-top: 46px;
  width: 100%;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  // padding-right: 32px;
  box-sizing: border-box;
}

.remark-brief {
  font-weight: 400;
  font-size: 14px;
  line-height: 16px;
  margin-top: 8px;
  color: #818496;
}
</style>
