<template>
    <div>
        <el-dialog title="修改" :visible.sync="dialogVisible" width="1600px"  :destroy-on-close="true">
            <div class="title-wrap">
                <div class="title-icon"></div>
                <div class="title">{{ stationName }}</div>
            </div>

                <div class="card-head">
                    <div class="card-head-text">电费调整</div>
                </div>

                <div class="info-item-wrap">
                    <el-radio v-model="electricType" label="1">不配置</el-radio>
                    <div class="info-item-brief">
                        <div class="info-item-brief-icon"></div>
                        <div class="info-item-brief-text">
                            每月固定日期，通知管理员账户余额情况
                        </div>
                    </div>
                </div>

                <div class="info-item-wrap">
                    <el-radio v-model="electricType" label="2">电费一口价</el-radio>
                    <el-input-number
                        v-model="onePirce"
                        placeholder="最多保留两位小数"
                        :min="0"
                        :precision="2"
                        :step="0.01"
                        :controls="false"
                         style="flex: 1"
                    >
                    </el-input-number>
                    <div style="margin-left: 8px;">元</div>
                    <div class="info-item-brief">
                        <div class="info-item-brief-icon"></div>
                        <div class="info-item-brief-text">
                            服务费和场站保持一致，电费按配置电价计算
                        </div>
                    </div>
                </div>

                <div class="info-item-wrap">
                    <el-radio v-model="electricType" label="3">电费折扣率</el-radio>
                    <el-input-number
                        v-model="discount"
                        placeholder="如打8折，输入80"
                        :min="0"
                        :precision="2"
                        :step="0.01"
                        :controls="false"
                         style="flex: 1"
                    >
                    </el-input-number>
                    <div style="margin-left: 8px;">元</div>
                    <div class="info-item-brief">
                        <div class="info-item-brief-icon"></div>
                        <div class="info-item-brief-text">
                            服务费和场站保持一致，电费按折扣后计算
                        </div>
                    </div>
                </div>

                <div class="info-item-wrap">
                    <el-radio v-model="electricType" label="4">电费调价</el-radio>
                    <el-select 
                        v-model="electricChangeType" 
                        placeholder="请选择"
                        style="width: 72px; margin-right: 8px;"
                    >
                        <el-option
                            v-for="item in electricChangeTypeList"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        ></el-option>
                    </el-select>
                    <el-input-number
                        v-model="electricChange"
                        placeholder="最多保留两位小数"
                        :min="0"
                        :precision="2"
                        :step="0.01"
                        :controls="false"
                         style="flex: 1"
                    >
                    </el-input-number>
                    <div style="margin-left: 8px;">元</div>
                    <div class="info-item-brief">
                        <div class="info-item-brief-icon"></div>
                        <div class="info-item-brief-text">
                            服务费和场站保持一致，电费按在场站基础上增减，减至0元以下按0计算
                        </div>
                    </div>
                </div>

                <div class="card-head">
                    <div class="card-head-text">服务费调整</div>
                </div>

                <div class="info-item-wrap">
                    <el-radio v-model="serviceType" label="1">不配置</el-radio>
                    <div class="info-item-brief">
                        <div class="info-item-brief-icon"></div>
                        <div class="info-item-brief-text">
                            每月固定日期，通知管理员账户余额情况
                        </div>
                    </div>
                </div>

                <div class="info-item-wrap">
                    <el-radio v-model="serviceType" label="2">服务费一口价</el-radio>
                    <el-input-number
                        v-model="serviceOnePirce"
                        placeholder="最多保留两位小数"
                        :min="0"
                        :precision="2"
                        :step="0.01"
                        :controls="false"
                         style="flex: 1"
                    >
                    </el-input-number>
                    <div style="margin-left: 8px;">元</div>
                    <div class="info-item-brief">
                        <div class="info-item-brief-icon"></div>
                        <div class="info-item-brief-text">
                            电价和场站保持一致，服务费按配置电价计算
                        </div>
                    </div>
                </div>

                <div class="info-item-wrap">
                    <el-radio v-model="serviceType" label="3">服务费折扣率</el-radio>
                    <el-input-number
                        v-model="serviceDiscount"
                        placeholder="如打8折，输入80"
                        :min="0"
                        :precision="2"
                        :step="0.01"
                        :controls="false"
                         style="flex: 1"
                    >
                    </el-input-number>
                    <div style="margin-left: 8px;">元</div>
                    <div class="info-item-brief">
                        <div class="info-item-brief-icon"></div>
                        <div class="info-item-brief-text">
                            电价和场站保持一致，服务费按折扣后计算
                        </div>
                    </div>
                </div>

                <div class="info-item-wrap">
                    <el-radio v-model="serviceType" label="4">服务费调价</el-radio>
                    <el-select 
                        v-model="serviceChangeType" 
                        placeholder="请选择"
                        style="width: 72px; margin-right: 8px;"
                    >
                        <el-option
                            v-for="item in electricChangeTypeList"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        ></el-option>
                    </el-select>
                    <el-input-number
                        v-model="serviceChange"
                        placeholder="最多保留两位小数"
                        :min="0"
                        :precision="2"
                        :step="0.01"
                        :controls="false"
                         style="flex: 1"
                    >
                    </el-input-number>
                    <div style="margin-left: 8px;">元</div>
                    <div class="info-item-brief">
                        <div class="info-item-brief-icon"></div>
                        <div class="info-item-brief-text">
                            电价和场站保持一致，服务费按在场站基础上增减，减至0元以下按0计算
                        </div>
                    </div>
                </div>

                <div class="time-wrap">
                    <el-checkbox-group v-model="checkList" class="time">
                        <el-checkbox class="time-item"  label="beginTime">
                            <div class="time-item-label">
                                <div>
                                    启用时间
                                </div>
                                <el-date-picker
                                    style="flex: 1; margin-left: 8px;"
                                    type="datetime"
                                    v-model="beginTime"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                ></el-date-picker>
                            </div>
                            
                        </el-checkbox>
                        <el-checkbox class="time-item" label="endTime">
                            <div class="time-item-label">
                                <div>
                                    结束时间
                                </div>
                                <el-date-picker
                                    style="flex: 1; margin-left: 8px;"
                                    type="datetime"
                                    v-model="endTime"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                ></el-date-picker>
                            </div>
  
                               
                           
                        </el-checkbox>

                        <div class="time-brief">
                            <div class="time-brief-icon"></div>
                            <div class="time-brief-text">
                                不配置结束时间，则为长期有效；定价失效将会按正常场站价计费
                            </div>
                        </div>
                    </el-checkbox-group>

                   
                    
                </div>

               



        <div class="bottom-button-wrap">
        
            <div>
                <el-button @click="handleCancel">取消</el-button>
                <el-button @click="handleSave" type="primary">确定</el-button>

            </div>

        </div>

        
        </el-dialog>

    
    </div>
   
   
</template>
<script>  

import {
    enterpriseDiscountStationUpdate
} from '@/api/user/enterprise'

  export default {
    props: {
        editInfo: {
            type: Object,
            default: () => {},
        },
    },
    dicts: [
        'ls_order_except_level',
        'ls_order_except_type',
        'ls_charging_station_type',
        'ls_charging_operation_mode',
    ],
    components: {
    
    },
    data() {
        return {
            dialogVisible: false,
            enterpriseId: '',
            stationId: '',
            stationName: '',
            priceNo: '',

            electricType: '1',
            onePirce: undefined,
            discount: undefined,
            electricChangeType: '1',
            electricChangeTypeList: [
                {
                    label: '增加',
                    value: '1',
                },
                {
                    label: '减少',
                    value: '2',
                },
            ],
            electricChange: undefined,
 

            serviceType:  '1',
            serviceOnePirce: undefined,
            serviceDiscount: undefined,
            serviceChangeType: '1',

            serviceChange: undefined,

            checkList: [],
            beginTime: '',
            endTime: '',
        };
    },
    watch: {
        
    },
    computed: {
       
    },
    mounted() {
       
    },
    methods: {
       
        handleCancel() {
            this.dialogVisible = false;
            this.resetData();
        },

        resetData() {
            this.enterpriseId = '',
            this.stationId= '',
            this.stationName= '',
            this.priceNo= '',
            this.electricType= '1',
            this.onePirce= undefined,
            this.discount= undefined,
            this.electricChangeType = '1'
            this.electricChange= undefined,
            this.serviceType=  '1',
            this.serviceOnePirce= undefined,
            this.serviceDiscount= undefined,
            this.serviceChangeType= '1',

            this.serviceChange = undefined,

            this.checkList= [],
            this.beginTime= '',
            this.endTime= ''
        },

        getTablePage() {
            this.table.data = this.table.dataTotal.slice(
                (this.table.page.currentPage - 1) * this.table.page.pageSize,
                this.table.page.currentPage * this.table.page.pageSize
            );

            console.log(this.table.data,'333');
        },



        // 
        async handleSave() {

            if (this.electricType === '2' && !this.onePirce) {
                this.$message.warning('请输入电费一口价');
                return;
            }

            if (this.electricType === '3' && !this.discount) {
                this.$message.warning('请输入电费折扣率');
                return;
            }

            if (this.electricType === '4' && !this.electricChange) {
                this.$message.warning('请输入电费调价');
                return;
            }

            if (this.serviceType === '2' && !this.serviceOnePirce) {
                this.$message.warning('请输入服务费一口价');
                return;
            }

            if (this.serviceType === '3' && !this.serviceDiscount) {
                this.$message.warning('请输入服务费折扣率');
                return;
            }

            if (this.serviceType === '4' && !this.serviceChange) {
                this.$message.warning('请输入服务费调价');
                return;
            }

            if(!this.checkList.includes("beginTime")){
                this.$message.warning('请选择启动时间');
                return;
            }

            if(this.checkList.includes("beginTime") && !this.beginTime) {
                this.$message.warning('请输入启动时间');
                return;
            }

            if(this.checkList.includes("endTime") && !this.endTime) {
                this.$message.warning('请输入结束时间');
                return;
            }
           
            let elecPriceMode = 'NONE' 
            let elecPriceValue = ''
            if(this.electricType ==='2') {
                elecPriceMode = 'FIXED'
                elecPriceValue = this.onePirce
            } else if (this.electricType === '3') {
                elecPriceMode = 'RATE'
                elecPriceValue = this.discount
            } else if(  this.electricType === '4') {
                elecPriceMode = 'ADJUSTMENT'

                let unit = ''
                if(this.electricChangeType === '2') {
                    unit = '-'
                }
                elecPriceValue = unit + this.electricChange
            }


            let servicePriceMode = 'NONE'
            let servicePriceValue = ''
            if(this.serviceType === '2') {
                servicePriceMode = 'FIXED'
                servicePriceValue = this.serviceOnePirce
            } else if (this.serviceType === '3') {
                servicePriceMode = 'RATE'
                servicePriceValue = this.serviceDiscount
            } else  if( this.serviceType === '4') {
                servicePriceMode = 'ADJUSTMENT'
                let unit = ''
                if(this.serviceChangeType === '2') {
                    unit = '-'
                }
                servicePriceValue = unit + this.serviceChange
            }


            const startTime = this.checkList.includes("beginTime")?this.beginTime: ''
            const endTime = this.checkList.includes("endTime")?this.endTime: ''

            const params = {
                enterpriseId: this.enterpriseId,
                stationId: this.stationId,
                priceNo: this.priceNo,
                elecPriceMode,
                elecPriceValue,
                servicePriceMode,
                servicePriceValue,
                startTime,
                endTime,
            }

            const [err, res] = await enterpriseDiscountStationUpdate({
                ...params,
            });

            if (err) return 

            this.$message.success('场站配置修改成功');
            this.$emit('loadData');
            this.dialogVisible = false;
            this.resetData();

        },



    },
  };
  </script>

<style lang="scss" scoped>
.bottom-button-wrap {
    height: 86px;
    width: 100%;
    background-color: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    // padding-right: 32px;
    box-sizing: border-box;
}


.title-wrap {
    display: flex;
    align-items: center;
    height: 32px;
    margin-bottom: 24px;
    .title-icon {
        width: 32px;
        height: 32px;
        background-image: url('~@/assets/enterprise/station.png');
        margin-right: 12px;
        background-size: 32px 32px;
        background-repeat: no-repeat;
    }
    .title {
        
        font-weight: 500;
        font-size: 32px;
        
        color: #21252E;
    }
}



.card-head {
        // position: relative; 
        height: 56px;
        padding: 0 16px 0 0;
        display: flex;
        align-items: center;
        margin-top: -20px;
        .card-head-text {
        flex:1;
        width: 520px;
            height: 26px;
            background-image: url('~@/assets/images/bg-title.png');
            background-size: 520px 26px;
            background-repeat: no-repeat;
            font-size: 20px;
            font-style: normal;
            font-weight: 500;
            line-height: 16px;
            padding-left: 36px;
            color: #21252e;
        &::before {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            left: -3px; /* 调整这个值来改变边框的宽度 */
            width: 0;
            border-top: 3px solid transparent;
            border-bottom: 3px solid transparent;
            border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
        }
        }   

    }

    .choose-info-wrap {
            border-radius: 2px;
            height: 34px;
            padding: 0 10px;
            display: flex;
            align-items: center;
            background-color: #EBF3FF;
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 14px;
            margin-right: 16px;
            color: #217AFF;
            .choose-number {
                font-size: 16px;
                font-weight: 500;
                margin: 0 4px;
            }
        }

.info-item-wrap {
    display: flex;
    height: 36px;
    align-items: center;
    margin-bottom: 24px;
    font-size: 16px;
    .info-item-brief {
        width: 66%;
        display: flex;
        align-items: center;
        padding-left: 8px;
        box-sizing: border-box;
        .info-item-brief-icon {
            width: 20px;
            height: 20px;
            background-image: url('~@/assets/enterprise/help.png');
            margin-right: 4px;
            background-size: 20px 20px;
            background-repeat: no-repeat;
        }
        .info-item-brief-text {
            flex: 1;
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 16px;
            line-height: 16px;
            letter-spacing: 0px;
            vertical-align: middle;

        }
    }
}

.time-wrap {
    width: 100%;
    background-color: #F9F9FB;
    padding: 16px;
    box-sizing: border-box;
    .time {
        display: flex;
        align-items: center;
        .time-item {
            width: 33%;
            display: flex;
            align-items: center;
            .time-item-label {
                width: 100%;
                display: flex;
                align-items: center;
            }
        }
    }
    .time-brief {
        // width: 100%;
        display: flex;
        align-items: center;

        .time-brief-icon {
            width: 20px;
            height: 20px;
            background-image: url('~@/assets/enterprise/help.png');
            margin-right: 4px;
            background-size: 20px 20px;
            background-repeat: no-repeat;
        }
        .time-brief-text {
            flex: 1;
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 16px;
            line-height: 16px;
            letter-spacing: 0px;
            vertical-align: middle;

        }
    }
}
  </style>
  