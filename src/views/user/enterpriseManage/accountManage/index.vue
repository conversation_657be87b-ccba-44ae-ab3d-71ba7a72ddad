<template>
    <div class="container container-float " style="padding: 0 0 100px 0;">
        <div class="device-head">
            <img
                src="@/assets/order/abnormal-detail-icon.png"
                class="device-head-icon"
            />

            <div class="device-info-wrap">
                <div class="device-title-wrap">
                    <div class="device-title">账户编码：{{ accountCode }}</div>
                    <div class="device-status">{{ status }}</div>
                </div>
                <div class="device-detail-wrap">
                    <el-row>
                        <el-col :span="6">
                            <span class="label">可用余额：</span>
                            <span class="value">{{ availableBalance }}</span>
                        </el-col>
                        <el-col :span="6">
                            <span class="label">企业余额：</span>
                            <span class="value">{{ enterpriseBalance }}</span>
                        </el-col>
                        <el-col :span="6">
                            <span class="label">信用额度：</span>
                            <span class="value">{{ creditLimit }}</span>
                        </el-col>
                        <el-col :span="6">
                            <span class="label">安全额度：</span>
                            <span class="value">{{ safeLimit }}</span>
                        </el-col>
                    </el-row>
                </div>
            </div>

            
            <div class="button-wrap">
                <el-button
                    type="primary"
                    class="set-btn"
                    @click="handleSetAccount"
                >
                    <svg-icon iconClass="a-set"></svg-icon>
                   
                    账户设置
                </el-button>

                <el-button
                    type="primary"
                    class="set-btn"
                    @click="handleWithdrawal"
                >
                    <svg-icon iconClass="a-withdrawal"></svg-icon>
                    提现
                </el-button>

                <el-button
                    type="primary"
                    @click="handleRecharge"
                >
                    <svg-icon iconClass="a-recharge"></svg-icon>
                    充值
                </el-button>

                
            </div>

        </div>

        <div class="table-wrap">
            <div class="card-head">
                <div class="card-head-text">账户详情</div>
            </div>

            <div class="info-wrap">
                <div class="info-item"  v-for="item in infoList" :key="item.name">
                    <img :src="item.icon" class="info-icon">
                    <div class="info-right-wrap">
                        <div class="info-title">{{ item.name }}</div>
                        <div class="info-number">
                            {{ item.value }}
                            <span class="info-unit">{{ item.unit }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <BuseCrud
                ref="crud"
                :loading="loading"
                :filterOptions="filterOptions"
                :tablePage="tablePage"
                :tableColumn="tableColumn"
                :tableData="tableData"
                :pagerProps="pagerProps"
                :modalConfig="modalConfig"
                @loadData="loadData"
            >
                <template slot="defaultHeader">
                    <el-button style="margin-bottom: 16px;" class="export-btn" type="primary"  @click="handleExport">
                        <svg-icon iconClass="a-export-blue"></svg-icon>
                            导出
                    </el-button>
                </template>

            </BuseCrud>

            
        </div>

        <SetAccountModal ref="setAccountModal" @loadData="loadData" />
        <WithdrawalModal ref="withdrawalModal" @loadData="loadData" />
        <RechargeModal ref="rechargeModal" @loadData="loadData" />
    </div>
    
  </template>
  
  <script>

import icon1 from '@/assets/enterprise/icon-1.png';
import icon2 from '@/assets/enterprise/icon-2.png';
import icon3 from '@/assets/enterprise/icon-3.png';
import icon4 from '@/assets/enterprise/icon-4.png';
import icon5 from '@/assets/enterprise/icon-5.png';
import icon6 from '@/assets/enterprise/icon-6.png';
import SetAccountModal from './components/setAccountModal.vue'
import WithdrawalModal from './components/withdrawalModal.vue'
import RechargeModal from './components/rechargeModal.vue'

  
    export default {
    components: {
        SetAccountModal,
        WithdrawalModal,
        RechargeModal,
    },
    dicts: [],
    data() {
        return {
            accountCode: '********',
            status: '启用',
            availableBalance: '212312.11',
            enterpriseBalance: '12312.23',
            creditLimit: '1000',
            safeLimit:'500',

            infoList: [
                {
                    name: '消费次数',
                    value:'1401',
                    unit: '次',
                    icon: icon1,
                },
                {
                    name: '退款次数',
                    value:'2212',
                    unit: '次',
                    icon: icon2,
                },
                {
                    name: '充值次数',
                    value: '222',
                    unit: '次',
                    icon: icon3,
                },
                {
                    name: '提现次数',
                    value: '11',
                    unit: '次',
                    icon: icon4,
                },
                {
                    name: '转入次数',
                    value: '1',
                    unit: '次',
                    icon: icon5,
                },
                {
                    name: '转出次数',
                    value: '11',
                    unit: '次',
                    icon: icon6,
                },
            ],

            loading: false,
            tablePage: { total: 0, currentPage: 1, pageSize: 10 },
            tableData: [
            {
                serialNumber: '************',
                operationType: '消费',
                employeeOrEnterpriseAccount: '2342432',
                businessOrderNumber: '************',
                transactionTime: '2024/11/23 11:20:02',
                paymentAmount: '-89.00',
                accountBalance: '-189.00',
                operator: '客户李四',
                transactionStatus: '已交易'
            },
            {
                serialNumber: '************',
                operationType: '充值',
                employeeOrEnterpriseAccount: '2342432',
                businessOrderNumber: '************',
                transactionTime: '2024/11/23 11:20:02',
                paymentAmount: '123.00',
                accountBalance: '0.00',  // 原图未显示余额，按交易失败逻辑补全
                operator: '管理员张三',
                transactionStatus: '交易失败'
            },
            {
                serialNumber: '************',
                operationType: '提现',
                employeeOrEnterpriseAccount: '2342432',
                businessOrderNumber: '************',
                transactionTime: '2024/11/23 11:20:02',
                paymentAmount: '-89.00',
                accountBalance: '123.00',
                operator: '管理员张三',
                transactionStatus: '已交易'
            },
            {
                serialNumber: '************',
                operationType: '转出',
                employeeOrEnterpriseAccount: '************',
                businessOrderNumber: '************',
                transactionTime: '2024/11/23 11:20:02',
                paymentAmount: '-89.00',
                accountBalance: '123.00',
                operator: '管理员张三',
                transactionStatus: '已交易'
            },
            {
                serialNumber: '************',
                operationType: '退款',
                employeeOrEnterpriseAccount: '2342432',
                businessOrderNumber: '************',
                transactionTime: '2024/11/23 11:20:02',
                paymentAmount: '-89.00',
                accountBalance: '123.00',
                operator: '管理员张三',
                transactionStatus: '交易失败'  // 修正原图笔误
            },
            {
                serialNumber: '************',
                operationType: '转入',
                employeeOrEnterpriseAccount: '************',
                businessOrderNumber: '************',
                transactionTime: '2024/11/23 11:20:02',
                paymentAmount: '+123.00',  // 根据业务逻辑补正符号
                accountBalance: '123.00',
                operator: '管理员张三',
                transactionStatus: '已交易'
            },
            {
                serialNumber: '************',
                operationType: '转入',
                employeeOrEnterpriseAccount: '2342432',
                businessOrderNumber: '************',
                transactionTime: '2024/11/23 11:20:02',
                paymentAmount: '+200.00',  // 原图数据缺失，按常规补全
                accountBalance: '323.00',
                operator: '管理员张三',
                transactionStatus: '已交易'
            },
            {
                serialNumber: '************',
                operationType: '消费',
                employeeOrEnterpriseAccount: '2342432',
                businessOrderNumber: '************',
                transactionTime: '2024/11/23 11:20:02',
                paymentAmount: '-150.00',
                accountBalance: '173.00',
                operator: '客户李四',
                transactionStatus: '已交易'
            }
            ],
            tableColumn: [
                {
                    type: 'seq',
                    title: '序号',
                    width: 60,
                },
                {
                    field: 'serialNumber',
                    title: '流水号',
                    minWidth: 200,
                },
                {
                    field: 'operationType',
                    title: '操作类型',
                    minWidth: 200,
                },
                { 
                    field: 'employeeOrEnterpriseAccount', 
                    title: '员工/企业机构账户ID', 
                    minWidth: 200 
                },
                { 
                    field: 'businessOrderNumber', 
                    title: '业务订单号', 
                    minWidth: 200 
                },
                { 
                    field: 'transactionTime', 
                    title: '交易时间', 
                    minWidth: 200 
                },
                { 
                    field: 'paymentAmount', 
                    title: '支付金额', 
                    minWidth: 200 
                },
                { 
                    field: 'accountBalance', 
                    title: '账户余额',
                    minWidth: 200 
                },
                { 
                    field: 'operator', 
                    title: '操作员', 
                    minWidth: 200 
                },
                {
                    field: 'transactionStatus', 
                    title: '交易状态', 
                    minWidth: 200,
                    fixed: 'right',
                }
                
            ],
            pagerProps: {
                layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
            },
            params: {
                serialNumber: '',
                orderNo: '',
                operator: '',
                operationType: '',
                tradeTime: [],
                status:'',
            },
        };
    },

    computed: {
        filterOptions() {
        return {
            config: [
            {
                field: 'serialNumber',
                title: '流水号',
                element: 'el-input',
                placeholder: '请输入',
            },
            {
                field: 'orderNo',
                title: '订单编号',
                element: 'el-input',
                placeholder: '请输入',
            },
            {
                field: 'operator',
                title: '操作员',
                element: 'el-select',
                props: {
                    options: [],
                },
            },
            {
                field: 'operationType',
                title: '操作类型',
                element: 'el-select',
                props: {
                placeholder: '请选择',
                    options: []
                },
            },
            {
                field: 'tradeTime',
                title: '交易时间',
                element: 'el-date-picker',
                props: {
                type: 'daterange',
                startPlaceholder: '开始日期',
                endPlaceholder: '结束日期',
                valueFormat: 'yyyy-MM-dd',
                },
            },
            {
                field: 'status',
                title: '交易状态',
                element: 'el-select',
                props: {
                placeholder: '请选择',
                    options: []
                },
            },
            ],
            params: this.params,
        };
        },
        modalConfig() {
        return {
            addBtn: false,
            viewBtn: false,
            menu: false,
            editBtn: false,
            delBtn: false,
        };
        },
    },
    mounted() {
        this.loadData()
    },
    methods: {
        async loadData() {},

        // 账户设置
        handleSetAccount() {
            this.$refs.setAccountModal.dialogVisible = true;
        },

        // 账户提现
        handleWithdrawal() {
            this.$refs.withdrawalModal.dialogVisible = true;
        },

        // 账户提现
        handleRecharge() {
            this.$refs.rechargeModal.dialogVisible = true;
        }
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .container-full {
    background-color: rgba(244, 246, 249, 1);
    padding-bottom: 16px;
    min-height: calc(100% - 80px);
    overflow-y: auto;
  }
   

  .device-head {
  background-color: #fff;

  display: flex;
  height: 112px;
  display: flex;
  align-items: center;
  padding: 0 24px;
  box-sizing: border-box;
  .device-head-icon {
    width: 48px;
    height: 48px;
    margin-right: 24px;
  }
  .device-info-wrap {
    flex: 1;
    .device-title-wrap {
      height: 32px;
      display: flex;
      align-items: center;
      .device-title {
        font-weight: 500;
        font-size: 24px;
        color: #12151a;
      }
      .device-status {
        // width: 50px;
        padding: 0 10px;
        height: 24px;
        border-radius: 10px 0 10px 0;
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;
        text-align: center;
        color: #fff;
        background: linear-gradient(321.01deg, #217AFF 8.79%,  #21C8FF 100.27%);
        margin-left: 12px;
      }
    }
    .device-detail-wrap {
      height: 16px;
      margin-top: 16px;
      font-size: 16px;
      font-weight: 400;
      color: #292b33;

      .label {
        font-weight: 400;
        font-size: 16px;
        color: #505363;
      }
      .value {
        font-weight: 400;
        font-size: 20px;
        color:  #292B33;
      }
    }
  }
  
  .button-wrap {
    display: flex;
    align-items: center;
    .set-btn {
        background-color: #FFFFFF;
        color: #292B33;
        border-color: #DFE1E5;
    }
    .withdrawal-btn {

    }
  }
}

.table-wrap {
    background-color: #FFFFFF;
    border-radius: 5px;
    margin: 16px;
    .card-head {
        // position: relative; 
        height: 56px;
        padding: 0 16px;
        display: flex;
        align-items: center;
        // margin-top: -20px;
        .card-head-text {
        flex:1;
        width: 520px;
            height: 26px;
            background-image: url('~@/assets/images/bg-title.png');
            background-size: 520px 26px;
            background-repeat: no-repeat;
            font-size: 20px;
            font-style: normal;
            font-weight: 500;
            line-height: 16px;
            padding-left: 36px;
            color: #21252e;
        &::before {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            left: -3px; /* 调整这个值来改变边框的宽度 */
            width: 0;
            border-top: 3px solid transparent;
            border-bottom: 3px solid transparent;
            border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
        }
        }   

    }

    .info-wrap {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;

        .info-item {
            // background-color: #FAFBFC;
            flex: 1 1 0;
            // min-width: 180px;
            
            border-radius: 5px;
            padding: 8px 24px;
            box-sizing: border-box;
            // margin-right: 16px;
            display: flex;
            .info-icon {
                width: 42px;
                height: 42px;
            }
            .info-right-wrap {
                flex:1;
                margin-left: 24px;
                .info-title {
                    font-weight: 400;
                    font-size: 14px;
                    line-height: 14px;
                    margin-bottom: 8px;
                }
                .info-number {
                    font-size: 20px;
                    font-weight: 500;
                    .info-unit {
                        font-size: 14px;
                        font-weight: 400;
                    }
                }
            }
        }
        .info-item:last-child {
            margin-right: 0;
        }
    }
}


::v-deep .bd3001-auto-filters-container {
    margin-bottom: 0px !important;
}

::v-deep .bd3001-auto-filters-container {
    box-shadow: none !important;
}

::v-deep .bd3001-content {
    padding-top: 0px !important;
}
  
.export-btn {
    background-color: #FFFFFF;
    color: #217AFF;
    border-color: #217AFF;
}
 
  </style>
  