<template>
  <div class="container">
    <div class="table-wrap">
      <BuseCrud
        ref="crud"
        :loading="loading"
        :filterOptions="filterOptions"
        :tablePage="tablePage"
        :tableColumn="tableColumn"
        :tableData="tableData"
        :pagerProps="pagerProps"
        :modalConfig="modalConfig"
        class="buse-wrap-station"
        @loadData="loadData"
      >
        <template slot="defaultHeader">
          <div class="top-button-wrap">
            <div class="tabs">
              <div
                v-for="(item, index) in activeTab"
                :key="index"
                class="tab-item"
                :class="{ active: selectedItem === item.value }"
                @click="selectTab(item.value)"
              >
                {{ item.label }}
              </div>
            </div>
            <el-date-picker
              v-if="selectedItem === '1'"
              v-model="selectDate"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="selectDateChange"
              value-format="yyyy-MM-dd"
            ></el-date-picker>

            <el-date-picker
              v-if="selectedItem === '2'"
              v-model="selectDate"
              type="monthrange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="selectDateChange"
              value-format="yyyy-MM"
            ></el-date-picker>
          </div>
          <div class="card-head-after"></div>
          <div class="top-button-wrap">
            <el-button
              type="primary"
              icon="el-icon-download"
              plain
              @click="handleExport()"
            >
              导出
            </el-button>
            <div>
              单位：桩：根；电量：千瓦时；金额：元；均价：元/千瓦时；功率：千瓦时
            </div>
          </div>
        </template>
        <template #theoryMaxChargeHeader>
          <el-popover
            placement="top-start"
            width="200"
            trigger="hover"
            content="理论最大充电量=功率*投运时长(h)"
          >
            <span slot="reference">理论最大充电量</span>
          </el-popover>
        </template>
        <template slot="gunChargingCapacity">
          <el-popover
            placement="top-start"
            width="200"
            trigger="hover"
            content="单枪充电量=充电量/枪数"
          >
            <span slot="reference">单枪充电量</span>
          </el-popover>
        </template>
        <template slot="orderChargingAmount">
          <el-popover
            placement="top-start"
            width="200"
            trigger="hover"
            content="订单充电金额=充电电费+充电服务费"
          >
            <span slot="reference">订单充电金额</span>
          </el-popover>
        </template>
        <template slot="actualReceivedAmount">
          <el-popover
            placement="top-start"
            width="200"
            trigger="hover"
            content="实收金额=实收电费+实收服务费"
          >
            <span slot="reference">实收金额</span>
          </el-popover>
        </template>
        <template slot="chargingAveragePrice">
          <el-popover
            placement="top-start"
            width="200"
            trigger="hover"
            content="充电均价=实收电费/充电电量"
          >
            <span slot="reference">充电均价</span>
          </el-popover>
        </template>
        <template slot="averageServiceFee">
          <el-popover
            placement="top-start"
            width="200"
            trigger="hover"
            content="服务费均价=实收服务费/充电电量"
          >
            <span slot="reference">服务费均价</span>
          </el-popover>
        </template>
        <template slot="abnormalProportion">
          <el-popover
            placement="top-start"
            width="200"
            trigger="hover"
            content="异常订单电量占比=异常订单电量/充电电量"
          >
            <span slot="reference">异常订单电量占比</span>
          </el-popover>
        </template>
        <template slot="durationOfOperation">
          <el-popover
            placement="top-start"
            width="200"
            trigger="hover"
            content="在运时长=正常时长+故障时长+离线时长+停运时长"
          >
            <span slot="reference">在运时长</span>
          </el-popover>
        </template>
        <template slot="offlineRate">
          <el-popover
            placement="top-start"
            width="200"
            trigger="hover"
            content="离线率=离线时长/在运时长"
          >
            <span slot="reference">离线率</span>
          </el-popover>
        </template>
        <template slot="faultRate">
          <el-popover
            placement="top-start"
            width="200"
            trigger="hover"
            content="故障率=故障时长/在运时长"
          >
            <span slot="reference">故障率</span>
          </el-popover>
        </template>
        <template slot="stopRate">
          <el-popover
            placement="top-start"
            width="200"
            trigger="hover"
            content="停运率=停运时长/在运时长"
          >
            <span slot="reference">停运率</span>
          </el-popover>
        </template>
        <template slot="availableRate">
          <el-popover
            placement="top-start"
            width="200"
            trigger="hover"
            content="可用率=正常时长/在运时长"
          >
            <span slot="reference">可用率</span>
          </el-popover>
        </template>
        <template slot="oneChargingSuccessRate">
          <el-popover
            placement="top-start"
            width="200"
            trigger="hover"
            content="一次充电成功率=1-异常结束订单/总订单"
          >
            <span slot="reference">一次充电成功率</span>
          </el-popover>
        </template>
        <template slot="removeCarEndReason">
          <el-popover
            placement="top-start"
            width="200"
            trigger="hover"
            content="一次充电成功率(剔除车端原因)=1-(异常结束订单-车端异常订单)/总订单"
          >
            <span slot="reference">一次充电成功率（剔除车端原因）</span>
          </el-popover>
        </template>
        <template #powerUsageRate>
          <el-popover
            placement="top-start"
            width="200"
            trigger="hover"
            content="功率利用率=实际充电电量/理论充电电量"
          >
            <span slot="reference">功率利用率</span>
          </el-popover>
        </template>
        <template #timeUsageRate>
          <el-popover
            placement="top-start"
            width="200"
            trigger="hover"
            content="时间利用率=实际充电时长/投运时长"
          >
            <span slot="reference">时间利用率</span>
          </el-popover>
        </template>
        <template slot="operate" slot-scope="{ row }">
          <div class="menu-box">
            <el-button type="primary" plain @click="handleDetail(row)">
              未纳入统计的订单明细
            </el-button>
          </div>
        </template>
      </BuseCrud>
    </div>
  </div>
</template>

<script>
import { getPileList } from '@/api/intelligentReport/index';
import { getAreaList } from '@/api/electricPricePeriod/index';
export default {
  data() {
    return {
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [],
      tableColumn: [
        {
          field: 'statisticCycle',
          title: '统计周期',
          minWidth: 120,
          fixed: 'left',
        },
        {
          field: 'pileName',
          title: '充电桩名称',
          minWidth: 120,
        },
        {
          field: 'stationName',
          title: '所属充电站',
          minWidth: 120,
        },
        {
          field: 'stationCode',
          title: '充电站编号',
          minWidth: 120,
        },
        {
          field: 'siteType',
          title: '站点类型',
          minWidth: 120,
        },
        {
          field: 'assetType',
          title: '资产属性',
          minWidth: 120,
        },
        {
          field: 'stationSource',
          title: '站点来源',
          minWidth: 120,
        },
        {
          field: 'mainPropertyName',
          title: '一级产权单位',
          minWidth: 120,
        },
        {
          field: 'subPropertyName',
          title: '二级产权单位',
          minWidth: 120,
        },
        {
          field: 'cityName',
          title: '地市',
          minWidth: 120,
        },
        {
          field: 'districtName',
          title: '区县',
          minWidth: 120,
        },
        {
          field: 'thirdPartnarName',
          title: '运营单位',
          minWidth: 120,
        },
        {
          field: 'operOrgName',
          title: '运维单位',
          minWidth: 120,
        },
        {
          field: 'elecProvider',
          title: '供电机构',
          minWidth: 120,
        },
        {
          field: 'equipManufacturer',
          title: '设备厂家',
          minWidth: 120,
        },
        {
          field: 'marketingNo',
          title: '营销户号',
          minWidth: 120,
        },
        {
          title: '基础信息',
          minWidth: 600,
          align: 'center',
          children: [
            { field: 'gunNum', title: '枪数', minWidth: 120 },
            { field: 'meterNo', title: '营销电表号', minWidth: 120 },
            { field: 'power', title: '功率', minWidth: 120 },
            {
              field: 'chargePqMax',
              title: '理论最大充电量',
              minWidth: 120,
              slots: {
                header: 'theoryMaxChargeHeader',
              },
            },
            // { field: 'chargePq', title: '充电量', minWidth: 120 },
          ],
        },
        {
          title: '订单信息',
          minWidth: 720,
          align: 'center',
          children: [
            { field: 'chargePq', title: '充电量', minWidth: 120 },
            { field: 'cuspElec', title: '尖电量', minWidth: 120 },
            { field: 'peakElec', title: '峰电量', minWidth: 120 },
            { field: 'flatElec', title: '平电量', minWidth: 120 },
            { field: 'valleyElec', title: '谷电量', minWidth: 120 },
            {
              field: 'gunChargePq',
              title: '单枪充电量',
              minWidth: 120,
              slots: {
                header: 'gunChargingCapacity',
              },
            },
          ],
        },
        {
          title: '订单电费',
          minWidth: 600,
          align: 'center',
          children: [
            { field: 'elecAmt', title: '充电电费', minWidth: 120 },
            { field: 'cuspElecAmt', title: '尖电费', minWidth: 120 },
            { field: 'peakElecAmt', title: '峰电费', minWidth: 120 },
            { field: 'flatElecAmt', title: '平电费', minWidth: 150 },
            { field: 'valleyElecAmt', title: '谷电费', minWidth: 120 },
          ],
        },
        {
          title: '订单服务费',
          minWidth: 600,
          align: 'center',
          children: [
            { field: 'serviceAmt', title: '充电服务费', minWidth: 120 },
            { field: 'cuspServiceAmt', title: '尖服电费', minWidth: 120 },
            { field: 'peakServiceAmt', title: '峰服务费', minWidth: 120 },
            { field: 'flatServiceAmt', title: '平服务费', minWidth: 150 },
            { field: 'valleyServiceAmt', title: '谷服务费', minWidth: 120 },
          ],
        },
        {
          field: 'chargeAmt',
          title: '订单充电金额',
          minWidth: 120,
          slots: {
            header: 'orderChargingAmount',
          },
        },
        {
          title: '订单优惠',
          minWidth: 480,
          align: 'center',
          children: [
            {
              field: 'couponDiscounted',
              title: '优惠券优惠金额',
              minWidth: 120,
            },
            {
              field: 'activityDiscounted',
              title: '立减优惠金额',
              minWidth: 120,
            },
            { field: 'otherDiscounted', title: '其他优惠金额', minWidth: 120 },
            { field: 'totalDiscounted', title: '优惠总金额', minWidth: 120 },
          ],
        },
        {
          title: '订单实收',
          minWidth: 360,
          align: 'center',
          children: [
            { field: 'elecReciept', title: '实收电费', minWidth: 120 },
            { field: 'serviceReciept', title: '实收服务费', minWidth: 120 },
            {
              field: 'realReciept',
              title: '实收金额',
              minWidth: 120,
              slots: {
                header: 'actualReceivedAmount',
              },
            },
          ],
        },
        {
          field: 'elecPrice',
          title: '充电均价',
          minWidth: 120,
          slots: {
            header: 'chargingAveragePrice',
          },
        },
        {
          field: 'servicePrice',
          title: '服务费均价',
          minWidth: 120,
          slots: {
            header: 'averageServiceFee',
          },
        },
        {
          title: '订单数',
          minWidth: 360,
          align: 'center',
          children: [
            { field: 'orderCount', title: '总订单数', minWidth: 120 },
            { field: 'exceptOrderCount', title: '异常订单数', minWidth: 120 },
            {
              field: 'exceptOrderPer',
              title: '异常订单电量占比',
              minWidth: 120,
              slots: {
                header: 'abnormalProportion',
              },
            },
          ],
        },
        {
          field: 'chargeTimes',
          title: '充电时长',
          minWidth: 120,
        },
        {
          title: '可用率统计',
          minWidth: 1080,
          align: 'center',
          children: [
            {
              field: 'opTimes',
              title: '在运时长',
              minWidth: 120,
              slots: {
                header: 'durationOfOperation',
              },
            },
            { field: 'normalTimes', title: '正常时长', minWidth: 120 },
            { field: 'faultTimes', title: '故障时长', minWidth: 120 },
            { field: 'offlineTimes', title: '离线时长', minWidth: 120 },
            { field: 'stopTimes', title: '停运时长', minWidth: 120 },
            { field: 'warnTimes', title: '告警时长', minWidth: 120 },
            {
              field: 'offlinePer',
              title: '离线率',
              minWidth: 120,
              slots: {
                header: 'offlineRate',
              },
            },
            {
              field: 'faultPer',
              title: '故障率',
              minWidth: 120,
              slots: {
                header: 'faultRate',
              },
            },
            {
              field: 'stopPer',
              title: '停运率',
              minWidth: 120,
              slots: {
                header: 'stopRate',
              },
            },
            {
              field: 'usablePer',
              title: '可用率',
              minWidth: 120,
              slots: {
                header: 'availableRate',
              },
            },
          ],
        },
        {
          title: '一次性充电成功率统计',
          minWidth: 720,
          align: 'center',
          children: [
            { field: 'exceptEndCount', title: '异常结束订单数', minWidth: 120 },
            {
              field: 'manExceptCount',
              title: '人为原因异常订单数',
              minWidth: 120,
            },
            {
              field: 'carExceptCount',
              title: '车端原因异常订单数',
              minWidth: 120,
            },
            {
              field: 'pileExceptCount',
              title: '桩端原因异常订单数',
              minWidth: 120,
            },
            {
              field: 'onceSuccPer',
              title: '一次充电成功率',
              minWidth: 120,
              slots: {
                header: 'oneChargingSuccessRate',
              },
            },
            {
              field: 'onceSuccExclueCarPer',
              title: '一次充电成功率（剔除车端原因）',
              minWidth: 120,
              slots: {
                header: 'removeCarEndReason',
              },
            },
          ],
        },
        {
          title: '利用率统计',
          minWidth: 240,
          align: 'center',
          children: [
            {
              field: 'powerUsagePer',
              title: '功率利用率',
              minWidth: 120,
              slots: {
                header: 'powerUsageRate',
              },
            },
            {
              field: 'timeUsagePer',
              title: '时间利用率',
              minWidth: 120,
              slots: {
                header: 'timeUsageRate',
              },
            },
          ],
        },
        {
          title: '操作',
          slots: { default: 'operate' },
          width: 200,
          align: 'center',
          fixed: 'right',
        },
      ],
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      areaList: [],
      params: {
        areaCode: '',
        assetType: '',
        siteType: '',
        stationSource: '',
        mainPropertyCode: '',
        thirdPartnarCode: '',
        operOrgCode: '',
        elecProvider: '',
        powerSupplyAgency: '',
        stationName: '',
        stationCode: '',
        pileName: '',
      },
      selectedItem: '1',
      activeTab: [
        {
          label: '日表',
          value: '1',
        },
        {
          label: '周表',
          value: '4',
        },
        {
          label: '月表',
          value: '2',
        },
      ],
      selectDate: [],
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'areaCode',
            title: '地市',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.areaList,
            },
          },
          {
            field: 'assetType',
            title: '资产属性',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
          {
            field: 'siteType',
            title: '站点类型',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
          {
            field: 'stationSource',
            title: '站点来源',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
          {
            field: 'mainPropertyCode',
            title: '产权单位',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
          {
            field: 'thirdPartnarCode',
            title: '运营单位',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
          {
            field: 'operOrgCode',
            title: '运维单位',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
          {
            field: 'powerSupplyAgency',
            title: '设备厂家',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
          {
            field: 'elecProvider',
            title: '供电机构',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
          {
            field: 'stationName',
            title: '充电站',
            element: 'el-input',
          },
          {
            field: 'stationCode',
            title: '充电站编号',
            element: 'el-input',
          },
          {
            field: 'pileName',
            title: '充电桩名称',
            element: 'el-input',
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  mounted() {
    this.getAreaList();
    this.loadData();
  },
  methods: {
    // 获取适用地市
    async getAreaList() {
      const [err, res] = await getAreaList({
        areaLevel: '03',
        huNanOnly: true,
      });
      if (err) return;
      const { data } = res;
      const list = [];
      data.forEach((item) => {
        list.push({
          label: item.areaName,
          value: item.areaCode,
        });
      });
      this.areaList = list;
    },
    async loadData() {
      const {
        areaCode,
        assetType,
        siteType,
        stationSource,
        mainPropertyCode,
        thirdPartnarCode,
        operOrgCode,
        elecProvider,
        powerSupplyAgency,
        stationName,
        stationCode,
        pileName,
      } = this.params;

      let timeStart = '';
      let timeEnd = '';
      if (this.selectDate && this.selectDate.length > 0) {
        timeStart = this.selectDate[0];
        timeEnd = this.selectDate[1];
      }

      const params = {
        areaCode,
        assetType,
        siteType,
        stationSource,
        mainPropertyCode,
        thirdPartnarCode,
        operOrgCode,
        elecProvider,
        powerSupplyAgency,
        stationName,
        stationCode,
        pileName,
        timeStart,
        timeEnd,
        timeType: this.selectedItem,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };
      this.loading = true;

      const [err, res] = await getPileList(params);

      this.loading = false;

      if (err) return;
      const { data, total } = res;
      this.tableData = data;
      this.tablePage.total = total;
    },
    // 切换tab
    selectTab(value) {
      this.selectedItem = value;

      this.selectDate = [];
      this.tableData = [];
      this.tablePage.currentPage = 1;
      this.tablePage.total = 0;

      this.loadData();
    },
    // 时间选择
    selectDateChange() {
      this.tableData = [];
      this.tablePage.currentPage = 1;
      this.tablePage.total = 0;

      this.loadData();
    },
    // 未纳入统计的订单明细
    handleDetail(val) {
      console.log(val, 'val');
      this.$router.push({
        path: '/v2g-charging/intelligentReport/basicBusinessReport/abnormalOrder',
        query: val,
      });
    },
    // 导出
    handleExport() {
      const {
        areaCode,
        assetType,
        siteType,
        stationSource,
        mainPropertyCode,
        thirdPartnarCode,
        operOrgCode,
        elecProvider,
        powerSupplyAgency,
        stationName,
        stationCode,
        pileName,
      } = this.params;

      let timeStart = '';
      let timeEnd = '';
      if (this.selectDate && this.selectDate.length > 0) {
        timeStart = this.selectDate[0];
        timeEnd = this.selectDate[1];
      }

      const params = {
        areaCode,
        assetType,
        siteType,
        stationSource,
        mainPropertyCode,
        thirdPartnarCode,
        operOrgCode,
        elecProvider,
        powerSupplyAgency,
        stationName,
        stationCode,
        pileName,
        timeStart,
        timeEnd,
        timeType: this.selectedItem,
      };

      this.download(
        '/vehicle-charging-admin/pileBiz/report/export',
        {
          ...params,
        },
        `充电桩基础业务表.xlsx`
      );
    },
  },
};
</script>

<style lang="scss" scoped>
.table-wrap {
  // background: #fff;
  // padding: 20px;
  // border-radius: 4px;

  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }
  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }
  .top-button-wrap {
    display: flex;
    margin: 16px 0;
    justify-content: space-between;
    .tabs {
      display: flex;
      gap: 5rpx;
      .tab-item {
        cursor: pointer;
        font-size: 18px;
        color: #606266; /* 默认颜色 */
        transition: color 0.3s; /* 平滑过渡 */
        padding: 0 16px;
        margin: 0 6px;
      }

      .tab-item.active {
        color: #409eff; /* 选中时的颜色 */
        border-bottom: 2px solid #409eff; /* 选中时的下划线 */
        padding-bottom: 4px; /* 调整下划线的位置 */
      }
    }
  }
}
</style>
