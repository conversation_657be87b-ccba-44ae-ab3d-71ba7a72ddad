<template>
  <div class="container">
    <div class="table-wrap">
      <BuseCrud
        ref="crud"
        :loading="loading"
        :filterOptions="filterOptions"
        :tablePage="tablePage"
        :tableColumn="tableColumn"
        :tableData="tableData"
        :pagerProps="pagerProps"
        :modalConfig="modalConfig"
        @loadData="loadData"
      >
        <template slot="defaultHeader">
          <div class="card-head">
            <div class="card-head-text">充电站基础业务报表列表</div>
            <div class="top-button-wrap">
              <el-button
                type="primary"
                icon="el-icon-upload2"
                @click="handleInput"
              >
                导入
              </el-button>
              <el-button
                type="primary"
                icon="el-icon-plus"
                @click="handleExport"
              >
                导出
              </el-button>
            </div>
          </div>
          <div class="card-head-after"></div>
          <div class="top-button-wrap">
            <div class="tabs">
              <div
                v-for="(item, index) in activeTab"
                :key="index"
                class="tab-item"
                :class="{ active: selectedItem === item.value }"
                @click="selectTab(item.value)"
              >
                {{ item.label }}
              </div>
            </div>
            <el-date-picker
              v-if="selectedItem === '1'"
              v-model="selectDate"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="selectDateChange"
              value-format="yyyy-MM-dd"
            ></el-date-picker>
            <el-date-picker
              v-if="selectedItem === '2'"
              v-model="selectDate"
              type="monthrange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="selectDateChange"
              value-format="yyyy-MM"
            ></el-date-picker>
          </div>

          
        </template>
        <template #theoryMaxChargeHeader>
          <el-popover
            placement="top-start"
            width="200"
            trigger="hover"
            content="理论最大充电量=充电站功率*投运时长(h)"
          >
            <span slot="reference">理论最大充电量</span>
          </el-popover>
        </template>
        <template #pileChargingCapacity>
          <el-popover
            placement="top-start"
            width="200"
            trigger="hover"
            content="单桩充电量=充电量/桩数"
          >
            <span slot="reference">单桩充电量</span>
          </el-popover>
        </template>
        <template slot="gunChargingCapacity">
          <el-popover
            placement="top-start"
            width="200"
            trigger="hover"
            content="单枪充电量=充电量/枪数"
          >
            <span slot="reference">单枪充电量</span>
          </el-popover>
        </template>
        <template #lossRate>
          <el-popover
            placement="top-start"
            width="200"
            trigger="hover"
            content="电量损耗率=(供电-充电)/供电"
          >
            <span slot="reference">电量损耗率</span>
          </el-popover>
        </template>
        <template #orderChargingAmount>
          <el-popover
            placement="top-start"
            width="200"
            trigger="hover"
            content="订单充电金额=充电电费+充电服务费"
          >
            <span slot="reference">订单充电金额</span>
          </el-popover>
        </template>
        <template #actualReceivedAmount>
          <el-popover
            placement="top-start"
            width="200"
            trigger="hover"
            content="实收金额=实收电费+实收服务费"
          >
            <span slot="reference">实收金额</span>
          </el-popover>
        </template>
        <template slot="chargingAveragePrice">
          <el-popover
            placement="top-start"
            width="200"
            trigger="hover"
            content="充电均价=实收电费/充电电量"
          >
            <span slot="reference">充电均价</span>
          </el-popover>
        </template>
        <template slot="averageServiceFee">
          <el-popover
            placement="top-start"
            width="200"
            trigger="hover"
            content="服务费均价=实收服务费/充电电量"
          >
            <span slot="reference">服务费均价</span>
          </el-popover>
        </template>
        <template #averageSupplyElectricity>
          <el-popover
            placement="top-start"
            width="200"
            trigger="hover"
            content="供电均价=供电电费/供电电量"
          >
            <span slot="reference">供电均价</span>
          </el-popover>
        </template>
        <template #billLossRate>
          <el-popover
            placement="top-start"
            width="200"
            trigger="hover"
            content="电费损耗率=(供电电费-实收电费)/供电电费"
          >
            <span slot="reference">电费损耗率</span>
          </el-popover>
        </template>
        <template slot="abnormalProportion">
          <el-popover
            placement="top-start"
            width="200"
            trigger="hover"
            content="异常订单电量占比=异常订单电量/充电电量"
          >
            <span slot="reference">异常订单电量占比</span>
          </el-popover>
        </template>
        <template #clearElectricityAveragePrice>
          <el-popover
            placement="top-start"
            width="200"
            trigger="hover"
            content="清分电费均价=清分电费/清分电量"
          >
            <span slot="reference">清分电费均价</span>
          </el-popover>
        </template>
        <template #clearServiceFeeAveragePrice>
          <el-popover
            placement="top-start"
            width="200"
            trigger="hover"
            content="清分服务费均价=清分服务费/清分电量"
          >
            <span slot="reference">清分服务费均价</span>
          </el-popover>
        </template>
        <template #orderClearingRate>
          <el-popover
            placement="top-start"
            width="200"
            trigger="hover"
            content="订单清分率=1-未清分电量/订单电量"
          >
            <span slot="reference">订单清分率</span>
          </el-popover>
        </template>
        <template slot="durationOfOperation">
          <el-popover
            placement="top-start"
            width="200"
            trigger="hover"
            content="在运时长=正常时长+故障时长+离线时长+停运时长"
          >
            <span slot="reference">在运时长</span>
          </el-popover>
        </template>
        <template slot="offlineRate">
          <el-popover
            placement="top-start"
            width="200"
            trigger="hover"
            content="离线率=离线时长/在运时长"
          >
            <span slot="reference">离线率</span>
          </el-popover>
        </template>
        <template slot="faultRate">
          <el-popover
            placement="top-start"
            width="200"
            trigger="hover"
            content="故障率=故障时长/在运时长"
          >
            <span slot="reference">故障率</span>
          </el-popover>
        </template>
        <template slot="stopRate">
          <el-popover
            placement="top-start"
            width="200"
            trigger="hover"
            content="停运率=停运时长/在运时长"
          >
            <span slot="reference">停运率</span>
          </el-popover>
        </template>
        <template slot="availableRate">
          <el-popover
            placement="top-start"
            width="200"
            trigger="hover"
            content="可用率=正常时长/在运时长"
          >
            <span slot="reference">可用率</span>
          </el-popover>
        </template>
        <template slot="oneChargingSuccessRate">
          <el-popover
            placement="top-start"
            width="200"
            trigger="hover"
            content="一次充电成功率=1-异常结束订单/总订单"
          >
            <span slot="reference">一次充电成功率</span>
          </el-popover>
        </template>
        <template slot="removeCarEndReason">
          <el-popover
            placement="top-start"
            width="200"
            trigger="hover"
            content="一次充电成功率(剔除车端原因)=1-(异常结束订单-车端异常订单)/总订单"
          >
            <span slot="reference">一次充电成功率（剔除车端原因）</span>
          </el-popover>
        </template>
        <template #powerUsageRate>
          <el-popover
            placement="top-start"
            width="200"
            trigger="hover"
            content="功率利用率=实际充电电量/理论充电电量"
          >
            <span slot="reference">功率利用率</span>
          </el-popover>
        </template>
        <template #timeUsageRate>
          <el-popover
            placement="top-start"
            width="200"
            trigger="hover"
            content="时间利用率=实际充电时长/投运时长"
          >
            <span slot="reference">时间利用率</span>
          </el-popover>
        </template>
        <template #zeroLowPileRatio>
          <el-popover
            placement="top-start"
            width="200"
            trigger="hover"
            content="零低桩占比=零低桩/总投运充电桩"
          >
            <span slot="reference">零低桩占比</span>
          </el-popover>
        </template>
        <template #inefficientPileRatio>
          <el-popover
            placement="top-start"
            width="200"
            trigger="hover"
            content="低效桩占比=低效桩/总投运充电桩"
          >
            <span slot="reference">低效桩占比</span>
          </el-popover>
        </template>
        <template slot="operate" slot-scope="{ row }">
          <div class="menu-box">
            <el-button type="primary" plain @click="handleDetail(row)">
              未纳入统计的订单明细
            </el-button>
          </div>
        </template>
      </BuseCrud>
    </div>
    <!-- 导入对话框 -->
    <el-dialog
      :title="upload.title"
      :visible.sync="upload.open"
      width="760px"
      height="398px"
      append-to-body
      @close="handleCancel"
    >
      <div>模板下载：</div>
      <div class="box link-box">
        <el-link
          type="primary"
          :underline="false"
          style="font-size: 16px; vertical-align: baseline"
          @click="downloadTemplate"
        >
          导入模板.xlsx
        </el-link>
      </div>
      <div>上传文件：</div>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :auto-upload="false"
        :data="upload.data"
        :on-exceed="handleExceed"
        :http-request="customUpload"
      >
        <div class="box el-upload__text">
          <img
            class="upload-icon"
            src="@/assets/icons/responseAndRegulation/upload.png"
          />
          选择要导入上传的文件
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth';

import {
    getAreaList,
  } from "@/api/electricPricePeriod/index";

  import {
    getStationList
  } from "@/api/intelligentReport/index";

export default {
  data() {
    return {
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [],
      tableColumn: [
        {
          field: 'statisticCycle',
          title: '统计周期',
          minWidth: 120,
          fixed: 'left',
        },
        {
          field: 'stationName',
          title: '充电站名称',
          minWidth: 120,
        },
        {
          field: 'stationCode',
          title: '充电站编号',
          minWidth: 120,
        },
        {
          field: 'siteType',
          title: '站点类型',
          minWidth: 120,
        },
        {
          field: 'assetType',
          title: '资产属性',
          minWidth: 120,
        },
        {
          field: 'stationSource',
          title: '站点来源',
          minWidth: 120,
        },
        {
          field: 'mainPropertyName',
          title: '一级产权单位',
          minWidth: 120,
        },
        {
          field: 'subPropertyName',
          title: '二级产权单位',
          minWidth: 120,
        },
        {
          field: 'cityName',
          title: '地市',
          minWidth: 120,
        },
        {
          field: 'districtName',
          title: '区县',
          minWidth: 120,
        },
        {
          field: 'thirdPartnarName',
          title: '运营单位',
          minWidth: 120,
        },
        {
          field: 'operOrgName',
          title: '运维单位',
          minWidth: 120,
        },
        {
          field: 'elecProvider',
          title: '供电机构',
          minWidth: 120,
        },
        {
          field: 'marketingNo',
          title: '营销户号',
          minWidth: 120,
        },
        {
          title: '基础信息',
          minWidth: 480,
          align: 'center',
          children: [
            { field: 'pileNum', title: '桩数', minWidth: 120 },
            { field: 'gunNum', title: '枪数', minWidth: 120 },
            { field: 'power', title: '充电站总功率', minWidth: 120 },
            {
              field: 'chargePqMax',
              title: '理论最大充电量',
              minWidth: 120,
              slots: {
                header: 'theoryMaxChargeHeader',
              },
            },
          ],
        },
        {
          title: '订单信息',
          minWidth: 600,
          align: 'center',
          children: [
            { field: 'chargePq', title: '充电量', minWidth: 120 },
            { field: 'cuspElec', title: '尖电量', minWidth: 120 },
            { field: 'peakElec', title: '峰电量', minWidth: 120 },
            { field: 'flatElec', title: '平电量', minWidth: 120 },
            { field: 'valleyElec', title: '谷电量', minWidth: 120 },
            {
              field: 'pileChargePq',
              title: '单桩充电量',
              minWidth: 120,
              slots: {
                header: 'pileChargingCapacity',
              },
            },
            {
              field: 'gunChargePq+1',
              title: '单枪充电量',
              minWidth: 120,
              slots: {
                header: 'gunChargingCapacity',
              },
            },
          ],
        },
        {
          field: 'elecSupply',
          title: '供电电量',
          minWidth: 120,
        },
        {
          field: 'elecLossPer',
          title: '电量损耗率',
          minWidth: 120,
          slots: {
            header: 'lossRate',
          },
        },
        {
          title: '订单电费',
          minWidth: 600,
          align: 'center',
          children: [
            { field: 'elecAmt', title: '充电电费', minWidth: 120 },
            { field: 'cuspElecAmt', title: '尖电费', minWidth: 120 },
            { field: 'peakElecAmt', title: '峰电费', minWidth: 120 },
            { field: 'flatElecAmt', title: '平电费', minWidth: 150 },
            { field: 'valleyElecAmt', title: '谷电费', minWidth: 120 },
          ],
        },
        {
          field: 'elecSupplyAmt',
          title: '供电电费',
          minWidth: 120,
        },
        {
          title: '订单服务费',
          minWidth: 600,
          align: 'center',
          children: [
            { field: 'serviceAmt', title: '充电服务费', minWidth: 120 },
            { field: 'cuspServiceAmt', title: '尖服电费', minWidth: 120 },
            { field: 'peakServiceAmt', title: '峰服务费', minWidth: 120 },
            { field: 'flatServiceAmt', title: '平服务费', minWidth: 150 },
            { field: 'valleyServiceAmt', title: '谷服务费', minWidth: 120 },
          ],
        },
        {
          field: 'chargeAmt',
          title: '订单充电金额',
          minWidth: 120,
          slots: {
            header: 'orderChargingAmount',
          },
        },
        {
          title: '订单优惠',
          minWidth: 480,
          align: 'center',
          children: [
            { field: 'couponDiscounted', title: '优惠券优惠金额', minWidth: 120 },
            { field: 'activityDiscounted', title: '立减优惠金额', minWidth: 120 },
            { field: 'otherDiscounted', title: '其他优惠金额', minWidth: 120 },
            { field: 'totalDiscounted	', title: '优惠总金额', minWidth: 120 },
          ],
        },
        {
          title: '订单实收',
          minWidth: 360,
          align: 'center',
          children: [
            { field: 'elecReciept', title: '实收电费', minWidth: 120 },
            { field: 'serviceReciept', title: '实收服务费', minWidth: 120 },
            {
              field: 'realReciept',
              title: '实收金额',
              minWidth: 120,
              slots: {
                header: 'actualReceivedAmount',
              },
            },
          ],
        },
        {
          field: 'elecPrice',
          title: '充电均价',
          minWidth: 120,
          slots: {
            header: 'chargingAveragePrice',
          },
        },
        {
          field: 'elecSupplyPrice',
          title: '供电均价',
          minWidth: 120,
          slots: {
            header: 'averageSupplyElectricity',
          },
        },
        {
          field: 'servicePrice',
          title: '服务费均价',
          minWidth: 120,
          slots: {
            header: 'averageServiceFee',
          },
        },
        {
          field: 'elecAmtLossPer',
          title: '电费损耗率',
          minWidth: 120,
          slots: {
            header: 'billLossRate',
          },
        },
        {
          title: '订单数',
          minWidth: 360,
          align: 'center',
          children: [
            { field: 'orderCount', title: '总订单数', minWidth: 120 },
            { field: 'exceptOrderCount', title: '异常订单数', minWidth: 120 },
            {
              field: 'exceptOrderPer',
              title: '异常订单电量占比',
              minWidth: 120,
              slots: {
                header: 'abnormalProportion',
              },
            },
          ],
        },
        {
          title: '清分电量',
          minWidth: 480,
          align: 'center',
          children: [
            { field: 'finClearingElec', title: '清分电量', minWidth: 120 },
            { field: 'finClearingIncludeElec', title: '清分车电包电量', minWidth: 120 },
            { field: 'enFinClearingElec', title: '企业内部车辆清分电量', minWidth: 120 },
            {
              field: 'enFinClearingIncludeElec',
              title: '企业内部车辆清分车电包电量',
              minWidth: 120,
            },
          ],
        },
        {
          title: '清分电费收入',
          minWidth: 480,
          align: 'center',
          children: [
            { field: 'finClearingElecAmt', title: '清分电费', minWidth: 120 },
            { field: 'finClearingIncludeElecAmt', title: '清分车电包电费', minWidth: 120 },
            { field: 'enFinClearingElecAmt', title: '企业内部车辆清分电费', minWidth: 120 },
            {
              field: 'enFinClearingIncludeElecAmt',
              title: '企业内部车辆清分车电包电费',
              minWidth: 120,
            },
          ],
        },
        {
          field: 'finClearingElecPrice',
          title: '清分电费均价',
          minWidth: 120,
          slots: {
            header: 'clearElectricityAveragePrice',
          },
        },
        {
          title: '清分服务费收入',
          minWidth: 480,
          align: 'center',
          children: [
            { field: 'finClearingServiceAmt', title: '清分服务费', minWidth: 120 },
            { field: 'finClearingIncludeServiceAmt', title: '清分车电包服务费', minWidth: 120 },
            {
              field: 'enFinClearingServiceAmt',
              title: '企业内部车辆清分服务费',
              minWidth: 120,
            },
            {
              field: 'enFinClearingIncludeServiceAmt',
              title: '企业内部车辆清分车电包服务费',
              minWidth: 120,
            },
          ],
        },
        {
          field: 'finClearingServicePrice',
          title: '清分服务费均价',
          minWidth: 120,
          slots: {
            header: 'clearServiceFeeAveragePrice',
          },
        },
        {
          title: '未清分电量',
          minWidth: 480,
          align: 'center',
          children: [
            { field: 'noFinClearingElec', title: '未清分电量', minWidth: 120 },
            { field: 'noFinClearingIncludeElec', title: '未清分车电包电量', minWidth: 120 },
            {
              field: 'enNoFinClearingElec',
              title: '企业内部车辆未清分电量',
              minWidth: 120,
            },
            {
              field: 'enNoFinClearingIncludeElec',
              title: '企业内部车辆未清分车电包电量',
              minWidth: 120,
            },
          ],
        },
        {
          field: 'orderFinClearingPer',
          title: '订单清分率',
          minWidth: 120,
          slots: {
            header: 'orderClearingRate',
          },
        },
        {
          title: '未清分电量',
          minWidth: 480,
          align: 'center',
          children: [
            { field: 'noFinClearingElecAmt', title: '未清分电费', minWidth: 120 },
            { field: 'noFinClearingIncludeElecAmt', title: '未清分车电包电费', minWidth: 120 },
            {
              field: 'enNoFinClearingElecAmt',
              title: '企业内部车辆未清分电费',
              minWidth: 120,
            },
            {
              field: 'enNoFinClearingIncludeElecAmt',
              title: '企业内部车辆未清分车电包电费',
              minWidth: 120,
            },
          ],
        },
        {
          title: '未清分服务费',
          minWidth: 480,
          align: 'center',
          children: [
            { field: 'noFinClearingServiceAmt', title: '未清分服务费', minWidth: 120 },
            { field: 'noFinClearingIncludeServiceAmt', title: '未清分车电包服务费', minWidth: 120 },
            {
              field: 'enNoFinClearingServiceAmt',
              title: '企业内部车辆未清分服务费',
              minWidth: 120,
            },
            {
              field: 'enNoFinClearingIncludeServiceAmt',
              title: '企业内部车辆未清分车电包服务费',
              minWidth: 120,
            },
          ],
        },
        {
          title: '线下结算收入',
          minWidth: 600,
          align: 'center',
          children: [
            { field: 'offlineSettleElec', title: '线下结算电量', minWidth: 120 },
            { field: 'offlineSettleElecAmt', title: '线下结算电费', minWidth: 120 },
            {
              field: 'offlineSettleServiceAmt',
              title: '线下结算服务费',
              minWidth: 120,
            },
            {
              field: 'offlineSettleOpFee',
              title: '线下结算代运营费',
              minWidth: 120,
            },
            {
              field: 'offlineSettleRentFee',
              title: '线下结算租赁费',
              minWidth: 120,
            },
          ],
        },
        {
          title: '支出',
          minWidth: 960,
          align: 'center',
          children: [
            { field: 'settleElecExpense', title: '结算电费支出', minWidth: 120 },
            { field: 'settleServiceFeeShare', title: '结算服务费分成', minWidth: 120 },
            {
              field: 'settlePlaceRentExpense',
              title: '结算场地租金支出',
              minWidth: 120,
            },
            {
              field: 'settleTotalAmt',
              title: '结算总金额',
              minWidth: 120,
            },
            {
              field: 'platformFee',
              title: '平台费',
              minWidth: 120,
            },
            {
              field: 'basicOpFee',
              title: '基础运营费',
              minWidth: 120,
            },
            {
              field: 'repairCost',
              title: '检修成本',
              minWidth: 120,
            },
            {
              field: 'maintenanceCost',
              title: '运维成本',
              minWidth: 120,
            },
          ],
        },
        {
          field: 'operatingProfit',
          title: '营业利润',
          minWidth: 120,
        },
        {
          title: '工单',
          minWidth: 360,
          align: 'center',
          children: [
            { field: 'repairTicket', title: '检修工单', minWidth: 120 },
            { field: 'maintenanceTicket', title: '运维工单', minWidth: 120 },
            {
              field: 'patrolTicket',
              title: '巡视工单',
              minWidth: 120,
            },
          ],
        },
        {
          field: 'chargeTimes',
          title: '充电时长',
          minWidth: 120,
        },
        {
          title: '可用率统计',
          minWidth: 1200,
          align: 'center',
          children: [
            {
              field: 'opTimes',
              title: '在运时长',
              minWidth: 120,
              slots: {
                header: 'durationOfOperation',
              },
            },
            { field: 'normalTimes', title: '正常时长', minWidth: 120 },
            { field: 'faultTimes', title: '故障时长', minWidth: 120 },
            { field: 'offlineTimes', title: '离线时长', minWidth: 120 },
            { field: 'stopTimes', title: '停运时长', minWidth: 120 },
            { field: 'warnTimes', title: '告警时长', minWidth: 120 },
            {
              field: 'offlinePer',
              title: '离线率',
              minWidth: 120,
              slots: {
                header: 'offlineRate',
              },
            },
            {
              field: 'faultPer',
              title: '故障率',
              minWidth: 120,
              slots: {
                header: 'faultRate',
              },
            },
            {
              field: 'stopPer',
              title: '停运率',
              minWidth: 120,
              slots: {
                header: 'stopRate',
              },
            },
            {
              field: 'usablePer',
              title: '可用率',
              minWidth: 120,
              slots: {
                header: 'availableRate',
              },
            },
            { field: 'upPileCount', title: '可用率低于90%桩数', minWidth: 120 },
          ],
        },
        {
          title: '一次性充电成功率统计',
          minWidth: 720,
          align: 'center',
          children: [
            { field: 'exceptEndCount', title: '异常结束订单数', minWidth: 120 },
            { field: 'manExceptCount', title: '人为原因异常订单数', minWidth: 120 },
            { field: 'carExceptCount', title: '车端原因异常订单数', minWidth: 120 },
            { field: 'pileExceptCount', title: '桩端原因异常订单数', minWidth: 120 },
            {
              field: 'onceSuccPer',
              title: '一次充电成功率',
              minWidth: 120,
              slots: {
                header: 'oneChargingSuccessRate',
              },
            },
            {
              field: 'onceSuccExclueCarPer',
              title: '一次充电成功率（剔除车端原因）',
              minWidth: 120,
              slots: {
                header: 'removeCarEndReason',
              },
            },
            {
              field: 'osecpPileCount',
              title: '一次充电成功率低于85%桩数',
              minWidth: 120,
            },
          ],
        },
        {
          title: '利用率统计',
          minWidth: 240,
          align: 'center',
          children: [
            {
              field: 'powerUsagePer',
              title: '功率利用率',
              minWidth: 120,
              slots: {
                header: 'powerUsageRate',
              },
            },
            {
              field: 'timeUsagePer',
              title: '时间利用率',
              minWidth: 120,
              slots: {
                header: 'timeUsageRate',
              },
            },
          ],
        },
        {
          title: '零低统计',
          minWidth: 240,
          align: 'center',
          children: [
            {
              field: 'zeroPilePer',
              title: '零低桩占比',
              minWidth: 120,
              slots: {
                header: 'zeroLowPileRatio',
              },
            },
            {
              field: 'lowPilePer',
              title: '低效桩占比',
              minWidth: 120,
              slots: {
                header: 'inefficientPileRatio',
              },
            },
          ],
        },
        {
          title: '操作',
          slots: { default: 'operate' },
          width: 200,
          align: 'center',
          fixed: 'right',
        },
      ],
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      areaList: [],
      params: {
        areaCode: '',
        assetType: '',
        siteType: '',
        stationSource: '',
        mainPropertyCode: '',
        thirdPartnarCode: '',
        operOrgCode: '',
        elecProvider: '',
        stationName: '',
        stationCode: '',
      },
      selectedItem: '1',
      activeTab: [
        {
          label: '日表',
          value: '1',
        },
        {
          label: '周表',
          value: '4',
        },
        {
          label: '月表',
          value: '2',
        },
      ],
      selectDate: '',
      // 导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: '',
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: 'Bearer ' + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '/vehicle-charging-admin/insp/soc/import',
        data: {
          requireId: '',
        },
      },
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'areaCode',
            title: '地市',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.areaList,
            },
          },
          {
            field: 'assetType',
            title: '资产属性',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
          {
            field: 'siteType',
            title: '站点类型',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
          {
            field: 'stationSource',
            title: '站点来源',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
          {
            field: 'mainPropertyCode',
            title: '产权单位',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
          {
            field: 'thirdPartnarCode',
            title: '运营单位',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
          {
            field: 'operOrgCode',
            title: '运维单位',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
          {
            field: 'elecProvider',
            title: '供电机构',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
          {
            field: 'stationName',
            title: '充电站',
            element: 'el-input',
          },
          {
            field: 'stationCode',
            title: '充电站编号',
            element: 'el-input',
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  mounted() {
    this.getAreaList();
    this.loadData();
  },
  methods: {
      // 获取适用地市
      async getAreaList() {
            const [err, res] = await getAreaList({
                areaLevel: '03',
                huNanOnly: true
            })
            if (err) return
            const {
                data
            } = res
            const list = []
            data.forEach(item => {
                list.push({
                    label: item.areaName,
                    value: item.areaCode,
                })
            })
            this.areaList = list
            
        },
    async loadData() {
      const {
        areaCode,
        assetType,
        siteType,
        stationSource,
        mainPropertyCode,
        thirdPartnarCode,
        operOrgCode,
        elecProvider,
        stationName,
        stationCode,
      } = this.params
      
      let timeStart = ''
      let timeEnd = ''
      if(this.selectDate && this.selectDate.length > 0){
        timeStart = this.selectDate[0]
        timeEnd = this.selectDate[1]
      }
      const params = {
        areaCode,
        assetType,
        siteType,
        stationSource,
        mainPropertyCode,
        thirdPartnarCode,
        operOrgCode,
        elecProvider,
        stationName,
        stationCode,
        timeStart,
        timeEnd,
        timeType: this.selectedItem,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      }

      this.loading = true;
      const [err, res] = await getStationList(params)
      this.loading = false;

      if (err) return 
      const { data, total } = res;
      this.tableData = data;
      this.tablePage.total = total;
    },
    // 导入
    handleInput() {
      this.upload.title = '导入';
      this.upload.open = true;
    },
    // 导入关闭
    handleCancel() {
      this.upload.open = false;
      this.$refs.upload.clearFiles();
    },
    // 下载模板
    downloadTemplate() {
      this.download(
        '/vehicle-charging-admin/insp/soc/import/template',
        {},
        `充电桩基础业务报表导入模板.xlsx`
      );
    },
    // 用户选择的文件数量超过 limit 限制
    handleExceed(files, fileList) {
      console.log(files, fileList);
      // 清空已选文件列表
      this.$refs.upload.clearFiles();
      // 手动添加新选择的文件（首个文件）
      this.$refs.upload.handleStart(files[0]);
    },
    // 调用上传接口
    async customUpload({ action, file, data }) {
      this.upload.isUploading = true;
      this.upload.open = false;
      const formData = new FormData();
      formData.append('file', file);
      try {
        // const [err, res] = await uploadClearingResult(formData);
        // if (err) return;
        // if (res.success) {
        //   // console.log('导入res', res);
        //   this.$message.success('导入成功');
        //   this.handleCancel();
        //   this.loadData();
        // } else {
        //   this.$message.error(res.subMsg || '导入失败');
        // }
      } finally {
        this.upload.isUploading = false;
      }
    },
    // 提交上传文件
    submitFileForm() {
      console.log('upload data', this.upload.data);
      this.$refs.upload.submit();
    },
    // 导出
    handleExport() {
      const {
        areaCode,
        assetType,
        siteType,
        stationSource,
        mainPropertyCode,
        thirdPartnarCode,
        operOrgCode,
        elecProvider,
        stationName,
        stationCode,
      } = this.params
      
      let timeStart = ''
      let timeEnd = ''
      if(this.selectDate && this.selectDate.length > 0){
        timeStart = this.selectDate[0]
        timeEnd = this.selectDate[1]
      }
      const params = {
        areaCode,
        assetType,
        siteType,
        stationSource,
        mainPropertyCode,
        thirdPartnarCode,
        operOrgCode,
        elecProvider,
        stationName,
        stationCode,
        timeStart,
        timeEnd,
        timeType: this.selectedItem,
      }


      this.download(
          '/vehicle-charging-admin/stationBiz/report/export',
          {
            ...params,
          },
          `充电站基础业务表.xlsx`
      );
    },
    selectTab(value) {
      this.selectedItem = value;

      this.selectDate = [] ;
      this.tableData = [];
      this.tablePage.currentPage = 1;
      this.tablePage.total = 0;

      this.loadData()
    },
    selectDateChange() {
      this.tableData = [];
      this.tablePage.currentPage = 1;
      this.tablePage.total = 0;

      this.loadData()
    },
    // 未纳入统计的订单明细
    handleDetail(val) {
      console.log(val, 'val');
      this.$router.push({
        path: '/v2g-charging-web/intelligentReport/basicBusinessReport/abnormalOrder',
        query: val,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.table-wrap {
  background: #fff;
  padding: 20px;
  border-radius: 4px;

  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }
  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }
  .top-button-wrap {
    display: flex;
    margin: 16px 0;
    justify-content: space-between;
    .tabs {
      display: flex;
      gap: 5rpx;
      .tab-item {
        cursor: pointer;
        font-size: 18px;
        color: #606266; /* 默认颜色 */
        transition: color 0.3s; /* 平滑过渡 */
        padding: 0 16px;
        margin: 0 6px;
      }

      .tab-item.active {
        color: #409eff; /* 选中时的颜色 */
        border-bottom: 2px solid #409eff; /* 选中时的下划线 */
        padding-bottom: 4px; /* 调整下划线的位置 */
      }
    }
  }
}
</style>
