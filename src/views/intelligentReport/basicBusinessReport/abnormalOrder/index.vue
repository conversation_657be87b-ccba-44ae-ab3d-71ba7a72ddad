<template>
  <div class="container">
    <div class="table-wrap">
      <BuseCrud
        ref="crud"
        :loading="loading"
        :filterOptions="filterOptions"
        :tablePage="tablePage"
        :tableColumn="tableColumn"
        :tableData="tableData"
        :pagerProps="pagerProps"
        :modalConfig="modalConfig"
        @loadData="loadData"
      >
        <template slot="defaultHeader">
          <div class="card-head">
            <div class="card-head-text">未纳入统计的异常订单</div>
            <div class="top-button-wrap">
              <el-button
                type="primary"
                icon="el-icon-download"
                @click="handleDownload"
              >
                导出
              </el-button>
            </div>
          </div>
          <div class="card-head-after"></div>
        </template>
      </BuseCrud>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [],
      tableColumn: [
        {
          type: 'seq',
          title: '序号',
          minWidth: 60,
        },
        {
          field: 'orderNumber',
          title: '订单编号',
          minWidth: 200,
        },
        {
          field: 'thirdOorderNumber',
          title: '第三方订单编号',
          minWidth: 200,
        },
        {
          field: '',
          title: '充电站',
          minWidth: 200,
        },
        {
          field: '',
          title: '充电桩',
          minWidth: 200,
        },
        {
          field: '',
          title: '下单渠道',
          minWidth: 200,
        },
        {
          field: '',
          title: '订单来源',
          minWidth: 200,
        },
        {
          field: '',
          title: '桩接入方式',
          minWidth: 200,
        },
        {
          field: '',
          title: '桩来源',
          minWidth: 200,
        },
        {
          field: '',
          title: '用户类型',
          minWidth: 200,
        },
        {
          field: '',
          title: '用户手机号',
          minWidth: 200,
        },
        {
          field: '',
          title: '所属企业编号',
          minWidth: 200,
        },
        {
          field: '',
          title: '下单时间',
          minWidth: 200,
        },
        {
          field: '',
          title: '修正前充电开始时间',
          minWidth: 200,
        },
        {
          field: '',
          title: '修正后充电开始时间',
          minWidth: 200,
        },
        {
          field: '',
          title: '修正前充电结束时间',
          minWidth: 200,
        },
        {
          field: '',
          title: '修正后充电结束时间',
          minWidth: 200,
        },
        {
          field: '',
          title: '异常上报时间',
          minWidth: 200,
        },
        {
          field: '',
          title: '充电状态',
          minWidth: 200,
        },
        {
          field: '',
          title: '订单状态',
          minWidth: 200,
        },
        {
          field: '',
          title: '异常等级',
          minWidth: 200,
        },
        {
          field: '',
          title: '异常类型',
          minWidth: 200,
        },
        {
          field: '',
          title: '异常编号',
          minWidth: 200,
        },
        {
          field: '',
          title: '异常名称',
          minWidth: 200,
        },
        {
          field: '',
          title: '异常描述',
          minWidth: 360,
        },
        {
          field: '',
          title: '修正前充电电量(kwh)',
          minWidth: 200,
        },
        {
          field: '',
          title: '修正后充电电量(kwh)',
          minWidth: 200,
        },
        {
          field: '',
          title: '修正前抄表电量(kwh)',
          minWidth: 200,
        },
        {
          field: '',
          title: '修正后抄表电量(kwh)',
          minWidth: 200,
        },
        {
          field: '',
          title: '修正前充电电费(元)',
          minWidth: 200,
        },
        {
          field: '',
          title: '修正后充电电费(元)',
          minWidth: 200,
        },
        {
          field: '',
          title: '修正前充电服务费(元)',
          minWidth: 200,
        },
        {
          field: '',
          title: '修正后充电服务费(元)',
          minWidth: 200,
        },
        {
          field: '',
          title: '修正前充电总金额(元)',
          minWidth: 200,
        },
        {
          field: '',
          title: '修正后充电总金额(元)',
          minWidth: 200,
        },
        {
          field: '',
          title: '修正前优惠电费(元)',
          minWidth: 200,
        },
        {
          field: '',
          title: '修正后优惠电费(元)',
          minWidth: 200,
        },
        {
          field: '',
          title: '修正前优惠服务费(元)',
          minWidth: 200,
        },
        {
          field: '',
          title: '修正后优惠服务费(元)',
          minWidth: 200,
        },
        {
          field: '',
          title: '修正前优惠金额(元)',
          minWidth: 200,
        },
        {
          field: '',
          title: '修正后优惠金额(元)',
          minWidth: 200,
        },
        {
          field: '',
          title: '修正前实扣电费(元)',
          minWidth: 200,
        },
        {
          field: '',
          title: '修正后实扣电费(元)',
          minWidth: 200,
        },
        {
          field: '',
          title: '修正前实扣服务费(元)',
          minWidth: 200,
        },
        {
          field: '',
          title: '修正后实扣服务费(元)',
          minWidth: 200,
        },
        {
          field: '',
          title: '修正前实扣金额(元)',
          minWidth: 200,
        },
        {
          field: '',
          title: '修正后实扣金额(元)',
          minWidth: 200,
        },
        {
          field: '',
          title: '处理状态',
          minWidth: 200,
        },
        {
          field: '',
          title: '处理人',
          minWidth: 200,
        },
        {
          field: '',
          title: '处理时间',
          minWidth: 200,
        },
      ],
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      params: {
        chargingStation: '',
        chargingPile: '',
        orderNumber: '',
        thirdOorderNumber: '',
        userMobile: '',
        userType: '',
        enterpriseNumber: '',
        orderDate: [],
        orderStatus: '',
        exception: '',
        abnormalLevel: '',
        processingStatus: '',
        reportDate: [],
        processingDate: [],
        orderingChannel: '',
        orderSource: '',
        beforeChargingStartTime: [],
        afterChargingStartTime: [],
        beforeChargingEndTime: [],
        afterChargingEndTime: [],
        pileAccessMethod: '',
        pileSource: '',
      },
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'chargingStation',
            title: '充电站',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
          {
            field: 'chargingPile',
            title: '充电桩',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
          {
            field: 'orderNumber',
            title: '订单编号',
          },
          {
            field: 'thirdOorderNumber',
            title: '第三方订单编号',
          },
          {
            field: 'userMobile',
            title: '用户手机号',
          },
          {
            field: 'userType',
            title: '用户类型',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
          {
            field: 'enterpriseNumber',
            title: '企业编号',
          },
          {
            field: 'orderDate',
            title: '下单时间',
            element: 'el-date-picker',
            props: {
              type: 'datetimerange',
              placeholder: '开始日期 - 自定日期',
              startPlaceholder: '开始日期',
              endPlaceholder: '结束日期',
              valueFormat: 'yyyy-MM-DD HH:mm:ss',
            },
          },
          {
            field: 'orderStatus',
            title: '订单状态',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
          {
            field: 'exception',
            title: '异常类型',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
          {
            field: 'abnormalLevel',
            title: '异常等级',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
          {
            field: 'processingStatus',
            title: '处理状态',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
          {
            field: 'reportDate',
            title: '上报时间',
            element: 'el-date-picker',
            props: {
              type: 'datetimerange',
              placeholder: '开始日期 - 自定日期',
              startPlaceholder: '开始日期',
              endPlaceholder: '结束日期',
              valueFormat: 'yyyy-MM-dd HH:mm:ss',
            },
          },
          {
            field: 'processingDate',
            title: '上报时间',
            element: 'el-date-picker',
            props: {
              type: 'datetimerange',
              placeholder: '开始日期 - 自定日期',
              startPlaceholder: '开始日期',
              endPlaceholder: '结束日期',
              valueFormat: 'yyyy-MM-dd HH:mm:ss',
            },
          },
          {
            field: 'orderingChannel',
            title: '下单渠道',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
          {
            field: 'orderSource',
            title: '订单来源',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
          {
            field: 'beforeChargingStartTime',
            title: '修正前充电开始时间',
            element: 'el-date-picker',
            props: {
              type: 'datetimerange',
              placeholder: '开始日期 - 自定日期',
              startPlaceholder: '开始日期',
              endPlaceholder: '结束日期',
              valueFormat: 'yyyy-MM-DD HH:mm:ss',
            },
          },
          {
            field: 'afterChargingStartTime',
            title: '修正后充电开始时间',
            element: 'el-date-picker',
            props: {
              type: 'datetimerange',
              placeholder: '开始日期 - 自定日期',
              startPlaceholder: '开始日期',
              endPlaceholder: '结束日期',
              valueFormat: 'yyyy-MM-DD HH:mm:ss',
            },
          },
          {
            field: 'beforeChargingEndTime',
            title: '修正前充电结束时间',
            element: 'el-date-picker',
            props: {
              type: 'datetimerange',
              placeholder: '开始日期 - 自定日期',
              startPlaceholder: '开始日期',
              endPlaceholder: '结束日期',
              valueFormat: 'yyyy-MM-DD HH:mm:ss',
            },
          },
          {
            field: 'afterChargingEndTime',
            title: '修正后充电结束时间',
            element: 'el-date-picker',
            props: {
              type: 'datetimerange',
              placeholder: '开始日期 - 自定日期',
              startPlaceholder: '开始日期',
              endPlaceholder: '结束日期',
              valueFormat: 'yyyy-MM-DD HH:mm:ss',
            },
          },
          {
            field: 'pileAccessMethod',
            title: '桩接入方式',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
          {
            field: 'pileSource',
            title: '桩来源',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  mounted() {
    console.log('this.$route', this.$route);
    console.log('this.$route', this.$route.query);
    this.loadData();
  },
  methods: {
    loadData() {
      console.log('parms', this.params);
    },
    // 下载
    handleDownload() {},
  },
};
</script>

<style lang="scss" scoped>
.table-wrap {
  background: #fff;
  padding: 20px;
  border-radius: 4px;

  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }

  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }
}
</style>
