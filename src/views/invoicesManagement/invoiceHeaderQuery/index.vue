<template>
  <div class="container">
    <div class="table-wrap">
      <BuseCrud
        ref="crud"
        :loading="loading"
        :filterOptions="filterOptions"
        :tablePage="tablePage"
        :tableColumn="tableColumn"
        :tableData="tableData"
        :pagerProps="pagerProps"
        :modalConfig="modalConfig"
        @loadData="loadData"
      >
        <template slot="defaultHeader">
          <div class="card-head">
            <div class="card-head-text">发票抬头查询</div>
            <div class="top-button-wrap">
              <el-button
                type="primary"
                icon="el-icon-download"
                @click="handleOutput"
              >
                导出
              </el-button>
            </div>
          </div>
          <div class="card-head-after"></div>
        </template>
      </BuseCrud>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [
        {
          invoiceType: '企业',
          invoiceTitle: '阿里巴巴集团',
          taxNumber: '91330108MA2K1K6A5W',
          registerAddress: '浙江省杭州市滨江区网商路699号',
          registerPhone: '0571-********',
          bankName: '中国工商银行',
          bankAccount: '1234567890123456789',
          isDefault: '是',
          relatedUser: '张三',
          createTime: '2023-10-10 10:00:00',
        },
        {
          invoiceType: '个人/非企业',
          invoiceTitle: '李四',
          taxNumber: '-',
          registerAddress: '-',
          registerPhone: '***********',
          bankName: '-',
          bankAccount: '-',
          isDefault: '否',
          relatedUser: '李四',
          createTime: '2023-10-09 15:30:00',
        },
      ],
      tableColumn: [
        {
          type: 'seq',
          title: '序号',
          minWidth: 60,
        },
        {
          field: 'invoiceType',
          title: '抬头类型',
          minWidth: 120,
        },
        {
          field: 'invoiceTitle',
          title: '发票抬头',
          minWidth: 120,
        },
        {
          field: 'taxNumber',
          title: '税号',
          minWidth: 120,
        },
        {
          field: 'registerAddress',
          title: '注册地址',
          minWidth: 120,
        },
        {
          field: 'registerPhone',
          title: '注册电话',
          minWidth: 120,
        },
        {
          field: 'bankName',
          title: '开户银行',
          minWidth: 120,
        },
        {
          field: 'bankAccount',
          title: '银行账号',
          minWidth: 120,
        },
        {
          field: 'isDefault',
          title: '是否为默认抬头',
          minWidth: 150,
        },
        {
          field: 'relatedUser',
          title: '关联用户',
          minWidth: 120,
        },
        {
          field: 'createTime',
          title: '创建时间',
          minWidth: 120,
        },
      ],
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      params: {
        invoiceType: '',
        invoiceTitle: '',
        bankAccount: '',
        taxNumber: '',
        createTime: [],
        creatorPhone: '',
      },
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'invoiceType',
            title: '抬头类型',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [
                { label: '企业', value: '1' },
                { label: '个人/非企业', value: '2' },
              ],
            },
          },
          {
            field: 'invoiceTitle',
            title: '发票抬头',
            element: 'el-input',
            props: {
              placeholder: '请输入',
            },
          },
          {
            field: 'bankAccount',
            title: '银行账号',
            element: 'el-input',
            props: {
              placeholder: '请输入',
            },
          },
          {
            field: 'taxNumber',
            title: '税号',
            element: 'el-input',
            props: {
              placeholder: '请输入',
            },
          },
          {
            field: 'createTime',
            title: '创建时间',
            element: 'el-date-picker',
            props: {
              type: 'daterange',
              startPlaceholder: '开始时间',
              endPlaceholder: '结束时间',
            },
          },
          {
            field: 'creatorPhone',
            title: '创建人手机号',
            element: 'el-input',
            props: {
              placeholder: '请输入',
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  mounted() {
    this.loadData();
  },
  methods: {
    loadData() {},
    handleOutput() {},
  },
};
</script>

<style lang="scss" scoped>
.table-wrap {
  background: #fff;
  padding: 20px;
  border-radius: 4px;

  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }

  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }
}
</style>
