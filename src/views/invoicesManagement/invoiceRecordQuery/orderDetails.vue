<template>
  <div class="container container-float" style="padding: 0 0 100px 0">
    <div class="table-wrap">
      <div class="card-head">
        <div class="card-head-text">企业员工账户</div>
      </div>
      <div class="info-wrap">
        <div class="info-item" v-for="item in infoList" :key="item.name">
          <img :src="item.icon" class="info-icon" />
          <div class="info-right-wrap">
            <div class="info-title">{{ item.name }}</div>
            <div class="info-number">
              {{ item.value }}
              <span class="info-unit">{{ item.unit }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="table-wrap">
      <BuseCrud
        :tableColumn="tableColumn"
        :tableData="tableData"
        :loading="loading"
        :pagerProps="{
          layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
        }"
        :tablePage="tablePage"
        :modalConfig="modalConfig"
      ></BuseCrud>
    </div>
  </div>
</template>

<script>
import icon1 from '@/assets/invoiceRecordQuery/icon-1.png';
import icon2 from '@/assets/invoiceRecordQuery/icon-2.png';
import icon3 from '@/assets/invoiceRecordQuery/icon-3.png';
import icon4 from '@/assets/invoiceRecordQuery/icon-4.png';
import icon5 from '@/assets/invoiceRecordQuery/icon-5.png';
import icon6 from '@/assets/invoiceRecordQuery/icon-6.png';
import icon7 from '@/assets/invoiceRecordQuery/icon-7.png';

export default {
  data() {
    return {
      infoList: [
        {
          name: '订单数',
          value: '3',
          unit: '个',
          icon: icon1,
        },
        {
          name: '充电总金额',
          value: '60',
          unit: '元',
          icon: icon2,
        },
        {
          name: '充电电费',
          value: '54',
          unit: '元',
          icon: icon3,
        },
        {
          name: '充电服务费',
          value: '6',
          unit: '元',
          icon: icon4,
        },
        {
          name: '实扣总金额',
          value: '60',
          unit: '元',
          icon: icon5,
        },
        {
          name: '实扣电费',
          value: '54',
          unit: '元',
          icon: icon6,
        },
        {
          name: '实扣服务费',
          value: '6',
          unit: '元',
          icon: icon7,
        },
      ],
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [
        {
          orderNo: 'ORD20231010001',
          placeOrderTime: '2023-10-10 10:00:00',
          payTime: '2023-10-10 11:30:00',
          payMethod: '支付宝',
          chargingStation: '杭州滨江区充电站',
          chargingPile: 'CP001',
          chargingAmount: '30.5',
          chargingDuration: '1.5',
          chargingElectricityFee: '45.00',
          chargingServiceFee: '5.00',
          totalChargingFee: '50.00',
          deductElectricityFee: '45.00',
          deductServiceFee: '5.00',
          totalDeductFee: '50.00',
        },
        {
          orderNo: 'ORD20231010002',
          placeOrderTime: '2023-10-10 12:00:00',
          payTime: '2023-10-10 13:15:00',
          payMethod: '微信支付',
          chargingStation: '上海浦东新区充电站',
          chargingPile: 'CP002',
          chargingAmount: '20.0',
          chargingDuration: '1.0',
          chargingElectricityFee: '30.00',
          chargingServiceFee: '4.00',
          totalChargingFee: '34.00',
          deductElectricityFee: '30.00',
          deductServiceFee: '4.00',
          totalDeductFee: '34.00',
        },
      ],
      tableColumn: [
        { type: 'seq', title: '序号', minWidth: 60 },
        { field: 'orderNo', title: '订单编号', minWidth: 150 },
        { field: 'placeOrderTime', title: '下单时间', minWidth: 160 },
        { field: 'payTime', title: '支付时间', minWidth: 160 },
        { field: 'payMethod', title: '支付方式', minWidth: 120 },
        { field: 'chargingStation', title: '充电站', minWidth: 180 },
        { field: 'chargingPile', title: '充电桩', minWidth: 120 },
        { field: 'chargingAmount', title: '充电量(kWh)', minWidth: 120 },
        { field: 'chargingDuration', title: '充电时长(h)', minWidth: 120 },
        {
          field: 'chargingElectricityFee',
          title: '充电电费(元)',
          minWidth: 130,
        },
        { field: 'chargingServiceFee', title: '充电服务费(元)', minWidth: 140 },
        { field: 'totalChargingFee', title: '充电总金额(元)', minWidth: 140 },
        {
          field: 'deductElectricityFee',
          title: '实扣电费(元)',
          minWidth: 130,
        },
        { field: 'deductServiceFee', title: '实扣服务费(元)', minWidth: 140 },
        { field: 'totalDeductFee', title: '实扣总金额(元)', minWidth: 140 },
      ],
    };
  },
  computed: {
    modalConfig() {
      return {
        addBtn: false, // 隐藏新增按钮
        editBtn: false,
        delBtn: false,
        viewBtn: false,
        menu: false,
      };
    },
  },
  mounted() {},
  methods: {},
};
</script>

<style lang="scss" scoped>
.table-wrap {
  background-color: #ffffff;
  border-radius: 5px;
  margin: 16px;
  .card-head {
    // position: relative;
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    // margin-top: -20px;
    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }

  .info-wrap {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;

    .info-item {
      // background-color: #FAFBFC;
      flex: 1 1 0;
      // min-width: 180px;

      border-radius: 5px;
      padding: 8px 24px;
      box-sizing: border-box;
      // margin-right: 16px;
      display: flex;
      .info-icon {
        width: 42px;
        height: 42px;
      }
      .info-right-wrap {
        flex: 1;
        margin-left: 24px;
        .info-title {
          font-weight: 400;
          font-size: 14px;
          line-height: 14px;
          margin-bottom: 8px;
        }
        .info-number {
          font-size: 20px;
          font-weight: 500;
          .info-unit {
            font-size: 14px;
            font-weight: 400;
          }
        }
      }
    }
    .info-item:last-child {
      margin-right: 0;
    }
  }
}

::v-deep .bd3001-auto-filters-container {
  margin-bottom: 0px !important;
}

::v-deep .bd3001-auto-filters-container {
  box-shadow: none !important;
}

::v-deep .bd3001-content {
  padding-top: 0px !important;
}
</style>
