<template>
  <div class="container">
    <div class="table-wrap">
      <BuseCrud
        ref="crud"
        :loading="loading"
        :filterOptions="filterOptions"
        :tablePage="tablePage"
        :tableColumn="tableColumn"
        :tableData="tableData"
        :pagerProps="pagerProps"
        :modalConfig="modalConfig"
        @loadData="loadData"
      >
        <template slot="defaultHeader">
          <div class="card-head">
            <div class="card-head-text">开票记录查询</div>
            <div class="top-button-wrap">
              <el-button
                type="primary"
                icon="el-icon-download"
                @click="handleOutput"
              >
                导出
              </el-button>
            </div>
          </div>
          <div class="card-head-after"></div>
        </template>
        <template slot="relatedOrderCount" slot-scope="{ row }">
          <a @click="handleDetail(row)" style="color: blue">
            {{ row.relatedOrderCount }}
          </a>
        </template>
        <template slot="invoiceType" slot-scope="{ row }">
          <div v-if="row.invoiceType == '红票'" class="red-invoiceType">
            {{ row.invoiceType }}
          </div>
          <div v-if="row.invoiceType == '蓝票'" class="blue-invoiceType">
            {{ row.invoiceType }}
          </div>
        </template>
        <template slot="operate" slot-scope="{ row }">
          <div class="menu-box">
            <el-button type="primary" plain @click="handlePreview(row)">
              预览
            </el-button>
            <el-button
              type="primary"
              plain
              @click="handleRed(row)"
              v-if="row.invoiceType == '蓝票'"
            >
              冲红
            </el-button>
            <el-button type="primary" plain @click="handleReissue(row)">
              重新开具
              <!-- 开具发票 -->
            </el-button>
          </div>
        </template>
      </BuseCrud>
    </div>
    <el-dialog
      :title="redData.title"
      :visible.sync="redData.open"
      width="760px"
      height="398px"
      append-to-body
      @close="handleCancel"
    >
      <el-form size="small" :model="redData" :rules="rules" ref="redData">
        <el-form-item
          label="冲红原因"
          prop="remark"
          :label-width="formLabelWidth"
        >
          <el-input
            v-model="redData.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入冲红原因"
          ></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      :title="previewData.title"
      :visible.sync="previewData.open"
      width="80%"
      append-to-body
    >
      <img :src="previewData.imgUrl" style="width: 100%" />
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [
        {
          businessType: '充电服务',
          invoiceDate: '2023-10-10',
          invoiceCategory: '增值税专用发票',
          invoiceNumber: 'INV20231010001',
          invoiceCode: 'C100001',
          invoiceStatus: '已开票',
          invoiceSerialNo: 'SN20231010001',
          buyerName: '阿里巴巴集团',
          sellerName: '阿里云',
          invoiceType: '蓝票',
          invoiceAmount: '1000.00',
          relatedOrderCount: '5',
          invoiceApplicant: '张三',
          applyInvoiceTime: '2023-10-09 15:00',
          receiveEmail: '<EMAIL>',
          receivePhone: '***********',
          redDate: '-',
          redReason: '-',
          redPerson: '-',
        },
        {
          businessType: '其他服务',
          invoiceDate: '2023-10-09',
          invoiceCategory: '普通发票',
          invoiceNumber: 'INV20231009001',
          invoiceCode: 'C100002',
          invoiceStatus: '未开票',
          invoiceSerialNo: 'SN20231009001',
          buyerName: '腾讯科技',
          sellerName: '阿里云',
          invoiceType: '红票',
          invoiceAmount: '500.00',
          relatedOrderCount: '2',
          invoiceApplicant: '李四',
          applyInvoiceTime: '2023-10-08 10:00',
          receiveEmail: '<EMAIL>',
          receivePhone: '***********',
          redDate: '2023-10-11',
          redReason: '信息错误',
          redPerson: '王五',
        },
      ],
      tableColumn: [
        {
          type: 'seq',
          title: '序号',
          minWidth: 60,
        },
        {
          field: 'businessType',
          title: '业务类型',
          minWidth: 120,
        },
        {
          field: 'invoiceDate',
          title: '开票日期',
          minWidth: 140,
        },
        {
          field: 'invoiceCategory',
          title: '发票种类',
          minWidth: 130,
        },
        {
          field: 'invoiceNumber',
          title: '发票编号',
          minWidth: 150,
        },
        {
          field: 'invoiceCode',
          title: '发票代码',
          minWidth: 140,
        },
        {
          field: 'invoiceStatus',
          title: '开票状态',
          minWidth: 120,
        },
        {
          field: 'invoiceSerialNo',
          title: '开票流水号',
          minWidth: 160,
        },
        {
          field: 'buyerName',
          title: '购方名称',
          minWidth: 180,
        },
        {
          field: 'sellerName',
          title: '销方名称',
          minWidth: 180,
        },
        {
          title: '开票类型',
          slots: { default: 'invoiceType' },
          minWidth: 120,
          align: 'center',
        },
        {
          field: 'invoiceAmount',
          title: '开票金额(元)',
          minWidth: 130,
        },
        {
          //   field: 'relatedOrderCount',
          slots: { default: 'relatedOrderCount' },
          title: '关联订单数',
          minWidth: 120,
        },
        {
          field: 'invoiceApplicant',
          title: '开票申请人',
          minWidth: 130,
        },
        {
          field: 'applyInvoiceTime',
          title: '申请开票时间',
          minWidth: 150,
        },
        {
          field: 'receiveEmail',
          title: '接收邮箱',
          minWidth: 160,
        },
        {
          field: 'receivePhone',
          title: '接收手机号',
          minWidth: 130,
        },
        {
          field: 'redDate',
          title: '冲红日期',
          minWidth: 140,
        },
        {
          field: 'redReason',
          title: '发票冲红原因',
          minWidth: 180,
        },
        {
          field: 'redPerson',
          title: '发票冲红人',
          minWidth: 130,
        },
        {
          title: '操作',
          slots: { default: 'operate' },
          minWidth: 300,
          align: 'center',
          fixed: 'right',
        },
      ],
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      params: {
        invoiceDate: [], // 开票日期
        invoiceCode: '', // 发票代码
        invoiceCategory: '', // 发票种类
        invoiceStatus: '', // 开票状态
        invoiceType: '', // 开票类型
        invoiceSerialNo: '', // 开票流水号
        invoiceApplicant: '', // 开票申请人
        applyInvoiceDate: [], // 申请开票日期
        buyerName: '', // 购方名称
        businessType: '', // 业务类型
        invoiceNumber: '', // 发票编号
        redPerson: '', // 冲红人
        redDate: [], // 冲红日期
      },
      redData: {
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: '申请冲红',
        // 冲红原因
        remark: '',
      },
      selectData: {},
      formLabelWidth: '100px',
      rules: {
        remark: [
          { required: true, message: '请输入冲红原因', trigger: 'blur' },
        ],
      },
      previewData: {
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: '预览',
        // 图片地址
        imgUrl:
          'https://www.baidu.com/img/PCtm_d9c8750bed0b3c7d089fa7d55720d6cf.png',
      },
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'invoiceDate',
            title: '开票日期',
            element: 'el-date-picker',
            required: true,
            props: {
              type: 'daterange',
              startPlaceholder: '开始时间',
              endPlaceholder: '结束时间',
            },
          },
          {
            field: 'invoiceCode',
            title: '发票代码',
            element: 'el-input',
            props: {
              placeholder: '请输入',
            },
          },
          {
            field: 'invoiceCategory',
            title: '发票种类',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [
                { label: '增值税专用发票', value: 'special' },
                { label: '普通发票', value: 'general' },
              ],
            },
          },
          {
            field: 'invoiceStatus',
            title: '开票状态',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [
                { label: '已开票', value: 'issued' },
                { label: '未开票', value: 'not_issued' },
              ],
            },
          },
          {
            field: 'invoiceType',
            title: '开票类型',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [
                { label: '蓝票', value: 'blue' },
                { label: '红票', value: 'red' },
              ],
            },
          },
          {
            field: 'invoiceSerialNo',
            title: '开票流水号',
            element: 'el-input',
            props: {
              placeholder: '请输入',
            },
          },
          {
            field: 'invoiceApplicant',
            title: '开票申请人',
            element: 'el-input',
            props: {
              placeholder: '请输入',
            },
          },
          {
            field: 'applyInvoiceDate',
            title: '申请开票日期',
            element: 'el-date-picker',
            props: {
              type: 'daterange',
              startPlaceholder: '开始时间',
              endPlaceholder: '结束时间',
            },
          },
          {
            field: 'buyerName',
            title: '购方名称',
            element: 'el-input',
            props: {
              placeholder: '请输入',
            },
          },
          {
            field: 'businessType',
            title: '业务类型',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [
                { label: '充电服务', value: 'charging' },
                { label: '其他服务', value: 'other' },
              ],
            },
          },
          {
            field: 'invoiceNumber',
            title: '发票编号',
            element: 'el-input',
            props: {
              placeholder: '请输入',
            },
          },
          {
            field: 'redPerson',
            title: '冲红人',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [
                { label: '张三', value: 'zhangsan' },
                { label: '李四', value: 'lisi' },
              ],
            },
          },
          {
            field: 'redDate',
            title: '冲红日期',
            element: 'el-date-picker',
            props: {
              type: 'daterange',
              startPlaceholder: '开始时间',
              endPlaceholder: '结束时间',
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  mounted() {
    this.loadData();
  },
  methods: {
    loadData() {},
    handleOutput() {},
    // 订单详情
    handleDetail(row) {
      this.$router.push({
        path: '/v2g-charging-web/invoicesManagement/invoiceRecordQuery/orderDetails',
        //   query: {
        //     operatorId: row.operatorId,
        //     type: 'detail',
        //   },
      });
    },
    // 预览
    handlePreview(row) {
      this.previewData.open = true;
    },
    // 冲红
    handleRed(row) {
      this.selectData = row;
      this.redData.open = true;
    },
    // 冲红弹窗关闭
    handleCancel() {
      this.redData.open = false;
    },
    // 弹窗确定
    submitFileForm() {
      this.$refs.redData.validate((valid) => {
        if (valid) {
          console.log(this.redData);
          this.handleCancel();
        }
      });
    },
    // 重新开具
    handleReissue(row) {},
  },
};
</script>

<style lang="scss" scoped>
.table-wrap {
  background: #fff;
  padding: 20px;
  border-radius: 4px;

  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }

  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }

  .red-invoiceType {
    color: red;
    padding: 3px;
    border-radius: 6px;
    background: #ff000024;
    width: 80px;
  }

  .blue-invoiceType {
    color: blue;
    padding: 3px;
    border-radius: 6px;
    background: #0000ff24;
    width: 80px;
  }
}
</style>
