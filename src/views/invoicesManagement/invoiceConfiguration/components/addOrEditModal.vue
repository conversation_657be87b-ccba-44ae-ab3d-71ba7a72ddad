<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    @close="handleCancel"
    width="80%"
    :destroy-on-close="true"
  >
    <div>
      <el-form :model="form" :rules="rules" ref="form" label-position="top">
        <el-row
          :gutter="20"
          type="flex"
          justify="start"
          style="flex-wrap: wrap; flex-direction: row"
        >
          <el-col :span="12">
            <el-form-item label="商品编码" prop="code">
              <el-input
                v-model="form.code"
                placeholder="请输入商品编码"
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="商品名称" prop="name">
              <el-input
                v-model="form.name"
                placeholder="请输入商品名称"
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="税率(%)" prop="taxRate">
              <el-input
                v-model.number="form.taxRate"
                placeholder="请输入税率(%)"
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="规格型号" prop="specification">
              <el-input
                v-model="form.specification"
                placeholder="请输入规格型号"
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="税费分类编码" prop="goodsCode">
              <el-input
                v-model="form.goodsCode"
                placeholder="请输入税费分类编码"
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="税收分类名称" prop="goodsName">
              <el-input
                v-model="form.goodsName"
                placeholder="请输入税收分类名称"
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="费用类型" prop="zeroTaxType">
              <el-select
                v-model="form.zeroTaxType"
                placeholder="请选择费用类型"
                style="width: 100%"
              >
                <el-option
                  v-for="item in zeroTaxTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="含税标志" prop="taxFlag">
              <el-select
                v-model="form.taxFlag"
                placeholder="请选择含税标志"
                style="width: 100%"
              >
                <el-option label="是" value="是"></el-option>
                <el-option label="否" value="否"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="计量单位" prop="measureUnit">
              <el-input
                v-model="form.measureUnit"
                placeholder="请输入计量单位"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" @click="handleConfirm">确 定</el-button>
    </div>
  </el-dialog>
</template>
<script>
import {
  addGoodsConfig,
  updateGoodsConfig,
} from '@/api/invoicesManagement/invoiceConfiguration';

export default {
  dicts: [
    'ls_zero_tax_flag', // 费用类型
  ],
  props: {
    dialogTitle: {
      type: String,
      default: '充电站选择',
    },
  },
  components: {},
  data() {
    return {
      dialogVisible: false,
      form: {
        code: '', // 商品编码
        name: '', // 商品名称
        taxRate: '', // 税率(%)
        specification: '', // 规格型号
        goodsCode: '', // 税费分类编码
        goodsName: '', // 税收分类名称
        zeroTaxType: '', // 费用类型
        taxFlag: '', // 含税标志（非必填）
        measureUnit: '', // 计量单位（非必填）
      },
      rules: {
        code: [{ required: true, message: '请输入商品编码', trigger: 'blur' }],
        name: [{ required: true, message: '请输入商品名称', trigger: 'blur' }],
        taxRate: [
          { required: true, message: '请输入税率(%)', trigger: 'blur' },
          { type: 'number', message: '税率必须为数字', trigger: 'blur' },
        ],
        specification: [
          { required: true, message: '请输入规格型号', trigger: 'blur' },
        ],
        goodsCode: [
          { required: true, message: '请输入税费分类编码', trigger: 'blur' },
        ],
        goodsName: [
          { required: true, message: '请输入税收分类名称', trigger: 'blur' },
        ],
        zeroTaxType: [
          { required: true, message: '请选择费用类型', trigger: 'change' },
        ],
      },
      zeroTaxTypeOptions: [],
    };
  },
  mounted() {
    this.zeroTaxTypeOptions = this.dict.type.ls_zero_tax_flag;
  },
  methods: {
    handleCancel() {
      this.dialogVisible = false;
    },
    handleConfirm() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          let params = {
            ...this.form,
          };
          if (params.id) {
            const [err, res] = await updateGoodsConfig(params);
            if (err) return;
            this.$message({
              message: '修改成功',
              type: 'success',
            });
          } else {
            const [err, res] = await addGoodsConfig(params);
            if (err) return;
            this.$message({
              message: '新增成功',
              type: 'success',
            });
          }

          this.$emit('confirm');
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped></style>
