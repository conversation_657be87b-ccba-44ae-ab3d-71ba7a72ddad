<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    @close="handleCancel"
    width="80%"
    :destroy-on-close="true"
  >
    <div>
      <el-form :model="form" :rules="rules" ref="form" label-position="top">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="商品编码" prop="goodsCode">
              <el-input
                v-model="form.goodsCode"
                placeholder="请输入商品编码"
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="商品名称" prop="goodsName">
              <el-input
                v-model="form.goodsName"
                placeholder="请输入商品名称"
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="税率(%)" prop="taxRate">
              <el-input
                v-model.number="form.taxRate"
                placeholder="请输入税率(%)"
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="规格型号" prop="specification">
              <el-input
                v-model="form.specification"
                placeholder="请输入规格型号"
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="税费分类编码" prop="taxCategoryCode">
              <el-input
                v-model="form.taxCategoryCode"
                placeholder="请输入税费分类编码"
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="税收分类名称" prop="taxCategoryName">
              <el-input
                v-model="form.taxCategoryName"
                placeholder="请输入税收分类名称"
              ></el-input>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="费用类型" prop="feeType">
              <el-select
                v-model="form.feeType"
                placeholder="请选择费用类型"
                style="width: 100%"
              >
                <el-option
                  v-for="item in feeTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="含税标志" prop="includeTaxFlag">
              <el-select
                v-model="form.includeTaxFlag"
                placeholder="请选择含税标志"
                style="width: 100%"
              >
                <el-option label="是" value="true"></el-option>
                <el-option label="否" value="false"></el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12">
            <el-form-item label="计量单位" prop="unit">
              <el-input
                v-model="form.unit"
                placeholder="请输入计量单位"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" @click="handleConfirm">确 定</el-button>
    </div>
  </el-dialog>
</template>
<script>
export default {
  props: {
    dialogTitle: {
      type: String,
      default: '充电站选择',
    },
  },
  components: {},
  data() {
    return {
      dialogVisible: false,
      form: {
        goodsCode: '', // 商品编码
        goodsName: '', // 商品名称
        taxRate: '', // 税率(%)
        specification: '', // 规格型号
        taxCategoryCode: '', // 税费分类编码
        taxCategoryName: '', // 税收分类名称
        feeType: '', // 费用类型
        includeTaxFlag: '', // 含税标志（非必填）
        unit: '', // 计量单位（非必填）
      },
      rules: {
        goodsCode: [
          { required: true, message: '请输入商品编码', trigger: 'blur' },
        ],
        goodsName: [
          { required: true, message: '请输入商品名称', trigger: 'blur' },
        ],
        taxRate: [
          { required: true, message: '请输入税率(%)', trigger: 'blur' },
          { type: 'number', message: '税率必须为数字', trigger: 'blur' },
        ],
        specification: [
          { required: true, message: '请输入规格型号', trigger: 'blur' },
        ],
        taxCategoryCode: [
          { required: true, message: '请输入税费分类编码', trigger: 'blur' },
        ],
        taxCategoryName: [
          { required: true, message: '请输入税收分类名称', trigger: 'blur' },
        ],
        feeType: [
          { required: true, message: '请选择费用类型', trigger: 'change' },
        ],
      },
      feeTypeOptions: [
        { label: '服务费', value: 'service_fee' },
        { label: '设备租赁费', value: 'rental_fee' },
        { label: '电费', value: 'electricity_fee' },
      ],
    };
  },
  mounted() {},
  methods: {
    handleCancel() {
      this.dialogVisible = false;
    },
    handleConfirm() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          this.$emit('confirm');
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped></style>
