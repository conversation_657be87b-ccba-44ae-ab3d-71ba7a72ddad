<template>
  <div class="container">
    <div class="info-card" style="margin-top: 0">
      <div class="card-head" style="margin-top: 0">
        <div class="before-icon"></div>
        <div class="card-head-text">企业信息配置</div>
        <div class="top-button-wrap">
          <el-button type="primary" @click="handleEdit()" v-if="!isEdit">
            编辑
          </el-button>
        </div>
      </div>
      <div class="card-head-after"></div>
      <div class="form-wrap">
        <el-form
          :model="enterpriseForm"
          :rules="enterpriseRules"
          ref="enterpriseForm"
          label-position="top"
        >
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                label="开票服务商"
                prop="stationSoc"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="enterpriseForm.serviceProvider"
                  placeholder="请选择开票服务商"
                  style="width: 100%"
                  :disabled="!isEdit"
                >
                  <el-option
                    v-for="item in serviceProviderList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="企业名称" prop="enterpriseName">
                <el-input
                  v-model="enterpriseForm.enterpriseName"
                  placeholder="请输入企业名称"
                  :disabled="!isEdit"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="纳税人识别号" prop="taxpayerId">
                <el-input
                  v-model="enterpriseForm.taxpayerId"
                  placeholder="请输入纳税人识别号"
                  :disabled="!isEdit"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="地址" prop="address">
                <el-input
                  v-model="enterpriseForm.address"
                  placeholder="请输入地址"
                  :disabled="!isEdit"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="电话" prop="phone">
                <el-input
                  v-model="enterpriseForm.phone"
                  placeholder="请输入电话"
                  :disabled="!isEdit"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="开户行" prop="bankName">
                <el-input
                  v-model="enterpriseForm.bankName"
                  placeholder="请输入开户行"
                  :disabled="!isEdit"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="开户账号" prop="bankAccount">
                <el-input
                  v-model="enterpriseForm.bankAccount"
                  placeholder="请输入开户账号"
                  :disabled="!isEdit"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="邮箱" prop="email">
                <el-input
                  v-model="enterpriseForm.email"
                  placeholder="请输入邮箱"
                  :disabled="!isEdit"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="备注" prop="remark">
                <el-input
                  v-model="enterpriseForm.remark"
                  placeholder="请输入备注"
                  :disabled="!isEdit"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="请求地址" prop="requestUrl">
                <el-input
                  v-model="enterpriseForm.requestUrl"
                  placeholder="请输入请求地址"
                  :disabled="!isEdit"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="AppKey" prop="appKey">
                <el-input
                  v-model="enterpriseForm.appKey"
                  placeholder="请输入AppKey"
                  :disabled="!isEdit"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="AppSecret" prop="appSecret">
                <el-input
                  v-model="enterpriseForm.appSecret"
                  placeholder="请输入AppSecret"
                  :disabled="!isEdit"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="开票回调" prop="invoiceCallback">
                <el-input
                  v-model="enterpriseForm.invoiceCallback"
                  placeholder="请输入开票回调地址"
                  :disabled="!isEdit"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <div class="info-card">
      <div class="card-head" style="margin-top: 0">
        <div class="before-icon"></div>
        <div class="card-head-text">开票信息配置</div>
      </div>
      <div class="card-head-after"></div>
      <div class="form-wrap">
        <el-form
          :model="invoicingForm"
          :rules="invoicingRules"
          ref="invoicingForm"
          label-position="top"
        >
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="开票人" prop="invoicer">
                <el-input
                  v-model="invoicingForm.invoicer"
                  placeholder="请输入开票人"
                  :disabled="!isEdit"
                ></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="收费人" prop="payee">
                <el-input
                  v-model="invoicingForm.payee"
                  placeholder="请输入收费人"
                  :disabled="!isEdit"
                ></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item label="复核人" prop="reviewer">
                <el-input
                  v-model="invoicingForm.reviewer"
                  placeholder="请输入复核人"
                  :disabled="!isEdit"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <div class="info-card" :style="isEdit ? 'margin-bottom: 100px' : ''">
      <div class="card-head" style="margin-top: 0">
        <div class="before-icon"></div>
        <div class="card-head-text">开票商品配置</div>
        <div class="top-button-wrap" v-if="isEdit">
          <el-button type="primary" @click="handleRowAdd()">新增</el-button>
        </div>
      </div>
      <div class="card-head-after"></div>
      <div class="form-wrap">
        <BuseCrud
          :tableColumn="goodsTableColumn"
          :tableData="goodsTableData"
          :loading="loading"
          :pagerProps="{
            layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
          }"
          :tablePage="goodsTablePage"
          :modalConfig="goodsModalConfig"
        >
          <template slot="operate" slot-scope="{ row }">
            <div class="menu-box">
              <el-button type="primary" plain @click="handleRowEdit(row)">
                编辑
              </el-button>
              <el-button type="danger" plain @click="handleRowDelete(row)">
                删除
              </el-button>
            </div>
          </template>
        </BuseCrud>
      </div>
    </div>

    <div class="bottom-wrap" v-if="isEdit">
      <el-button @click="() => handleCancel()">取消</el-button>
      <el-button type="primary" @click="() => handleConfirm()">保存</el-button>
    </div>

    <AddOrEditModal
      ref="addOrEditModal"
      @confirm="handleModalConfirm"
      :dialogTitle="dialogTitle"
    />
  </div>
</template>

<script>
import AddOrEditModal from './components/addOrEditModal';

export default {
  components: {
    AddOrEditModal,
  },
  data() {
    return {
      isEdit: false,
      formLabelWidth: '120px',
      // 企业信息配置
      enterpriseForm: {
        serviceProvider: '', // 开票服务商（已存在）
        enterpriseName: '', // 企业名称
        taxpayerId: '', // 纳税人识别号
        address: '', // 地址
        phone: '', // 电话
        bankName: '', // 开户行
        bankAccount: '', // 开户账号
        email: '', // 邮箱
        remark: '', // 备注（非必填）
        requestUrl: '', // 请求地址
        appKey: '', // AppKey
        appSecret: '', // AppSecret
        invoiceCallback: '', // 开票回调
      },
      enterpriseRules: {
        serviceProvider: [
          { required: true, message: '请选择开票服务商', trigger: 'change' },
        ],
        enterpriseName: [
          { required: true, message: '请输入企业名称', trigger: 'blur' },
        ],
        taxpayerId: [
          { required: true, message: '请输入纳税人识别号', trigger: 'blur' },
        ],
        address: [{ required: true, message: '请输入地址', trigger: 'blur' }],
        phone: [{ required: true, message: '请输入电话', trigger: 'blur' }],
        bankName: [
          { required: true, message: '请输入开户行', trigger: 'blur' },
        ],
        bankAccount: [
          { required: true, message: '请输入开户账号', trigger: 'blur' },
        ],
        email: [
          { required: true, message: '请输入邮箱', trigger: 'blur' },
          {
            type: 'email',
            message: '请输入正确的邮箱格式',
            trigger: ['blur', 'change'],
          },
        ],
        requestUrl: [
          { required: true, message: '请输入请求地址', trigger: 'blur' },
        ],
        appKey: [{ required: true, message: '请输入AppKey', trigger: 'blur' }],
        appSecret: [
          { required: true, message: '请输入AppSecret', trigger: 'blur' },
        ],
        invoiceCallback: [
          { required: true, message: '请输入开票回调地址', trigger: 'blur' },
        ],
      },
      // 开票信息配置
      invoicingForm: {
        invoicer: '', // 开票人
        payee: '', // 收费人
        reviewer: '', // 复核人
      },
      invoicingRules: {
        invoicer: [
          { required: true, message: '请输入开票人', trigger: 'blur' },
        ],
        payee: [{ required: true, message: '请输入收费人', trigger: 'blur' }],
        reviewer: [
          { required: true, message: '请输入复核人', trigger: 'blur' },
        ],
      },
      // 开票商品配置
      loading: false,
      goodsTableData: [
        {
          goodsCode: 'PROD001',
          goodsName: '充电服务费',
          taxRate: '13',
          specification: '标准版',
          unit: '次',
          taxCategoryCode: '*********',
          taxCategoryName: '电力产品',
          includeTaxFlag: true,
        },
        {
          goodsCode: 'PROD002',
          goodsName: '电池租赁服务',
          taxRate: '9',
          specification: '高级版',
          unit: '月',
          taxCategoryCode: '*********',
          taxCategoryName: '设备租赁',
          includeTaxFlag: false,
        },
      ],
      goodsTableColumn: [
        { type: 'seq', title: '序号', minWidth: 60 },
        { field: 'goodsCode', title: '商品编号', minWidth: 120 },
        { field: 'goodsName', title: '商品名称', minWidth: 150 },
        { field: 'taxRate', title: '税率(%)', minWidth: 100 },
        { field: 'specification', title: '规格型号', minWidth: 140 },
        { field: 'unit', title: '计量单位', minWidth: 120 },
        { field: 'taxCategoryCode', title: '税收分类编码', minWidth: 150 },
        { field: 'taxCategoryName', title: '税收分类名称', minWidth: 160 },
        {
          field: 'includeTaxFlag',
          title: '含税标志',
          minWidth: 120,
          slots: {
            default: ({ row }) => {
              return row.includeTaxFlag ? '是' : '否';
            },
          },
        },

        // {
        //   field: 'operation',
        //   title: '操作',
        //   minWidth: 120,
        //   slots: {
        //     default: () => {
        //       return [
        //         <el-button type="text" onClick={() => this.handleEditGoods()}>
        //           编辑
        //         </el-button>,
        //         <el-button type="text" onClick={() => this.handleDeleteGoods()}>
        //           删除
        //         </el-button>,
        //       ];
        //     },
        //   },
        // },
      ],
      goodsTablePage: { total: 0, currentPage: 1, pageSize: 10 },
      goodsModalConfig: {
        addBtn: false,
        editBtn: false,
        delBtn: false,
        viewBtn: false,
        menu: false,
      },
      serviceProviderList: [
        {
          label: '商家1',
          value: 1,
        },
        {
          label: '商家2',
          value: 2,
        },
      ],
      dialogTitle: '',
    };
  },
  mounted() {
    this.getData();
  },
  methods: {
    getData() {},
    // 编辑
    handleEdit() {
      this.goodsTableColumn.push({
        title: '操作',
        slots: { default: 'operate' },
        width: 200,
        align: 'center',
        fixed: 'right',
      });
      this.isEdit = true;
    },
    // 新增行数据
    handleRowAdd() {
      this.$refs.addOrEditModal.form = {
        goodsCode: '', // 商品编码
        goodsName: '', // 商品名称
        taxRate: '', // 税率(%)
        specification: '', // 规格型号
        taxCategoryCode: '', // 税费分类编码
        taxCategoryName: '', // 税收分类名称
        feeType: '', // 费用类型
        includeTaxFlag: '', // 含税标志（非必填）
        unit: '', // 计量单位（非必填）
      };
      this.dialogTitle = '新增';
      this.$refs.addOrEditModal.dialogVisible = true;
    },
    // 编辑行数据
    handleRowEdit(row) {
      console.log(row);
      this.$refs.addOrEditModal.form = { ...row };
      this.dialogTitle = '编辑';
      this.$refs.addOrEditModal.dialogVisible = true;
    },
    // 删除行数据
    handleRowDelete(row) {
      this.$confirm('确定删除该充电站吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.$message({
            message: '删除成功',
            type: 'success',
          });
          this.getData();
        })
        .catch(() => {});
    },
    // 取消
    handleCancel() {
      this.isEdit = false;
      const obj = this.goodsTableColumn;
      const keys = Object.keys(obj);
      const lastKey = keys[keys.length - 1];
      if (lastKey) {
        delete obj[lastKey];
      }
      this.goodsTableColumn = obj;
    },
    // 保存
    handleConfirm() {
      let enterpriseFormValid = false;
      let invoicingFormValid = false;
      this.$refs.enterpriseForm.validate(async (valid) => {
        enterpriseFormValid = valid;
      });
      this.$refs.invoicingForm.validate(async (valid) => {
        invoicingFormValid = valid;
      });
      if (enterpriseFormValid && invoicingFormValid) {
        this.handleCancel();
      }
    },
    // 弹窗确定
    handleModalConfirm() {
      this.$refs.addOrEditModal.dialogVisible = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.info-card {
  margin: 16px 0 16px 0;
  border-radius: 5px;
  border: 1px solid #fff;
  overflow: hidden;
  background-color: #fff;
  .form-wrap {
    padding: 0 16px 16px 16px;
    .custom-header {
      background: -webkit-gradient(
          linear,
          left top,
          left bottom,
          from(rgba(0, 149, 255, 0.5)),
          to(rgba(87, 152, 255, 0))
        ),
        #f5faff;
      background: linear-gradient(
          180deg,
          rgba(0, 149, 255, 0.5) 0%,
          rgba(87, 152, 255, 0) 100%
        ),
        #f5faff;
      background-repeat: no-repeat;
    }
  }
}

.card-head {
  // position: relative;
  height: 56px;
  padding: 0 16px;
  display: flex;
  align-items: center;
  margin-top: -20px;
  .card-head-text {
    flex: 1;
    width: 520px;
    height: 26px;
    background-image: url('~@/assets/images/bg-title.png');
    background-size: 520px 26px;
    background-repeat: no-repeat;
    font-size: 20px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px;
    padding-left: 36px;
    color: #21252e;
    &::before {
      content: '';
      position: absolute;
      top: 0;
      bottom: 0;
      left: -3px; /* 调整这个值来改变边框的宽度 */
      width: 0;
      border-top: 3px solid transparent;
      border-bottom: 3px solid transparent;
      border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
    }
  }
}

.bottom-wrap {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 86px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  background-color: #ffffff;
  padding-right: 32px;
  box-sizing: border-box;
}
</style>
