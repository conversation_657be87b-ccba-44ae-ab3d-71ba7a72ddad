<template>
  <el-dialog
    :title="dialogTitle"
    :visible.sync="dialogVisible"
    @close="handleCancel"
    width="80%"
    :destroy-on-close="true"
  >
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :pagerProps="pagerProps"
      :modalConfig="modalConfig"
      :tableOn="{
        'checkbox-change': handleCheckboxChange,
        'checkbox-all': handleCheckboxChange,
      }"
      @loadData="loadData"
    ></BuseCrud>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" @click="handleConfirm">确 定</el-button>
    </div>
  </el-dialog>
</template>
<script>
import {
  getStationList,
  getAssetUnit,
  getOperationUnit,
  getMaintenanceUnit,
} from '@/api/station/index';

export default {
  props: {
    dialogTitle: {
      type: String,
      default: '充电站选择',
    },
  },
  components: {},
  dicts: [
    'ls_charging_station_type', // 站点类型
    'ls_charging_construction', // 建设场所
    'ls_charging_operation_mode', // 运营模式
    'ls_charging_asset_property', // 资产属性
    'ls_charging_station_access_type', // 充电站接入类型
    'ls_charging_operation_status', // 运营状态
    'ls_charging_station_source', // 数据来源
    'ls_charging_status', // 是否状态
    'ls_charging_adjustable_type', // 可控类型
    'ls_charging_parking_charge_type', // 停车场收费类型
    'ls_charging_area_type', // 所属区域
    'ls_charging_contracted_unit', // 签约单位 投资主体 供电机构
  ],

  data() {
    return {
      dialogVisible: false,
      operationStatusList: [],

      assetUnitList: [], // 资产单位列表
      operationUnitList: [], // 运营单位列表
      maintenanceUnitList: [], // 运维单位列表

      tablePage: { total: 0, currentPage: 1, pageSize: 10 },

      loading: false,
      stationList: [],
      tableColumn: [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
        },
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60, // 最小宽度
        },
        {
          field: 'stationNo',
          title: '充电站编号',
          minWidth: 120, // 最小宽度
        },
        {
          field: 'stationName',
          title: '充电站名称',
          minWidth: 150, // 最小宽度
        },
        {
          field: 'stationType',
          title: '站点类型',
          minWidth: 100, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_station_type,
              cellValue
            );
          },
        },
        {
          field: 'operationMode',
          title: '运营模式',
          minWidth: 100, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_operation_mode,
              cellValue
            );
          },
        },
        {
          field: 'assetProperty',
          title: '资产属性',
          minWidth: 100, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_asset_property,
              cellValue
            );
          },
        },
        {
          field: 'stationAccessType',
          title: '接入方式',
          minWidth: 100, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_station_access_type,
              cellValue
            );
          },
        },
        {
          field: 'pileNum',
          title: '充电桩数量',
          minWidth: 120, // 最小宽度
        },
        // {
        //   field: 'isAdjustable',
        //   title: '是否参与调控',
        //   minWidth: 120, // 最小宽度
        //   formatter: ({ cellValue }) => {
        //     return this.selectDictLabel(
        //       this.dict.type.ls_charging_status,
        //       cellValue
        //     );
        //   },
        // },
        // {
        //   field: 'adjustableType',
        //   title: '可控类型',
        //   minWidth: 100, // 最小宽度
        //   formatter: ({ cellValue }) => {
        //     return this.selectDictLabel(
        //       this.dict.type.ls_charging_adjustable_type,
        //       cellValue
        //     );
        //   },
        // },
        // {
        //   field: 'unifiedConstruction',
        //   title: '是否统建统服',
        //   minWidth: 120, // 最小宽度
        //   formatter: ({ cellValue }) => {
        //     return this.selectDictLabel(
        //       this.dict.type.ls_charging_status,
        //       cellValue
        //     );
        //   },
        // },
        {
          field: 'construction',
          title: '建设场所',
          minWidth: 120, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_construction,
              cellValue
            );
          },
        },
        {
          field: 'areaType',
          title: '所属区域',
          minWidth: 120, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_area_type,
              cellValue
            );
          },
        },

        {
          field: 'stationAddress',
          title: '充电站地址',
          minWidth: 200, // 最小宽度
        },
        // {
        //   title: '资产单位',
        //   field: 'assetUnitName',
        //   minWidth: 120, // 最小宽度
        // },
        // {
        //   title: '运营单位',
        //   field: 'operatingUnitName',
        //   minWidth: 120, // 最小宽度
        // },
        // {
        //   title: '运维单位',
        //   field: 'maintenanceUnitName',
        //   minWidth: 120, // 最小宽度
        // },
        {
          title: '数据来源',
          field: 'stationSource',
          minWidth: 120, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_station_source,
              cellValue
            );
          },
        },
      ],
      tableData: [],
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      params: {
        stationName: '',
        stationType: '',
        operationMode: '',
        assetAttribute: '',
        assetType: '',
        // isRegulation: '',
        controlType: '',
        // isTjtf: '',
        constructionSite: '',
        dataSources: '',
        // operationUnit: '',
        // assetUnit: '',
        // operationMaintainUnit: '',
      },
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'stationName',
            title: '充电站名称',
            element: 'el-input',
          },
          {
            field: 'stationType',
            title: '站点类型',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_charging_station_type,
            },
          },
          {
            field: 'operationMode',
            title: '运营模式',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_charging_operation_mode,
            },
          },
          {
            field: 'assetAttribute',
            title: '资产属性',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_charging_asset_property,
            },
          },
          {
            field: 'assetType',
            title: '接入方式',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_charging_station_access_type,
            },
          },
          // {
          //   field: 'isRegulation',
          //   title: '是否参与调控',
          //   element: 'el-select',
          //   props: {
          //     options: this.dict.type.ls_charging_status,
          //   },
          // },
          // {
          //   field: 'controlType',
          //   title: '可控类型',
          //   element: 'el-select',
          //   props: {
          //     options: this.dict.type.ls_charging_adjustable_type,
          //   },
          // },
          // {
          //   field: 'isTjtf',
          //   title: '是否统建统服',
          //   element: 'el-select',
          //   props: {
          //     options: this.dict.type.ls_charging_status,
          //   },
          // },
          {
            field: 'constructionSite',
            title: '建设场所',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_charging_construction,
            },
          },
          {
            field: 'dataSources',
            title: '数据来源',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_charging_station_source,
            },
          },
          // {
          //   field: 'operationUnit',
          //   title: '运营单位',
          //   element: 'el-select',
          //   props: {
          //     options: this.operationUnitList,
          //   },
          // },
          // {
          //   field: 'assetUnit',
          //   title: '资产单位',
          //   element: 'el-select',
          //   props: {
          //     options: this.assetUnitList,
          //   },
          // },
          // {
          //   field: 'operationMaintainUnit',
          //   title: '运维单位',
          //   element: 'el-select',
          //   props: {
          //     options: this.maintenanceUnitList,
          //   },
          // },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  mounted() {
    this.loadData();

    this.getAssetUnit();
    this.getOperationUnit();
    this.getMaintenanceUnit();
  },
  methods: {
    // 获取资产单位
    async getAssetUnit() {
      const [err, res] = await getAssetUnit({});
      if (err) return;
      this.assetUnitList = res.data;
    },

    // 获取运营单位
    async getOperationUnit() {
      const [err, res] = await getOperationUnit({});
      if (err) return;
      this.operationUnitList = res.data;
    },

    // 获取运维单位
    async getMaintenanceUnit() {
      const [err, res] = await getMaintenanceUnit({});
      if (err) return;
      this.maintenanceUnitList = res.data;
    },

    handleCancel() {
      this.dialogVisible = false;
    },

    // 新增按钮防抖
    handleConfirm: _.debounce(function () {
      if (!this.stationList.length) {
        this.$message.warning('请先选择站点');
      } else {
        this.$emit('confirm', this.stationList);
        this.stationList = [];
        this.dialogVisible = false;
      }
    }, 300),

    async loadData() {
      const {
        stationName,
        stationType,
        operationMode,
        assetAttribute,
        assetType,
        // isRegulation,
        // controlType,
        // isTjtf,
        constructionSite,
        dataSources,
        // operationUnit,
        // assetUnit,
        // operationMaintainUnit,
      } = this.filterOptions.params;

      this.loading = true;
      const [err, res] = await getStationList({
        stationName,
        stationType,
        operationMode,
        assetProperty: assetAttribute,
        stationAccessType: assetType,
        // assetUnit,
        // operatingUnit: operationUnit,
        // maintenanceUnit: operationMaintainUnit,
        stationSource: dataSources,
        // isAdjustable: isRegulation,
        // adjustableType: controlType,
        // unifiedConstruction: isTjtf,
        construction: constructionSite,
        operationStatusList: this.operationStatusList,

        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      });

      this.loading = false;
      if (err) return;
      const { data, total } = res;

      this.tableData = data;
      this.tablePage.total = total;
    },

    handleCheckboxChange({ records }) {
      console.log('选中的记录:', records);
      this.stationList = records;
    },
  },
};
</script>
<style lang="scss" scoped>
::v-deep .el-form-item__content {
  display: flex !important;
}
</style>
