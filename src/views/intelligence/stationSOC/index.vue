<template>
  <div class="container">
    <div class="table-wrap">
      <BuseCrud
        ref="crud"
        :loading="loading"
        :filterOptions="filterOptions"
        :tablePage="tablePage"
        :tableColumn="tableColumn"
        :tableData="tableData"
        :pagerProps="pagerProps"
        :modalConfig="modalConfig"
         class="buse-wrap-organization"
        @loadData="loadData"
      >
        <template slot="defaultHeader">
          <div class="card-head">
            <div class="card-head-text">场站SOC配置</div>
            <div class="top-button-wrap">
              <el-button
                type="primary"
                icon="el-icon-upload2"
                @click="handleInput"
              >
                导入
              </el-button>
              <el-button
                type="primary"
                icon="el-icon-plus"
                @click="handleAdd('01')"
              >
                新增
              </el-button>
            </div>
          </div>
          <div class="card-head-after"></div>
        </template>
        <template slot="stationSoc" slot-scope="{ row }">
          <div>{{ row.stationSoc }}%</div>
        </template>
        <template slot="validStartTime" slot-scope="{ row }">
          <div>{{ row.validStartTime }}-{{ row.validEndTime }}</div>
        </template>
        <template slot="operate" slot-scope="{ row }">
          <div class="menu-box">
            <el-button
              type="primary"
              plain
              @click="handleEdit(row)"
              v-if="row.status === '03'"
            >
              编辑
            </el-button>
            <el-button
              type="primary"
              plain
              v-if="row.status === '01' || row.status === '02'"
              @click="deactivate(row)"
            >
              停用
            </el-button>
            <el-button
              type="primary"
              plain
              v-if="row.status === '03'"
              @click="enable(row)"
            >
              启用
            </el-button>
            <el-button type="danger" plain @click="handleDelete(row)">
              删除
            </el-button>
          </div>
        </template>
      </BuseCrud>
    </div>
    <!-- 导入对话框 -->
    <el-dialog
      :title="upload.title"
      :visible.sync="upload.open"
      width="760px"
      height="398px"
      append-to-body
      @close="handleCancel"
    >
      <div>模板下载：</div>
      <div class="box link-box">
        <el-link
          type="primary"
          :underline="false"
          style="font-size: 16px; vertical-align: baseline"
          @click="downloadTemplate"
        >
          导入模板.xlsx
        </el-link>
      </div>
      <div>上传文件：</div>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :auto-upload="false"
        :data="upload.data"
        :on-exceed="handleExceed"
        :on-error="handleError"
        :on-success="handleSuccess"
        :http-request="customUpload"
      >
        <div class="box el-upload__text">
          <img
            class="upload-icon"
            src="@/assets/icons/responseAndRegulation/upload.png"
          />
          选择要导入上传的文件
        </div>
      </el-upload>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getStationSOCList,
  getStationList,
  changeStatus,
  deleteSoc,
  uploadClearingResult,
} from '@/api/stationSOC/index.js';
import { getToken } from '@/utils/auth';

export default {
  dicts: [
    'ls_charging_battery_type', // 电池类型
    'ls_charging_soc_config_status', // 状态
  ],
  data() {
    return {
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [],
      tableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
        },
        {
          field: 'stationName',
          title: '场站名称',
          width: 200,
        },
        {
          // field: 'stationSoc',
          title: '场站SOC',
          slots: { default: 'stationSoc' },
          width: 120,
        },
        {
          field: 'batterySocSummary',
          title: '电池SOC',
          width: 300,
        },
        {
          title: '有效时间',
          slots: { default: 'validStartTime' },
          width: 300,
        },
        {
          field: 'statusLabel',
          title: '生效状态',
          width: 120,
          //   formatter: ({ cellValue }) => {
          //     return this.selectDictLabel(
          //       this.dict.type.ls_charging_soc_config_status,
          //       cellValue
          //     );
          //   },
        },
        {
          field: 'createdBy',
          title: '创建人',
          width: 120,
        },
        {
          title: '操作',
          slots: { default: 'operate' },
          width: 300,
          align: 'center',
          fixed: 'right',
        },
      ],
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      params: {
        stationId: '',
        batteryType: '',
        status: '',
      },
      stationList: [],
      stationLoading: false,
      // 导入参数
      upload: {
        // 是否显示弹出层（用户导入）
        open: false,
        // 弹出层标题（用户导入）
        title: '',
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: 'Bearer ' + getToken() },
        // 上传的地址
        url:
          process.env.VUE_APP_BASE_API +
          '/vehicle-charging-admin/insp/soc/import',
        data: {
          requireId: '',
        },
      },
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'stationId',
            title: '场站名称',
            element: 'el-select',
            props: {
              options: this.stationList,
              filterable: true,
              remote: true,
              remoteMethod: this.debouncedStationSearch,
              loading: this.stationLoading,
            },
          },
          {
            field: 'batteryType',
            title: '电池类型',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.dict.type.ls_charging_battery_type,
            },
          },
          {
            field: 'status',
            title: '状态',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.dict.type.ls_charging_soc_config_status,
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  mounted() {
    this.loadData();
  },
  methods: {
    // 场站名称
    async debouncedStationSearch(query) {
      // console.log(query, 'query');
      if (query !== '') {
        this.stationLoading = true;
        setTimeout(async () => {
          const [err, res] = await getStationList({
            stationName: query,
          });

          if (err) return;
          this.stationLoading = false;
          this.stationList = res.data.map((item) => ({
            label: item.stationName,
            value: item.stationId,
          }));
        }, 200);
      } else {
        this.stationList = [];
      }
    },
    async loadData() {
      this.loading = true;
      let params = {
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
        stationId: this.params.stationId,
        batteryType: this.params.batteryType,
        status: this.params.status,
      };
      const [err, res] = await getStationSOCList(params);
      this.loading = false;
      if (err) {
        return;
      }
      const { data, total } = res;
      this.tableData = data;
      this.tablePage.total = total;
    },
    // 导入
    handleInput() {
      this.upload.title = '导入';
      this.upload.open = true;
    },
    // 导入关闭
    handleCancel() {
      this.upload.open = false;
      this.$refs.upload.clearFiles();
    },
    // 下载模板
    downloadTemplate() {
      this.download(
        '/vehicle-charging-admin/insp/soc/import/template',
        {},
        `场站SOC导入模板.xlsx`
      );
    },
    // 用户选择的文件数量超过 limit 限制
    handleExceed(files, fileList) {
      console.log(files, fileList);
      // 清空已选文件列表
      this.$refs.upload.clearFiles();
      // 手动添加新选择的文件（首个文件）
      this.$refs.upload.handleStart(files[0]);
    },
    // 文件上传失败
    handleError(err, file, fileList) {
      console.log('文件上传失败', err);
      // this.$message.error('接口请求失败');
    },
    // 文件上传成功
    handleSuccess(res, file, fileList) {
      console.log('文件上传成功', res, file, fileList);
      // if (res.code == 10000) {
      //   this.$message.success('导入成功');
      // } else {
      //   this.$message.error(res.subMsg);
      // }
    },
    async customUpload({ action, file, data }) {
      this.upload.isUploading = true;
      this.upload.open = false;
      const formData = new FormData();
      formData.append('file', file);
      try {
        const [err, res] = await uploadClearingResult(formData);
        if (err) return;
        if (res.success) {
          // console.log('导入res', res);
          this.$message.success('导入成功');
          this.handleCancel();
          this.loadData();
        } else {
          this.$message.error(res.subMsg || '导入失败');
        }
      } finally {
        this.upload.isUploading = false;
      }
    },
    // 提交上传文件
    submitFileForm() {
      // this.upload.data.requireId = this.requireId;
      console.log('upload data', this.upload.data);
      this.$refs.upload.submit();
      // setTimeout(() => {
      //   this.handleCancel(), this.loadData();
      // }, 1000);
    },
    // 新增
    handleAdd(status) {
      this.$router.push({
        path: '/v2g-charging/intelligence/stationSOC/apply',
        query: {
          status,
        },
      });
    },
    // 编辑
    handleEdit(val) {
      this.$router.push({
        path: '/v2g-charging/intelligence/stationSOC/apply',
        query: {
          // type: 'detail',
          // applyNo: val.socConfigId,
          status: '02',
          operateId: val.socConfigId,
        },
      });
    },
    // 删除
    handleDelete(val) {
      this.$confirm('确定删除此场站吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          let params = {
            operateId: val.socConfigId,
          };
          const [err, res] = await deleteSoc(params);
          if (err) {
            return;
          }
          this.$message.success('删除成功');
          this.loadData();
        })
        .catch(() => {});
    },
    // 停用
    async deactivate(val) {
      this.$confirm('确定停用此场站吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          let params = {
            operateId: val.socConfigId,
          };
          const [err, res] = await changeStatus(params);
          if (err) {
            return;
          }
          this.$message.success('停用成功');
          this.loadData();
        })
        .catch(() => {});
    },
    // 启用
    async enable(val) {
      this.$confirm('确定启用此场站吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          let params = {
            operateId: val.socConfigId,
          };
          const [err, res] = await changeStatus(params);
          if (err) {
            return;
          }
          this.$message.success('启用成功');
          this.loadData();
        })
        .catch(() => {});
    },
  },
};
</script>

<style lang="scss" scoped>
.table-wrap {
  // background: #fff;
  // padding: 20px;
  // border-radius: 4px;

  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }

  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }

  ::v-deep .el-upload {
    text-align: left;
    font-size: 16px;
  }
  .box {
    margin-top: 4px;
    width: 688px;
    height: 36px;
    line-height: 36px;
    border-radius: 2px;
    text-align: center;
  }
  .link-box {
    margin-bottom: 38px;
    border: 1px solid #dfe1e5;
  }
  ::v-deep .el-upload__text {
    border: 1px dashed #dfe1e5;
  }
  .upload-icon {
    width: 14px;
    height: 14px;
    margin-right: 6px;
  }
}
</style>
