<template>
  <div class="container container-float" style="padding: 0 0 100px 0">
    <!-- <div class="device-head">
      <img
        src="@/assets/station/station-detail-top-icon.png"
        class="device-head-icon"
      />
      <div class="device-head-title">
        {{
          type === 'detail'
            ? `充电站${applyInfo.applyType}审核详情`
            : `充电站${applyInfo.applyType}审核`
        }}
      </div>

      <el-button type="primary" @click="drawer = true">审核轨迹</el-button>
    </div> -->

    <div class="info-card">
      <div class="card-head" style="margin-bottom: 8px">
        <div class="before-icon"></div>
        <div class="card-head-text">申请信息</div>
      </div>

      <div class="form-wrap">
        <el-row :gutter="20" style="margin-bottom: 24px">
          <el-col :span="8">
            <div style="display: flex">
              <div class="info-title">申请编码：</div>
              <div class="info-detail">{{ applyInfo.applyId }}</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div style="display: flex">
              <div class="info-title">申请类型：</div>
              <div class="info-detail">{{ applyInfo.applyType }}</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div style="display: flex">
              <div class="info-title">申请人：</div>
              <div class="info-detail">{{ applyInfo.applyer }}</div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20" style="margin-bottom: 24px">
          <el-col :span="8">
            <div style="display: flex">
              <div class="info-title">申请时间：</div>
              <div class="info-detail">{{ applyInfo.applyTime }}</div>
            </div>
          </el-col>

          <el-col :span="8" v-if="applyInfo.applyTypeInfo === '10'">
            <div style="display: flex">
              <div class="info-title">临时停运时长：</div>
              <div class="info-detail">
                {{ applyInfo.returnTimeNum }} {{ applyInfo.unit }}
              </div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20" style="margin-bottom: 24px">
          <el-col :span="24">
            <div style="display: flex">
              <div class="info-title">申请说明：</div>
              <div class="info-detail">{{ applyInfo.applyRemark }}</div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <div class="info-card">
      <div class="card-head" style="margin-bottom: 8px">
        <div class="before-icon"></div>
        <div class="card-head-text">充电站信息</div>
      </div>

      <div class="choose-info-wrap">
        已选择
        <div class="choose-number">{{ stationNumber }}</div>
        个充电站，
        <div class="choose-number">{{ pileNumber }}</div>
        个充电桩
      </div>
      <div class="form-wrap">
        <BuseCrud
          ref="crud"
          :loading="table.loading"
          :tablePage="table.page"
          :tableColumn="tableColumn"
          :tableData="table.data"
          :pagerProps="pagerProps"
          :modalConfig="modalConfig"
          @loadData="loadData"
        >
          <template slot="operate" slot-scope="{ row }">
            <div class="menu-box">
              <el-button type="primary" plain @click="hanleDetail(row)">
                详情
              </el-button>
            </div>
          </template>
        </BuseCrud>
      </div>
    </div>

    <div class="info-card" v-if="type !== 'detail'">
      <div class="card-head" style="margin-bottom: 8px">
        <div class="before-icon"></div>
        <div class="card-head-text">审核信息</div>
      </div>

      <div class="form-wrap">
        <el-form :model="form" :rules="rules" ref="form" label-position="top">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                label="申请类型"
                prop="result"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="form.result"
                  placeholder="请选择申请类型"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in dict.type.ls_charging_approval"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item
                label="申请说明"
                prop="remark"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-model="form.remark"
                  type="textarea"
                  :rows="3"
                  placeholder="请输入申请说明"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <div class="bottom-wrap" v-if="type !== 'detail'">
      <el-button @click="() => handleCancel()">取消</el-button>
      <el-button type="primary" @click="() => handleConfirm()">提交</el-button>
    </div>

    <el-drawer :visible.sync="drawer" :with-header="false" :size="825">
      <div class="draw-wrap">
        <div class="draw-card-head">
          <div class="card-head-text">审核轨迹</div>
          <div class="card-head-close" @click="onClickCloseDrawer"></div>
        </div>
        <div class="draw-card-head-after"></div>

        <div class="card-head" style="margin-bottom: 8px">
          <div class="before-icon"></div>
          <div class="card-head-text">申请信息</div>
        </div>

        <div class="approval-steps">
          <el-steps direction="vertical" :active="3" space="100px">
            <el-step v-for="(item, index) in examineList" :key="index">
              <!-- 自定义步骤图标 -->
              <template #icon>
                <div :class="`step-icon step-icon-${item.status}`">
                  {{ index + 1 }}
                </div>
              </template>

              <!-- 自定义步骤内容 -->
              <template #title>
                <div class="step-header">
                  <span class="title">{{ item.title }}</span>
                </div>
              </template>

              <template #description>
                <div
                  class="remark-box"
                  v-if="index !== 3 && item.status !== '04'"
                >
                  <div class="remark-icon"></div>
                  <!-- 处理人信息 -->
                  <div v-if="item.name" class="person-info">
                    <div class="person-title">
                      <span>{{ item.name }}</span>
                      <span class="identity">{{ item.identity }}</span>
                      <span>{{ item.department }}</span>
                    </div>

                    <div
                      :class="`person-info-status person-info-status-${item.status} `"
                    >
                      {{ statusObj[item.status] }}
                    </div>

                    <div v-if="item.remark" class="remark">
                      <span>审核意见：</span>
                      <span>{{ item.remark }}</span>
                    </div>
                  </div>

                  <div class="remark-time">
                    {{ item.time }}
                  </div>
                </div>
              </template>
            </el-step>
          </el-steps>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import {
  getAuditDetail,
  getStationList,
  stationAudit,
} from '@/api/station/index';

export default {
  components: {},
  dicts: [
    'ls_charging_apply_status', // 申请类型
    'ls_charging_return_time_unit', // 临时退运时长-单位
    'ls_charging_approval', // 审批状态
    'ls_charging_station_type', // 站点类型
    'ls_charging_construction', // 建设场所
    'ls_charging_operation_mode', // 运营模式
    'ls_charging_asset_property', // 资产属性
    'ls_charging_station_access_type', // 充电站接入类型
    'ls_charging_operation_status', // 运营状态
    'ls_charging_station_source', // 数据来源
    'ls_charging_status', // 是否状态
    'ls_charging_adjustable_type', // 可控类型
    'ls_charging_parking_charge_type', // 停车场收费类型
    'ls_charging_area_type', // 所属区域
    'ls_charging_contracted_unit', // 签约单位 投资主体 供电机构
  ],
  data() {
    return {
      type: 'index',
      applyNo: '',
      applyInfo: {
        applyId: '',
        applyType: '',
        applyer: '',
        applyTime: '',
        applyRemark: '',
        returnTimeNum: '',
        unit: '',
        applyTypeInfo: '',
      },
      stationNumber: '',
      pileNumber: '',

      stationIdList: [], // 站点id列表

      table: {
        loading: false,
        page: {
          total: 0,
          currentPage: 1,
          pageSize: 10,
        },
        data: [],
      },
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      tableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60, // 最小宽度
        },
        {
          field: 'stationNo',
          title: '充电站编号',
          minWidth: 120, // 最小宽度
        },
        {
          field: 'stationName',
          title: '充电站名称',
          minWidth: 150, // 最小宽度
        },
        {
          field: 'stationType',
          title: '站点类型',
          minWidth: 100, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_station_type,
              cellValue
            );
          },
        },
        {
          field: 'operationMode',
          title: '运营模式',
          minWidth: 100, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_operation_mode,
              cellValue
            );
          },
        },
        {
          field: 'assetProperty',
          title: '资产属性',
          minWidth: 100, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_asset_property,
              cellValue
            );
          },
        },
        {
          field: 'stationAccessType',
          title: '接入方式',
          minWidth: 100, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_station_access_type,
              cellValue
            );
          },
        },
        {
          field: 'pileNum',
          title: '充电桩数量',
          minWidth: 120, // 最小宽度
        },
        {
          field: 'isAdjustable',
          title: '是否参与调控',
          minWidth: 120, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_status,
              cellValue
            );
          },
        },
        {
          field: 'adjustableType',
          title: '可控类型',
          minWidth: 100, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_adjustable_type,
              cellValue
            );
          },
        },
        {
          field: 'unifiedConstruction',
          title: '是否统建统服',
          minWidth: 120, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_status,
              cellValue
            );
          },
        },
        {
          field: 'construction',
          title: '建设场所',
          minWidth: 120, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_construction,
              cellValue
            );
          },
        },
        {
          field: 'areaType',
          title: '所属区域',
          minWidth: 120, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_area_type,
              cellValue
            );
          },
        },
        {
          title: '资产单位',
          field: 'assetUnitName',
          minWidth: 120, // 最小宽度
        },
        {
          title: '运营单位',
          field: 'operatingUnitName',
          minWidth: 120, // 最小宽度
        },
        {
          title: '运维单位',
          field: 'maintenanceUnitName',
          minWidth: 120, // 最小宽度
        },
        {
          title: '签约单位',
          field: 'contractedUnit',
          minWidth: 120, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_contracted_unit,
              cellValue
            );
          },
        },

        {
          title: '数据来源',
          field: 'stationSource',
          minWidth: 120, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_station_source,
              cellValue
            );
          },
        },

        {
          title: '操作',
          slots: { default: 'operate' },
          width: 300,
          align: 'center',
          fixed: 'right',
        },
      ],

      form: {
        result: '',
        remark: '',
      },

      rules: {
        result: [
          { required: true, message: '请选择审核结果', trigger: 'blur' },
        ],
      },
      formLabelWidth: '120px',
      resultList: [
        { label: '审核通过', value: '01' },
        { label: '审核不通过', value: '02' },
      ],

      drawer: false, // 审核轨迹抽屉

      examineList: [
        {
          title: '发起人',
          name: '张三',
          identity: '项目经理',
          department: '研发技术中心',
          status: '00',
          time: '2021-01-01 12:00:00',
        },
        {
          title: '部门初审',
          name: '李四',
          identity: '项目经理1',
          department: '研发技术中心',
          status: '01',
          remark: '描述描述描述内容信息信息',
          time: '2021-01-01 12:00:00',
        },
        {
          title: '部门复审',
          name: '王五',
          identity: '项目经理1',
          department: '研发技术中心',
          status: '01',
          // remark: '描述描述描述内容信息信息',
          time: '2021-01-01 12:00:00',
        },
        {
          title: '流程结束',
          status: '04',
        },
      ], // 审核轨迹列表

      statusObj: {
        '00': '已提交',
        '01': '已通过',
        '02': '已拒绝',
        '03': '处理中',
        '04': '等待中',
      }, // 审核状态对象

      statusTagType: {
        '00': 'info',
        '01': 'success',
        '02': 'danger',
        '03': 'warning',
        '04': 'info',
      },
    };
  },

  computed: {
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },

    getStepStatus() {
      return (status) => {
        const map = {
          '01': 'success',
          '02': 'wait',
          '03': 'error',
          '04': 'process',
          '05': 'wait',
        };
        return map[status] || 'wait';
      };
    },
    getIconColor() {
      return (status) => {
        const colors = {
          '01': '#909399',
          '02': '#67C23A',
          '03': '#F56C6C',
          '04': '#409EFF',
          '05': '#909399',
        };
        return colors[status] || '#909399';
      };
    },
  },
  mounted() {
    this.applyNo = this.$route.query.applyNo;
    this.type = this.$route.query.type;

    this.getAuditDetail();
  },
  methods: {
    async loadData() {
      this.table.loading = true;
      const [err, res] = await getStationList({
        stationIdList: this.stationIdList,
        pageNum: this.table.page.currentPage,
        pageSize: this.table.page.pageSize,
      });
      this.table.loading = false;
      if (err) return;

      // console.log(res, 'res');
      const { data, total } = res;
      this.table.data = data;
      this.table.page.total = total;

      // console.log(this.table, 'data');
    },

    // 关闭抽屉
    onClickCloseDrawer() {
      this.drawer = false;
    },

    async getAuditDetail() {
      // 获取审核详情
      this.loading = true;
      const [err, res] = await getAuditDetail({
        applyNo: this.applyNo,
      });
      this.loading = false;
      if (err) return;
      const {
        applyType,
        applyNo,
        applicant,
        applyTime,
        applyRemark,
        returnTimeNum,
        unit,
        stationIdList,
        pileNum,
        approvalDtoList,
        isFinish,
      } = res.data;

      let applyText = this.selectDictLabel(
        this.dict.type.ls_charging_apply_status,
        applyType
      );

      let unitText = this.selectDictLabel(
        this.dict.type.ls_charging_return_time_unit,
        unit
      );

      (this.applyInfo = {
        applyId: applyNo,
        applyType: applyText,
        applyer: applicant,
        applyTime: applyTime,
        applyRemark: applyRemark,
        returnTimeNum,
        unit: unitText,
        applyTypeInfo: applyType,
      }),
        (this.stationNumber = stationIdList.length);
      this.pileNumber = pileNum;
      this.stationIdList = stationIdList;

      const list = [];
      approvalDtoList.forEach((item, index) => {
        if (index === 0) {
          list.push({
            title: '发起人',
            name: item.approval,
            identity: '项目经理',
            department: item.approvalDepartment,
            status: '00', // 已提交
            time: item.approvalTime,
          });
        } else if (index === 1) {
          list.push({
            title: '部门初审',
            name: item.approval,
            identity: '',
            department: item.approvalDepartment,
            status: item.approvalStatus,
            time: item.approvalTime,
          });
        } else if (index === 2) {
          list.push({
            title: '部门复审',
            name: item.approval,
            identity: '',
            department: item.approvalDepartment,
            status: item.approvalStatus,
            time: item.approvalTime,
          });
        }
      });

      if (isFinish) {
        list.push({
          title: '流程结束',
          status: '04',
        });
      }

      this.examineList = list;
      console.log(this.examineList);

      this.loadData();
    },

    // 取消
    handleCancel() {
      this.$router.back();
    },
    async handleConfirm() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
          const { result, remark } = this.form;

          const [err, res] = await stationAudit({
            applyNo: this.applyInfo.applyId,
            approvalOpinion: remark,
            approvalStatus: result,
          });
          if (err) return;

          this.$message.success('审核成功');
          setTimeout(() => {
            this.$router.back();
          }, 2000);
        }
      });
    },

    // 充电站详情
    hanleDetail(row) {
      const { stationId } = row;
      this.$router.push({
        path: '/v2g-charging-web/baseInfo/equipmentAndAssets/station/detail',
        query: {
          stationId,
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.container-full {
  background-color: rgba(244, 246, 249, 1);
  padding-bottom: 16px;
  min-height: calc(100% - 80px);
  overflow-y: auto;
}

.device-head {
  background-color: #fff;

  display: flex;
  height: 96px;
  display: flex;
  align-items: center;
  padding: 0 24px;
  box-sizing: border-box;
  .device-head-icon {
    width: 48px;
    height: 48px;
    margin-right: 24px;
  }
  .device-head-title {
    font-weight: 500;
    font-size: 24px;
    color: #12151a;
    flex: 1;
  }
}

.info-card {
  margin: 16px;
  border-radius: 5px;
  border: 1px solid #fff;
  overflow: hidden;
  background-color: #fff;
  // min-height: 300px;
  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    background: linear-gradient(180deg, #e9f2ff 0%, #ffffff 100%);
    .before-icon {
      width: 3px;
      height: 16px;
      background-image: url('~@/assets/station/consno-before.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin-right: 8px;
    }
    .card-head-text {
      flex: 1;
      font-weight: 500;
      font-size: 16px;
      color: #12151a;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }

    .button-wrap {
      display: flex;
      .invite-btn {
        background-color: #1ab2ff;
        border-color: #1ab2ff;
      }
      ::v-deep .el-button--small {
        font-size: 14px;
      }
      .distribution {
        margin-left: 24px;
        margin-right: 24px;
        display: flex;
        align-items: center;
      }
    }
  }

  .people-table-wrap {
    ::v-deep .bd3001-table-select-box {
      display: none;
    }
    ::v-deep .bd3001-button {
      width: 100%;
    }
  }
  .form-wrap {
    padding: 0 16px 16px 16px;
    .info-title {
      font-weight: 400;
      font-size: 16px;
      line-height: 16px;
      color: #505363;
    }
    .info-detail {
      font-weight: 400;
      font-size: 16px;
      line-height: 16px;
      color: #292b33;
    }
    .info-amount {
      height: 28px;
      background-color: #fff7e6;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 8px;
      font-weight: 400;
      font-size: 20px;
      color: #fe8921;
      margin-top: -6px;
      margin-right: 4px;
    }
    .info-img {
      width: 140px;
      height: 140px;
    }
  }
}

.choose-info-wrap {
  height: 28px;
  display: flex;
  align-items: center;
  margin: 8px 0 16px 0;

  font-family: PingFang SC;
  font-weight: 400;
  font-size: 14px;
  margin-left: 16px;
  color: #292b33;
  .choose-number {
    height: 28px;
    background-color: #fff7e6;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 8px;
    font-weight: 400;
    font-size: 20px;
    color: #fe8921;
    margin-right: 4px;
  }
}

.form-edit-wrap {
  padding: 0 0 16px 0;
}

::v-deep .bd3001-content {
  padding: 0 !important;
}

.container {
  position: relative;
  padding-bottom: 100px;
  box-sizing: border-box;
  .bottom-wrap {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 86px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background-color: #ffffff;
    padding-right: 32px;
    box-sizing: border-box;
    z-index: 100;
  }
}

.draw-wrap {
  .draw-card-head {
    // position: relative;
    height: 82px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    // margin-top: -20px;
    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 24px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }

    .card-head-close {
      width: 24px;
      height: 24px;
      background-image: url('~@/assets/station/drawer-close.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
    }
  }

  .draw-card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }
  .card-head {
    height: 18px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    margin-bottom: 32px;
    .before-icon {
      width: 3px;
      height: 16px;
      background-image: url('~@/assets/station/consno-before.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin-right: 8px;
    }
    .card-head-text {
      flex: 1;
      font-weight: 500;
      font-size: 16px;
      color: #12151a;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }
  .approval-steps {
    padding: 20px;
    background: #fff;
    border-radius: 4px;
  }

  :deep(.el-step__head) {
    padding-bottom: 10px;
  }

  :deep(.el-step__title) {
    line-height: 1.5;
    max-width: 600px;
  }
  ::v-deep .el-step__icon {
    border: 0px;
  }

  ::v-deep .el-step__description {
    padding-right: 10% !important;
  }
  .step-icon {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .step-icon-00 {
    background-color: #ebf3ff;
    border-radius: 50%;
    color: #217aff;
  }

  .step-icon-01 {
    background-color: #ebf3ff;
    border-radius: 50%;
    color: #217aff;
  }
  .step-icon-02 {
    background-color: #fc1e31;
    border-radius: 50%;
    color: #fff;
  }
  .step-icon-03 {
    background-color: #217aff;
    border-radius: 50%;
    color: #fff;
  }
  .step-icon-04 {
    background-color: #f3f6fc;
    border-radius: 50%;
    color: #818496;
  }
  .step-header {
    display: flex;
    align-items: center;
    //   margin-bottom: 8px;
  }

  .title {
    margin-right: 12px;
    font-weight: 400;
    font-size: 16px;
    color: #12151a;
  }

  .status-tag {
    margin-right: 12px;
  }

  .time {
    color: #999;
    font-size: 12px;
    margin-left: auto;
  }

  .remark-box {
    margin-top: 8px;
    padding: 16px 12px 16px 16px;
    background: #f9f9fb;
    border-radius: 5px;
    display: flex;
    margin-bottom: 32px;
    margin-right: 12px;
    .remark-icon {
      width: 48px;
      height: 48px;
      background-image: url('~@/assets/station/drawer-icon.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin-right: 16px;
    }
    .person-info {
      font-weight: 400;
      font-size: 16px;
      line-height: 20px;
      flex: 1;
      .person-title {
        color: #292b33;
        .identity {
          margin: 0 8px;
        }
      }
      .person-info-status {
        font-weight: 400;
        font-size: 16px;
        line-height: 20px;
        margin: 10px 0 0 0;
      }
      .person-info-status-00 {
        color: #00c864;
      }
      .person-info-status-01 {
        color: #00c864;
      }
      .person-info-status-02 {
        color: #fc1e31;
      }
      .person-info-status-03 {
        color: #217aff;
      }
      .person-info-status-04 {
        color: #217aff;
      }

      .remark {
        font-weight: 400;
        font-size: 16px;
        line-height: 20px;
        color: #292b33;
        margin-top: 16px;
      }
    }

    .remark-time {
      font-weight: 400;
      font-size: 16px;
      line-height: 20px;
      color: #292b33;
      margin-top: 15px;
    }
  }

  .remark-label {
    color: #666;
    margin-right: 6px;
  }

  .remark-text {
    color: #999;
  }

  .processing {
    animation: rotating 2s linear infinite;
  }

  @keyframes rotating {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }
}
</style>
