<template>
    <div class="container container-float " style="padding: 0 0 100px 0;">
        <div class="device-head">
            <img src="@/assets/coupon/coupon-detail-top-icon.png" class="device-head-icon">
            
            <div class="device-info-wrap">
                <div class="device-title-wrap">
                    <div class="device-title">优惠券名称：{{ couponName }}</div>
                </div>
                <div class="device-info-wrap">
                    <el-row>
                        <el-col :span="8">
                            <span class="label">充电桩编号：</span>
                            <span class="value">{{couponNo}}</span>
                        </el-col>
                        <el-col :span="8">
                            <span class="label">券业务类型：</span>
                            <span class="value">{{couponBusinessType}}</span>
                        </el-col>
                        <el-col :span="8">
                            <span class="label">券基本类型：</span>
                            <span class="value">{{ couponBasicType}}</span>
                        </el-col>
                    </el-row>
                </div>
            </div>
        </div>

        <div class="info-wrap" >
            <el-tabs v-model="activeName" >
                <el-tab-pane label="优惠券概览" name="overview">
                    <div class="info-item">
                        <div class="info-item-head">
                            <div class="info-item-before-icon"></div>
                            <div class="info-item-head-text">基本信息</div>
                        </div>

                        <div class="form-wrap">
                            <el-row :gutter="20">
                                <el-col :span="6" v-for="(item, key) in baseInfo" :key="key" style="margin-bottom: 24px;">
                                    <div style="display: flex;">
                                        <div class="info-title">{{labels.baseInfo[key]}}：</div>
                                        <div class="info-detail">{{ item }}</div>
                                    </div>
                                </el-col>
                            </el-row>
                        </div>
                    </div>

                    <div class="info-item">
                        <el-tabs v-model="overviewActiveName" >
                            <el-tab-pane label="业务参数" name="business">
                                <div class="form-wrap" style="margin-top: 16px;">
                                    <el-row :gutter="20">
                                        <el-col :span="6">
                                            <div style="display: flex; margin-bottom: 24px;">
                                                <div class="info-title">使用范围：</div>
                                                <div class="info-detail">{{ useScope }}</div>
                                            </div>
                                        </el-col>
                                    </el-row>

                                    <el-row :gutter="20">
                                        <el-col :span="6" v-for="(item, key) in limitInfo" :key="key" style="margin-bottom: 24px;">
                                            <div style="display: flex;">
                                                <div class="info-title">{{labels.limitInfo[key]}}：</div>
                                                <div class="info-detail">{{ item }}</div>
                                            </div>
                                        </el-col>
                                    </el-row>

                                    <BuseCrud
                                            ref="business"
                                            :loading="businessTable.loading"
                                            :tablePage="businessTable.page"
                                            :tableColumn="businessTableColumn"
                                            :tableData="businessTable.data"
                                            :pagerProps="pagerProps"
                                            :modalConfig="modalConfig"
                                            @loadData="getBusinessTablePage"
                                        >
                                        <template slot="defaultHeader">
                                            <div>

                                                <div class="top-button-wrap">
                                                
                                                    <div class="choose-info-wrap">
                                                        已选择
                                                        <div class="choose-number">{{ stationNumber }} </div>
                                                        个充电站，
                                                        <div class="choose-number">{{ pileNumber }}</div>

                                                        个充电桩
                                                    </div>
                                                
                                                </div>
                                            </div>
                                            
                                        </template>
                                    </BuseCrud>
                                </div>

                                
                               
                            </el-tab-pane>

                            <el-tab-pane label="用户参数" name="user">
                                <div class="form-wrap" style="margin-top: 16px;">
                                    <el-row :gutter="20">
                                        <el-col :span="6">
                                            <div style="display: flex; margin-bottom: 24px;">
                                                <div class="info-title">用户类型：</div>
                                                <div class="info-detail">{{ userType }}</div>
                                            </div>
                                        </el-col>
                                    </el-row>

                                    <el-row :gutter="20">
                                        <el-col :span="6" v-for="(item, key) in limitInfo" :key="key" style="margin-bottom: 24px;">
                                            <div style="display: flex;">
                                                <div class="info-title">{{labels.limitInfo[key]}}：</div>
                                                <div class="info-detail">{{ item }}</div>
                                            </div>
                                        </el-col>
                                    </el-row>

                                    <BuseCrud
                                            ref="business"
                                            :loading="userTable.loading"
                                            :tablePage="userTable.page"
                                            :tableColumn="userTableColumn"
                                            :tableData="userTable.data"
                                            :pagerProps="pagerProps"
                                            :modalConfig="modalConfig"
                                            @loadData="getUserTablePage"
                                        >
                                        <template slot="defaultHeader">
                                            <div>

                                                <div class="top-button-wrap">
                                                
                                                    <div class="choose-info-wrap">
                                                        圈选
                                                        <div class="choose-number">{{ userNumber }} </div>
                                                        个用户
                                                        
                                                    </div>
                                                
                                                </div>
                                            </div>
                                            
                                        </template>
                                    </BuseCrud>
                                </div>
                            </el-tab-pane>
                            <el-tab-pane label="限制参数" name="limit">
                                <div class="form-wrap">
                                    <el-form
                                        :model="limitDetailInfo.form"
                                        :rules="limitDetailInfo.rules"
                                        ref="limitInfoForm"
                                        label-position="top"
                                    >
                                        <el-row :gutter="20" style="margin-top: 16px;">
                                            <el-col :span="6">
                                                <el-form-item
                                                    label=""
                                                    prop="useLimit"
                                                    class="no-label"
                                                    :label-width="formLabelWidth"
                                                >
                                                <el-checkbox-group v-model="limitDetailInfo.form.useLimit" disabled>
                                                    <el-checkbox 
                                                        v-for="(item, index) in useLimitList"
                                                        :label="item.value"
                                                        :key="index"
                                                    >
                                                        {{ item.label }}
                                                    </el-checkbox >
                                                </el-checkbox-group>
                                            </el-form-item>
                                            </el-col>
                                        </el-row>
                                    </el-form>
                                </div>
                            </el-tab-pane>

                        </el-tabs>
                    </div>
                </el-tab-pane>

                <el-tab-pane label="发放记录" name="distributionRecord">
                    <div style="padding: 16px;">
                        <BuseCrud
                            ref="distributionRecord"
                            :loading="distributionLoading"
                            :filterOptions="distributionFilterOptions"
                            :tablePage="distributionTablePage"
                            :tableColumn="distributionTableColumn"
                            :tableData="distributionTableData"
                            :pagerProps="pagerProps"
                            :modalConfig="modalConfig"
                            @loadData="distributionLoadData"
                        >
                            <template slot="operate" slot-scope="{ row }">
                                <div class="menu-box">
                                    <el-button
                                        class="button-border"
                                        @click="hanleRedistribution(row)"
                                    >
                                    重新发放
                                    </el-button>
                                
                                </div>
                            
                            </template>

                        </BuseCrud>
                    </div>
                   
                </el-tab-pane>

                <el-tab-pane label="核销记录" name="verificationRecord">
                    <div style="padding: 16px;">
                        <BuseCrud
                            ref="verificationRecord"
                            :loading="verificationLoading"
                            :filterOptions="verificationFilterOptions"
                            :tablePage="verificationTablePage"
                            :tableColumn="verificationTableColumn"
                            :tableData="verificationTableData"
                            :pagerProps="pagerProps"
                            :modalConfig="modalConfig"
                            @loadData="verificationLoadData"
                        >
                       </BuseCrud>
                    </div>
                    
                </el-tab-pane>


                <el-tab-pane label="库存调整记录" name="inventoryAdjustmentRecord">
                    <div style="padding: 16px;">
                        <BuseCrud
                            ref="inventoryRecord"
                            :loading="inventoryLoading"
                            :filterOptions="inventoryFilterOptions"
                            :tablePage="inventoryTablePage"
                            :tableColumn="inventoryTableColumn"
                            :tableData="inventoryTableData"
                            :pagerProps="pagerProps"
                            :modalConfig="modalConfig"
                            @loadData="inventoryLoadData"
                        >
                       </BuseCrud>
                    </div>
                    
                </el-tab-pane>
               
                
            </el-tabs>
        </div>
    </div>
    
  </template>
  
  <script>
  
    export default {
    components: {
        
    },
    dicts: [],
    data() {
      return {
        activeName: 'overview',

        couponName: '优惠券AAA',
        couponNo: 'JS020123012',
        couponBusinessType: '充电券',
        couponBasicType: '满减券',

        labels: {
            baseInfo: {
                showTitle: '展示标题',
                generationMethod: '生成方式',
                validityPeriod: '有效期',
                initialInventory: '初始库存',
                discountMethod: '优惠方式',
                status: '券状态',
                ruleIntroduce: '规则说明',
                useIntroduce: '使用说明',
                creator: '创建人',
                createTime: '创建时间',
            },
            limitInfo: {
                discountScope: '优惠范围',
                holidayLimit: '节假日限制',
                weekLimit: '星期限制',
            }
        },
        baseInfo: {
            showTitle: '优惠券AAA',
            generationMethod: '批量生成',
            validityPeriod:'领取后立即生效，有效期8天',
            initialInventory: '200',
            discountMethod: '满5元减2元',
            status: '生效中',
            ruleIntroduce: 'AAAAAAABBBBB',
            useIntroduce: 'CCCCCCCCCCCC',
            creator: '张三',
            createTime: '2022-01-01 12:00:00',
        },

        limitInfo: {
            discountScope: '电费',
            holidayLimit: '排除节假日',
            weekLimit: '星期二、星期五'
        },

        overviewActiveName: 'business',

        useScope: '不限制',

        userType: '个人用户',


        businessTable: {
            loading: false,
            page: {
                total: 0,
                currentPage: 1,
                pageSize: 10,
            },
            dataTotal: [],
            data: [],
        },
        businessTableColumn: [
                {
                    type: 'seq',
                    title: '序号',
                    width: 60,
                    minWidth: 60,
                },
                {
                    field: 'stationNo',
                    title: '充电站编号',
                    minWidth: 120,
                },
                {
                    field: 'stationName',
                    title: '充电站名称',
                    minWidth: 150,
                },
                {
                    field: 'stationType',
                    title: '站点类型',
                    minWidth: 150,
                },
                {
                    field: 'operationMode',
                    title: '运营模式',
                    minWidth: 100, // 最小宽度
                },
                
        ],

        stationNumber: '0',
        pileNumber: '0',



        pagerProps: {
            layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
        },


        userTable: {
            loading: false,
            page: {
                total: 0,
                currentPage: 1,
                pageSize: 10,
            },
            dataTotal: [],
            data: [],
        },

        userTableColumn: [
            {
                type: 'seq',
                title: '序号',
                width: 60,
                minWidth: 60,
            },
            {
                field: 'userId',
                title: '用户ID',
                minWidth: 150,
            },
            {
                field: 'nickName',
                title: '用户昵称',
                minWidth: 150,
            },
            {
                field: 'phone', 
                title: '手机号',
                minWidth: 150,
            },
        ],

        userNumber: '0',

        limitDetailInfo: {
                form: {
                    useLimit: [
                       
                    ], 
                },
                rules: {
                }
            },

        useLimitList: [
            {  label: '单笔订单仅可用一张券', value: '1' },
        ],



        distributionLoading: false,
        distributionTablePage: { total: 0, currentPage: 1, pageSize: 10 },
        distributionTableColumn: [
            {
                type: 'seq',
                title: '序号',
                width: 60,
                minWidth: 60,
            },
            {
                field: 'distributeSerial',
                title: '发放流水',
                minWidth: 150,
            },
            {
                field: 'couponNo',
                title: '券码',
                minWidth: 160
            },
            {
                field: 'activityName',
                title: '活动名称',
                minWidth: 150
            },
            {
                field: 'activityNo',
                title: '活动编号',
                minWidth: 150
            },
            {
                field: 'userId',
                title: '用户ID',
                minWidth: 120
            },
            {
                field: 'userPhone',
                title: '用户手机号',
                minWidth: 120
            },
            {
                field: 'distributeTime',
                title: '发放时间',
                minWidth: 180
            },
            {
                field: 'distributeStatus',
                title: '发放状态',
                minWidth: 100,
               
            },
            {
                field: 'couponStatus',
                title: '优惠状态',
                minWidth: 100,
                
            },
            {
                title: '操作',
                slots: { default: 'operate' },
                width: 300,
                align: 'center',
                fixed: 'right',
            },
        ],
        distributionTableData: [
            {
                distributeSerial: '41943019031902',
                couponNo: '01031049302',
                activityName: '双十一满减活动',    // 示例活动名称
                activityNo: 'A20241123001',      // 补充活动编号
                userId: 'U1357924680',
                userPhone: '135****1234',
                distributeTime: '2024/11/23 11:20:02',
                distributeStatus: '已发放',
                couponStatus: '可使用',
            },
            {
                distributeSerial: '41943019031902',
                couponNo: '0103141',
                activityName: '新人注册礼包',     // 示例活动名称
                activityNo: 'A20241123002',      // 补充活动编号
                userId: 'U2468013579',
                userPhone: '135****1234',
                distributeTime: null,            // 发放失败无时间
                distributeStatus: '发放失败',
                couponStatus: null,              // 发放失败无优惠状态
            }
        ],
        distributionParams: {
            distributeSerial: '',
            couponNo: '',
            activityName: '',
            activityNo: '',
            userId: '',
            mobile: '',
            distributeTime: [],
            distributeStatus: '',
            couponStatus: '', 
        },

        verificationLoading: false,
        verificationTablePage: { total: 0, currentPage: 1, pageSize: 10 },
        verificationTableColumn: [
            {
                type: 'seq',
                title: '序号',
                width: 60,
                minWidth: 60,
            },
            {
                field: 'couponNo',
                title: '券码',
                minWidth: 160
            },
            {
                field: 'orderNo',
                title: '订单号',
                minWidth: 160
            },
            {
                field: 'orderAmount',
                title: '订单金额',
                minWidth: 160
            },
            {
                field: 'couponAmount',
                title: '优惠金额',
                minWidth: 160
            },
            {
                field: 'userId',
                title: '用户ID',
                minWidth: 120
            },
            {
                field: 'userPhone',
                title: '用户手机号',
                minWidth: 120
            },
            {
                field: 'verificationTime',
                title: '核销时间',
                minWidth: 160
            },
            {
                field: 'verificationStatus',
                title: '核销状态',
                minWidth: 100,
                fixed: 'right',
            },
        ],
        verificationTableData: [
            {
                seq: 1,
                couponNo: 'CDK202403200001',
                orderNo: 'ORDER202403200001',
                orderAmount: 299.00,
                couponAmount: 50.00,
                userId: 'U10086',
                userPhone: '13800138000',
                verificationTime: '2024-03-20 14:30:00',
                verificationStatus: '已核销'
            },
            {
                seq: 2,
                couponNo: 'CDK202403200002',
                orderNo: 'ORDER202403200002',
                orderAmount: 599.00,
                couponAmount: 100.00,
                userId: 'U10010',
                userPhone: '15912345678',
                verificationTime: '2024-03-20 15:15:00',
                verificationStatus: '已核销'
            },
        ],
        verificationParams: {
            orderNo: '',
            couponNo: '',
            userId: '',
            mobile: '',
            verificationTime: [],
            verificationStatus: '',
        },


        inventoryLoading: false,
        inventoryTablePage: { total: 0, currentPage: 1, pageSize: 10 },
        inventoryTableColumn: [
            {
                type: 'seq',
                title: '序号',
                width: 60,
                minWidth: 60,
            },
            
            {
                field: 'adjustType',
                title: '操作类型',
                minWidth: 120
            },
            {
                field: 'adjustNumber',
                title: '调整数量',
                minWidth: 120
            },
            {
                field: 'adjustUser',
                title: '操作人',
                minWidth: 120
            },
            {
                field: 'adjustTime',
                title: '调整时间',
                minWidth: 160
            },
          
        ],
        inventoryTableData: [
            {
                adjustType: '新增',
                adjustNumber: '100',
                adjustUser: '张三',
                adjustTime: '2022-01-01 12:00:00',
            },
            {
                adjustType: '减少',
                adjustNumber: '50',
                adjustUser: '李四',
                adjustTime: '2022-01-02 12:00:00',
            }
        ],
        inventoryParams: {
            adjustType:'',
            adjustTime: [],
            adjustPeople:'',
        },
      };
    },

    computed: {
        distributionFilterOptions() {
            return {
                config: [
                    {
                        field: 'distributeSerial',
                        title: '发放流水',
                        element: 'el-input',
                    },
                    {
                        field: 'couponNo',
                        title: '券码',
                        element: 'el-input',
                    },
                    {
                        field: 'activityName',
                        title: '活动名称',
                        element: 'el-input',
                    },
                    {
                        field: 'activityNo',
                        title: '活动编码',
                        element: 'el-input',
                    },
                    {
                        field: 'userId',
                        title: '用户ID',
                        element: 'el-input',
                    },
                    {
                        field: 'mobile',
                        title: '用户手机号',
                        element: 'el-input',
                    },
                    {
                        field: 'distributeTime',
                        title: '发放时间',
                        element: 'el-date-picker',
                        props: {
                            type: 'daterange',
                            valueFormat: 'yyyy-MM-dd',
                            options: [],
                        }
                    },
                    {
                        field: 'distributeStatus',
                        title: '发放状态',
                        element: 'el-select',
                        props: {
                            placeholder: '请选择',
                            options: []
                        }
                    },
                    {
                        field: 'couponStatus',
                        title: '优惠券状态',
                        element: 'el-select',
                        props: {
                            placeholder: '请选择',
                            options: []
                        }
                    }
                ],
                params: this.distributionParams,
            };
        },
        verificationFilterOptions() {
            return {
                config: [
                    
                    {
                        field: 'orderNo',
                        title: '订单号',
                        element: 'el-input',
                    },
                    {
                        field: 'couponNo',
                        title: '券码',
                        element: 'el-input',
                    },
                    {
                        field: 'userId',
                        title: '用户ID',
                        element: 'el-input',
                    },
                    {
                        field: 'mobile',
                        title: '用户手机号',
                        element: 'el-input',
                    },
                    {
                        field: 'verificationTime',
                        title: '核销时间',
                        element: 'el-date-picker',
                        props: {
                            type: 'daterange',
                            valueFormat: 'yyyy-MM-dd',
                            options: [],
                        }
                    },
                    {
                        field: 'verificationStatus',
                        title: '核销状态',
                        element: 'el-select',
                        props: {
                            placeholder: '请选择',
                            options: []
                        }
                    }
                ],
                params: this.verificationParams,
            };
        },
        inventoryFilterOptions() {
            return {
                config: [
                    {
                        field: 'adjustType',
                        title: '操作类型',
                        element: 'el-select',
                        props: {
                            placeholder: '请选择',
                            options: []
                        }
                    },
                    {
                        field: 'adjustTime',
                        title: '核销时间',
                        element: 'el-date-picker',
                        props: {
                            type: 'daterange',
                            valueFormat: 'yyyy-MM-dd',
                            options: [],
                        }
                    },
                    {
                        field: 'adjustPeople',
                        title: '核销状态',
                        element: 'el-input',
                    }
                ],
                params: this.inventoryParams,
            };
        },
        modalConfig() {
            return {
                addBtn: false,
                viewBtn: false,
                menu: false,
                editBtn: false,
                delBtn: false,
            }
        },
    },
    mounted() {
        this.businessTable.dataTotal =[
            {
                stationNo: '001',
                stationName: '充电站1',
                stationType: '快充站',
                operationMode: '自营',
            },
            {
                stationNo: '002',
                stationName: '充电站2',
                stationType: '慢充站',
                operationMode: '合作',
            }
        ],

        this.getBusinessTablePage();

        this.userTable.dataTotal = [
            {
                userId: '121232',
                nickName: '张三',
                phone: '***********',
            },
            {
                userId: '121232',
                nickName: '李四',
                phone: '***********',
            }
        ],

        this.getUserTablePage();
    },
    methods: {
        getBusinessTablePage() {
            this.businessTable.data = this.businessTable.dataTotal.slice(
                (this.businessTable.page.currentPage - 1) * this.businessTable.page.pageSize,
                this.businessTable.page.currentPage * this.businessTable.page.pageSize
            );
        },

        getUserTablePage() {
            this.userTable.data = this.userTable.dataTotal.slice(
                (this.userTable.page.currentPage - 1) * this.userTable.page.pageSize,
                this.userTable.page.currentPage * this.userTable.page.pageSize
            );
        }
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .container-full {
    background-color: rgba(244, 246, 249, 1);
    padding-bottom: 16px;
    min-height: calc(100% - 80px);
    overflow-y: auto;
  }
   

  .device-head {
    background-color: #fff;
    
    display: flex;
    height: 112px;
    display: flex;
    align-items: center;
    padding: 0 24px;
    box-sizing: border-box;
    .device-head-icon {
        width: 48px;
        height: 48px;
        margin-right: 24px;
    }
    .device-info-wrap {
        flex: 1;
        .device-title-wrap {
            height: 32px;
            display: flex;
            align-items: center;
            .device-title {
                font-weight: 500;
                font-size: 24px;
                color: #12151A;
            }
            .device-status {
                // width: 50px;
                padding: 0 10px;
                box-sizing: border-box;
                height: 24px;
                border-radius: 10px 0 10px 0;
                font-size: 16px;
                font-weight: 400;
                line-height: 24px;
                text-align: center;
                color: #fff;
                background: linear-gradient(321.01deg, #00C864 8.79%, #38F3CA 100.27%);
                margin-left: 12px;
            }
        }
        .device-info-wrap {
            height: 16px;
            margin-top: 16px;
            font-size: 16px;
            font-weight: 400;
            color: #292B33;
        }
    }
    
  
  }
 

  .info-wrap {
    margin: 16px;
    border-radius: 5px;
    border: 1px solid #fff;
    overflow: hidden;
    background-color: #fff;
    .info-item {
        margin: 16px 16px 0 16px;
        border-radius: 5px;
        border: 1px solid #E9EBF0;
        .info-item-head {
            height: 56px;
            padding: 0 16px;
            display: flex;
            align-items: center;
            .info-item-before-icon {
                width: 3px;
                height: 16px;
                background-image: url('~@/assets/station/consno-before.png');
                background-size: 100% 100%;
                background-repeat: no-repeat;
                margin-right: 8px;
            }
            .info-item-head-text {
                flex:1;
                font-weight: 500;
                font-size: 16px;
                color: #12151A;

                &::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    bottom: 0;
                    left: -3px; /* 调整这个值来改变边框的宽度 */
                    width: 0;
                    border-top: 3px solid transparent;
                    border-bottom: 3px solid transparent;
                    border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
                }        
            }

        }
        .form-wrap {
            padding: 0 24px 24px 24px;
            .form-title-wrap {
                margin: 8px 0 16px 0;
                height: 32px;
                display: flex;
                align-items: center;
                .form-title-blue {
                    line-height: 32px;
                    border-radius: 2px;
                    padding: 0 10px;
                    background-color: #EBF3FF;
                    color:#217AFF;
                    font-size: 16px;
                    font-weight: 400;
                    margin-right: 16px;
                }
                .form-title {
                    line-height: 32px;
                    border-radius: 2px;
                    padding: 0 10px;
                    background-color: #F9F9FB;
                    color:#292B33;
                    font-size: 16px;
                    font-weight: 400;
                    margin-right: 16px;
                }
            }
        }
        
    }

    .chart-wrap {
        padding: 0 24px 24px 24px;
        display: flex;
        .chart-item-left-wrap {
            margin: 16px 8px 0 0;
            border-radius: 5px;
            border: 1px solid #E9EBF0;
            width: calc(50% - 8px);
            // height: 734px;
        }
        .chart-item-right-wrap {
            margin: 16px 0 0 8px;
            border-radius: 5px;
            border: 1px solid #E9EBF0;
            width: calc(50% - 8px);
            // height: 734px;
        }

        .info-item-head {
            height: 56px;
            padding: 0 16px;
            display: flex;
            align-items: center;
            .info-item-before-icon {
                width: 3px;
                height: 16px;
                background-image: url('~@/assets/station/consno-before.png');
                background-size: 100% 100%;
                background-repeat: no-repeat;
                margin-right: 8px;
            }
            .info-item-head-text {
                flex:1;
                font-weight: 500;
                font-size: 16px;
                color: #12151A;

                &::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    bottom: 0;
                    left: -3px; /* 调整这个值来改变边框的宽度 */
                    width: 0;
                    border-top: 3px solid transparent;
                    border-bottom: 3px solid transparent;
                    border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
                }        
            }

        }
    }
  }

  .top-button-wrap {
        // margin-left: 24px;
        display: flex;
        align-items: center;
        height: 34px;
        align-items: center;
        margin-bottom: 14px;
        .choose-info-wrap {
            border-radius: 2px;
            height: 34px;
            padding: 0 10px;
            display: flex;
            align-items: center;
            background-color: #EBF3FF;
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 14px;
            // margin-left: 16px;
            color: #217AFF;
            .choose-number {
                font-size: 16px;
                font-weight: 500;
                margin: 0 4px;
            }
        }
        
    }

  ::v-deep  .bd3001-content{
    box-shadow: none;
    padding: 0 !important;
  }
 
  .approval-steps {
  padding: 20px;
  background: #fff;
  border-radius: 4px;
}

:deep(.el-step__head) {
  padding-bottom: 10px;
}

:deep(.el-step__title) {
  line-height: 1.5;
  max-width: 600px;
}
::v-deep .el-step__icon {
  border: 0px;
}

::v-deep .bd3001-filter {
    box-shadow: none;
}

 
.button-border {
    border: 0.01rem solid #217AFF;
    color: #217AFF;
    background-color: #fff;
}


.no-label ::v-deep .el-form-item__label {
//   display: none;
    color: #fff;
}
  </style>
  