<template>
    <div class="container container-float " style="padding: 0 0 100px 0;">
        <div class="info-card">
            <div class="card-head">
                <div class="before-icon"></div>
                <div class="card-head-text">基础信息</div>
            </div>
            <div class="card-head-split"></div>
            <div class="form-wrap">
                <el-form
                    :model="baseInfo.form"
                    :rules="baseInfo.rules"
                    ref="baseInfoForm"
                    label-position="top"
                >
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item label="优惠券编码" prop="couponCode">
                                <el-input 
                                    v-model="baseInfo.form.couponCode"
                                    placeholder="自动生成"
                                    disabled
                                ></el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="8">
                            <el-form-item label="优惠券名称" prop="couponName">
                                <el-input 
                                    v-model="baseInfo.form.couponName"
                                    placeholder="请输入优惠券名称"
                                ></el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="8">
                            <el-form-item label="展示标题" prop="couponTitle">
                                <el-input 
                                    v-model="baseInfo.form.couponTitle"
                                    placeholder="请输入展示标题"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item label="生成方式" prop="generationMethod">
                                <el-radio-group
                                    v-model="baseInfo.form.generationMethod"
                                >
                                    <el-radio
                                        v-for="dict in generationMethodList"
                                        :key="dict.value"
                                        :label="dict.value"
                                    >
                                        {{ dict.label }}
                                    </el-radio>
                                </el-radio-group>

                            </el-form-item>
                        </el-col>

                        <el-col :span="8" v-if="baseInfo.form.generationMethod === '1'">
                            <el-form-item label="是否动态成本单位" prop="isDynamicCostUnit">
                                <div style="display: flex; align-items: center; height: 28px;">
                                    <el-radio-group
                                        v-model="baseInfo.form.isDynamicCostUnit"
                                        style="margin-right: 10px; width: 50%;"
                                    >
                                        <el-radio
                                            v-for="dict in isDynamicCostUnitList"
                                            :key="dict.value"
                                            :label="dict.value"
                                        >
                                            {{ dict.label }}
                                        </el-radio>
                                    </el-radio-group>

                                    <el-input 
                                       v-if="baseInfo.form.isDynamicCostUnit === '2'"
                                        v-model="baseInfo.form.dynamicCostUnit"
                                        placeholder="请输入"
                                    ></el-input>
                                </div>
                               

                            </el-form-item>
                        </el-col>

                        <el-col :span="8" v-if="baseInfo.form.generationMethod === '2'">
                            <el-form-item label="初始库存" prop="initialInventory">
                                <el-input 
                                    v-model="baseInfo.form.initialInventory"
                                    placeholder="请输入初始库存"
                                ></el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="8">
                            <el-form-item label="券业务类型" prop="couponBusinessType">
                                <el-select
                                    v-model="baseInfo.form.couponBusinessType"
                                    placeholder="请选择券业务类型"
                                    style="width: 100%"
                                >
                                    <el-option
                                        v-for="item in couponBusinessTypeList"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item label="有效期" prop="validityPeriod">
                                <div style="display: flex; align-items: center;">
                                    <el-select v-model="baseInfo.form.validityPeriod" style="width: 100%;" @change="handleValidityChange"> 
                                        <el-option
                                            v-for="item in validityPeriodList"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value"
                                        ></el-option>
                                    </el-select>

                                </div>
                                
                            </el-form-item>

                           
                           


                        </el-col>

                        <el-col :span="8">
                            <el-form-item
                                label="1"
                                v-if="baseInfo.form.validityPeriod === '1'"
                                prop="fixedValidityPeriod"
                                class="no-label"
                            >
                                <el-date-picker
                                    v-model="baseInfo.form.fixedValidityPeriod"
                                    type="datetimerange"
                                    value-format="yyyy-MM-dd HH:mm:ss"
                                    style="width: 100%"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="4">
                            <el-form-item 
                                label="1"
                                v-if="baseInfo.form.validityPeriod === '2'"  
                                prop="effectType" 
                                class="no-label">
                                <div style="display: flex; align-items: center;">
                                    <div>领取后：</div>
                                    <el-select
                                        v-model="baseInfo.form.effectType"
                                        style="width: 60%;margin-right: 10px;"
                                    >
                                        <el-option
                                        v-for="item in effectTypeList"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                        />
                                    </el-select>
                                    ，
                                </div>
                            </el-form-item>
                           
                        </el-col>

                        <el-col :span="4"> 
                            <el-form-item 
                                label="1"
                                prop="effectDay"
                                v-if="baseInfo.form.validityPeriod === '2'"  
                                class="no-label">
                                <div style="display: flex; align-items: center;">
                                <div>有效天数：</div>
                                <el-input
                                    v-model="baseInfo.form.effectDay"
                                    placeholder="请输入"
                                    style="width: 50%; margin-right: 10px;"
                                />
                                <div>天</div>
                                </div>
                            </el-form-item>
                        </el-col>

                    </el-row>

                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item label="券基本类型" prop="basicType">
                                    <el-select v-model="baseInfo.form.basicType" style="width: 100%;" > 
                                        <el-option
                                            v-for="item in basicTypeList"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value"
                                        ></el-option>
                                    </el-select>                                
                            </el-form-item>
                        </el-col>

                        <el-col :span="8" v-if="baseInfo.form.basicType === '1'">
                            <el-form-item label="优惠方式" prop="discountMethod1">
                                <div style="display: flex;align-items: center; height: 28px;">
                                    满：
                                    <el-input 
                                        style="width: 30%; margin-right: 10px;"
                                        v-model="baseInfo.form.full"
                                        placeholder="金额"
                                    ></el-input>
                                    减：
                                    <el-input
                                        style="width: 30%;"
                                        v-model="baseInfo.form.reduce"
                                    >  </el-input>
                                </div>                 
                            </el-form-item>
                        </el-col>

                        <el-col :span="12" v-if="baseInfo.form.basicType === '2'">
                            <el-form-item label="优惠方式" prop="discountMethod2">
                                <div style="display: flex;align-items: center; height: 28px;">
                                    满：
                                    <el-input 
                                        style="width: 20%; margin-right: 10px;"
                                        v-model="baseInfo.form.discountFull"
                                        placeholder="金额"
                                    ></el-input>
                                    折扣：
                                    <el-input
                                        style="width: 20%; margin-right: 10px;"
                                        v-model="baseInfo.form.discount"
                                         placeholder="折扣力度"
                                    >  </el-input>
                                    最高减：
                                    <el-input
                                        style="width: 20%; margin-right: 10px;"
                                        v-model="baseInfo.form.maxReduce"
                                        placeholder="金额"
                                    >  </el-input>
                                </div>                 
                            </el-form-item>
                        </el-col>


                       
                    </el-row>

                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item label="优惠券状态" prop="couponStatus">
                                <el-radio-group
                                    v-model="baseInfo.form.couponStatus"
                                >
                                    <el-radio
                                        v-for="dict in couponStatusList"
                                        :key="dict.value"
                                        :label="dict.value"
                                    >
                                        {{ dict.label }}
                                    </el-radio>
                                </el-radio-group>

                            </el-form-item>
                        </el-col>

                    </el-row>

                    <el-row :gutter="20">
                        <el-col :span="24">
                            <el-form-item
                                    label="规则说明"
                                    prop="ruleIntroduce"
                                    :label-width="formLabelWidth"
                            >
                                <el-input
                                    v-model="baseInfo.form.ruleIntroduce"
                                    type="textarea"
                                    :rows="3"
                                    placeholder="请输入"
                                ></el-input>

                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row :gutter="20">
                        <el-col :span="24">
                            <el-form-item
                                    label="使用说明"
                                    prop="useIntroduce"
                                    :label-width="formLabelWidth"
                            >
                                <el-input
                                    v-model="baseInfo.form.useIntroduce"
                                    type="textarea"
                                    :rows="3"
                                    placeholder="请输入"
                                ></el-input>

                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row :gutter="20">
                        <el-col :span="24">
                            <el-form-item
                                label="优惠券预览"
                                prop="preview"
                                :label-width="formLabelWidth"
                            >
                                <div class="coupon-wrap">
                                    <div class="coupon-img">
                                        <div class="coupon-amount-wrap" v-if="baseInfo.form.basicType === '1' && baseInfo.form.reduce">
                                            {{  baseInfo.form.reduce }}
                                            <div class="unit">元</div>
                                        </div>

                                        <div class="coupon-amount-wrap" v-if="baseInfo.form.basicType === '2' && baseInfo.form.discount">
                                            {{  baseInfo.form.discount }}
                                            <div class="unit">折</div>
                                        </div>

                                        <div class="coupon-name-wrap" v-if="baseInfo.form.couponBusinessType">
                                            {{ selectDictLabel(
                                                    this.couponBusinessTypeList,
                                                    baseInfo.form.couponBusinessType
                                            ) }}
                                        </div>
                                    </div>

                                    <div class="coupon-info-wrap">
                                        <div class="coupon-title" v-if="baseInfo.form.couponName">
                                            {{ baseInfo.form.couponName }}
                                        </div>

                                        <div class="coupon-time" v-if="baseInfo.form.validityPeriod === '1' && baseInfo.form.fixedValidityPeriod.length">
                                            {{ `${baseInfo.form.fixedValidityPeriod[0]}-${baseInfo.form.fixedValidityPeriod[1]}` }}
                                        </div>

                                        <div class="coupon-time" v-if="baseInfo.form.validityPeriod === '2' && baseInfo.form.effectType && baseInfo.form.effectDay">
                                            {{ `领取后${selectDictLabel(
                                                    this.effectTypeList,
                                                    baseInfo.form.effectType
                                            )}，有效天数${baseInfo.form.effectDay}天` }}
                                        </div>

                                        <div class="coupon-discount" v-if="baseInfo.form.basicType === '1' && baseInfo.form.full && baseInfo.form.reduce">
                                            {{ `满${baseInfo.form.full}元减${baseInfo.form.reduce}元` }}
                                        </div>

                                        <div class="coupon-discount" v-if="baseInfo.form.basicType === '2' && baseInfo.form.discountFull && baseInfo.form.discount && baseInfo.form.maxReduce">
                                            {{ `满${baseInfo.form.discountFull}元打${baseInfo.form.discount}折，最高可减${baseInfo.form.maxReduce}元` }}
                                        </div>
                                    </div>
                                </div>
                            </el-form-item>
                        </el-col>
                       
                    </el-row>
                    
                </el-form>
            </div>
        </div>

        <div class="info-wrap" >
            <el-tabs v-model="activeName" >
                <el-tab-pane label="业务参数" name="business">
                    <div class="form-wrap">
                        <el-form
                            :model="businessInfo.form"
                            :rules="businessInfo.rules"
                            ref="businessInfoForm"
                            label-position="top"
                        >
                            <el-row :gutter="20">
                                <el-col :span="24">
                                    <el-form-item
                                        label="使用范围"
                                        prop="useScope"
                                        :label-width="formLabelWidth"
                                    >
                                    <el-radio-group v-model="businessInfo.form.useScope" @change="useScopeChange">
                                        <el-radio
                                            v-for="(item, index) in useScopeList"
                                            :label="item.value"
                                            :key="index"
                                        >
                                            {{ item.label }}
                                        </el-radio>
                                        </el-radio-group>
                                    </el-form-item>

                                    <el-form-item
                                        v-if="businessInfo.form.useScope === '1'"
                                        label=""
                                        prop="stationLimit"
                                        :label-width="formLabelWidth"
                                    >
                                        <el-radio-group v-model="businessInfo.form.stationLimit">
                                        <el-radio
                                            v-for="(item, index) in stationLimitList"
                                            :label="item.value"
                                            :key="index"
                                        >
                                            {{ item.label }}
                                        </el-radio>
                                        </el-radio-group>
                                    </el-form-item>

                                    <el-form-item
                                        v-if="businessInfo.form.useScope === '2'"
                                        label=""
                                        prop="pileLimit"
                                        :label-width="formLabelWidth"
                                    >
                                        <el-radio-group v-model="businessInfo.form.pileLimit">
                                        <el-radio
                                            v-for="(item, index) in pileLimitList"
                                            :label="item.value"
                                            :key="index"
                                        >
                                            {{ item.label }}
                                        </el-radio>
                                        </el-radio-group>
                                    </el-form-item>

                                    <el-form-item
                                        v-if="businessInfo.form.useScope === '3'"
                                        label=""
                                        prop="institutionLimit"
                                        :label-width="formLabelWidth"
                                    >
                                        <el-radio-group v-model="businessInfo.form.institutionLimit">
                                        <el-radio
                                            v-for="(item, index) in institutionLimitList"
                                            :label="item.value"
                                            :key="index"
                                        >
                                            {{ item.label }}
                                        </el-radio>
                                        </el-radio-group>
                                    </el-form-item>
                                    

                                    
                                </el-col>
                            </el-row>

                            <el-row :gutter="20">
                                <el-col :span="6">
                                    <el-form-item
                                        label="优惠范围"
                                        prop="discountScope"
                                        :label-width="formLabelWidth"
                                    >
                                    <el-radio-group v-model="businessInfo.form.discountScope">
                                        <el-radio
                                            v-for="(item, index) in discountScopeList"
                                            :label="item.value"
                                            :key="index"
                                        >
                                            {{ item.label }}
                                        </el-radio>
                                        </el-radio-group>
                                    </el-form-item>
                                </el-col>
                                <el-col :span="6">
                                    <el-form-item
                                        label="节假日限制"
                                        prop="holidayLimit"
                                        :label-width="formLabelWidth"
                                    >
                                    <el-radio-group v-model="businessInfo.form.holidayLimit">
                                        <el-radio
                                            v-for="(item, index) in holidayLimitList"
                                            :label="item.value"
                                            :key="index"
                                        >
                                            {{ item.label }}
                                        </el-radio>
                                        </el-radio-group>
                                    </el-form-item>
                                </el-col>

                                <el-col :span="12">
                                    <el-form-item
                                        label="星期限制"
                                        prop="weekLimit"
                                        :label-width="formLabelWidth"
                                    >
                                    <el-checkbox-group v-model="businessInfo.form.weekLimit" >
                                        <el-checkbox 
                                            v-for="(item, index) in weekLimitList" 
                                            :label="item.value" 
                                            :key="index"
                                        >
                                            {{item.label}}
                                        </el-checkbox>
                                    </el-checkbox-group>
                                    </el-form-item>
                                </el-col>
                               
                            </el-row>

                            <BuseCrud
                                    v-if="businessInfo.form.useScope === '1' && businessInfo.form.stationLimit ==='2'"
                                    ref="crud1"
                                    :loading="table.loading"
                                    :tablePage="table.page"
                                    :tableColumn="tableColumn"
                                    :tableData="table.data"
                                    :pagerProps="pagerProps"
                                    :modalConfig="modalConfig"
                                    @loadData="getTablePage"
                                >
                                <template slot="defaultHeader">
                                    <div>

                                        <div class="top-button-wrap">
                                            <el-button
                                                type="primary"
                                                @click="() => handleChooseStation()"
                                            >
                                                充电站选择
                                            </el-button>

                                            <el-button
                                                type="primary"
                                                plain
                                                @click="() => handleChooseStation()"
                                            >
                                                充电站导入
                                            </el-button>

                                            <el-button
                                                type="primary"
                                                plain
                                                @click="() => handleChooseStation()"
                                            >
                                                下载模版
                                            </el-button>

                                            <div class="choose-info-wrap">
                                                已选择
                                                <div class="choose-number">{{ stationNumber }} </div>
                                                个充电站，
                                                <div class="choose-number">{{ pileNumber }}</div>

                                                个充电桩
                                            </div>
                                        
                                        </div>
                                    </div>
                                    
                                </template>

                                    <template slot="operate" slot-scope="{ row }">
                                        <div class="menu-box">
                                            <el-button
                                                type="primary"
                                                plain
                                                @click="hanleDetail(row)"
                                            >
                                                详情
                                            </el-button>

                                            <el-button
                                                type="danger"
                                                plain
                                                @click="() => handleDelete(row)"
                                            >
                                                删除
                                            </el-button>

                                        
                                        </div>
                                    
                                    </template>

                            </BuseCrud>


                            <BuseCrud
                                    v-if="businessInfo.form.useScope === '2' && businessInfo.form.pileLimit ==='2'"
                                    ref="crud2"
                                    :loading="table.loading"
                                    :tablePage="table.page"
                                    :tableColumn="tablePileColumn"
                                    :tableData="table.data"
                                    :pagerProps="pagerProps"
                                    :modalConfig="modalConfig"
                                    @loadData="getTablePage"
                                >
                                <template slot="defaultHeader">
                                    <div>


                                        <div class="top-button-wrap">
                                            <el-button
                                                type="primary"
                                                @click="() => handleChoosePile()"
                                            >
                                                充电桩选择
                                            </el-button>

                                            <el-button
                                                type="primary"
                                                plain
                                                @click="() => handleChoosePile()"
                                            >
                                                充电桩导入
                                            </el-button>

                                            <el-button
                                                type="primary"
                                                plain
                                                @click="() => handleChoosePile()"
                                            >
                                                下载模版
                                            </el-button>

                                            <div class="choose-info-wrap">
                                                已选择
                                            
                                                <div class="choose-number">{{ pileNumber }}</div>

                                                个充电桩
                                            </div>
                                        
                                        </div>
                                    </div>
                                    
                                </template>

                                    <template slot="operate" slot-scope="{ row }">
                                        <div class="menu-box">
                                            <el-button
                                                type="primary"
                                                plain
                                                @click="hanlePileDetail(row)"
                                            >
                                                详情
                                            </el-button>

                                            <el-button
                                                type="danger"
                                                plain
                                                @click="() => handlePileDelete(row)"
                                            >
                                                删除
                                            </el-button>

                                        
                                        </div>
                                    
                                    </template>

                            </BuseCrud>

                            <BuseCrud
                                    v-if="businessInfo.form.useScope === '3' && businessInfo.form.institutionLimit ==='2'"
                                    ref="crud3"
                                    :loading="institutionTable.loading"
                                    :tablePage="institutionTable.page"
                                    :tableColumn="tableInstitutionColumn"
                                    :tableData="institutionTable.data"
                                    :pagerProps="pagerProps"
                                    :modalConfig="modalConfig"
                                    @loadData="getInstitutionTablePage"
                                >
                                <template slot="defaultHeader">
                                    <div>


                                        <div class="top-button-wrap">
                                            <el-button
                                                type="primary"
                                                @click="() => handleChooseInstitution()"
                                            >
                                                产权机构范围选择
                                            </el-button>

                                            <el-button
                                                type="primary"
                                                plain
                                                @click="() => handleChooseInstitution()"
                                            >
                                                产权机构范围导入
                                            </el-button>

                                            <el-button
                                                type="primary"
                                                plain
                                                @click="() => handleChooseInstitution()"
                                            >
                                                下载模版
                                            </el-button>

                                            <div class="choose-info-wrap">
                                                已选择
                                            
                                                <div class="choose-number">{{ institutionNumber }}</div>

                                                个产权机构
                                            </div>
                                        
                                        </div>
                                    </div>
                                    
                                </template>

                                    <template slot="operate" slot-scope="{ row }">
                                        <div class="menu-box">
                                            <el-button
                                                type="danger"
                                                plain
                                                @click="() => handleInstitutionDelete(row)"
                                            >
                                                删除
                                            </el-button>

                                        
                                        </div>
                                    
                                    </template>

                            </BuseCrud>
                        </el-form>
                    </div>
                </el-tab-pane>

                <el-tab-pane label="用户参数" name="user">
                    <div class="form-wrap">
                        <el-form
                            :model="userInfo.form"
                            :rules="userInfo.rules"
                            ref="userInfoForm"
                            label-position="top"
                        >
                            <el-row :gutter="20">
                                <el-col :span="6">
                                    <el-form-item
                                        label="用户类型"
                                        prop="useType"
                                        :label-width="formLabelWidth"
                                    >
                                    <el-radio-group v-model="userInfo.form.useType">
                                        <el-radio
                                            v-for="(item, index) in useTypeList"
                                            :label="item.value"
                                            :key="index"
                                        >
                                            {{ item.label }}
                                        </el-radio>
                                        </el-radio-group>
                                    </el-form-item>
                                </el-col>
                            </el-row>

                            <!-- todo 用户列表-->
                        </el-form>
                    </div>
                </el-tab-pane>

                <el-tab-pane label="限制参数" name="limit">
                    <div class="form-wrap">
                        <el-form
                            :model="limitInfo.form"
                            :rules="limitInfo.rules"
                            ref="limitInfoForm"
                            label-position="top"
                        >
                            <el-row :gutter="20">
                                <el-col :span="6">
                                    <el-form-item
                                        label=""
                                        prop="useLimit"
                                        :label-width="formLabelWidth"
                                    >
                                    <el-checkbox-group v-model="limitInfo.form.useLimit">
                                        <el-checkbox 
                                            v-for="(item, index) in useLimitList"
                                            :label="item.value"
                                            :key="index"
                                        >
                                            {{ item.label }}
                                        </el-checkbox >
                                    </el-checkbox-group>
                                </el-form-item>
                                </el-col>
                            </el-row>
                        </el-form>
                    </div>
                </el-tab-pane>
            </el-tabs>
        </div>
       

        <ChooseStationModal ref="chooseStationModal" @confirm="handleChooseStationConfirm" />
        <ChoosePileModal ref="choosePileModal" @confirm="handleChoosePileConfirm" />
        <ChooseInstitutionModal ref="chooseInstitutionModal" @confirm="handleChooseInstitutionConfirm" />

        <div class="bottom-wrap">
            <div>
                <el-button @click="handleCancel">取消</el-button>
                <el-button @click="handleSave('save')" type="primary">保存</el-button>
                <el-button @click="handleSave('audit')" type="primary">提交审核</el-button>

            </div>

        </div>

    </div>
    
  </template>
  
  <script>

import ChooseStationModal from './components/chooseStationModal';
import ChoosePileModal from './components/choosePileModal';

import ChooseInstitutionModal from './components/chooseInstitutionModal';

  
    export default {
    components: {
        ChooseStationModal,
        ChoosePileModal,
        ChooseInstitutionModal
    },
    dicts: [
        'ls_charging_operation_mode',
        'ls_charging_subType',
    ],
    data() {
      return {
        baseInfo: {
            form: {
                couponCode: '',
                couponName: '',
                couponTitle: '',
                generationMethod: '',
                isDynamicCostUnit: '',
                dynamicCostUnit: '',
                initialInventory: '',
                couponBusinessType: '',
                validityPeriod: '1',
                fixedValidityPeriod: [],
                effectType: '',
                effectDay: '',
                basicType: '',
                full:'',
                reduce: '',
                discountFull:'',
                discount:'',
                maxReduce: '',
                couponStatus: '',
                ruleIntroduce: '',
                useIntroduce: '',
            },
            rules: {
                couponName: [
                    { required: true, message: '请输入优惠券名称', trigger: 'blur'}
                ],
                couponTitle: [
                    { required: true, message: '请输入展示标题', trigger: 'blur'}
                ],
                generationMethod: [
                    { required: true, message: '请选择生成方式', trigger: 'change'}
                ],
                isDynamicCostUnit: [
                    { required: true, message: '请选择是否动态成本单位', trigger: 'change'}
                ],
                initialInventory: [
                    { required: true, message: '请输入初始库存', trigger: 'blur'}
                ],
                couponBusinessType: [
                    { required: true, message: '请选择优惠券业务类型', trigger: 'change'},
                ],
                validityPeriod: [
                    { required: true, message: '请选择有效期类型', trigger: 'change' }
                ],
                fixedValidityPeriod: [
                    { 
                        validator: (rule, value, callback) => {
                        if (this.baseInfo.form.validityPeriod === '1' && (!value || value.length === 0)) {
                            callback(new Error('请选择固定有效期'));
                        } else {
                            callback();
                        }
                        },
                        trigger: 'change'
                    }
                ],
                effectType: [
                    { 
                        validator: (rule, value, callback) => {
                        if (this.baseInfo.form.validityPeriod === '2' && !value) {
                            callback(new Error('请选择生效类型'));
                        } else {
                            callback();
                        }
                        },
                        trigger: 'change'
                    }
                ],
                effectDay: [
                    { 
                        validator: (rule, value, callback) => {
                        if (this.baseInfo.form.validityPeriod === '2' && !value) {
                            callback(new Error('请输入有效天数'));
                        } else {
                            callback();
                        }
                        },
                        trigger: 'blur'
                    }
                ],
                basicType: [
                    { required: true, message: '请选择券基础类型', trigger: 'change' },
                ],
                discountMethod1: [
                    {
                        required: true, 
                        validator: (rule, value, callback) => {
                        if (!this.baseInfo.form.full || !this.baseInfo.form.reduce) {
                            callback(new Error('请输入满减方式'));
                        } else {
                            callback();
                        }
                        },
                        trigger: 'blur'
                    }
                ],
                discountMethod2: [
                    {
                        required: true,
                        validator: (rule, value, callback) => {
                            if (!this.baseInfo.form.discountFull || !this.baseInfo.form.discount || !this.baseInfo.form.maxReduce) {
                                callback(new Error('请输入折扣方式'));
                            } else {
                                callback();
                            }
                        },
                        trigger: 'blur'
                    }
                ],
                couponStatus: [
                    { required: true, message: '请选择优惠券状态', trigger: 'blur'}
                ],
                ruleIntroduce: [
                    { required: true, message: '请输入规则说明', trigger: 'blur'}
                ],
                useIntroduce: [
                    { required: true, message: '请输入使用说明', trigger: 'blur'}
                ]

            },
        },
        generationMethodList: [
            { label: '动态生成', value: '1' },
            { label: '批量生成', value: '2' },
        ],
        isDynamicCostUnitList: [
            { label: '是', value: '1' },
            { label: '否', value: '2' },
        ],
        couponBusinessTypeList: [
            { label: '充电券', value: '1' },
        ],
        validityPeriodList: [
            { label: '固定有效期', value: '1' },
            { label: '相对有效期', value: '2' },
        ],
        effectTypeList: [
            { label: '立即生效', value: '1' },
        ],
        basicTypeList: [
            { label: '满减券', value: '1' },
            { label: '折扣券', value: '2' },
        ],
        couponStatusList: [
            { label: '启用', value: '1' },
            { label: '停用', value: '2' },
        ],

        activeName: 'business',

        businessInfo: {
            form: {
                useScope: '',
                stationLimit: '',
                pileLimit: '',
                institutionLimit: '',
                discountScope: '',
                holidayLimit: '',
                weekLimit: [],
            },
            rules: {
                useScope: [
                    { required: true, message: '请选择使用范围', trigger: 'blur' }
                ],
                discountScope: [
                    { required: true, message: '请选择优惠范围', trigger: 'blur' }
                ]

            }
        },

        useScopeList: [
            { label: '按场站', value: '1' },
            { label: '按充电桩', value: '2' },
            { label: '按产权机构', value: '3' },
        ],
        stationLimitList:[
            { label: '不限制', value: '1' },
            { label: '指定场站', value: '2' },
        ],
        pileLimitList:[
            { label: '不限制', value: '1' },
            { label: '指定充电桩', value: '2' },
        ],
        institutionLimitList:[
            { label: '不限制', value: '1' },
            { label: '指定产权机构', value: '2' },
        ],
        discountScopeList: [
            { label: '服务费', value: '1' },
            { label: '电费', value: '2' },
            { label: '服务费+电费', value: '3' },
        ],
        holidayLimitList: [
            { label: '不区分', value: '1' },
            { label: '仅限节假日', value: '2' },
            { label: '排除节假日', value: '3' },
        ],
        weekLimitList: [
            { label: '周一', value: '1' },
            { label: '周二', value: '2' },
            { label: '周三', value: '3' },
            { label: '周四', value: '4' },
            { label: '周五', value: '5' },
            { label: '周六', value: '6' },
            { label: '周日', value: '7' },
        ],

            stationNumber: '0',
            pileNumber: '0',

            table: {
                loading: false,
                page: {
                    total: 0,
                    currentPage: 1,
                    pageSize: 10,
                },
                dataTotal: [],
                data: [],
            },

            pagerProps: {
                layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
            },

            tableColumn: [
                {
                    type: 'seq',
                    title: '序号',
                    width: 60,
                    minWidth: 60,
                },
                {
                    field: 'stationNo',
                    title: '充电站编号',
                    minWidth: 120,
                },
                {
                    field: 'stationName',
                    title: '充电站名称',
                    minWidth: 150,
                },
                {
                    field: 'stationType',
                    title: '站点类型',
                    minWidth: 150,
                },
                {
                    field: 'operationMode',
                    title: '运营模式',
                    minWidth: 100, // 最小宽度
                },
                {
                    field: 'assetProperty',
                    title: '资产属性',
                    minWidth: 100, // 最小宽度
                },
                {
                    field: 'stationAccessType',
                    title: '接入方式',
                    minWidth: 100, // 最小宽度
                },
                {
                    field: 'pileNum',
                    title: '充电桩数量',
                    minWidth: 120, // 最小宽度
                },
                {
                    field: 'construction',
                    title: '建设场所',
                    minWidth: 120, // 最小宽度
                },
                {
                    field: 'areaType',
                    title: '行政区域',
                    minWidth: 120, // 最小宽度
                },
                {
                    title: '操作',
                    slots: { default: 'operate' },
                    width: 300,
                    align: 'center',
                    fixed: 'right',
                },
            ],

            tablePileColumn:[
                {
                    type: 'seq',
                    title: '序号',
                    width: 60,
                    minWidth: 60, // 最小宽度
                },
                {
                    field: 'stationName',
                    title: '所属充电站',
                    minWidth: 150, // 最小宽度
                },
                {
                    field: 'operationMode',
                    title: '运营模式',
                    minWidth: 120, // 最小宽度
                    formatter: ({ cellValue }) => {
                        return this.selectDictLabel(
                          this.dict.type.ls_charging_operation_mode,
                          cellValue
                        );
                    },
                },
                {
                    field: 'pileNo',
                    title: '充电桩编号',
                    minWidth: 150, // 最小宽度
                },
                {
                    field: 'pileName',
                    title: '充电桩名称',
                    minWidth: 150, // 最小宽度
                },
                {
                    field: 'brandName',
                    title: '设备品牌',
                    minWidth: 100, // 最小宽度
                },
                {
                    title: '设备型号',
                    field: 'modelName',
                    minWidth: 100, // 最小宽度
                },
                {
                  field: 'subType',
                  title: '设备类型',
                  minWidth: 120, // 最小宽度
                  formatter: ({ cellValue }) => {
                          return this.selectDictLabel(
                            this.dict.type.ls_charging_subType,
                            cellValue
                          );
                        },
                },

                {
                    title: '额定功率(kW)',
                    field: 'ratePower',
                    minWidth: 150, // 最小宽度
                },
                {
                    title: '枪数量',
                    field: 'gunSum',
                    minWidth: 100, // 最小宽度
                },

                {
                    title: '操作',
                    slots: { default: 'operate' },
                    width: 200,
                    align: 'center',
                    fixed: 'right',
                },
            ],

            institutionTable: {
                loading: false,
                page: {
                    total: 0,
                    currentPage: 1,
                    pageSize: 10,
                },
                dataTotal: [],
                data: [],
            },

            institutionNumber: '',

            tableInstitutionColumn: [
                {
                    type: 'seq',
                    title: '序号',
                    width: 60,
                    minWidth: 60,
                },
                {
                    field: 'assetUnitName', // todo 更新
                    title: '产权机构',
                    minWidth: 220, // 最小宽度
                },
                {
                    title: '操作',
                    slots: { default: 'operate' },
                    width: 300,
                    align: 'center',
                    fixed: 'right',
                },
            ],

            formLabelWidth: '120px',

            userInfo: {
                form: {
                    useType: '',
                   
                },
                rules: {
                    useType: [
                        { required: true, message: '请选择用户类型', trigger: 'blur' }
                    ],

                }
            },

            useTypeList: [
                {  label: '个人用户', value: '1' },
                {  label: '企业用户', value: '2' },
            ],

            limitInfo: {
                form: {
                    useLimit: [], 
                },
                rules: {
                }
            },

            useLimitList: [
                {  label: '单笔订单仅可用一张券', value: '1' },
            ]


      };
    },

    computed: {
        modalConfig() {
            return {
                addBtn: false,
                viewBtn: false,
                menu: false,
                editBtn: false,
                delBtn: false,
            }
        },
    },
    mounted() {
       
    },
    methods: {
        handleValidityChange() {
            // 切换时清除相关字段的校验状态
            this.$nextTick(() => {
                this.$refs.baseInfoForm.clearValidate(['fixedValidityPeriod', 'effectType', 'effectDay']);
            });
        },

        // 适用范围切换
        useScopeChange() {
            
            this.table = {
                loading: false,
                page: {
                    total: 0,
                    currentPage: 1,
                    pageSize: 10,
                },
                dataTotal: [],
                data: [],
            },
            this.stationNumber= '0',
            this.pileNumber= '0'
        },

         // 选择充电站
        handleChooseStation() {

            this.$refs.chooseStationModal.loadData();

            this.$refs.chooseStationModal.dialogVisible = true;
        },

         // 充电站详情
         hanleDetail(row) {
            const {
                stationId
            } = row;
            this.$router.push({
                path: '/v2g-charging/baseInfo/equipmentAndAssets/station/detail',
                query: {
                    stationId
                }
            })
        },


        // 删除充电站
        handleDelete(row) {
            this.$confirm('确定删除该充电站吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            })
                .then(() => {
                    const index = this.table.dataTotal.findIndex(item => item.stationId === row.stationId);
                    this.table.dataTotal.splice(index, 1);

                    this.table.page.currentPage = 1;
                    this.table.page.total = this.table.dataTotal.length;

                    this.stationNumber =  this.table.dataTotal.length;
                    let number = 0;
                    this.table.dataTotal.forEach(item => {
                        number += Number(item.pileNum);
                    })

                    // this.pileNumber = 0;
                    this.pileNumber = number;

                    this.getTablePage();
                })
                .catch(() => {});
        },

          // 选择充电站确认
          handleChooseStationConfirm(stationList) {
            const list = this.table.dataTotal.concat(stationList);

            const uniqueArray = Array.from(new Map(list.map(item => [item.stationId, item])).values());

            this.table.dataTotal = uniqueArray;

             
            this.stationNumber =  this.table.dataTotal.length;
            let number = 0;
            this.table.dataTotal.forEach(item => {
                number += Number(item.pileNum);
            })

            this.pileNumber = 0;
            this.pileNumber = number;

            this.table.page.currentPage = 1;
            this.table.page.total = this.table.dataTotal.length;

            this.getTablePage();
        },

        // 选择充电桩
        handleChoosePile() {
            this.$refs.choosePileModal.loadData();

            this.$refs.choosePileModal.dialogVisible = true;
        },


        // 删除充电桩
        handlePileDelete(row) {
            this.$confirm('确定删除该充电桩吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            })
                .then(() => {
                    const index = this.table.dataTotal.findIndex(item => item.pileId === row.pileId);
                    this.table.dataTotal.splice(index, 1);

                    this.table.page.currentPage = 1;
                    this.table.page.total = this.table.dataTotal.length;

                    this.pileNumber = this.table.page.total;

                    this.getTablePage();
                })
                .catch(() => {});
        },

        // 充电桩选择确认
        handleChoosePileConfirm(stationList) {
            const list = this.table.dataTotal.concat(stationList);

            const uniqueArray = Array.from(new Map(list.map(item => [item.pileId, item])).values());

            this.table.dataTotal = uniqueArray;

            this.pileNumber =  this.table.dataTotal.length;

            this.table.page.currentPage = 1;
            this.table.page.total = this.table.dataTotal.length;
            this.getTablePage();
            
            
        },

          // 充电桩详情
          hanlePileDetail(row) {
            const { pileId } = row;
            this.$router.push({
                path: '/v2g-charging/baseInfo/equipmentAndAssets/pile/detail',
                query: {
                pileId
                }
            });
        },

        getTablePage() {
            this.table.data = this.table.dataTotal.slice(
                (this.table.page.currentPage - 1) * this.table.page.pageSize,
                this.table.page.currentPage * this.table.page.pageSize
            );
        },


        // 选择产权机构
        handleChooseInstitution() {
            this.$refs.chooseInstitutionModal.loadData();

            this.$refs.chooseInstitutionModal.dialogVisible = true;
        },


          // 选择充电站确认
        handleChooseInstitutionConfirm(institutionList) {
            const list = this.institutionTable.dataTotal.concat(institutionList);

            const uniqueArray = Array.from(new Map(list.map(item => [item.stationId, item])).values());

            this.institutionTable.dataTotal = uniqueArray;

             
            this.institutionNumber =  this.institutionTable.dataTotal.length;


            this.institutionTable.page.currentPage = 1;
            this.institutionTable.page.total = this.institutionTable.dataTotal.length;

            this.getInstitutionTablePage();
        },

        getInstitutionTablePage() {
            this.institutionTable.data = this.institutionTable.dataTotal.slice(
                (this.institutionTable.page.currentPage - 1) * this.institutionTable.page.pageSize,
                this.institutionTable.page.currentPage * this.institutionTable.page.pageSize
            );
        },

        // 删除产权机构
        handleInstitutionDelete(row) {
            this.$confirm('确定删除该产权机构吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            })
                .then(() => {
                    const index = this.institutionTable.dataTotal.findIndex(item => item.stationId === row.stationId);
                    this.institutionTable.dataTotal.splice(index, 1);

                    this.institutionTable.page.currentPage = 1;
                    this.institutionTable.page.total = this.institutionTable.dataTotal.length;

                    this.institutionNumber =  this.institutionTable.dataTotal.length;
                

                    this.getInstitutionTablePage();
                })
                .catch(() => {});
        },

        handleCancel() {
            this.$router.back();
        },

    },
  };
  </script>
  
  <style lang="scss" scoped>
  .container-full {
    background-color: rgba(244, 246, 249, 1);
    padding-bottom: 16px;
    min-height: calc(100% - 80px);
    overflow-y: auto;
  }
   

  .info-card {
  margin: 16px;
  border-radius: 5px;
  border: 1px solid #fff;
  background-color: #fff;

  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;

    .before-icon {
      width: 3px;
      height: 16px;
      background-image: url('~@/assets/station/consno-before.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin-right: 8px;
    }

    .card-head-text {
      font-weight: 500;
      font-size: 16px;
      color: #12151a;
    }
  }

  .card-head-split {
    height: 1.5px;
    background-color: #F9F9FB;
  }

  .form-wrap {
    padding: 16px;
  }

  ::v-deep .el-form-item__label {
    display: flex;
  }

  .protocol-label {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .btn-wrap {
      display: flex;
      align-items: center;
      color: #409eff;
      cursor: pointer;
      font-size: 14px;
      font-weight: 400;

      .icon-add {
        width: 16px;
        height: 16px;
        margin-right: 8px;
      }
    }
  }
}


.no-label ::v-deep .el-form-item__label {
//   display: none;
    color: #fff;
}

.coupon-wrap {
    width: 544px;
    height: 96px;
    padding: 12px;
    box-sizing: border-box;
    border-radius: 5px;
    border: 1px solid #DCDFE6;
    display: flex;
    align-items: center;
    .coupon-img {
        width: 100px;
        height: 72px;
        background-image: url('~@/assets/coupon/preview-icon.png');
        background-size: 100px 72px;
        background-repeat: no-repeat;
        border-radius: 2px;
        position: relative;
        .coupon-amount-wrap {
            height: 44px;
            display: flex;
            width: 100%;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 32px;
            color: #fff;
            .unit {
                font-weight: 400;
                font-size: 12px;   
                margin-left: 3px;
                margin-top: 12px; 
            }
        }

        .coupon-name-wrap {
            position: absolute;
            left: 0;
            top: 46px;
            height: 12px;
            width: 100%;
            
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 500;
            font-size: 12px;
            color: #fff;

        }
        
    }

    .coupon-info-wrap {
        // flex: 1;
        margin-left: 16px;
        height: 100%;
        .coupon-title {
            height: 16px;
            display: flex;
            align-items: center;
            font-weight: 500;
            font-size: 16px;
            color: #21252E;
            margin-bottom: 12px;
        }
        .coupon-time {
            height: 20px;
            background-color: #FFF7E6;
            border-radius: 2px;
            padding: 0 8px;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            font-weight: 400;
            font-size: 12px;
            color: #FF8D24;
            margin-bottom: 12px;

        }
        .coupon-discount {
            height: 12px;
            display: flex;
            align-items: center;
            font-weight: 400;
            font-size: 12px;
            color: #818496;
        }
        
    }
}

.info-wrap {
    margin: 16px;
    border-radius: 5px;
    border: 1px solid #fff;
    overflow: hidden;
    background-color: #fff;

    .form-wrap {
        padding: 16px;
    }

  }
   

  .top-button-wrap {
        // margin-left: 24px;
        display: flex;
        align-items: center;
        height: 34px;
        align-items: center;
        margin-bottom: 14px;
        .choose-info-wrap {
            border-radius: 2px;
            height: 34px;
            padding: 0 10px;
            display: flex;
            align-items: center;
            background-color: #EBF3FF;
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 14px;
            margin-left: 16px;
            color: #217AFF;
            .choose-number {
                font-size: 16px;
                font-weight: 500;
                margin: 0 4px;
            }
        }
        
    }

    ::v-deep .bd3001-content {
        box-shadow: none;
        padding: 0;
    }
 

    .container {
      position: relative;
      padding-bottom: 100px;
      box-sizing: border-box;
      .bottom-wrap {
        z-index: 100;
          position: fixed;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 86px;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          background-color: #FFFFFF;
          padding-right: 32px;
          box-sizing: border-box;
      }
  }
  </style>
  