<template>
    <div class="container container-float ">
      <div class="table-wrap">
            <BuseCrud
                ref="crud"
                :loading="loading"
                :filterOptions="filterOptions"
                :tablePage="tablePage"
                :tableColumn="tableColumn"
                :tableData="tableData"
                :pagerProps="pagerProps"
                :modalConfig="modalConfig"
                @loadData="loadData"
            >
            <template slot="defaultHeader">
                <div>
                    <div class="card-head">
                        <div class="card-head-text">优惠券列表</div>

                        <div class="top-button-wrap">
                          <el-button
                              type="primary"
                              class="set-btn"
                              @click="handleExport"
                          >
                              <svg-icon iconClass="a-export-black"></svg-icon>
                            
                              批量导出
                          </el-button>

                          <el-button
                              type="primary"
                              @click="handleAdd"
                          >
                              <svg-icon iconClass="a-add"></svg-icon>
                              新增优惠券
                          </el-button>

                        </div>
                    </div>
                    

                    
                </div>
                
            </template>

                <template slot="operate" slot-scope="{ row }">
                    <div class="menu-box">
                        <el-button
                            class="button-border"
                            @click="hanleDetail(row)"
                        >
                            详情
                        </el-button>

                        <el-button
                            class="button-border"
                            @click="handelAdjustInventory(row)"
                        >
                            调整库存
                        </el-button>

                        <!-- <el-button
                            class="button-border"
                            @click="handleAudit(row)"
                        >
                            审核
                        </el-button> -->

                        <el-button
                            class="button-border"
                            @click="handleDelete(row)"
                        >
                            停用
                        </el-button>



                    
                    </div>
                
                </template>

            </BuseCrud>
        </div>
      

        <AdjustInventoryModal ref="adjustInventoryModal" />
    </div>
    
  </template>
  
  <script>

  import AdjustInventoryModal from './components/adjustInventoryModal.vue'
  
    export default {
    components: {
      AdjustInventoryModal
    },
    dicts: [],
    data() {
      return {
        loading: false,
        tablePage: { total: 0, currentPage: 1, pageSize: 10 },
        tableColumn: [
          {
            type: 'seq',
            title: '序号',
            width: 60,
            minWidth: 60,
          },
          {
            field: 'couponName',
            title: '优惠券名称',
            minWidth: 180,
          },
          {
            field: 'couponCode',
            title: '优惠券编码',
            minWidth: 180,
          },
          {
            field: 'displayTitle',
            title: '展示标题',
            minWidth: 180,
          },
          {
            field: 'couponBusinessType',
            title: '券业务类型',
            minWidth: 180,
          },
          {
            field: 'couponBasicClassification',
            title: '券基本分类',
            minWidth: 180,
          },
          {
            field: 'preferentialWay',
            title: '优惠方式',
            minWidth: 180,
          },
          {
            field: 'generationMethod',
            title: '生成方式',
            minWidth: 180,
          },
          {
            field: 'dynamicCostUnit',
            title: '动态成本单位',
            minWidth: 180,
          },
          {
            field: 'couponTotalQuantity',
            title: '优惠券总量',
            minWidth: 180,
          },
          {
            field: 'availableInventory',
            title: '可用库存',
            minWidth: 180,
          },
          {
            field: 'validityPeriod',
            title: '有效期',
            minWidth: 180,
          },
         
          {
            field: 'creator',
            title: '创建人',
            minWidth: 180,
          },
          {
            field: 'creationTime',
            title: '创建时间',
            minWidth: 180,
          },
          {
            field: 'couponEffectStatus',
            title: '券生效状态',
            minWidth: 180,
            fixed: 'right',
          },
          {
            field: 'auditStatus',
            title: '审核状态',
            minWidth: 180,
            fixed: 'right',
          },
          {
            title: '操作',
            slots: { default: 'operate' },
            width: 300,
            align: 'center',
            fixed: 'right',
          },
        ],
        tableData: [
          {
            couponName: '服务费满52券',
            couponCode: '010310493021',
            displayTitle: '充电券缴费满5减2元',
            couponBusinessType: '充电券',
            couponBasicClassification: '满减券',
            preferentialWay: '服务费满5元减2元',
            generationMethod: '动态生成',
            dynamicCostUnit: 'XX单位',
            couponTotalQuantity: 1,     // 原"一"转换为数字
            availableInventory: null,   // 原数据为空
            validityPeriod: '领取后立即生效，有效期8天',
            couponEffectStatus: '待生效',
            creator: '张三',
            creationTime: '2024/11/23 11:20:02',
            auditStatus: '未审核'        // 新增字段默认值
          },
          {
            couponName: '服务费8折券',
            couponCode: '010310493022',
            displayTitle: '充电服务费8折券',
            couponBusinessType: '充电券',
            couponBasicClassification: '折扣券',  // 修正"折扣扣"
            preferentialWay: '服务费满5元打8折，最高可减2元',
            generationMethod: '批量生成',
            dynamicCostUnit: 'XX单位',
            couponTotalQuantity: 1000,
            availableInventory: 900,
            validityPeriod: '2024/11/23 至 2024/12/23',  // 修正时间格式
            couponEffectStatus: '生效中',
            creator: '张三',
            creationTime: '2024/11/23 11:20:02',
            auditStatus: '已通过'
          },
          {
            couponName: '服务费满10减2券',
            couponCode: '010310493023',
            displayTitle: '充电服务费满10元减2元',
            couponBusinessType: '充电券',
            couponBasicClassification: '满减券',
            preferentialWay: '10元减2元',
            generationMethod: '批量生成',
            dynamicCostUnit: 'XX单位',
            couponTotalQuantity: 0,
            availableInventory: 0,
            validityPeriod: '领取后立即生效，有效期8天',
            couponEffectStatus: '已失败',
            creator: '张三',             // 修正"前三元"
            creationTime: '2024/11/21 12:02:02',
            auditStatus: '已驳回'
          },
          {
            couponName: '服务费满5减2券',
            couponCode: '010310493024',
            displayTitle: '充电服务费满5减2元',
            couponBusinessType: '充电券',
            couponBasicClassification: '满减券',
            preferentialWay: '服务费满5元减2元',
            generationMethod: '动态生成',
            dynamicCostUnit: 'XX单位',
            couponTotalQuantity: 1000,
            availableInventory: 800,
            validityPeriod: '领取后立即生效，有效期8天',
            couponEffectStatus: '生效中',
            creator: '张三',
            creationTime: '2024/11/20 09:15:00',
            auditStatus: '已通过'
          },
        ],
        params: {
                couponName: '',
                couponCode: '',
                basicCategorie: '',
                generationMethod: '',
                effectiveStatus: '',
                auditStatus: '',
                createTime: [],
        },

      };
    },

    computed: {
      filterOptions() {
        return {
          config: [
            {
                field: 'couponName',
                title: '优惠券名称',
                element: 'el-input',
            },
            {
                field: 'couponCode',
                title: '优惠券编码',
                element: 'el-input',
            },
            {
                field: 'basicCategorie',
                title: '优惠券基本分类',
                element: 'el-select',
                props: {
                    placeholder: '请选择',
                    options: [
                      { label: '满减券', value: '1' },
                      { label: '折扣券', value: '2' },
                    ]
                }
            },
            {
                field: 'generationMethod',
                title: '生成方式',
                element: 'el-select',
                props: {
                    placeholder: '请选择',
                    options: [
                      { label: '动态生成', value: '1' },
                      { label: '批量生成', value: '2' },
                    ]
                }
            },
            {
                field: 'effectiveStatus',
                title: '生效状态',
                element: 'el-select',
                props: {
                    placeholder: '请选择',
                    options: [
                      { label: '已生效', value: '1' },
                      { label: '未生效', value: '2' },
                    ]
                }
            },
            {
                field: 'auditStatus',
                title: '审核状态',
                element: 'el-select',
                props: {
                    placeholder: '请选择',
                    options: [
                      { label: '审核通过', value: '1' },
                      { label: '审核未通过', value: '2' },
                    ]
                }
            },
            {
                field: 'createTime',
                title: '创建时间',
                element: 'el-date-picker',
                props: {
                    type: 'daterange',
                    valueFormat: 'yyyy-MM-dd',
                    options: [],
                }
            },

          ],
          params: this.params,
        };
      },
      modalConfig() {
        return {
            addBtn: false,
            viewBtn: false,
            menu: false,
            editBtn: false,
            delBtn: false,
        }
      },
    },
    mounted() {

    },
    methods: {
      // 新增优惠券
      handleAdd() {
        this.$router.push({
          path: '/v2g-charging-web/operatorManage/couponManage/create',
        })
      },

      // 优惠券详情
      hanleDetail(row) {
        const {
          id
        } = row;
        this.$router.push({
          path: '/v2g-charging-web/operatorManage/couponManage/detail',
          query: {
            id,
          }
        })
      },

      // // 调整库存
      handelAdjustInventory(row) {

        
        this.$refs.adjustInventoryModal.form = {
            couponBusinessType: '充电券',
            basicType: '折扣券',
            couponName: '卡券名称卡券名称卡券名称',
            couponNo: '23454657678769',
        }

        this.$refs.adjustInventoryModal.dialogVisible = true
      }
    },
}
  </script>
  
  <style lang="scss" scoped>
  .container-full {
    background-color: rgba(244, 246, 249, 1);
    padding-bottom: 16px;
    min-height: calc(100% - 80px);
    overflow-y: auto;
  }
   

  .table-wrap {
    ::v-deep .bd3001-table-select-box {
        display: none;
    }
    ::v-deep .bd3001-header  {
        display: block;
    }
    ::v-deep .bd3001-button {
        display: block !important;
    }
    
    .card-head {
        // position: relative; 
        height: 56px;
        padding: 0 16px;
        display: flex;
        align-items: center;
        margin-top: -20px;
        .card-head-text {
        flex:1;
        width: 520px;
            height: 26px;
            background-image: url('~@/assets/images/bg-title.png');
            background-size: 520px 26px;
            background-repeat: no-repeat;
            font-size: 20px;
            font-style: normal;
            font-weight: 500;
            line-height: 16px;
            padding-left: 36px;
            color: #21252e;
        &::before {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            left: -3px; /* 调整这个值来改变边框的宽度 */
            width: 0;
            border-top: 3px solid transparent;
            border-bottom: 3px solid transparent;
            border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
        }
        }   

    }

    .card-head-after {
        width: 100%;
        height: 1px;
        background-color: #DCDEE2;
        margin-bottom: 16px;
    }
    .info-wrap {
        margin-top: 16px;
        display: flex;
        flex-wrap: wrap;
        gap: 16px;

        .info-item {
            background-color: #FAFBFC;
            flex: 1 1 0;
            // min-width: 180px;
            
            border-radius: 5px;
            padding: 8px 24px;
            box-sizing: border-box;
            // margin-right: 16px;
            display: flex;
            .info-icon {
                width: 42px;
                height: 42px;
            }
            .info-right-wrap {
                flex:1;
                margin-left: 8px;
                .info-title {
                    font-weight: 400;
                    font-size: 14px;
                    line-height: 14px;
                    margin-bottom: 8px;
                }
                .info-number {
                    font-size: 20px;
                    font-weight: 500;
                    .info-unit {
                        font-size: 14px;
                        font-weight: 400;
                    }
                }
            }
        }
        .info-item:last-child {
            margin-right: 0;
        }
    }

    .top-button-wrap {
        display:flex;
        margin: 16px 0;
        .set-btn {
          background-color: #FFFFFF;
          color: #292B33;
          border-color: #DFE1E5;
      }
    }
}

.button-border {
    border: 0.01rem solid #217AFF;
    color: #217AFF;
    background-color: #fff;
}
 
  </style>
  