<template>
  <div class="container container-float" style="padding: 0">
    <!-- 互联互通基础信息 -->
    <div class="info-card">
      <div class="card-head">
        <div class="before-icon"></div>
        <div class="card-head-text">互联互通基础信息</div>
      </div>
      <div class="form-wrap">
        <el-form
          :model="baseInfo.form"
          :rules="baseInfo.rules"
          ref="baseInfoForm"
          :label-position="type === 'detail' ? 'right' : 'top'"
        >
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                label="渠道ID："
                prop="operatorCode"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-if="type !== 'detail'"
                  v-model="baseInfo.form.operatorCode"
                  placeholder="固定9位,唯一标识"
                  maxlength="9"
                ></el-input>
                <span v-else class="text">
                  {{ baseInfo.form.operatorCode }}
                </span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="渠道名称："
                prop="operatorName"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-if="type !== 'detail'"
                  v-model="baseInfo.form.operatorName"
                  placeholder="请输入"
                ></el-input>
                <span v-else class="text">
                  {{ baseInfo.form.operatorName }}
                </span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="渠道秘钥："
                prop="inOperatorSecret"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-if="type !== 'detail'"
                  v-model="baseInfo.form.inOperatorSecret"
                  placeholder="请输入"
                ></el-input>
                <span v-else class="text">
                  {{ baseInfo.form.inOperatorSecret }}
                </span>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                label="渠道数据加密秘钥："
                prop="inDataSecret"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-if="type !== 'detail'"
                  v-model="baseInfo.form.inDataSecret"
                  placeholder="请输入"
                ></el-input>
                <span v-else class="text">
                  {{ baseInfo.form.inDataSecret }}
                </span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="渠道初始化向量："
                prop="inDataSecretIv"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-if="type !== 'detail'"
                  v-model="baseInfo.form.inDataSecretIv"
                  placeholder="请输入"
                ></el-input>
                <span v-else class="text">
                  {{ baseInfo.form.inDataSecretIv }}
                </span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="渠道签名秘钥："
                prop="inSigSecret"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-if="type !== 'detail'"
                  v-model="baseInfo.form.inSigSecret"
                  placeholder="请输入"
                ></el-input>
                <span v-else class="text">
                  {{ baseInfo.form.inSigSecret }}
                </span>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                label="互联互通URL地址："
                prop="preUrl"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-if="type !== 'detail'"
                  v-model="baseInfo.form.preUrl"
                  placeholder="请输入"
                ></el-input>
                <span v-else class="text">
                  {{ baseInfo.form.preUrl }}
                </span>
              </el-form-item>
            </el-col>
            <!-- TODO:关联合同 -->
            <!-- <el-col :span="8">
              <el-form-item
                label="关联合同："
                prop="contract"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.contract"
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in contractList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col> -->
            <el-col :span="8">
              <el-form-item
                label="启用状态："
                prop="enableFlag"
                :label-width="formLabelWidth"
              >
                <el-radio-group
                  v-if="type !== 'detail'"
                  v-model="baseInfo.form.enableFlag"
                >
                  <el-radio
                    v-for="dict in dict.type.ls_charging_hlht_status"
                    :key="dict.value"
                    :label="dict.value"
                  >
                    {{ dict.label }}
                  </el-radio>
                </el-radio-group>
                <span v-else class="text">
                  {{
                    getDictLabel(
                      'ls_charging_hlht_status',
                      baseInfo.form.enableFlag
                    )
                  }}
                </span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <!-- 平台信息 -->
    <div class="info-card">
      <div class="card-head">
        <div class="before-icon"></div>
        <div class="card-head-text">平台信息</div>
      </div>
      <div class="form-wrap">
        <el-form
          :model="platformInfo.form"
          :rules="platformInfo.rules"
          ref="platformInfoForm"
          :label-position="type === 'detail' ? 'right' : 'top'"
        >
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                label="平台ID："
                prop="ourOperatorId"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-if="type !== 'detail'"
                  v-model="platformInfo.form.ourOperatorId"
                  placeholder="固定9位"
                  maxlength="9"
                ></el-input>
                <span v-else class="text">
                  {{ baseInfo.form.ourOperatorId }}
                </span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="平台运营商类型："
                prop="ourOperatorType"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-if="type !== 'detail'"
                  v-model="platformInfo.form.ourOperatorType"
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in dict.type.ls_charging_hlht_our_operator_type"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
                <span v-else class="text">
                  {{
                    getDictLabel(
                      'ls_charging_hlht_our_operator_type',
                      platformInfo.form.ourOperatorType
                    )
                  }}
                </span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="平台秘钥："
                prop="outOperatorSecret"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-if="type !== 'detail'"
                  v-model="platformInfo.form.outOperatorSecret"
                  placeholder="请输入"
                ></el-input>
                <span v-else class="text">
                  {{ baseInfo.form.outOperatorSecret }}
                </span>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                label="平台数据加密秘钥："
                prop="outDataSecret"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-if="type !== 'detail'"
                  v-model="platformInfo.form.outDataSecret"
                  placeholder="请输入"
                ></el-input>
                <span v-else class="text">
                  {{ baseInfo.form.outDataSecret }}
                </span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="平台初始化向量："
                prop="outDataSecretIv"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-if="type !== 'detail'"
                  v-model="platformInfo.form.outDataSecretIv"
                  placeholder="固定16位"
                  maxlength="16"
                ></el-input>
                <span v-else class="text">
                  {{ baseInfo.form.outDataSecretIv }}
                </span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="平台签名秘钥："
                prop="outSigSecret"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-if="type !== 'detail'"
                  v-model="platformInfo.form.outSigSecret"
                  placeholder="请输入"
                ></el-input>
                <span v-else class="text">
                  {{ baseInfo.form.outSigSecret }}
                </span>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                label="互联互通协议："
                prop="agreement"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-if="type !== 'detail'"
                  v-model="platformInfo.form.agreement"
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in dict.type.ls_charging_hlht_agreement"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
                <span v-else class="text">
                  {{
                    getDictLabel(
                      'ls_charging_hlht_agreement',
                      platformInfo.form.agreement
                    )
                  }}
                </span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="协议版本号："
                prop="agreementVersion"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-if="type !== 'detail'"
                  v-model="platformInfo.form.agreementVersion"
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in dict.type.ls_charging_hlht_agreement_version"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
                <span v-else class="text">
                  {{
                    getDictLabel(
                      'ls_charging_hlht_agreement_version',
                      platformInfo.form.agreementVersion
                    )
                  }}
                </span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <!-- 接口权限配置 -->
    <div class="info-card">
      <div class="card-head">
        <div class="before-icon"></div>
        <div class="card-head-text">接口权限配置</div>
      </div>
      <div class="form-wrap">
        <el-form
          :model="interfaceInfo.form"
          :rules="interfaceInfo.rules"
          ref="interfaceInfoForm"
          :label-position="type === 'detail' ? 'right' : 'top'"
        >
          <el-row>
            <el-col :span="24">
              <div class="interface-section">
                <el-form-item label="对外开放接口：" prop="outConfig">
                  <el-checkbox-group
                    v-if="type !== 'detail'"
                    v-model="interfaceInfo.form.outConfig"
                  >
                    <el-checkbox
                      v-for="item in dict.type.ls_charging_hlht_open_interface"
                      :key="item.value"
                      :label="item.label"
                    />
                  </el-checkbox-group>
                  <span v-else class="text">
                    {{
                      interfaceInfo.form.outConfig.length
                        ? interfaceInfo.form.outConfig.join('，')
                        : '--'
                    }}
                  </span>
                </el-form-item>
              </div>
            </el-col>
          </el-row>

          <el-row>
            <el-col :span="24">
              <div class="interface-section">
                <el-form-item label="访问对外接口：" prop="inConfig">
                  <el-checkbox-group
                    v-if="type !== 'detail'"
                    v-model="interfaceInfo.form.inConfig"
                  >
                    <el-checkbox
                      v-for="item in dict.type
                        .ls_charging_hlht_access_interface"
                      :key="item.value"
                      :label="item.label"
                    />
                  </el-checkbox-group>
                  <span v-else class="text">
                    {{
                      interfaceInfo.form.inConfig.length
                        ? interfaceInfo.form.inConfig.join('，')
                        : '--'
                    }}
                  </span>
                </el-form-item>
              </div>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="bottom-button-wrap">
      <div>
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSave" v-if="type !== 'detail'">确定</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import {
  createInterconnection,
  updateInterconnection,
  getInterconnectionDetail,
} from '@/api/interconnection/index';
export default {
  name: 'InterconnectionCreate',
  dicts: [
    'ls_charging_hlht_status',
    'ls_charging_hlht_our_operator_type',
    'ls_charging_hlht_agreement',
    'ls_charging_hlht_agreement_version',
    'ls_charging_hlht_open_interface',
    'ls_charging_hlht_access_interface',
  ],
  data() {
    return {
      operatorId: '',
      formLabelWidth: '160px',
      baseInfo: {
        form: {
          operatorCode: '',
          operatorName: '',
          inOperatorSecret: '',
          inDataSecret: '',
          inDataSecretIv: '',
          inSigSecret: '',
          preUrl: '',
          contract: '',
          enableFlag: '',
        },
        rules: {
          operatorCode: [
            { required: true, message: '请输入渠道ID', trigger: 'blur' },
            {
              validator: (rule, value, callback) => {
                if (value && value.length !== 9) {
                  callback(new Error('请输入9位字符'));
                } else {
                  callback();
                }
              },
              trigger: 'blur',
            },
          ],
          operatorName: [
            { required: true, message: '请输入渠道名称', trigger: 'blur' },
          ],
          inOperatorSecret: [
            { required: true, message: '请输入渠道秘钥', trigger: 'blur' },
          ],
          inDataSecret: [
            {
              required: true,
              message: '请输入渠道数据加密秘钥',
              trigger: 'blur',
            },
          ],
          inDataSecretIv: [
            {
              required: true,
              message: '请输入渠道初始化向量',
              trigger: 'blur',
            },
          ],
          inSigSecret: [
            { required: true, message: '请输入渠道签名秘钥', trigger: 'blur' },
          ],
          preUrl: [
            {
              required: true,
              message: '请输入互联互通URL地址',
              trigger: 'blur',
            },
          ],
          contract: [
            { required: true, message: '请选择关联合同', trigger: 'change' },
          ],
          enableFlag: [
            { required: true, message: '请选择启用状态', trigger: 'change' },
          ],
        },
      },
      platformInfo: {
        form: {
          ourOperatorId: '',
          ourOperatorType: '',
          outOperatorSecret: '',
          outDataSecret: '',
          outDataSecretIv: '',
          outSigSecret: '',
          agreement: '',
          agreementVersion: '',
        },
        rules: {
          ourOperatorId: [
            { required: true, message: '请输入平台ID', trigger: 'blur' },
            {
              validator: (rule, value, callback) => {
                if (value && value.length !== 9) {
                  callback(new Error('请输入9位字符'));
                } else {
                  callback();
                }
              },
              trigger: 'blur',
            },
          ],
          ourOperatorType: [
            {
              required: true,
              message: '请选择平台运营商类型',
              trigger: 'change',
            },
          ],
          outOperatorSecret: [
            { required: true, message: '请输入平台秘钥', trigger: 'blur' },
          ],
          outDataSecretIv: [
            {
              required: true,
              message: '请输入平台初始化向量',
              trigger: 'blur',
            },
            {
              validator: (rule, value, callback) => {
                if (value && value.length !== 16) {
                  callback(new Error('请输入16位字符'));
                } else {
                  callback();
                }
              },
              trigger: 'blur',
            },
          ],
          outSigSecret: [
            { required: true, message: '请输入平台名称秘钥', trigger: 'blur' },
          ],
          outDataSecret: [
            {
              required: true,
              message: '请输入平台数据加密秘钥',
              trigger: 'blur',
            },
          ],
          agreement: [
            {
              required: true,
              message: '请选择互联互通协议',
              trigger: 'change',
            },
          ],
          agreementVersion: [
            { required: true, message: '请选择协议版本号', trigger: 'change' },
          ],
        },
      },
      interfaceInfo: {
        form: {
          outConfig: [],
          inConfig: [],
        },
        rules: {
          outConfig: [
            {
              type: 'array',
              required: true,
              message: '请至少选择一个对外开放接口',
              trigger: 'change',
            },
          ],
          inConfig: [
            {
              type: 'array',
              required: true,
              message: '请至少选择一个功能对外接口',
              trigger: 'change',
            },
          ],
        },
      },
      contractList: [],
      platformTypeList: [],
      protocolList: [],
      protocolVersionList: [],
      externalInterfacesList: [],
      functionalInterfacesList: [
        { label: '财务结算数据同步', value: '财务结算数据同步' },
        { label: '设备状态变化推送', value: '设备状态变化推送' },
        { label: '推送启动充电结果', value: '推送启动充电结果' },
        { label: '推送充电状态', value: '推送充电状态' },
        { label: '推送停止充电结果', value: '推送停止充电结果' },
        { label: '推送充电订单账单信息', value: '推送充电订单账单信息' },
        { label: '推送日对账文件下载地址', value: '推送日对账文件下载地址' },
        { label: '开票结果推送', value: '开票结果推送' },
        { label: '订单信息推送', value: '订单信息推送' },
        { label: '站点费率变化推送', value: '站点费率变化推送' },
        { label: '设备充电中状态变化推送', value: '设备充电中状态变化推送' },
        { label: '充电站信息变化推送', value: '充电站信息变化推送' },
        { label: '推送订单结算信息', value: '推送订单结算信息' },
        { label: '推送功率控制指令', value: '推送功率控制指令' },
      ],
    };
  },
  created() {
    const { type = '', operatorId = '' } = this.$route.query || {};
    this.type = type;
    this.operatorId = operatorId;
    if (type) {
      this.getInterconnectionDetail();
    }

    console.log(this.dict.type.ls_charging_hlht_agreement_version, 'dict.type.ls_charging_hlht_agreement_version')
  },
  methods: {
    getDictLabel(dictType, value) {
      console.log(dictType, value, 'dictType, value');
      return (
        this.dict?.type?.[dictType]?.find(
          (item) => item.value === value?.toString()
        )?.label || '--'
      );
    },
    parseConfigToArray(jsonStr, options) {
      try {
        const config = JSON.parse(jsonStr || '{}');
        const selectedKeys = Object.entries(config)
          .filter(([_, value]) => value === 1)
          .map(([key]) => key);
        // const b = options.filter((item) => selectedKeys.includes(item.value));
        // .map((item) => item.label);
        // console.log('selectedKeys', selectedKeys);
        // console.log('options', options);
        // console.log('b', b);
        return options
          .filter((item) => selectedKeys.includes(item.value))
          .map((item) => item.label);
      } catch (error) {
        console.error('解析配置失败:', error);
        return [];
      }
    },
    async getInterconnectionDetail() {
      const [err, res] = await getInterconnectionDetail(this.operatorId);
      if (err) return;
      console.log('res', res);
      const data = res?.data || {};
      data.enableFlag = data.enableFlag?.toString();
      // 回填表单数据
      Object.assign(this.baseInfo.form, data);
      Object.assign(this.platformInfo.form, data);
      this.interfaceInfo.form.outConfig = this.parseConfigToArray(
        data.outConfig,
        this.dict.type.ls_charging_hlht_open_interface
      );

      this.interfaceInfo.form.inConfig = this.parseConfigToArray(
        data.inConfig,
        this.dict.type.ls_charging_hlht_access_interface
      );

      console.log('platformInfo.form.agreementVersion', this.platformInfo.form.agreementVersion);
    },
    handleCancel() {
      this.$router.back();
    },
    getCheckBoxValue(options, value) {
      const json = {};
      options.forEach((item) => {
        json[item.value] = value.includes(item.label) ? 1 : 0;
      });
      return json;
    },
    async handleSave() {
      const outConfig = JSON.stringify(
        this.getCheckBoxValue(
          this.dict.type.ls_charging_hlht_open_interface,
          this.interfaceInfo.form.outConfig
        )
      );
      const inConfig = JSON.stringify(
        this.getCheckBoxValue(
          this.dict.type.ls_charging_hlht_access_interface,
          this.interfaceInfo.form.inConfig
        )
      );
      console.log('outConfig', outConfig);
      console.log('inConfig', inConfig);
      try {
        await Promise.all([
          this.$refs.baseInfoForm.validate(),
          this.$refs.platformInfoForm.validate(),
          this.$refs.interfaceInfoForm.validate(),
        ]);
        if (this.type === 'edit') {
          const [err, res] = await updateInterconnection({
            ...this.baseInfo.form,
            ...this.platformInfo.form,
            ...this.interfaceInfo.form,
            outConfig,
            inConfig,
          });
          if (err) return;
          if (res.code === '10000') {
            this.$message.success('保存成功');
            this.$router.back();
          }
        } else {
          const [err, res] = await createInterconnection({
            ...this.baseInfo.form,
            ...this.platformInfo.form,
            ...this.interfaceInfo.form,
            outConfig,
            inConfig,
          });
          if (err) return;
          console.log('res', res);
          if (res.code === '10000') {
            this.$message.success('保存成功');
            this.$router.back();
          }
        }
      } catch (error) {
        this.$message.error('表单校验失败，请检查输入');
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.info-card {
  margin: 16px;
  border-radius: 5px;
  border: 1px solid #fff;
  overflow: hidden;
  background-color: #fff;

  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;

    .before-icon {
      width: 3px;
      height: 16px;
      background-image: url('~@/assets/station/consno-before.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin-right: 8px;
    }

    .card-head-text {
      flex: 1;
      font-weight: 500;
      font-size: 16px;
      color: #12151a;
    }
  }

  .form-wrap {
    padding: 0 16px 16px 16px;
  }
}

.interface-section {
  margin: 16px 0;

  .el-checkbox-group {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 16px;
  }

  .el-checkbox {
    margin-right: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.bottom-button-wrap {
  height: 86px;
  margin-top: 16px;
  width: 100%;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: 32px;
  box-sizing: border-box;
}

::v-deep .el-input {
  width: 100%;
}
</style>
