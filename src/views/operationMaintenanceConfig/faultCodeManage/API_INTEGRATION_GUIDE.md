# 故障代码管理接口联调指南

## 概述

本文档说明故障代码管理页面与后端接口的字段对应关系和联调要点，参考了派单规则接口的规范进行标准化。

## 接口规范对比

### 派单规则接口规范（参考）
```typescript
// 派单规则数据结构
interface RuleInfo {
  ruleId: string;           // 规则ID
  merchantId: string;       // 租户ID
  ruleCode: string;         // 规则编号
  ruleName: string;         // 规则名称
  enableStatus: number;     // 启用状态：0停用/1启用
  createTime: string;       // 创建时间
  updateTime: string;       // 修改时间
  createBy: string;         // 创建人
  updateBy: string;         // 修改人
  remark: string;           // 备注信息
}
```

### 故障代码接口规范（对标）
```typescript
// 故障代码数据结构
interface FaultCodeInfo {
  faultCodeId: string;      // 故障代码ID
  merchantId: string;       // 租户ID
  faultCode: string;        // 故障代码
  faultName: string;        // 故障名称
  faultLevel: string;       // 故障级别：1一般/2严重/3紧急
  deviceType: string;       // 设备类型：1充电桩/2配电柜/3监控设备
  enableStatus: number;     // 启用状态：0停用/1启用
  createTime: string;       // 创建时间
  updateTime: string;       // 修改时间
  createBy: string;         // 创建人
  updateBy: string;         // 修改人
  remark: string;           // 备注信息
}
```

## 字段变更对照表

| 原字段名 | 新字段名 | 类型变更 | 说明 |
|---------|---------|---------|------|
| `id` | `faultCodeId` | - | 主键ID字段统一命名 |
| `faultStatus` | `enableStatus` | `string` → `number` | 状态字段统一为数值类型 |
| `createUser` | `createBy` | - | 创建人字段统一命名 |
| - | `updateBy` | 新增 | 修改人字段 |
| - | `updateTime` | 新增 | 修改时间字段 |
| - | `merchantId` | 新增 | 租户ID字段 |

## 接口端点规范

### 1. 分页查询
```
POST /vehicle-charging-admin/ops/faultCode/page
```

**请求参数：**
```typescript
interface FaultCodePageRequest {
  pageNum?: number;         // 当前页码，默认为1
  pageSize?: number;        // 每页显示条数，默认为10
  faultCode?: string;       // 故障代码
  faultName?: string;       // 故障名称
  faultLevel?: string;      // 故障级别
  deviceType?: string;      // 设备类型
  enableStatus?: number;    // 启用状态：0停用，1启用
  createTimeBeg?: string;   // 创建时间开始
  createTimeEnd?: string;   // 创建时间结束
  merchantId?: string;      // 租户ID
}
```

**响应格式：**
```typescript
interface FaultCodePageResponse {
  code: string;             // 返回码
  msg: string;              // 返回信息
  subCode?: string;         // 子返回码
  subMsg?: string;          // 子返回信息
  data: FaultCodeInfo[];    // 数据列表
  total: number;            // 总条数
}
```

### 2. 新增故障代码
```
POST /vehicle-charging-admin/ops/faultCode/create
```

**请求参数：**
```typescript
interface FaultCodeCreateRequest {
  faultCode: string;        // 故障代码（必填）
  faultName: string;        // 故障名称（必填）
  faultLevel: string;       // 故障级别（必填）
  deviceType: string;       // 设备类型（必填）
  enableStatus?: number;    // 启用状态，默认1
  remark?: string;          // 备注信息
  merchantId?: string;      // 租户ID
}
```

### 3. 修改故障代码
```
POST /vehicle-charging-admin/ops/faultCode/update
```

**请求参数：**
```typescript
interface FaultCodeUpdateRequest {
  faultCodeId: string;      // 故障代码ID（必填）
  faultCode: string;        // 故障代码（必填）
  faultName: string;        // 故障名称（必填）
  faultLevel: string;       // 故障级别（必填）
  deviceType: string;       // 设备类型（必填）
  enableStatus?: number;    // 启用状态
  remark?: string;          // 备注信息
  merchantId?: string;      // 租户ID
}
```

### 4. 删除故障代码
```
POST /vehicle-charging-admin/ops/faultCode/delete
```

**请求参数：**
```typescript
interface FaultCodeDeleteRequest {
  faultCodeIds: string[];   // 故障代码ID数组
}
```

### 5. 故障代码详情
```
POST /vehicle-charging-admin/ops/faultCode/detail
```

**请求参数：**
```typescript
interface FaultCodeDetailRequest {
  faultCodeId: string;      // 故障代码ID
}
```

### 6. 启用/停用故障代码
```
POST /vehicle-charging-admin/ops/faultCode/onOff
```

**请求参数：**
```typescript
interface FaultCodeToggleRequest {
  faultCodeId: string;      // 故障代码ID
}
```

## 前端页面适配

### 1. 表格列配置更新
- `id` → `faultCodeId`
- `faultStatus` → `enableStatus`
- `createUser` → `createBy`

### 2. 表单字段更新
- 状态字段类型：`string` → `number`
- 状态选项值：`'0'/'1'` → `0/1`

### 3. API调用更新
- 删除操作传递 `faultCodeId` 而非 `id`
- 编辑操作使用 `faultCodeId` 作为主键

## 联调检查清单

### 接口联调
- [ ] 分页查询接口返回数据格式正确
- [ ] 筛选功能正常工作
- [ ] 新增功能保存成功
- [ ] 编辑功能更新成功
- [ ] 删除功能执行成功
- [ ] 状态切换功能正常

### 数据验证
- [ ] 字段类型匹配（特别是 enableStatus 为数值类型）
- [ ] 必填字段验证正确
- [ ] 字段长度限制合理
- [ ] 特殊字符处理正确

### 用户体验
- [ ] 加载状态显示正常
- [ ] 错误提示信息清晰
- [ ] 操作反馈及时
- [ ] 页面响应速度合理

## 注意事项

1. **状态字段类型**：确保后端返回的 `enableStatus` 为数值类型（0或1），而非字符串
2. **主键字段**：所有操作都使用 `faultCodeId` 作为主键，确保前后端一致
3. **租户隔离**：如果系统支持多租户，确保 `merchantId` 字段正确传递
4. **时间格式**：确保时间字段格式统一（建议使用 ISO 8601 格式）
5. **错误处理**：确保接口返回的错误信息格式与前端错误处理逻辑匹配

## 测试用例

### 基础功能测试
1. 查询列表数据
2. 添加新的故障代码
3. 编辑现有故障代码
4. 删除故障代码
5. 切换启用/停用状态

### 边界条件测试
1. 空数据列表显示
2. 分页边界测试
3. 字段长度限制测试
4. 特殊字符输入测试
5. 网络异常处理测试
