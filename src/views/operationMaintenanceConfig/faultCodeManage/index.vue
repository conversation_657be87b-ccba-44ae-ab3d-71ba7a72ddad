<template>
  <div class="container container-float">
    <div class="table-wrap">
      <BuseCrud
        ref="crud"
        :loading="loading"
        :filterOptions="filterOptions"
        :tablePage="tablePage"
        :tableColumn="tableColumn"
        :tableData="tableData"
        :pagerProps="pagerProps"
        :modalConfig="modalConfig"
        class="buse-wrap-organization"
        @loadData="loadData"
      >
        <template slot="defaultHeader">
          <div>
            <div class="card-head">
              <div class="card-head-text">故障代码管理</div>

              <div class="top-button-wrap">
                <el-button type="primary" @click="() => handleAdd()">
                  新增
                </el-button>
              </div>
            </div>
          </div>
        </template>

        <template slot="operate" slot-scope="{ row }">
          <el-dropdown trigger="click">
            <el-button class="button-border" type="primary" plain>
              操作
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item>
                <div @click="handleEdit(row)">编辑</div>
              </el-dropdown-item>

              <el-dropdown-item>
                <div @click="() => handleDelete(row)">删除</div>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </BuseCrud>
    </div>

    <!-- 新增/编辑弹窗 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      @close="handleCancel"
      width="50%"
      :destroy-on-close="true"
    >
      <el-form
        :model="form"
        :rules="rules"
        ref="faultCodeForm"
        label-position="top"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="故障代码" prop="faultCode">
              <el-input
                v-model="form.faultCode"
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="故障名称" prop="faultName">
              <el-input
                v-model="form.faultName"
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="故障级别" prop="faultLevel">
              <el-select
                v-model="form.faultLevel"
                placeholder="请选择"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="item in faultLevelOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备类型" prop="deviceType">
              <el-select
                v-model="form.deviceType"
                placeholder="请选择"
                clearable
                style="width: 100%"
              >
                <el-option
                  v-for="item in deviceTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="故障状态" prop="faultStatus">
              <el-radio-group v-model="form.faultStatus">
                <el-radio label="1">启用</el-radio>
                <el-radio label="0">停用</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input
                v-model="form.remark"
                type="textarea"
                :rows="3"
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getFaultCodeList,
  addFaultCode,
  updateFaultCode,
  deleteFaultCode,
} from '@/api/operationMaintenanceConfig/faultCodeManage';

import StatusDot from '@/components/Business/StatusDot';

export default {
  components: {
    StatusDot,
  },
  dicts: [],
  data() {
    return {
      loading: false,
      dialogVisible: false,
      dialogTitle: '新增',
      isEdit: false,
      editId: '',

      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
        },
        {
          field: 'faultCode',
          title: '故障代码',
          minWidth: 120,
        },
        {
          field: 'faultName',
          title: '故障名称',
          minWidth: 180,
        },
        {
          field: 'faultLevel',
          title: '故障级别',
          minWidth: 120,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(this.faultLevelOptions, cellValue);
          },
        },
        {
          field: 'deviceType',
          title: '设备类型',
          minWidth: 120,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(this.deviceTypeOptions, cellValue);
          },
        },
        {
          field: 'faultStatus',
          title: '状态',
          minWidth: 100,
          slots: {
            default: ({ row }) => {
              return (
                <StatusDot
                  value={row.faultStatus}
                  dictValue={this.enableStatusList}
                  colors={['success', 'danger']}
                ></StatusDot>
              );
            },
          },
        },
        {
          field: 'createTime',
          title: '创建时间',
          minWidth: 180,
        },
        {
          field: 'createUser',
          title: '创建人',
          minWidth: 120,
        },
        {
          title: '操作',
          slots: { default: 'operate' },
          width: 80,
          align: 'center',
          fixed: 'right',
        },
      ],
      tableData: [],
      params: {
        faultCode: '',
        faultName: '',
        faultLevel: '',
        deviceType: '',
        faultStatus: '',
      },

      form: {
        faultCode: '',
        faultName: '',
        faultLevel: '',
        deviceType: '',
        faultStatus: '1',
        remark: '',
      },

      rules: {
        faultCode: [
          { required: true, message: '请输入故障代码', trigger: 'blur' },
        ],
        faultName: [
          { required: true, message: '请输入故障名称', trigger: 'blur' },
        ],
        faultLevel: [
          { required: true, message: '请选择故障级别', trigger: 'change' },
        ],
        deviceType: [
          { required: true, message: '请选择设备类型', trigger: 'change' },
        ],
        faultStatus: [
          { required: true, message: '请选择故障状态', trigger: 'change' },
        ],
      },

      enableStatusList: [
        { label: '启用', value: '1' },
        { label: '停用', value: '0' },
      ],

      faultLevelOptions: [
        { label: '一般', value: '1' },
        { label: '严重', value: '2' },
        { label: '紧急', value: '3' },
      ],

      deviceTypeOptions: [
        { label: '充电桩', value: '1' },
        { label: '配电柜', value: '2' },
        { label: '监控设备', value: '3' },
      ],
    };
  },

  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'faultCode',
            title: '故障代码',
            element: 'el-input',
          },
          {
            field: 'faultName',
            title: '故障名称',
            element: 'el-input',
          },
          {
            field: 'faultLevel',
            title: '故障级别',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.faultLevelOptions,
            },
          },
          {
            field: 'deviceType',
            title: '设备类型',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.deviceTypeOptions,
            },
          },
          {
            field: 'faultStatus',
            title: '状态',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.enableStatusList,
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  mounted() {
    this.loadData();
  },
  methods: {
    // 加载数据
    async loadData() {
      const { faultCode, faultName, faultLevel, deviceType, faultStatus } =
        this.params;

      const params = {
        faultCode,
        faultName,
        faultLevel,
        deviceType,
        faultStatus,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };

      this.loading = true;
      const [err, res] = await getFaultCodeList(params);

      this.loading = false;
      if (err) return;

      const { data, total } = res;

      this.tableData = data;
      this.tablePage.total = total;
    },

    // 新增
    handleAdd() {
      this.dialogTitle = '新增';
      this.isEdit = false;
      this.editId = '';
      this.form = {
        faultCode: '',
        faultName: '',
        faultLevel: '',
        deviceType: '',
        faultStatus: '1',
        remark: '',
      };
      this.dialogVisible = true;
    },

    // 编辑
    handleEdit(row) {
      this.dialogTitle = '编辑';
      this.isEdit = true;
      this.editId = row.id;
      this.form = {
        faultCode: row.faultCode,
        faultName: row.faultName,
        faultLevel: row.faultLevel,
        deviceType: row.deviceType,
        faultStatus: row.faultStatus,
        remark: row.remark || '',
      };
      this.dialogVisible = true;
    },

    // 删除
    handleDelete(row) {
      this.$confirm('确定删除该故障代码吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          const [err, res] = await deleteFaultCode({
            id: row.id,
          });
          if (err) return;

          this.$message.success('删除成功');
          this.loadData();
        })
        .catch(() => {});
    },

    // 取消
    handleCancel() {
      this.dialogVisible = false;
    },

    // 确认
    handleConfirm() {
      this.$refs.faultCodeForm.validate(async (valid) => {
        if (valid) {
          const params = {
            ...this.form,
          };

          if (this.isEdit) {
            params.id = this.editId;
          }

          const apiMethod = this.isEdit ? updateFaultCode : addFaultCode;
          const [err, res] = await apiMethod(params);

          if (err) return;

          this.$message.success(this.isEdit ? '编辑成功' : '新增成功');
          this.dialogVisible = false;
          this.loadData();
        }
      });
    },

    // 字典标签回显
    selectDictLabel(datas, value) {
      if (value === undefined) {
        return '';
      }
      var actions = [];
      Object.keys(datas).some((key) => {
        if (datas[key].value == '' + value) {
          actions.push(datas[key].label);
          return true;
        }
      });
      if (actions.length === 0) {
        actions.push(value);
      }
      return actions.join('');
    },
  },
};
</script>

<style lang="scss" scoped>
.container-full {
  background-color: rgba(244, 246, 249, 1);
  padding-bottom: 16px;
  min-height: calc(100% - 80px);
  overflow-y: auto;
}

.table-wrap {
  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }

  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    margin-top: -20px;
    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px;
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1);
      }
    }
  }

  .top-button-wrap {
    display: flex;
    margin: 16px 0;
    .set-btn {
      background-color: #ffffff;
      color: #292b33;
      border-color: #dfe1e5;
    }
  }
}

.button-border {
  border: 0.01rem solid #217aff;
  color: #217aff;
  background-color: #fff;
}
</style>
