# 故障代码管理页面

## 功能说明

这个页面是参考 `src/views/operationMaintenanceConfig/operatioMaintenanceTeam/` 文件生成的故障代码管理页面，实现了图片中展示的功能。

## 主要功能

### 1. 列表展示
- 故障代码
- 故障名称  
- 故障级别（一般、严重、紧急）
- 设备类型（充电桩、配电柜、监控设备）
- 状态（启用/停用）
- 创建时间
- 创建人
- 操作（编辑、删除）

### 2. 搜索筛选
- 故障代码搜索
- 故障名称搜索
- 故障级别筛选
- 设备类型筛选
- 状态筛选

### 3. 新增/编辑功能
弹窗表单包含以下字段：
- 故障代码（必填）
- 故障名称（必填）
- 故障级别（必填，下拉选择）
- 设备类型（必填，下拉选择）
- 故障状态（必填，单选框：启用/停用）
- 备注（可选，多行文本）

### 4. 删除功能
- 支持单个删除
- 删除前有确认提示

## 技术实现

### 组件结构
```
src/views/operationMaintenanceConfig/faultCodeManage/
├── index.vue                 # 主页面组件
└── README.md                # 说明文档
```

### API 接口
```
src/api/operationMaintenanceConfig/faultCodeManage.js
├── getFaultCodeList()       # 分页查询
├── addFaultCode()          # 新增
├── updateFaultCode()       # 编辑
├── deleteFaultCode()       # 删除
└── getFaultCodeDetail()    # 详情
```

### 使用的组件
- **BuseCrud**: 主要的表格组件，提供分页、筛选、操作等功能
- **StatusDot**: 状态显示组件
- **Element UI**: 表单、弹窗、按钮等基础组件

## 页面特点

1. **响应式设计**: 适配不同屏幕尺寸
2. **用户友好**: 操作简单，提示明确
3. **数据验证**: 表单字段有完整的验证规则
4. **状态管理**: 清晰的加载状态和错误处理
5. **样式统一**: 与项目整体风格保持一致

## 路由配置

由于项目使用动态路由，需要在主应用中配置相应的路由信息：

```javascript
{
  path: '/operationMaintenanceConfig/faultCodeManage',
  component: 'operationMaintenanceConfig/faultCodeManage/index',
  name: 'FaultCodeManage',
  meta: { 
    title: '故障代码管理',
    icon: 'fault-code'
  }
}
```

## 注意事项

1. 确保后端 API 接口已实现
2. 检查权限配置是否正确
3. 根据实际需求调整字段和验证规则
4. 可根据业务需要扩展更多功能（如批量操作、导入导出等）
