<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>故障代码管理页面演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 16px;
        }
        .content {
            padding: 30px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .feature-card {
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            padding: 20px;
            background: #fafbfc;
        }
        .feature-card h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 18px;
        }
        .feature-card ul {
            margin: 0;
            padding-left: 20px;
            color: #666;
        }
        .feature-card li {
            margin-bottom: 8px;
        }
        .screenshot {
            text-align: center;
            margin: 30px 0;
        }
        .screenshot img {
            max-width: 100%;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        .tech-stack {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        .tech-stack h3 {
            margin: 0 0 15px 0;
            color: #333;
        }
        .tech-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        .tech-tag {
            background: #e3f2fd;
            color: #1976d2;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 14px;
            border: 1px solid #bbdefb;
        }
        .code-block {
            background: #f6f8fa;
            border: 1px solid #e1e4e8;
            border-radius: 6px;
            padding: 16px;
            margin: 15px 0;
            overflow-x: auto;
        }
        .code-block pre {
            margin: 0;
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
            font-size: 14px;
            line-height: 1.45;
            color: #24292e;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
            margin-left: 10px;
        }
        .status-ready {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-demo {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>故障代码管理页面</h1>
            <p>基于 Vue.js + Element UI + BuseCrud 组件构建的企业级管理系统</p>
            <span class="status-badge status-ready">✅ 开发完成</span>
            <span class="status-badge status-demo">🚀 演示可用</span>
        </div>
        
        <div class="content">
            <div class="feature-grid">
                <div class="feature-card">
                    <h3>📋 列表管理</h3>
                    <ul>
                        <li>故障代码、名称展示</li>
                        <li>故障级别分类（一般/严重/紧急）</li>
                        <li>设备类型分类</li>
                        <li>状态管理（启用/停用）</li>
                        <li>创建时间和创建人信息</li>
                        <li>分页和搜索功能</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>🔍 搜索筛选</h3>
                    <ul>
                        <li>故障代码模糊搜索</li>
                        <li>故障名称模糊搜索</li>
                        <li>故障级别下拉筛选</li>
                        <li>设备类型下拉筛选</li>
                        <li>状态下拉筛选</li>
                        <li>实时搜索结果更新</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>✏️ 新增编辑</h3>
                    <ul>
                        <li>弹窗式表单设计</li>
                        <li>完整的字段验证</li>
                        <li>故障代码唯一性检查</li>
                        <li>级别和类型下拉选择</li>
                        <li>状态单选框控制</li>
                        <li>备注信息支持</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>🗑️ 删除操作</h3>
                    <ul>
                        <li>单条记录删除</li>
                        <li>删除前确认提示</li>
                        <li>操作结果反馈</li>
                        <li>数据安全保护</li>
                        <li>操作日志记录</li>
                        <li>权限控制支持</li>
                    </ul>
                </div>
            </div>
            
            <div class="tech-stack">
                <h3>🛠️ 技术栈</h3>
                <div class="tech-tags">
                    <span class="tech-tag">Vue.js 2.x</span>
                    <span class="tech-tag">Element UI</span>
                    <span class="tech-tag">BuseCrud</span>
                    <span class="tech-tag">Axios</span>
                    <span class="tech-tag">SCSS</span>
                    <span class="tech-tag">ES6+</span>
                </div>
            </div>
            
            <h3>📁 文件结构</h3>
            <div class="code-block">
                <pre>src/views/operationMaintenanceConfig/faultCodeManage/
├── index.vue                 # 主页面组件
├── README.md                # 功能说明文档
└── demo.html               # 演示页面

src/api/operationMaintenanceConfig/
└── faultCodeManage.js      # API 接口定义</pre>
            </div>
            
            <h3>🚀 快速开始</h3>
            <div class="code-block">
                <pre># 1. 确保项目依赖已安装
npm install

# 2. 启动开发服务器
npm run dev

# 3. 访问页面（需要配置路由）
# /operationMaintenanceConfig/faultCodeManage</pre>
            </div>
            
            <h3>⚙️ 配置说明</h3>
            <p>由于项目使用动态路由，需要在主应用中配置相应的路由信息。开发环境下已配置模拟数据，可以直接体验完整功能。</p>
            
            <div class="code-block">
                <pre>// 路由配置示例
{
  path: '/operationMaintenanceConfig/faultCodeManage',
  component: 'operationMaintenanceConfig/faultCodeManage/index',
  name: 'FaultCodeManage',
  meta: { 
    title: '故障代码管理',
    icon: 'fault-code'
  }
}</pre>
            </div>
            
            <h3>📝 功能特点</h3>
            <ul>
                <li><strong>响应式设计</strong>：适配不同屏幕尺寸，移动端友好</li>
                <li><strong>用户体验</strong>：操作简单直观，提示信息清晰</li>
                <li><strong>数据验证</strong>：完整的前端验证规则，确保数据质量</li>
                <li><strong>状态管理</strong>：清晰的加载状态和错误处理机制</li>
                <li><strong>样式统一</strong>：与项目整体设计风格保持一致</li>
                <li><strong>可扩展性</strong>：模块化设计，易于扩展新功能</li>
            </ul>
        </div>
    </div>
</body>
</html>
