<template>
  <div class="container container-float" style="padding: 0">
    <div class="table-wrap">
      <BuseCrud
        ref="crud"
        :loading="tableLoading"
        :filterOptions="tableFilterOptions"
        :tablePage="tablePage"
        :tableColumn="tableColumn"
        :tableData="tableData"
        :pagerProps="pagerProps"
        :modalConfig="modalConfig"
        class="buse-wrap-station"
        @loadData="loadTableData"
      >
        <template slot="defaultHeader">
          <div>
            <div class="card-head">
              <div class="card-head-text">派单规则列表</div>

              <div class="top-button-wrap">
                <el-button
                  type="primary"
                  @click="() => handleRuleEdit('create', {})"
                >
                  新增派单规则
                </el-button>
              </div>
            </div>
          </div>
        </template>
        <template slot="operate" slot-scope="{ row }">
          <div class="menu-box">
            <el-button type="primary" plain @click="toggleRuleStatus(row)">
              {{ row.enableStatus === 1 ? '停用' : '启用' }}
            </el-button>
            <el-button
              v-if="row.enableStatus === 0"
              type="primary"
              plain
              @click="handleRuleEdit('edit', row)"
            >
              编辑
            </el-button>
            <el-button
              v-if="row.enableStatus === 0"
              type="danger"
              plain
              @click="handleRuleDel(row)"
            >
              删除
            </el-button>
            <el-button
              type="primary"
              plain
              @click="handleRuleEdit('detail', row)"
            >
              详情
            </el-button>
          </div>
        </template>
      </BuseCrud>
    </div>
    <editRule
      ref="editRule"
      :type="ruleType"
      :detailObj="selectRule"
      @ruleAdd="loadTableData"
    />
  </div>
</template>

<script>
import editRule from './components/editRule.vue';
import {
  getRulePage,
  toggleRuleStatus,
  deleteRules,
  getRuleDetail,
} from '@/api/operationMaintenanceConfig/dispatchRule';

export default {
  components: {
    editRule,
  },
  dicts: [],
  data() {
    return {
      tableLoading: false,
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      params: {
        ruleCode: '',
        ruleName: '',
        enableStatus: '',
        createTimeBeg: '',
        createTimeEnd: '',
      },
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
          fixed: 'left',
        },
        {
          field: 'ruleCode',
          title: '规则编号',
          minWidth: 120,
        },
        {
          field: 'ruleName',
          title: '规则名称',
          minWidth: 120,
        },
        {
          field: 'checkRule',
          title: '校验规则',
          minWidth: 100,
          formatter: ({ cellValue }) => {
            const ruleMap = {
              VOLTAGE: '电压',
              CURRENT: '电流',
              TEMPERATURE: '温度',
            };
            return ruleMap[cellValue] || cellValue;
          },
        },
        {
          field: 'compareOperator',
          title: '比较操作符',
          minWidth: 100,
          formatter: ({ cellValue }) => {
            const operatorMap = {
              GT: '大于',
              LT: '小于',
            };
            return operatorMap[cellValue] || cellValue;
          },
        },
        {
          field: 'checkPercent',
          title: '校验阈值(%)',
          minWidth: 100,
        },
        {
          field: 'enableStatus',
          title: '状态',
          minWidth: 80,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              [
                {
                  value: 0,
                  label: '停用',
                },
                {
                  value: 1,
                  label: '启用',
                },
              ],
              cellValue
            );
          },
        },
        {
          field: 'createTime',
          title: '创建时间',
          minWidth: 160,
        },
        {
          title: '操作',
          slots: { default: 'operate' },
          width: 300,
          align: 'center',
          fixed: 'right',
        },
      ],
      tableData: [],
      selectRule: {},
      ruleType: 'create',
    };
  },

  computed: {
    tableFilterOptions() {
      return {
        config: [
          {
            field: 'ruleCode',
            title: '规则编号',
            element: 'el-input',
          },
          {
            field: 'ruleName',
            title: '规则名称',
            element: 'el-input',
          },
          {
            field: 'enableStatus',
            title: '状态',
            element: 'el-select',
            options: [
              {
                value: '',
                label: '全部',
              },
              {
                value: 0,
                label: '停用',
              },
              {
                value: 1,
                label: '启用',
              },
            ],
          },
          {
            field: 'createTime',
            title: '创建时间',
            element: 'el-date-picker',
            props: {
              type: 'daterange',
              rangeSeparator: '至',
              startPlaceholder: '开始日期',
              endPlaceholder: '结束日期',
              valueFormat: 'yyyy-MM-dd HH:mm:ss',
            },
            handler: (val) => {
              if (val) {
                this.params.createTimeBeg = val[0];
                this.params.createTimeEnd = val[1];
              } else {
                this.params.createTimeBeg = '';
                this.params.createTimeEnd = '';
              }
            },
          },
        ],
      };
    },
    modalConfig() {
      return {
        title: '派单规则',
        width: '800px',
      };
    },
  },
  mounted() {},
  methods: {
    // 加载表格数据
    async loadTableData() {
      this.tableLoading = true;
      try {
        const params = {
          pageNum: this.tablePage.currentPage,
          pageSize: this.tablePage.pageSize,
          ...this.params,
        };
        const [err, res] = await getRulePage(params);
        if (err) return;
        this.tableData = res.data || [];
        this.tablePage.total = res.total || 0;
      } finally {
        this.tableLoading = false;
      }
    },

    // 编辑规则
    async handleRuleEdit(type, row) {
      this.ruleType = type;
      this.selectRule = {};

      if (type !== 'create') {
        const [err, res] = await getRuleDetail({ ruleId: row.ruleId });
        if (err) return;
        this.selectRule = res.data || {};
      }

      this.$refs.editRule.dialogVisible = true;
    },

    // 启用、停用规则
    toggleRuleStatus(row) {
      this.$confirm(
        `确定${row.enableStatus === 1 ? '停用' : '启用'}派单规则：${
          row?.ruleName || ''
        }吗？`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(async () => {
        const [err, res] = await toggleRuleStatus({ ruleId: row.ruleId });
        if (err) return;
        if (row.enableStatus === 1) {
          this.$message({
            type: 'success',
            message: '停用成功!',
          });
        } else {
          this.$message({
            type: 'success',
            message: '启用成功!',
          });
        }
        this.tablePage.total = 0;
        this.tablePage.currentPage = 1;
        this.loadTableData();
      });
    },

    // 删除规则
    handleRuleDel(row) {
      this.$confirm(`确定删除派单规则：${row?.ruleName || ''}吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        const [err, res] = await deleteRules({
          ruleIds: [row?.ruleId || ''],
        });
        if (err) return;
        this.$message({
          type: 'success',
          message: '删除成功!',
        });
        this.tablePage.total = 0;
        this.tablePage.currentPage = 1;
        this.loadTableData();
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.table-wrap {
  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }
  margin: 16px;

  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    margin-top: -20px;
    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }

  .top-button-wrap {
    display: flex;
    margin: 16px 0;
  }
}
</style>
