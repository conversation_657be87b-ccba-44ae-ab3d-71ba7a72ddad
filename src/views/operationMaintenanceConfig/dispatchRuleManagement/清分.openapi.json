{"openapi": "3.0.1", "info": {"title": "清分", "description": "", "version": "1.0.0"}, "tags": [], "paths": {"/ops/rule/page": {"post": {"summary": "派单规则分页查询", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"pageNum": {"type": "integer", "description": "当前页码", "minimum": 1, "default": 1}, "pageSize": {"type": "integer", "description": "每页数量", "minimum": 1, "maximum": 100, "default": 10}, "ruleCode": {"type": "string", "description": "规则编号"}, "ruleName": {"type": "string", "description": "规则名称"}, "createTimeBeg": {"type": "string", "format": "date-time", "description": "创建时间开始"}, "createTimeEnd": {"type": "string", "format": "date-time", "description": "创建时间结束"}, "enableStatus": {"type": "integer", "description": " 枚举说明: 0:停用(规则不生效), 1:启用(规则生效中)", "enum": [0, 1]}, "merchantId": {"type": "string", "description": "租户ID", "pattern": "^MCH\\d{4,8}$"}}, "required": ["pageNum", "pageSize"]}, "examples": {}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"ruleId": {"type": "string", "description": "规则ID"}, "merchantId": {"type": "string", "description": "租户ID", "pattern": "^MCH\\d{4}$"}, "ruleCode": {"type": "string", "description": "规则编号"}, "ruleName": {"type": "string", "description": "规则名称"}, "checkRule": {"type": "string", "description": " 枚举说明: 电压/电流/温度", "enum": ["VOLTAGE", "CURRENT", "TEMPERATURE"]}, "compareOperator": {"type": "string", "description": " 枚举说明: 大于/小于", "enum": ["GT", "LT"]}, "checkPercent": {"type": "integer", "description": "校验阈值(%)", "minimum": 0, "maximum": 100}, "enableStatus": {"type": "integer", "description": " 枚举说明: 0:停用(规则不生效)/1:启用(规则生效中)", "enum": [0, 1]}, "remark": {"type": "string", "description": "备注信息", "maxLength": 500}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "updateTime": {"type": "string", "format": "date-time", "description": "修改时间"}, "createBy": {"type": "string", "description": "创建人"}, "updateBy": {"type": "string", "description": "修改人"}}, "required": ["ruleId", "merchantId", "ruleCode", "ruleName", "checkRule", "compareOperator", "checkPercent"]}}}, "headers": {}}}, "security": []}}, "/ops/rule/detail": {"post": {"summary": "派单规则详情", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"ruleId": {"type": "string", "description": "规则Id"}}}, "examples": {}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"ruleId": {"type": "string", "description": "规则ID"}, "merchantId": {"type": "string", "description": "租户ID", "pattern": "^MCH\\d{4}$"}, "ruleCode": {"type": "string", "description": "规则编号"}, "ruleName": {"type": "string", "description": "规则名称"}, "checkRule": {"type": "string", "description": " 枚举说明: 电压/电流/温度", "enum": ["VOLTAGE", "CURRENT", "TEMPERATURE"]}, "compareOperator": {"type": "string", "description": " 枚举说明: 大于/小于", "enum": ["GT", "LT"]}, "checkPercent": {"type": "integer", "description": "校验阈值(%)", "minimum": 0, "maximum": 100}, "enableStatus": {"type": "integer", "description": " 枚举说明: 0:停用(规则不生效)/1:启用(规则生效中)", "enum": [0, 1]}, "remark": {"type": "string", "description": "备注信息", "maxLength": 500}, "createTime": {"type": "string", "format": "date-time", "description": "创建时间"}, "updateTime": {"type": "string", "format": "date-time", "description": "修改时间"}, "createBy": {"type": "string", "description": "创建人"}, "updateBy": {"type": "string", "description": "修改人"}}, "required": ["ruleId", "merchantId", "ruleCode", "ruleName", "checkRule", "compareOperator", "checkPercent"]}}}, "headers": {}}}, "security": []}}, "/ops/rule/onOff": {"post": {"summary": "派单规则启用/停用", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"ruleId": {"type": "string", "description": "规则Id"}}}, "examples": {}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "string"}}}, "headers": {}}}, "security": []}}, "/ops/rule/create": {"post": {"summary": "派单规则新增", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"ruleId": {"type": "string", "description": "规则ID"}, "merchantId": {"type": "string", "description": "租户ID", "pattern": "^MCH\\d{4}$"}, "enableStatus": {"type": "integer", "description": " 枚举说明: 0:规则停用(停止调度) 1:规则启用(可被调度)", "enum": [0, 1]}, "ruleName": {"type": "string", "description": "规则名称"}, "remark": {"type": "string", "description": "备注信息", "maxLength": 500}, "checkRule": {"type": "string", "description": " 枚举说明: 电压/电流/温度", "enum": ["VOLTAGE", "CURRENT", "TEMPERATURE"]}, "compareOperator": {"type": "string", "description": " 枚举说明: 大于/小于", "enum": ["GT", "LT"]}, "checkPercent": {"type": "integer", "description": "校验阈值(%)", "minimum": 0, "maximum": 100}}, "required": ["ruleId", "ruleName", "checkRule", "compareOperator", "checkPercent"]}, "examples": {}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "string"}}}, "headers": {}}}, "security": []}}, "/ops/rule/update": {"post": {"summary": "派单规则修改", "deprecated": false, "description": "", "tags": [], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"ruleId": {"type": "string", "description": "规则ID"}, "merchantId": {"type": "string", "description": "租户ID", "pattern": "^MCH\\d{4}$"}, "enableStatus": {"type": "integer", "description": " 枚举说明: 0:规则停用(停止调度) 1:规则启用(可被调度)", "enum": [0, 1]}, "ruleName": {"type": "string", "description": "规则名称"}, "remark": {"type": "string", "description": "备注信息", "maxLength": 500}, "checkRule": {"type": "string", "description": " 枚举说明: 电压/电流/温度", "enum": ["VOLTAGE", "CURRENT", "TEMPERATURE"]}, "compareOperator": {"type": "string", "description": " 枚举说明: 大于/小于", "enum": ["GT", "LT"]}, "checkPercent": {"type": "integer", "description": "校验阈值(%)", "minimum": 0, "maximum": 100}}, "required": ["ruleId", "ruleName", "checkRule", "compareOperator", "checkPercent"]}, "examples": {}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "string"}}}, "headers": {}}}, "security": []}}}, "components": {"schemas": {}, "securitySchemes": {}}, "servers": [], "security": []}