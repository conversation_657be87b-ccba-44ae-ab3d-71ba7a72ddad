<template>
  <el-dialog title="派单规则" :visible.sync="dialogVisible" width="952px">
    <div class="info-card">
      <div class="form-wrap" v-if="dialogVisible">
        <el-form
          :model="baseInfo.form"
          :rules="baseInfo.rules"
          ref="baseInfoForm"
          label-position="top"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item
                label="规则名称："
                prop="ruleName"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-model="baseInfo.form.ruleName"
                  placeholder="请输入"
                  :disabled="type === 'detail'"
                ></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item
                label="校验规则："
                prop="checkRule"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.checkRule"
                  placeholder="请选择校验规则"
                  style="width: 100%"
                  :disabled="type === 'detail'"
                >
                  <el-option
                    v-for="item in checkRuleOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="比较操作符："
                prop="compareOperator"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.compareOperator"
                  placeholder="请选择比较操作符"
                  style="width: 100%"
                  :disabled="type === 'detail'"
                >
                  <el-option
                    v-for="item in compareOperatorOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item
                label="校验阈值(%)："
                prop="checkPercent"
                :label-width="formLabelWidth"
              >
                <el-input-number
                  v-model="baseInfo.form.checkPercent"
                  :min="0"
                  :max="100"
                  :disabled="type === 'detail'"
                  style="width: 100%"
                ></el-input-number>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="type !== 'create'">
              <el-form-item
                label="规则编号："
                prop="ruleCode"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-model="baseInfo.form.ruleCode"
                  placeholder="自动生成"
                  :disabled="true"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item
                label="备注信息："
                prop="remark"
                :label-width="formLabelWidth"
              >
                <el-input
                  type="textarea"
                  :rows="4"
                  v-model="baseInfo.form.remark"
                  placeholder="请输入备注信息"
                  :disabled="type === 'detail'"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <div class="bottom-wrap">
      <div>
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          @click="handleSave"
          :loading="submitLoading"
          type="primary"
          v-if="type !== 'detail'"
        >
          提交
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script>
import {
  createRule,
  updateRule,
} from '@/api/operationMaintenanceConfig/dispatchRule';

export default {
  props: {
    type: {
      type: String,
      default: 'create',
    },
    detailObj: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  components: {},
  data() {
    return {
      dialogVisible: false,
      formLabelWidth: '120px',
      checkRuleOptions: [
        { value: 'VOLTAGE', label: '电压' },
        { value: 'CURRENT', label: '电流' },
        { value: 'TEMPERATURE', label: '温度' },
      ],
      compareOperatorOptions: [
        { value: 'GT', label: '大于' },
        { value: 'LT', label: '小于' },
      ],

      baseInfo: {
        form: {
          ruleName: '',
          checkRule: '',
          compareOperator: '',
          checkPercent: 0,
          remark: '',
          ruleCode: '',
          ruleId: '',
        },
        rules: {
          ruleName: [
            { required: true, message: '请输入规则名称', trigger: 'blur' },
          ],
          checkRule: [
            {
              required: true,
              message: '请选择校验规则',
              trigger: 'change',
            },
          ],
          compareOperator: [
            { required: true, message: '请选择比较操作符', trigger: 'change' },
          ],
          checkPercent: [
            {
              required: true,
              message: '请输入校验阈值',
              trigger: 'blur',
            },
          ],
        },
      },
      submitLoading: false,
    };
  },
  watch: {
    dialogVisible(value) {
      if (!value) {
        this.baseInfo.form = {
          ruleName: '',
          checkRule: '',
          compareOperator: '',
          checkPercent: 0,
          remark: '',
          ruleCode: '',
          ruleId: '',
        };
      } else if (this.type !== 'create') {
        // 编辑或详情时，填充表单数据
        this.baseInfo.form = {
          ...this.detailObj,
        };
      }
    },
  },
  methods: {
    // 取消
    handleCancel() {
      this.dialogVisible = false;
    },

    // 保存
    handleSave() {
      this.$refs.baseInfoForm.validate(async (valid) => {
        if (!valid) return;

        this.submitLoading = true;
        try {
          const params = {
            ...this.baseInfo.form,
          };

          let err, res;
          if (this.type === 'create') {
            // 创建规则
            [err, res] = await createRule(params);
          } else {
            // 更新规则
            [err, res] = await updateRule(params);
          }

          if (err) return;

          this.$message({
            type: 'success',
            message: this.type === 'create' ? '创建成功!' : '更新成功!',
          });

          this.dialogVisible = false;
          this.$emit('ruleAdd');
        } finally {
          this.submitLoading = false;
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.info-card {
  background-color: #fff;
  border-radius: 5px;
  overflow: hidden;

  .form-wrap {
    padding: 0 16px 16px 16px;
  }
}

.bottom-wrap {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  background-color: #ffffff;
  padding-right: 32px;
  box-sizing: border-box;
  margin-top: 20px;
}

::v-deep .hidden-label .el-form-item__label:before {
  visibility: hidden;
}
</style>
