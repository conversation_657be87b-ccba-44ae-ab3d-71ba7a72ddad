<template>
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      @close="handleCancel"
      width="80%"
      :destroy-on-close="true"
    >
        <BuseCrud
            ref="crud"
            :loading="loading"
            :filterOptions="filterOptions"
            :tablePage="tablePage"
            :tableColumn="tableColumn"
            :tableData="tableData"
            :pagerProps="pagerProps"
            :modalConfig="modalConfig"
            :tableOn="{
                'checkbox-change': handleCheckboxChange,
                'checkbox-all': handleCheckboxChange,
            }"
            @loadData="loadData"
        >
            <template slot="dept" slot-scope="{ row }">
                {{ row.dept.length? row.dept[0].deptName : '' }}
            </template>
        </BuseCrud>

        <div slot="footer" class="dialog-footer">
            <el-button @click="handleCancel">取 消</el-button>
            <el-button type="primary" @click="handleConfirm">确 定</el-button>
        </div>
    </el-dialog>
  </template>
  <script>  



import {
    getDeptList,
    getUserList,
} from "@/api/operationMaintenanceConfig/operationMaintenanceTeam";

  export default {
    props: {
        dialogTitle:{
            type: String,
            default: '充电站选择'
        },

    },
    components: {
    },
    dicts: [

    ],
    
    data() {
        return {
            dialogVisible: false,

            deptOptions: [],
            tablePage: { total: 0, currentPage: 1, pageSize: 10 },
        
            loading: false,
            stationList: [],
            tableColumn:[
                {
                    type: 'checkbox',
                    width: 50,
                    fixed: 'left',
                },
                {
                    type: 'seq',
                    title: '序号',
                    width: 60,
                    minWidth: 60, // 最小宽度
                },
                {
                    field: 'userName',
                    title: '用户名称',
                    minWidth: 120, // 最小宽度
                },
                {
                    field: 'nickName',
                    title: '用户昵称',
                    minWidth: 120, // 最小宽度
                },
                {
                    field: 'dept',
                    title: '部门',
                    minWidth: 120, // 最小宽度
                    slots: { default: 'dept' },
                   
                },
            ],
            tableData: [],
            pagerProps: {
                layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
            },
            params: {
                userName: '',
                phonenumber: '',
                deptId: '',
            },
        };
    },
    computed: {
      filterOptions() {
        return {
          config: [
            {
              field: 'userName',
              title: '用户名称',
              element: 'el-input',
            },
            {
              field: 'phonenumber',
              title: '手机号码',
              element: 'el-input',
            },
            {
              field: 'deptId',
              title: '部门',
              element: 'el-select',
              props: {
                options: this.deptOptions,
              },
            },
          ],
          params: this.params,
        };
      },
      modalConfig() {
        return {
            addBtn: false,
            viewBtn: false,
            menu: false,
            editBtn: false,
            delBtn: false,
        }
      },
    },
    mounted() {
        
        this.loadData();
        // this.getDept();
    },
    methods: {
        // 初始化
        openModal() {
            this.tablePage = { total: 0, currentPage: 1, pageSize: 10 },
            this.loadData();

            this.getDeptList();
        },
      // 获取部门
      async getDeptList() {
        const [err, res] = await getDeptList({})
        if (err) return
       const {
        data
       } = res

       const list = []
       data.forEach(item => {
           list.push({
               label: item.deptName,
               value: item.deptId
           })
       })
       this.deptOptions = list
      },


        handleCancel() {
            this.dialogVisible = false;
        },


        // 新增按钮防抖        
        handleConfirm: _.debounce(function() {
            if(!this.stationList.length) {
                this.$message.warning('请先选择成员');
            } else {
                this.$emit('confirm', this.stationList);
                this.stationList = [];
                this.dialogVisible = false;
            }
        }, 300) ,

        async loadData() {
          const {
                userName,
                phonenumber,
                deptId,
          } = this.filterOptions.params

          this.loading = true;
          const [err, res] = await getUserList({
            userName,
            phonenumber,
            deptId,

            pageNum: this.tablePage.currentPage,
            pageSize: this.tablePage.pageSize,
          })

          this.loading = false;
          if (err) return 
          const { rows, total } = res;
          console.log('rows', res,rows)

          this.tableData = rows;
          this.tablePage.total = total;

        },

        handleCheckboxChange({ records }) {
            console.log('选中的记录:', records);
            this.stationList = records
        },


    },
  };
  </script>
  <style lang="scss" scoped>
::v-deep .el-form-item__content{
    display: flex !important;
}



  </style>
  