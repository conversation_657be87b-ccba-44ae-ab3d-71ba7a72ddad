<template>
  <div class="container container-float">
    <div class="table-wrap">
      <BuseCrud
        ref="crud"
        :loading="loading"
        :filterOptions="filterOptions"
        :tablePage="tablePage"
        :tableColumn="tableColumn"
        :tableData="tableData"
        :pagerProps="pagerProps"
        :modalConfig="modalConfig"
        @loadData="loadData"
      >
        <template slot="defaultHeader">
          <div>
            <div class="card-head">
              <div class="card-head-text">活动管理列表</div>

              <div class="top-button-wrap">
                <el-button class="btn-export" @click="handleExport">
                  导出
                </el-button>
                <el-button type="primary" @click="() => handleAdd()">
                  新增活动
                </el-button>
              </div>
            </div>
          </div>
        </template>

        <template slot="operate" slot-scope="{ row }">
          <div class="menu-box">
            <el-button class="add-btn" @click="hanleDetail(row)">
              详情
            </el-button>

            <el-button
              type="primary"
              plain
              @click="handleEdit(row)"
              v-if="row.showEditBtn"
            >
              修改
            </el-button>

            <el-button type="primary" plain @click="handleAudit(row)">
              审核
            </el-button>

            <el-button
              v-if="row.showDelBtn"
              type="danger"
              plain
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </div>
        </template>
      </BuseCrud>
    </div>
  </div>
</template>

<script>
import {
  getAreaList,
  getElectricPricePeriodList,
  deleteElectricPricePeriod,
} from '@/api/electricPricePeriod/index';

import StatusDot from '@/components/Business/StatusDot';

export default {
  components: {
    StatusDot,
  },
  dicts: ['ls_charging_audit_type', 'ls_charging_contracted_unit'],
  data() {
    return {
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
        },
        {
          field: 'periodNo',
          title: '活动名称',
          minWidth: 150,
        },
        {
          field: 'periodName',
          title: '活动ID',
          minWidth: 150,
        },
        {
          field: 'validMonthSummary',
          title: '展示标题',
          minWidth: 150,
        },
        {
          field: 'suitCityName',
          title: '活动类型',
          minWidth: 120,
          // formatter: ({ cellValue }) => {
          //     return this.selectDictLabel(
          //         this.areaList,
          //         cellValue
          //     );
          // },
        },
        {
          field: 'createTime',
          title: '活动起始时间',
          minWidth: 180,
        },
        {
          field: 'createUnit',
          title: '活动承办单位',
          minWidth: 220,
        },
        {
          field: 'createBy',
          title: '活动区域',
          minWidth: 120,
        },
        {
          field: 'createBy',
          title: '圈选用户数量',
          minWidth: 120,
        },
        {
          field: 'createBy',
          title: '参与用户数量',
          minWidth: 120,
        },
        {
          field: 'createBy',
          title: '券投放库存',
          minWidth: 120,
        },
        {
          field: 'createBy',
          title: '券使用数量',
          minWidth: 120,
        },
        {
          field: 'createBy',
          title: '活动状态',
          minWidth: 120,
        },
        {
          field: 'createBy',
          title: '创建人',
          minWidth: 120,
        },
        {
          field: 'createBy',
          title: '创建时间',
          minWidth: 120,
        },
        {
          field: 'approvalStatusc',
          title: '审核状态',
          minWidth: 110,
          fixed: 'right',
          slots: {
            // 自定义render函数
            default: ({ row }) => {
              return (
                <StatusDot
                  value={row.approvalStatus}
                  dictValue={this.dict.type.ls_charging_audit_type}
                  colors={['warning', 'success', 'danger']}
                ></StatusDot>
              );
            },
          },
        },
        {
          title: '操作',
          slots: { default: 'operate' },
          width: 300,
          align: 'center',
          fixed: 'right',
        },
      ],
      tableData: [],
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      params: {
        periodNo: '',
        periodName: '',
        suitCityCode: '',
        approvalStatus: '',
        monthDate: [],
      },

      areaList: [],
    };
  },

  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'periodNo',
            title: '活动ID：',
            element: 'el-input',
          },
          {
            field: 'periodName',
            title: '活动名称：',
            element: 'el-input',
          },
          {
            field: 'suitCityCode',
            title: '活动类型：',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.areaList,
            },
          },
          {
            field: 'periodName',
            title: '活动单位：',
            element: 'el-input',
          },
          {
            field: 'monthDate',
            title: '创建时间：',
            element: 'el-date-picker',
            props: {
              type: 'monthrange',
              options: [],
              valueFormat: 'yyyy-MM',
            },
          },
          {
            field: 'approvalStatus',
            title: '活动状态',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.dict.type.ls_charging_audit_type,
            },
          },
          {
            field: 'approvalStatus',
            title: '活动区域',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.dict.type.ls_charging_audit_type,
            },
          },
          {
            field: 'approvalStatus',
            title: '审核状态',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.dict.type.ls_charging_audit_type,
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  mounted() {
    this.getAreaList();
    this.loadData();
  },
  methods: {
    handleExport() {},
    // 获取适用地市
    async getAreaList() {
      const [err, res] = await getAreaList({
        areaLevel: '03',
        huNanOnly: true,
      });
      if (err) return;
      const { data } = res;
      const list = [];
      data.forEach((item) => {
        list.push({
          label: item.areaName,
          value: item.areaCode,
        });
      });
      this.areaList = list;
    },
    // 加载数据
    async loadData() {
      const { periodNo, periodName, suitCityCode, approvalStatus, monthDate } =
        this.params;

      let validMonthLeft = '';
      let validMonthRight = '';
      if (monthDate && monthDate.length > 0) {
        validMonthLeft = monthDate[0];
        validMonthRight = monthDate[1];
      }

      const params = {
        periodNo,
        periodName,
        suitCityCode,
        approvalStatus,
        validMonthLeft,
        validMonthRight,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };

      this.loading = true;
      const [err, res] = await getElectricPricePeriodList(params);

      this.loading = false;
      if (err) return;

      const { data, total } = res;

      this.tableData = data;
      this.tablePage.total = total;
    },
    // 新增电价时段
    handleAdd() {
      this.$router.push({
        path: '/v2g-charging-web/operatorManage/eventManage/create',
      });
    },

    // 编辑电价时段
    handleEdit(row) {
      const { chargePeriodId } = row;
      this.$router.push({
        path: '/v2g-charging-web/operatorManage/eventManage/create',
        query: {
          chargePeriodId,
        },
      });
    },

    // 查看详情
    hanleDetail(row) {
      const { chargePeriodId } = row;
      this.$router.push({
        path: '/v2g-charging-web/operatorManage/eventManage/detail',
        query: {
          chargePeriodId,
        },
      });
    },

    // 审核
    handleAudit(row) {
      const { periodNo } = row;
      this.$router.push({
        path: '/v2g-charging-web/operatorManage/eventManage/audit',
        query: {
          periodNo,
        },
      });
    },

    // 删除
    handleDelete(row) {
      const { chargePeriodId } = row;
      this.$confirm('确认删除该电价时段吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        const [err, res] = await deleteElectricPricePeriod({
          operateId: chargePeriodId,
        });

        if (err) return this.$message.error(err.message || '删除失败');
        this.$message({
          type: 'success',
          message: '删除成功!',
        });
        this.loadData();
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.container-full {
  background-color: rgba(244, 246, 249, 1);
  padding-bottom: 16px;
  min-height: calc(100% - 80px);
  overflow-y: auto;
}

.table-wrap {
  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }

  .card-head {
    // position: relative;
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    margin-top: -20px;
    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }
  .info-wrap {
    margin-top: 16px;
    display: flex;
    flex-wrap: wrap;
    gap: 16px;

    .info-item {
      background-color: #fafbfc;
      flex: 1 1 0;
      // min-width: 180px;

      border-radius: 5px;
      padding: 8px 24px;
      box-sizing: border-box;
      // margin-right: 16px;
      display: flex;
      .info-icon {
        width: 42px;
        height: 42px;
      }
      .info-right-wrap {
        flex: 1;
        margin-left: 8px;
        .info-title {
          font-weight: 400;
          font-size: 14px;
          line-height: 14px;
          margin-bottom: 8px;
        }
        .info-number {
          font-size: 20px;
          font-weight: 500;
          .info-unit {
            font-size: 14px;
            font-weight: 400;
          }
        }
      }
    }
    .info-item:last-child {
      margin-right: 0;
    }
  }

  .top-button-wrap {
    display: flex;
    margin: 16px 0;
  }
}

.ellipsis-text {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

::v-deep .vxe-table--render-default .vxe-body--column.col--center {
  text-align: left !important;
}
</style>
