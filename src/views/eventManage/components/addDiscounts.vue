<template>
  <el-dialog title="新增优惠" :visible.sync="dialogVisible" width="680px">
    <div class="info-card">
      <div class="form-wrap" v-if="dialogVisible">
        <el-form
          :model="baseInfo.form"
          :rules="baseInfo.rules"
          ref="baseInfoForm"
          label-position="top"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item
                label="立减时间段"
                prop="warningName"
                :label-width="formLabelWidth"
              >
                <el-date-picker
                  v-model="baseInfo.form.warningName"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  style="width: 100%"
                ></el-date-picker>
              </el-form-item>
            </el-col>

            <el-col :span="24">
              <el-form-item
                label="优惠范围"
                prop="type"
                :label-width="formLabelWidth"
              >
                <el-radio-group
                  v-model="baseInfo.form.type"
                  style="width: 100%"
                >
                  <el-radio
                    v-for="item in discountScopeList"
                    :key="item.value"
                    :label="item.value"
                  >
                    {{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item
                label="立减形式"
                prop="instantDiscount"
                :label-width="formLabelWidth"
              >
                <el-radio-group
                  v-model="baseInfo.form.instantDiscount"
                  style="width: 100%"
                >
                  <el-radio
                    v-for="item in instantDiscountList"
                    :key="item.value"
                    :label="item.value"
                  >
                    {{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="baseInfo.form.instantDiscount === '1'">
            <el-col :span="24">
              <el-form-item
                label=""
                prop="description"
                :label-width="formLabelWidth"
              >
                <span style="margin-right: 4px">减至</span>
                <el-input
                  style="width: 80% !important"
                  v-model="baseInfo.form.description"
                  placeholder="请输入"
                ></el-input>
                <span style="margin-left: 4px">元/度</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row v-if="baseInfo.form.instantDiscount === '2'">
            <el-row v-for="(item, index) in baseInfo.form.instantDiscountList">
              <el-col :span="11">
                <el-form-item
                  label=""
                  prop="description"
                  :label-width="formLabelWidth"
                >
                  <span style="margin-right: 4px">满：</span>
                  <el-input
                    style="width: 80% !important"
                    v-model="item.full"
                    placeholder="请输入"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="11">
                <el-form-item
                  label=""
                  prop="description"
                  :label-width="formLabelWidth"
                >
                  <span style="margin-right: 4px">减免：</span>
                  <el-input
                    style="width: 80% !important"
                    v-model="item.reduction"
                    placeholder="请输入"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="2">
                <i
                  v-if="baseInfo.form.instantDiscountList.length > 1"
                  style="font-size: 16px; margin: 6px 0 0 12px"
                  class="el-icon-delete"
                  @click="baseInfo.form.instantDiscountList.splice(index, 1)"
                ></i>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="22">
                <el-button
                  @click="
                    baseInfo.form.instantDiscountList.push({
                      full: '',
                      reduction: '',
                    })
                  "
                  style="width: 100%"
                >
                  添加
                </el-button>
              </el-col>
            </el-row>
          </el-row>
          <el-row v-if="baseInfo.form.instantDiscount === '3'">
            <el-row v-for="(item, index) in baseInfo.form.gradientList">
              <el-col :span="7">
                <el-form-item
                  label=""
                  prop="description"
                  :label-width="formLabelWidth"
                >
                  <span style="margin-right: 4px">满：</span>
                  <el-input
                    style="width: 70% !important"
                    v-model="item.full"
                    placeholder="请输入"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="7">
                <el-form-item
                  label=""
                  prop="description"
                  :label-width="formLabelWidth"
                >
                  <span style="margin-right: 4px">折扣：</span>
                  <el-input
                    style="width: 70% !important"
                    v-model="item.discount"
                    placeholder="请输入"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  label=""
                  prop="description"
                  :label-width="formLabelWidth"
                >
                  <span style="margin-right: 4px">最高减免：</span>
                  <el-input
                    style="width: 60% !important"
                    v-model="item.reduction"
                    placeholder="请输入"
                  ></el-input>
                </el-form-item>
              </el-col>
              <el-col :span="2">
                <i
                  v-if="baseInfo.form.gradientList.length > 1"
                  style="font-size: 16px; margin: 6px 0 0 12px"
                  class="el-icon-delete"
                  @click="baseInfo.form.gradientList.splice(index, 1)"
                ></i>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="22">
                <el-button
                  @click="
                    baseInfo.form.gradientList.push({
                      full: '',
                      discount: '',
                      reduction: '',
                    })
                  "
                  style="width: 100%"
                >
                  添加
                </el-button>
              </el-col>
            </el-row>
          </el-row>
        </el-form>
      </div>
    </div>

    <div class="bottom-wrap">
      <div>
        <el-button @click="handleCancel">取消</el-button>
        <el-button @click="handleSave" :loading="submitLoading" type="primary">
          提交
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>
<script>
export default {
  props: {},
  components: {},
  dicts: [
    'ls_order_except_level', // 异常级别
    'ls_order_except_type', // 异常类型
    'ls_order_except_measures', // 处理措施
    'ls_order_except_status', // 异常状态
    'ls_except_rule_item', // 异常规则
  ],
  data() {
    return {
      dialogVisible: false,
      formLabelWidth: '120px',
      channelList: [
        { label: 'web', value: 'web' },
        { label: 'APP', value: 'APP' },
        { label: '微信小程序', value: '微信小程序' },
        { label: '短信', value: '短信' },
      ],
      webPushTemplateList: [
        { label: 'web推送模版1', value: '1' },
        { label: 'web推送模版2', value: '2' },
      ],
      smsPushTemplateList: [
        { label: '短信推送模版1', value: '1' },
        { label: '短信推送模版2', value: '2' },
      ],
      discountScopeList: [
        { label: '服务费', value: '1' },
        { label: '电费', value: '2' },
        { label: '服务费+电费', value: '3' },
      ],
      instantDiscountList: [
        { label: '单价立减', value: '1' },
        { label: '阶梯满减', value: '2' },
        { label: '阶梯折扣', value: '3' },
      ],
      baseInfo: {
        form: {
          warningName: '',
          type: '',
          name: '',
          instantDiscount: '',
          description: '',
          level: '',
          notificationChannels: [],
          webPushTemplate: '',
          smsPushTemplate: '',
          instantDiscountList: [
            {
              full: '',
              reduction: '',
            },
          ],
          gradientList: [
            {
              full: '',
              discount: '',
              reduction: '',
            },
          ],
        },
        rules: {
          warningName: [
            { required: true, message: '请输入预警规则名称', trigger: 'blur' },
          ],
          type: [
            {
              required: true,
              message: '请选择订单异常类型',
              trigger: 'change',
            },
          ],
          name: [
            {
              required: true,
              message: '请选择订单异常名称',
              trigger: 'change',
            },
          ],
          notificationChannels: [
            { required: true, message: '请选择通知渠道', trigger: 'change' },
          ],
          webPushTemplate: [
            { required: true, message: '请选择web推送模版', trigger: 'change' },
          ],
          smsPushTemplate: [
            {
              required: true,
              message: '请选择短信推送模版',
              trigger: 'change',
            },
          ],
        },
      },
      submitLoading: false,
    };
  },
  watch: {
    dialogVisible(value) {
      console.log(value, 888);
      if (!value) {
        this.baseInfo.form = {
          warningName: '',
          type: '',
          name: '',
          instantDiscount: '',
          description: '',
          level: '',
          notificationChannels: [],
          webPushTemplate: '',
          smsPushTemplate: '',
        };
      }
    },
  },
  computed: {},
  mounted() {},
  methods: {
    orderTypeChange(event) {
      console.log(event, 88);
    },
    orderChange(event) {
      console.log(event, 88);
    },
    handleCancel() {
      this.dialogVisible = false;
    },
    // 保存
    handleSave() {
      this.$refs.baseInfoForm.validate(async (valid) => {
        if (valid) {
          this.$emit('orderruleAdd', true);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.container-full {
  background-color: rgba(244, 246, 249, 1);
  padding-bottom: 16px;
  min-height: calc(100% - 80px);
  overflow-y: auto;
}

.info-card {
  margin: 16px;
  border-radius: 5px;
  border: 1px solid #fff;
  overflow: hidden;
  background-color: #fff;
  // min-height: 300px;
  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    .before-icon {
      width: 3px;
      height: 16px;
      background-image: url('~@/assets/station/consno-before.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin-right: 8px;
    }
    .card-head-text {
      flex: 1;
      font-weight: 500;
      font-size: 16px;
      color: #12151a;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
    .button-wrap {
      display: flex;
      .invite-btn {
        background-color: #1ab2ff;
        border-color: #1ab2ff;
      }
      ::v-deep .el-button--small {
        font-size: 14px;
      }
      .distribution {
        margin-left: 24px;
        margin-right: 24px;
        display: flex;
        align-items: center;
      }
    }
  }

  .people-table-wrap {
    ::v-deep .bd3001-table-select-box {
      display: none;
    }
    ::v-deep .bd3001-button {
      width: 100%;
    }
  }
  .form-wrap {
    padding: 0 16px 16px 16px;
    .custom-header {
      background: -webkit-gradient(
          linear,
          left top,
          left bottom,
          from(rgba(0, 149, 255, 0.5)),
          to(rgba(87, 152, 255, 0))
        ),
        #f5faff;
      background: linear-gradient(
          180deg,
          rgba(0, 149, 255, 0.5) 0%,
          rgba(87, 152, 255, 0) 100%
        ),
        #f5faff;
      background-repeat: no-repeat;
    }
  }
}

.container {
  position: relative;
  padding-bottom: 100px;
  box-sizing: border-box;
  .bottom-wrap {
    // position: fixed;
    // bottom: 0;
    // left: 0;
    // width: 100%;
    // height: 86px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background-color: #ffffff;
    padding-right: 32px;
    box-sizing: border-box;
  }
}
::v-deep .hidden-label .el-form-item__label:before {
  visibility: hidden;
}
</style>
