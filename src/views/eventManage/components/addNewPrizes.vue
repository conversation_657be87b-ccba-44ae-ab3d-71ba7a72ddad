<template>
  <el-dialog title="新增奖品" :visible.sync="dialogVisible" width="680px">
    <div class="info-card">
      <div class="form-wrap" v-if="dialogVisible">
        <el-form
          :model="baseInfo.form"
          :rules="baseInfo.rules"
          ref="baseInfoForm"
          label-position="top"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item
                label="选择优惠券"
                prop="webPushTemplate"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.webPushTemplate"
                  placeholder="请选择优惠券"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in webPushTemplateList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <div class="coupon-ruler">
            <div class="ruler-no">
              优惠券编码：
              <span>12346587584585</span>
            </div>
            <div class="ruler-desc">
              当前可用库存数量：
              <span>1000</span>
            </div>
          </div>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item
                label="投放库存"
                prop="warningName"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-model="baseInfo.form.warningName"
                  placeholder="请输入"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <div class="bottom-wrap">
      <div>
        <el-button @click="handleCancel">取消</el-button>
        <el-button @click="handleSave" :loading="submitLoading" type="primary">
          确定
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>
<script>
export default {
  props: {},
  components: {},
  dicts: [
    'ls_order_except_level', // 异常级别
    'ls_order_except_type', // 异常类型
    'ls_order_except_measures', // 处理措施
    'ls_order_except_status', // 异常状态
    'ls_except_rule_item', // 异常规则
  ],
  data() {
    return {
      dialogVisible: false,
      formLabelWidth: '120px',
      channelList: [
        { label: 'web', value: 'web' },
        { label: 'APP', value: 'APP' },
        { label: '微信小程序', value: '微信小程序' },
        { label: '短信', value: '短信' },
      ],
      webPushTemplateList: [
        { label: 'web推送模版1', value: '1' },
        { label: 'web推送模版2', value: '2' },
      ],
      smsPushTemplateList: [
        { label: '短信推送模版1', value: '1' },
        { label: '短信推送模版2', value: '2' },
      ],

      baseInfo: {
        form: {
          warningName: '',

          webPushTemplate: '',
        },
        rules: {
          warningName: [
            { required: true, message: '请输入预警规则名称', trigger: 'blur' },
          ],
          webPushTemplate: [
            { required: true, message: '请选择web推送模版', trigger: 'change' },
          ],
        },
      },
      submitLoading: false,
    };
  },
  watch: {
    dialogVisible(value) {
      console.log(value, 888);
      if (!value) {
        this.baseInfo.form = {
          warningName: '',
          webPushTemplate: '',
        };
      }
    },
  },
  computed: {},
  mounted() {},
  methods: {
    orderTypeChange(event) {
      console.log(event, 88);
    },
    orderChange(event) {
      console.log(event, 88);
    },
    handleCancel() {
      this.dialogVisible = false;
    },
    // 保存
    handleSave() {
      this.$refs.baseInfoForm.validate(async (valid) => {
        if (valid) {
          this.$emit('orderRuleAdd', this.baseInfo.form);
          this.handleCancel();
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.container-full {
  background-color: rgba(244, 246, 249, 1);
  padding-bottom: 16px;
  min-height: calc(100% - 80px);
  overflow-y: auto;
}

.info-card {
  margin: 16px;
  border-radius: 5px;
  border: 1px solid #fff;
  overflow: hidden;
  background-color: #fff;
  // min-height: 300px;
  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    .before-icon {
      width: 3px;
      height: 16px;
      background-image: url('~@/assets/station/consno-before.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin-right: 8px;
    }
    .card-head-text {
      flex: 1;
      font-weight: 500;
      font-size: 16px;
      color: #12151a;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
    .button-wrap {
      display: flex;
      .invite-btn {
        background-color: #1ab2ff;
        border-color: #1ab2ff;
      }
      ::v-deep .el-button--small {
        font-size: 14px;
      }
      .distribution {
        margin-left: 24px;
        margin-right: 24px;
        display: flex;
        align-items: center;
      }
    }
  }

  .people-table-wrap {
    ::v-deep .bd3001-table-select-box {
      display: none;
    }
    ::v-deep .bd3001-button {
      width: 100%;
    }
  }
  .form-wrap {
    padding: 0 16px 16px 16px;
    .custom-header {
      background: -webkit-gradient(
          linear,
          left top,
          left bottom,
          from(rgba(0, 149, 255, 0.5)),
          to(rgba(87, 152, 255, 0))
        ),
        #f5faff;
      background: linear-gradient(
          180deg,
          rgba(0, 149, 255, 0.5) 0%,
          rgba(87, 152, 255, 0) 100%
        ),
        #f5faff;
      background-repeat: no-repeat;
    }
  }
}

.container {
  position: relative;
  padding-bottom: 100px;
  box-sizing: border-box;
  .bottom-wrap {
    // position: fixed;
    // bottom: 0;
    // left: 0;
    // width: 100%;
    // height: 86px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background-color: #ffffff;
    padding-right: 32px;
    box-sizing: border-box;
  }
}
.coupon-ruler {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #ebf3ff;
  height: 40px;
  padding: 12px 16px;
  margin: 6px 0 12px;
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 16px;
  color: #505363;
  .ruler-no span {
    color: #292b33;
  }
  .ruler-desc span {
    color: #ff8d24;
  }
}
::v-deep .hidden-label .el-form-item__label:before {
  visibility: hidden;
}
</style>
