<template>
  <div class="container container-float" style="padding: 0">
    <!-- 基本信息 -->
    <el-form
      :model="baseInfo.form"
      :rules="baseInfo.rules"
      ref="baseInfoForm"
      label-position="top"
    >
      <div class="info-card">
        <div class="card-head card-head-bottom">
          <div class="before-icon"></div>
          <div class="card-head-text">基本信息</div>
        </div>
        <div class="form-wrap">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                label="分成规则名称"
                prop="operatorName"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-model="baseInfo.form.operatorName"
                  placeholder="请输入"
                  maxLength="32"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="分成方类型"
                prop="operatorCategoryList"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="platformInfo.form.operatorCategoryList"
                  placeholder="请选择"
                  style="width: 100%"
                  :multiple="true"
                >
                  <el-option
                    v-for="item in this.dict.type.ls_charging_operator_category"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="分成方"
                prop="operatorCategoryList"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="platformInfo.form.operatorCategoryList"
                  placeholder="请选择"
                  style="width: 100%"
                  :multiple="true"
                >
                  <el-option
                    v-for="item in this.dict.type.ls_charging_operator_category"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                label="分成类型"
                prop="operatorCategoryList"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="platformInfo.form.operatorCategoryList"
                  placeholder="请选择"
                  style="width: 100%"
                  :multiple="true"
                >
                  <el-option
                    v-for="item in this.dict.type.ls_charging_operator_category"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="结算方"
                prop="operatorCategoryList"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="platformInfo.form.operatorCategoryList"
                  placeholder="请选择"
                  style="width: 100%"
                  :multiple="true"
                >
                  <el-option
                    v-for="item in this.dict.type.ls_charging_operator_category"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="生效时间"
                prop="operatorStatus"
                :label-width="formLabelWidth"
              >
                <el-date-picker
                  style="width: 100%"
                  v-model="baseInfo.form.operatorStatus"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                ></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 分成配置 -->
      <div class="info-card">
        <div class="card-head card-head-bottom">
          <div class="before-icon"></div>
          <div class="card-head-text">分成配置</div>
        </div>
        <div class="form-wrap" style="padding-bottom: 0">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                label="分成费用"
                prop="operatorCategoryList"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="platformInfo.form.operatorCategoryList"
                  placeholder="请选择"
                  style="width: 100%"
                  :multiple="true"
                >
                  <el-option
                    v-for="item in this.dict.type.ls_charging_operator_category"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="充电服务费"
                prop="linkBizTypeList"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="platformInfo.form.linkBizTypeList"
                  placeholder="请选择"
                  style="width: 100%"
                  :multiple="true"
                >
                  <el-option
                    v-for="item in dict.type.ls_charging_operator_link_biz"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="分成方是否承担电损"
                prop="contractId"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="platformInfo.form.contractId"
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in contractList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="电损计量类型"
                prop="contractId"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="platformInfo.form.contractId"
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in contractList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="电损电费单价"
                prop="contractId"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="platformInfo.form.contractId"
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in contractList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="电损计量类型"
                prop="contractId"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="platformInfo.form.contractId"
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in contractList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="充电电费"
                prop="contractId"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="platformInfo.form.contractId"
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in contractList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="电损分成方承担比例"
                prop="operatorName"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-model="baseInfo.form.operatorName"
                  placeholder="请输入"
                  maxLength="32"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="分成周期"
                prop="contractId"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="platformInfo.form.contractId"
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in contractList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="电损电费单价"
                prop="contractId"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="platformInfo.form.contractId"
                  placeholder="请选择"
                  style="width: 45%; margin-right: 4px"
                >
                  <el-option
                    v-for="item in contractList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
                <el-input
                  v-model="baseInfo.form.operatorName"
                  placeholder="请输入"
                  maxLength="32"
                  style="width: 45%"
                ></el-input>
                <span style="margin-left: 4px">元/度</span>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="card-head card-head-bottom">
          <div class="before-icon"></div>
          <div class="card-head-text">分成比例设置</div>
        </div>
        <div class="form-wrap" style="padding-bottom: 0">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                label="内外部车辆分成比例是否一致："
                prop="operatorStatus"
                :label-width="formLabelWidth"
              >
                <el-radio-group v-model="baseInfo.form.operatorStatus">
                  <el-radio :label="1">是</el-radio>
                  <el-radio :label="0">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <div class="form-wrap-box">
            <div class="card-head" style="margin-bottom: 0">
              <div class="before-icon"></div>
              <div class="card-head-text card-head-text-sing">内部车辆</div>
            </div>
            <div class="form-wrap" style="padding-bottom: 0">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item
                    label="固定保底分成金额"
                    prop="operatorStatus"
                    :label-width="formLabelWidth"
                  >
                    <el-input
                      v-model="baseInfo.form.operatorName"
                      placeholder="请输入"
                      maxLength="32"
                      style="width: 95%"
                    ></el-input>
                    <span style="margin-left: 4px">元</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    label="分成比例"
                    prop="operatorStatus"
                    :label-width="formLabelWidth"
                  >
                    <el-select
                      v-model="platformInfo.form.operatorCategoryList"
                      placeholder="请选择"
                      style="width: 46%; margin-right: 2%"
                      :multiple="true"
                    >
                      <el-option
                        v-for="item in this.dict.type
                          .ls_charging_operator_category"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                    <el-input
                      v-model="baseInfo.form.operatorName"
                      placeholder="请输入"
                      maxLength="32"
                      style="width: 46%"
                    ></el-input>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </div>
          <div class="form-wrap-box">
            <div class="card-head" style="margin-bottom: 0">
              <div class="before-icon"></div>
              <div class="card-head-text card-head-text-sing">外部车辆</div>
            </div>
            <div class="form-wrap" style="padding-bottom: 0">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item
                    label="固定保底分成金额"
                    prop="operatorStatus"
                    :label-width="formLabelWidth"
                  >
                    <el-input
                      v-model="baseInfo.form.operatorName"
                      placeholder="请输入"
                      maxLength="32"
                      style="width: 95%"
                    ></el-input>
                    <span style="margin-left: 4px">元</span>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item
                    label="分成比例"
                    prop="operatorStatus"
                    :label-width="formLabelWidth"
                  >
                    <el-select
                      v-model="platformInfo.form.operatorCategoryList"
                      placeholder="请选择"
                      style="width: 46%; margin-right: 2%"
                      :multiple="true"
                    >
                      <el-option
                        v-for="item in this.dict.type
                          .ls_charging_operator_category"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                    <el-input
                      v-model="baseInfo.form.operatorName"
                      placeholder="请输入"
                      maxLength="32"
                      style="width: 46%"
                    ></el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item
                    label=""
                    prop="operatorStatus"
                    :label-width="formLabelWidth"
                  >
                    <el-radio-group v-model="baseInfo.form.operatorStatus">
                      <el-radio :label="1">按充电量</el-radio>
                      <el-radio :label="0">按合作时间</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="4">
                  <el-form-item
                    label=""
                    prop="operatorStatus"
                    :label-width="formLabelWidth"
                  >
                    <el-input
                      v-model="baseInfo.form.operatorName"
                      placeholder="请输入"
                      maxLength="32"
                      style="width: 80%"
                    ></el-input>
                    <span style="margin-left: 4px">kwh</span>
                  </el-form-item>
                </el-col>
                <el-col :span="3">
                  <el-form-item
                    label=""
                    prop="operatorStatus"
                    :label-width="formLabelWidth"
                  >
                    <el-select
                      v-model="platformInfo.form.operatorCategoryList"
                      placeholder="请选择"
                      :multiple="true"
                    >
                      <el-option
                        v-for="item in this.dict.type
                          .ls_charging_operator_category"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="2">
                  <el-select
                    v-model="platformInfo.form.operatorCategoryList"
                    placeholder="请选择"
                    :multiple="true"
                  >
                    <el-option
                      v-for="item in this.dict.type
                        .ls_charging_operator_category"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-col>
                <el-col :span="3">
                  <el-select
                    v-model="platformInfo.form.operatorCategoryList"
                    placeholder="请选择"
                    :multiple="true"
                  >
                    <el-option
                      v-for="item in this.dict.type
                        .ls_charging_operator_category"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-col>
                <el-col :span="3">
                  <el-select
                    v-model="platformInfo.form.operatorCategoryList"
                    placeholder="请选择"
                    :multiple="true"
                  >
                    <el-option
                      v-for="item in this.dict.type
                        .ls_charging_operator_category"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-col>
                <el-col :span="4">
                  <el-form-item
                    label=""
                    prop="operatorStatus"
                    :label-width="formLabelWidth"
                  >
                    <el-input
                      v-model="baseInfo.form.operatorName"
                      placeholder="请输入"
                      maxLength="32"
                      style="width: 80%"
                    ></el-input>
                    <span style="margin-left: 4px">kwh</span>
                  </el-form-item>
                </el-col>
                <el-col :span="5">
                  <el-form-item
                    label=""
                    prop="operatorStatus"
                    :label-width="formLabelWidth"
                  >
                    <span style="margin-right: 4px">分成比例：</span>

                    <el-input
                      v-model="baseInfo.form.operatorName"
                      placeholder="请输入"
                      maxLength="32"
                      style="width: 60%"
                    ></el-input>
                    <i
                      style="color: #217aff; margin-left: 4px"
                      class="el-icon-delete"
                    ></i>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="9">
                  <el-date-picker
                    v-model="platformInfo.form.operatorCategoryList"
                    type="daterange"
                    range-separator="至"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    style="width: 100%"
                  ></el-date-picker>
                </el-col>

                <el-col :span="6">
                  <el-select
                    v-model="platformInfo.form.operatorCategoryList"
                    placeholder="请选择"
                    :multiple="true"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="item in this.dict.type
                        .ls_charging_operator_category"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-col>

                <el-col :span="9">
                  <el-form-item
                    label=""
                    prop="operatorStatus"
                    :label-width="formLabelWidth"
                  >
                    <span style="margin-right: 4px">分成比例：</span>

                    <el-input
                      v-model="baseInfo.form.operatorName"
                      placeholder="请输入"
                      maxLength="32"
                      style="width: 70%"
                    ></el-input>
                    <i
                      style="color: #217aff; margin-left: 4px"
                      class="el-icon-delete"
                    ></i>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-button
                  style="margin: 0 10px 10px 10px; width: calc(100% - 20px)"
                >
                  添加
                </el-button>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="24">
                  <el-form-item
                    label=""
                    prop="operatorStatus"
                    :label-width="formLabelWidth"
                  >
                    <el-radio-group v-model="baseInfo.form.operatorStatus">
                      <el-radio :label="1">比例整体提高</el-radio>
                      <el-radio :label="0">比例阶段提高</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
              </el-row>
            </div>
          </div>
        </div>
        <div class="form-division-formula">
          {{ divisionFormula() }}
        </div>
      </div>
      <div class="info-card">
        <div class="card-head card-head-bottom">
          <div class="before-icon"></div>
          <div class="card-head-text">场地租金配置</div>
        </div>
        <div class="form-wrap" style="padding-bottom: 0">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                label=""
                prop="operatorStatus"
                :label-width="formLabelWidth"
              >
                <el-radio-group v-model="baseInfo.form.operatorStatus">
                  <el-radio :label="1">固定场地租金</el-radio>
                  <el-radio :label="0">浮动场地租金</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-date-picker
                v-model="platformInfo.form.operatorCategoryList"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 80%"
              ></el-date-picker>
              <el-select
                style="width: 20%"
                v-model="platformInfo.form.operatorCategoryList"
                placeholder="请选择"
                :multiple="true"
              >
                <el-option
                  v-for="item in this.dict.type.ls_charging_operator_category"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-col>

            <el-col :span="12">
              <el-form-item
                label=""
                prop="operatorStatus"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-model="baseInfo.form.operatorName"
                  placeholder="请输入"
                  maxLength="32"
                  style="width: 70%"
                ></el-input>
                <el-select
                  style="width: 20%"
                  v-model="platformInfo.form.operatorCategoryList"
                  placeholder="请选择"
                  :multiple="true"
                >
                  <el-option
                    v-for="item in this.dict.type.ls_charging_operator_category"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
                <i
                  style="color: #217aff; margin-left: 4px"
                  class="el-icon-delete"
                ></i>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-button
              style="margin: 0 10px 10px 10px; width: calc(100% - 20px)"
            >
              添加
            </el-button>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                label="场地租金"
                prop="operatorStatus"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-model="baseInfo.form.operatorName"
                  placeholder="请输入"
                  maxLength="32"
                  style="width: 90%"
                ></el-input>
                <span style="margin-left: 4px">元/月</span>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item
                label="分成周期"
                prop="operatorStatus"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="platformInfo.form.operatorCategoryList"
                  placeholder="请选择"
                  :multiple="true"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in this.dict.type.ls_charging_operator_category"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="首次起始周期"
                prop="operatorStatus"
                :label-width="formLabelWidth"
              >
                <el-date-picker
                  v-model="platformInfo.form.operatorCategoryList"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  style="width: 100%"
                ></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="form-division-formula" style="padding-top: 0">
          {{ divisionFormula() }}
        </div>
      </div>

      <div class="info-card">
        <div class="card-head card-head-bottom">
          <div class="before-icon"></div>
          <div class="card-head-text">平台服务费配置</div>
        </div>
        <div class="form-wrap" style="padding-bottom: 0">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                label=""
                prop="operatorStatus"
                :label-width="formLabelWidth"
              >
                <el-radio-group v-model="baseInfo.form.operatorStatus">
                  <el-radio :label="1">按站</el-radio>
                  <el-radio :label="0">按桩</el-radio>
                  <el-radio :label="2">按充电服务费</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                label="充电服务费"
                prop="operatorStatus"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="platformInfo.form.operatorCategoryList"
                  placeholder="请选择"
                  :multiple="true"
                  style="width: 45%"
                >
                  <el-option
                    v-for="item in this.dict.type.ls_charging_operator_category"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
                <el-input
                  v-model="baseInfo.form.operatorName"
                  placeholder="请输入"
                  maxLength="32"
                  style="width: 45%"
                ></el-input>
                <span style="margin-left: 4px">元/度</span>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item
                label="分成周期"
                prop="operatorStatus"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="platformInfo.form.operatorCategoryList"
                  placeholder="请选择"
                  :multiple="true"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in this.dict.type.ls_charging_operator_category"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="首次起始周期"
                prop="operatorStatus"
                :label-width="formLabelWidth"
              >
                <el-date-picker
                  v-model="platformInfo.form.operatorCategoryList"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  style="width: 100%"
                ></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item
                label="分成比例"
                prop="operatorStatus"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="platformInfo.form.operatorCategoryList"
                  placeholder="请选择"
                  :multiple="true"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in this.dict.type.ls_charging_operator_category"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                label=""
                prop="operatorStatus"
                :label-width="formLabelWidth"
              >
                <el-radio-group v-model="baseInfo.form.operatorStatus">
                  <el-radio :label="1">按充电量</el-radio>
                  <el-radio :label="0">按合作时间</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-date-picker
                v-model="platformInfo.form.operatorCategoryList"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 80%"
              ></el-date-picker>
              <el-select
                style="width: 20%"
                v-model="platformInfo.form.operatorCategoryList"
                placeholder="请选择"
                :multiple="true"
              >
                <el-option
                  v-for="item in this.dict.type.ls_charging_operator_category"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-col>

            <el-col :span="12">
              <el-form-item
                label=""
                prop="operatorStatus"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-model="baseInfo.form.operatorName"
                  placeholder="请输入"
                  maxLength="32"
                  style="width: 70%"
                ></el-input>
                <el-select
                  style="width: 20%"
                  v-model="platformInfo.form.operatorCategoryList"
                  placeholder="请选择"
                  :multiple="true"
                >
                  <el-option
                    v-for="item in this.dict.type.ls_charging_operator_category"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
                <i
                  style="color: #217aff; margin-left: 4px"
                  class="el-icon-delete"
                ></i>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-button
              style="margin: 0 10px 10px 10px; width: calc(100% - 20px)"
            >
              添加
            </el-button>
          </el-row>
        </div>
      </div>
    </el-form>

    <!-- 底部按钮 -->
    <div class="bottom-button-wrap">
      <div>
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSave">确定</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import {
  createOperators,
  getOperatorsDetail,
  updateOperators,
} from '@/api/operator/index';

import { getToken } from '@/utils/auth';

import Upload from '@/components/Upload/index';

export default {
  name: 'InterconnectionCreate',
  dicts: [
    'ls_charging_operator_category',
    'ls_charging_operator_link_biz',
    'ls_charging_operator_status',
  ],
  components: {
    Upload,
  },
  data() {
    return {
      uploadHeaders: {
        Authorization: 'Bearer ' + getToken(),
      },
      formLabelWidth: '120px',
      pageType: 'create',
      operatorId: '',
      baseInfo: {
        form: {
          operatorName: '',
          shortname: '',
          socialCreditCode: '',
          legalRepresentative: '',
          legalRepresentativeCode: '',
          contactPerson: '',
          contactTel: '',
          registeredAddress: '',
          // registeredAddress: [],
          // detailAddress: '',
          operatorStatus: 1,
          businessLicensePicture: [],
          remark: '',
        },
        rules: {
          operatorName: [
            { required: true, message: '请输入分成规则名称', trigger: 'blur' },
          ],
          shortname: [
            { required: true, message: '请输入运营商简称', trigger: 'blur' },
          ],
          socialCreditCode: [
            {
              required: true,
              message: '请输入统一社会信用代码',
              trigger: 'blur',
            },
          ],
          legalRepresentative: [
            { required: true, message: '请输入法人', trigger: 'blur' },
          ],
          legalRepresentativeCode: [
            { required: true, message: '请输入法人身份证号', trigger: 'blur' },
          ],
          contactPerson: [
            { required: true, message: '请输入联系人', trigger: 'blur' },
          ],
          contactTel: [
            { required: true, message: '请输入联系电话', trigger: 'blur' },
          ],
          registeredAddress: [
            { required: true, message: '请输入地址', trigger: 'change' },
          ],
          // detailAddress: [
          //   { required: true, message: '请输入详细地址', trigger: 'blur' },
          // ],
          operatorStatus: [
            { required: true, message: '请选择运营商状态', trigger: 'change' },
          ],
          businessLicensePicture: [
            { required: true, message: '请上传资质图片', trigger: 'change' },
          ],
        },
      },
      platformInfo: {
        form: {
          // operatorId: '',
          operatorCategoryList: [],
          linkBizTypeList: [],
          contractId: '',
        },
        rules: {
          // operatorId: [
          //   { required: true, message: '请输入运营商ID', trigger: 'blur' },
          // ],
          operatorCategoryList: [
            { required: true, message: '请选择运营商类型', trigger: 'change' },
          ],
          linkBizTypeList: [
            {
              required: true,
              message: '请选择充电服务费',
              trigger: 'change',
            },
          ],
          contractId: [
            {
              required: true,
              message: '请选择分成方是否承担电损',
              trigger: 'change',
            },
          ],
        },
      },
      operatorTypeList: [],
      businessTypeList: [],
      contractList: [],
    };
  },
  mounted() {
    const operatorId = this.$route.query.operatorId;
    if (operatorId) {
      this.pageType = 'edit';
      this.operatorId = operatorId;
      this.getOperatorDetail();
    }
  },
  methods: {
    divisionFormula() {
      const type = '1';
      let returnStr =
        '分成公式：（车联网清分充电服务费-（供电电费-车联网清分充电电费））*分成比例';
      if (type === '2') {
        returnStr =
          '分成公式：（车联网清分充电服务费-供电均价*（供电电量-车联网清分电量））*分成比例';
      } else if (type === '3') {
        returnStr =
          '分成公式：车联网清分充电服务费*分成比例-（供电电费-车联网清分充电电费）*分成方承担比例';
      } else if (type === '4') {
        returnStr = '分成公式：车联网清分充电服务费*分成比例';
      } else if (type === '5') {
        returnStr =
          '分成公式：车联网清分充电电费+车联网清分充电服务费*分成比例';
      } else if (type === '6') {
        returnStr =
          '分成公式：固定服务费单价*车联网清分电量*分成比例-（供电电费-车联网清分充电电费）*分成方承担比例';
      } else if (type === '7') {
        returnStr =
          '分成公式：2011-2012年场地租金300元/月2013-2014年场地租金400元/月';
      } else if (type === '8') {
        returnStr =
          '分成公式：（充电电费+充电服务费-供电电费-运维成本-平台服务费）*分成比例';
      }
      return returnStr;
    },
    handleUpload(file) {
      console.log(this.baseInfo.form.businessLicensePicture, '1111');
      console.log('上传中:', file);
    },
    handleUploadSuccess(file) {
      console.log(this.baseInfo.form.businessLicensePicture, '2222');
      console.log('上传成功:', file);
    },
    handleExceed() {
      this.$message.warning('只能上传1张图片');
    },
    handleCancel() {
      this.$router.back();
    },
    async handleSave() {
      try {
        await Promise.all([
          this.$refs.baseInfoForm.validate(),
          this.$refs.platformInfoForm.validate(),
        ]);

        const {
          operatorName,
          shortname,
          socialCreditCode,
          legalRepresentative,
          legalRepresentativeCode,
          contactPerson,
          contactTel,
          registeredAddress,
          operatorStatus,
          businessLicensePicture,
          remark,
        } = this.baseInfo.form;

        const { operatorCategoryList, linkBizTypeList, contractId } =
          this.platformInfo.form;

        let picUrl = '';
        if (businessLicensePicture.length) {
          picUrl = businessLicensePicture[0].url;
        }

        const params = {
          operatorName,
          shortname,
          socialCreditCode,
          legalRepresentative,
          legalRepresentativeCode,
          contact: contactPerson,
          contactTel,
          address: registeredAddress,
          operatorStatus,
          businessLicensePicture: picUrl,
          remark,
          operatorCategoryList,
          linkBizTypeList,
          contractId,
        };

        console.log(params, this.pageType, 'params');

        if (this.pageType === 'create') {
          const [err, res] = await createOperators(params);

          if (err) {
            return this.$message.error(err.message || '新增运营商失败');
          }
          this.$message.success('新增成功');

          setTimeout(() => {
            this.$router.back();
          }, 2000);
        } else if (this.pageType === 'edit') {
          const [err, res] = await updateOperators({
            ...params,
            operatorId: this.operatorId,
          });

          if (err) {
            return this.$message.error(err.message || '修改运营商失败');
          }
          this.$message.success('修改成功');

          setTimeout(() => {
            this.$router.back();
          }, 2000);
        }
      } catch (error) {
        console.log(error, 'error');
        this.$message.error('表单校验失败，请检查输入');
      }
    },

    // 获取运营商详情
    async getOperatorDetail() {
      const [err, res] = await getOperatorsDetail({
        operatorId: this.operatorId,
      });

      if (err) return;

      // 基本信息

      const {
        operatorName,
        shortname,
        socialCreditCode,
        legalRepresentative,
        legalRepresentativeCode,
        contact,
        contactTel,
        address,
        operatorStatus,
        businessLicensePicture,
        remark,
      } = res.data;

      this.baseInfo.form = {
        operatorName,
        shortname,
        socialCreditCode,
        legalRepresentative,
        legalRepresentativeCode,
        contactPerson: contact,
        contactTel,
        registeredAddress: address,
        operatorStatus,
        businessLicensePicture: [{ url: businessLicensePicture }],
        remark,
      };

      // 接入信息
      const { operatorCategoryList, linkBizTypeList, contractId } = res.data;

      this.platformInfo.form = {
        operatorCategoryList,
        linkBizTypeList,
        contractId,
      };
    },
  },
};
</script>

<style lang="scss" scoped>
.info-card {
  margin: 16px;
  border-radius: 5px;
  border: 1px solid #fff;
  overflow: hidden;
  background-color: #fff;

  .card-head {
    height: 48px;
    padding: 0 15px;
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    .before-icon {
      width: 3px;
      height: 16px;
      background-image: url('~@/assets/station/consno-before.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin-right: 8px;
    }

    .card-head-text {
      flex: 1;
      font-weight: 500;
      font-size: 16px;
      color: #12151a;
    }
    .card-head-text-sing {
      font-weight: 400;
      color: #21252e;
    }
  }
  .card-head-bottom {
    border-bottom: 1px solid #e9ebf0;
  }

  .form-wrap {
    padding: 0 16px 16px 16px;
  }
}

.interface-section {
  margin: 16px 0;

  .el-checkbox-group {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 16px;
  }

  .el-checkbox {
    margin-right: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.bottom-button-wrap {
  height: 86px;
  margin-top: 16px;
  width: 100%;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: 32px;
  box-sizing: border-box;
}
.form-wrap-box {
  background: #f9f9fb;
}
.form-division-formula {
  padding: 24px;
  font-family: PingFang SC;
  font-weight: 500;
  font-size: 16px;
  line-height: 18px;
  color: #12151a;
}

::v-deep .el-input {
  width: 100%;
}
::v-deep .el-form-item__label {
  padding-bottom: 0;
}
</style>
