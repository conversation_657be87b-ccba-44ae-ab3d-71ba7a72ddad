<template>
  <div class="container container-float" style="padding: 0">
    <!-- 基本信息 -->
    <el-form
      :model="baseInfo.form"
      :rules="baseInfo.rules"
      ref="baseInfoForm"
      label-position="top"
    >
      <div class="info-card">
        <div class="card-head card-head-bottom">
          <div class="before-icon"></div>
          <div class="card-head-text">基本信息</div>
        </div>
        <div class="form-wrap">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                label="分成规则名称"
                prop="ruleName"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-model="baseInfo.form.ruleName"
                  placeholder="请输入"
                  maxLength="32"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="分成方类型"
                prop="toObjType"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.toObjType"
                  placeholder="请选择"
                  style="width: 100%"
                  @change="toObjTypeChange"
                >
                  <el-option
                    v-for="item in this.dict.type.ls_clear_to_obj_type"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="分成方"
                prop="toObj"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.toObj"
                  placeholder="请输入分成方"
                  style="width: 100%"
                  filterable
                  remote
                  :loading="operationLoading"
                  :remote-method="debouncedOperationSearch"
                >
                  <el-option
                    v-for="item in operationUnitList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- <el-col v-if="baseInfo.form.toObjType === '1'" :span="8">
              <el-form-item
                label="分成方"
                prop="toObj"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.toObj"
                  placeholder="请选择分成方"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in this.dict.type.ls_clear_space_owner"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col> -->
          </el-row>

          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                label="分成类型"
                prop="clearType"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.clearType"
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in clearTypeList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="结算方"
                prop="fromObj"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.fromObj"
                  placeholder="请选择"
                  style="width: 100%"
                  :disabled="baseInfo.form.toObjType === '0' ? true : false"
                >
                  <el-option
                    v-for="item in this.dict.type.ls_clear_space_operator"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="生效时间"
                prop="effectDate"
                :label-width="formLabelWidth"
              >
                <el-date-picker
                  style="width: 100%"
                  v-model="baseInfo.form.effectDate"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                ></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- 分成配置 -->
      <div class="info-card">
        <div class="card-head card-head-bottom">
          <div class="before-icon"></div>
          <div class="card-head-text">分成配置</div>
        </div>
        <div class="form-wrap" style="padding-bottom: 0">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                label="分成费用"
                prop="clearCostType"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.clearCostType"
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in clearCostTypeList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="充电服务费"
                prop="chargeServiceFeeType"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.chargeServiceFeeType"
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in chargeServiceFeeTypeList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col
              :span="8"
              v-if="baseInfo.form.chargeServiceFeeType === 'fixed'"
            >
              <el-form-item
                label="充电服务费单价"
                prop="chargeServiceFeePrice"
                :label-width="formLabelWidth"
              >
                <el-input-number
                  v-model="baseInfo.form.chargeServiceFeePrice"
                  placeholder="请输入"
                  maxLength="32"
                  :min="0"
                ></el-input-number>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="8">
              <el-form-item
                label="分成方是否承担电损"
                prop="contractId"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.contractId"
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in contractList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="电损计量类型"
                prop="contractId"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.contractId"
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in contractList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="电损电费单价"
                prop="contractId"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.contractId"
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in contractList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="电损计量类型"
                prop="contractId"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.contractId"
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in contractList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col> -->
            <!-- <el-col :span="8">
              <el-form-item
                label="充电电费"
                prop="contractId"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.contractId"
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in contractList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col> -->
            <!-- <el-col :span="8">
              <el-form-item
                label="电损分成方承担比例"
                prop="operatorName"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-model="baseInfo.form.operatorName"
                  placeholder="请输入"
                  maxLength="32"
                ></el-input>
              </el-form-item>
            </el-col> -->
            <el-col :span="8">
              <el-form-item
                label="分成周期"
                prop="clearPeriod"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.clearPeriod"
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in clearPeriodList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="8">
              <el-form-item
                label="电损电费单价"
                prop="contractId"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.contractId"
                  placeholder="请选择"
                  style="width: 45%; margin-right: 4px"
                >
                  <el-option
                    v-for="item in contractList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
                <el-input
                  v-model="baseInfo.form.operatorName"
                  placeholder="请输入"
                  maxLength="32"
                  style="width: 45%"
                ></el-input>
                <span style="margin-left: 4px">元/度</span>
              </el-form-item>
            </el-col> -->
          </el-row>
        </div>
        <div class="card-head card-head-bottom">
          <div class="before-icon"></div>
          <div class="card-head-text">分成比例设置</div>
        </div>
        <div class="form-wrap" style="padding-bottom: 0">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                label="内外部车辆分成比例是否一致："
                prop="inOutSameFlag"
                :label-width="formLabelWidth"
              >
                <el-radio-group v-model="baseInfo.form.inOutSameFlag">
                  <el-radio
                    v-for="item in inOutSameFlagList"
                    :key="item.value"
                    :label="item.value"
                  >
                    {{ item.label }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>

          <div class="form-wrap-box">
            <div class="card-head" style="margin-bottom: 0">
              <div class="before-icon"></div>
              <div class="card-head-text card-head-text-sing">内部车辆</div>
            </div>
            <div class="form-wrap" style="padding-bottom: 0">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item
                    label="固定保底分成金额"
                    prop="inMinClear"
                    :label-width="formLabelWidth"
                  >
                    <el-input-number
                      v-model="baseInfo.form.inMinClear"
                      placeholder="请输入"
                      maxLength="32"
                      style="width: 50%"
                      :min="0"
                    ></el-input-number>
                    <span style="margin-left: 4px">元</span>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item
                    label="分成比例"
                    prop="inClearRatioType"
                    :label-width="formLabelWidth"
                  >
                    <el-select
                      v-model="baseInfo.form.inClearRatioType"
                      placeholder="请选择"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in inClearRatioTypeList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col
                  :span="6"
                  v-if="baseInfo.form.inClearRatioType === 'fixed'"
                >
                  <el-form-item
                    label="内部车辆固定分成比例"
                    prop="inClearFixedRatio"
                    :label-width="formLabelWidth"
                  >
                    <el-input
                      v-model="baseInfo.form.inClearFixedRatio"
                      placeholder="请输入"
                      maxLength="32"
                      style="width: 80%"
                    ></el-input>
                    <span style="margin-left: 4px">%</span>
                  </el-form-item>
                </el-col>
                <el-col
                  :span="24"
                  v-if="baseInfo.form.inClearRatioType === 'ladder'"
                >
                  <el-form-item
                    label=""
                    prop="inLadderType"
                    :label-width="formLabelWidth"
                  >
                    <el-radio-group
                      v-model="baseInfo.form.inLadderType"
                      @change="inLadderTypeChange"
                    >
                      <el-radio
                        v-for="item in inLadderTypeList"
                        :label="item.value"
                      >
                        {{ item.label }}
                      </el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row v-if="baseInfo.form.inClearRatioType === 'ladder'">
                <el-row
                  :gutter="20"
                  v-if="baseInfo.form.inLadderType === 'power'"
                  v-for="(domains, index) in baseInfo.form.inLadderList"
                  :key="index"
                >
                  <el-col :span="6">
                    <el-form-item
                      label=""
                      :prop="'inLadderList.' + index + '.begRange'"
                      :rules="{
                        required: true,
                        message: '请输入',
                        trigger: 'blur',
                      }"
                      :label-width="formLabelWidth"
                    >
                      <el-input
                        v-model="domains.begRange"
                        placeholder="请输入"
                        maxLength="32"
                        style="width: 80%"
                      ></el-input>
                      <span style="margin-left: 4px">kwh</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="3">
                    <el-form-item
                      label=""
                      :prop="'inLadderList.' + index + '.rangeType'"
                      :rules="{
                        required: true,
                        message: '请选择',
                        trigger: 'blur',
                      }"
                      :label-width="formLabelWidth"
                    >
                      <el-select
                        v-model="baseInfo.form.inLadderList[0].rangeType"
                        placeholder="请选择"
                        :disabled="index !== 0"
                        @change="rangeTypeChange"
                      >
                        <el-option
                          v-for="item in rangeTypeList"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="4">
                    <el-select
                      v-model="domains.type"
                      placeholder="请选择"
                      :disabled="true"
                    >
                      <el-option
                        v-for="item in typeList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </el-col>

                  <el-col :span="6">
                    <el-form-item
                      label=""
                      :label-width="formLabelWidth"
                      :prop="'inLadderList.' + index + '.endRange'"
                      :rules="{
                        required: true,
                        message: '请选择',
                        trigger: 'blur',
                      }"
                    >
                      <el-input
                        v-model="domains.endRange"
                        placeholder="请输入"
                        maxLength="32"
                        style="width: 80%"
                      ></el-input>
                      <span style="margin-left: 4px">kwh</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="5">
                    <el-form-item
                      label=""
                      :prop="'inLadderList.' + index + '.clearRatio'"
                      :rules="{
                        required: true,
                        message: '请输入',
                        trigger: 'blur',
                      }"
                      :label-width="formLabelWidth"
                    >
                      <span style="margin-right: 4px">分成比例：</span>

                      <el-input
                        v-model="domains.clearRatio"
                        placeholder="请输入"
                        maxLength="32"
                        style="width: 60%"
                      ></el-input>
                      <span style="margin-left: 4px">%</span>
                      <i
                        style="color: #217aff; margin-left: 4px"
                        class="el-icon-delete"
                        v-if="baseInfo.form.inLadderList.length > 1"
                        @click="deleteRatio(domains)"
                      ></i>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row
                  :gutter="20"
                  v-if="baseInfo.form.inLadderType === 'time'"
                  v-for="(domains, index) in baseInfo.form.inLadderList"
                  :key="index"
                >
                  <el-col :span="5">
                    <el-form-item
                      label=""
                      :prop="'inLadderList.' + index + '.begRange'"
                      :rules="{
                        required: true,
                        message: '请选择',
                        trigger: 'blur',
                      }"
                      :label-width="formLabelWidth"
                    >
                      <el-date-picker
                        v-if="
                          baseInfo.form.inLadderList[0].rangeType === 'month'
                        "
                        v-model="domains.begRange"
                        type="month"
                        placeholder="选择月"
                      ></el-date-picker>
                      <el-date-picker
                        v-else
                        v-model="domains.begRange"
                        type="year"
                        placeholder="选择年"
                      ></el-date-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span="5">
                    <el-form-item
                      label=""
                      :prop="'inLadderList.' + index + '.endRange'"
                      :rules="{
                        required: true,
                        message: '请选择',
                        trigger: 'blur',
                      }"
                      :label-width="formLabelWidth"
                    >
                      <el-date-picker
                        v-if="
                          baseInfo.form.inLadderList[0].rangeType === 'month'
                        "
                        v-model="domains.endRange"
                        type="month"
                        placeholder="选择月"
                      ></el-date-picker>
                      <el-date-picker
                        v-else
                        v-model="domains.endRange"
                        type="year"
                        placeholder="选择年"
                      ></el-date-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span="5">
                    <el-form-item
                      label=""
                      :prop="'inLadderList.' + index + '.rangeType'"
                      :rules="{
                        required: true,
                        message: '请选择',
                        trigger: 'blur',
                      }"
                      :label-width="formLabelWidth"
                    >
                      <el-select
                        v-model="baseInfo.form.inLadderList[0].rangeType"
                        placeholder="请选择"
                        style="width: 100%"
                        :disabled="index !== 0"
                        @change="rangeTypeChange"
                      >
                        <el-option
                          v-for="item in rangeTypeNewList"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="9">
                    <el-form-item
                      label=""
                      :prop="'inLadderList.' + index + '.clearRatio'"
                      :rules="{
                        required: true,
                        message: '请输入',
                        trigger: 'blur',
                      }"
                      :label-width="formLabelWidth"
                    >
                      <span style="margin-right: 4px">分成比例：</span>
                      <el-input
                        v-model="domains.clearRatio"
                        placeholder="请输入"
                        maxLength="32"
                        style="width: 70%"
                      ></el-input>
                      <span style="margin-left: 4px">%</span>
                      <i
                        style="color: #217aff; margin-left: 4px"
                        class="el-icon-delete"
                        v-if="baseInfo.form.inLadderList.length > 1"
                        @click="deleteRatio(domains)"
                      ></i>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-button
                    @click="addinRatio"
                    style="margin: 0 10px 10px 10px; width: calc(100% - 20px)"
                  >
                    添加
                  </el-button>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="24">
                    <el-form-item
                      label=""
                      prop="inLadderCalType"
                      :label-width="formLabelWidth"
                    >
                      <el-radio-group v-model="baseInfo.form.inLadderCalType">
                        <el-radio
                          v-for="(item, index) in inLadderCalTypeList"
                          :label="item.value"
                          :key="index"
                        >
                          {{ item.label || '' }}
                        </el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-row>
            </div>
          </div>

          <div class="form-wrap-box" v-if="baseInfo.form.inOutSameFlag === 0">
            <div class="card-head" style="margin-bottom: 0">
              <div class="before-icon"></div>
              <div class="card-head-text card-head-text-sing">外部车辆</div>
            </div>
            <div class="form-wrap" style="padding-bottom: 0">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item
                    label="固定保底分成金额"
                    prop="outMinClear"
                    :label-width="formLabelWidth"
                  >
                    <el-input-number
                      v-model="baseInfo.form.outMinClear"
                      placeholder="请输入"
                      maxLength="32"
                      style="width: 50%"
                    ></el-input-number>
                    <span style="margin-left: 4px">元</span>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item
                    label="分成比例"
                    prop="outClearRatioType"
                    :label-width="formLabelWidth"
                  >
                    <el-select
                      v-model="baseInfo.form.outClearRatioType"
                      placeholder="请选择"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="item in inClearRatioTypeList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col
                  :span="6"
                  v-if="baseInfo.form.outClearRatioType === 'fixed'"
                >
                  <el-form-item
                    label="外部车辆固定分成比例"
                    prop="outClearFixedRatio"
                    :label-width="formLabelWidth"
                  >
                    <el-input
                      v-model="baseInfo.form.outClearFixedRatio"
                      placeholder="请输入"
                      maxLength="32"
                      style="width: 80%"
                    ></el-input>
                    <span style="margin-left: 4px">%</span>
                  </el-form-item>
                </el-col>
                <el-col
                  :span="24"
                  v-if="baseInfo.form.outClearRatioType === 'ladder'"
                >
                  <el-form-item
                    label=""
                    prop="outLadderType"
                    :label-width="formLabelWidth"
                  >
                    <el-radio-group
                      v-model="baseInfo.form.outLadderType"
                      @change="outLadderTypeChange"
                    >
                      <el-radio
                        v-for="item in inLadderTypeList"
                        :label="item.value"
                      >
                        {{ item.label }}
                      </el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row v-if="baseInfo.form.outClearRatioType === 'ladder'">
                <el-row
                  :gutter="20"
                  v-if="baseInfo.form.outLadderType === 'power'"
                  v-for="(domains, index) in baseInfo.form.outLadderList"
                  :key="index"
                >
                  <el-col :span="6">
                    <el-form-item
                      label=""
                      :prop="'outLadderList.' + index + '.begRange'"
                      :rules="{
                        required: true,
                        message: '请输入',
                        trigger: 'blur',
                      }"
                      :label-width="formLabelWidth"
                    >
                      <el-input
                        v-model="domains.begRange"
                        placeholder="请输入"
                        maxLength="32"
                        style="width: 80%"
                      ></el-input>
                      <span style="margin-left: 4px">kwh</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="3">
                    <el-form-item
                      label=""
                      :prop="'outLadderList.' + index + '.rangeType'"
                      :rules="{
                        required: true,
                        message: '请选择',
                        trigger: 'blur',
                      }"
                      :label-width="formLabelWidth"
                    >
                      <el-select
                        v-model="baseInfo.form.outLadderList[0].rangeType"
                        placeholder="请选择"
                        :disabled="index !== 0"
                        @change="outRangeTypeChange"
                      >
                        <el-option
                          v-for="item in rangeTypeList"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="4">
                    <el-select
                      v-model="domains.type"
                      placeholder="请选择"
                      :disabled="true"
                    >
                      <el-option
                        v-for="item in typeList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      ></el-option>
                    </el-select>
                  </el-col>

                  <el-col :span="6">
                    <el-form-item
                      label=""
                      :label-width="formLabelWidth"
                      :prop="'outLadderList.' + index + '.endRange'"
                      :rules="{
                        required: true,
                        message: '请选择',
                        trigger: 'blur',
                      }"
                    >
                      <el-input
                        v-model="domains.endRange"
                        placeholder="请输入"
                        maxLength="32"
                        style="width: 80%"
                      ></el-input>
                      <span style="margin-left: 4px">kwh</span>
                    </el-form-item>
                  </el-col>
                  <el-col :span="5">
                    <el-form-item
                      label=""
                      :prop="'outLadderList.' + index + '.clearRatio'"
                      :rules="{
                        required: true,
                        message: '请输入',
                        trigger: 'blur',
                      }"
                      :label-width="formLabelWidth"
                    >
                      <span style="margin-right: 4px">分成比例：</span>

                      <el-input
                        v-model="domains.clearRatio"
                        placeholder="请输入"
                        maxLength="32"
                        style="width: 60%"
                      ></el-input>
                      <span style="margin-left: 4px">%</span>
                      <i
                        style="color: #217aff; margin-left: 4px"
                        class="el-icon-delete"
                        v-if="baseInfo.form.outLadderList.length > 1"
                        @click="outDeleteRatio(domains)"
                      ></i>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row
                  :gutter="20"
                  v-if="baseInfo.form.outLadderType === 'time'"
                  v-for="(domains, index) in baseInfo.form.outLadderList"
                  :key="index"
                >
                  <el-col :span="5">
                    <el-form-item
                      label=""
                      :prop="'outLadderList.' + index + '.begRange'"
                      :rules="{
                        required: true,
                        message: '请选择',
                        trigger: 'blur',
                      }"
                      :label-width="formLabelWidth"
                    >
                      <el-date-picker
                        v-if="
                          baseInfo.form.outLadderList[0].rangeType === 'month'
                        "
                        v-model="domains.begRange"
                        type="month"
                        placeholder="选择月"
                      ></el-date-picker>
                      <el-date-picker
                        v-else
                        v-model="domains.begRange"
                        type="year"
                        placeholder="选择年"
                      ></el-date-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span="5">
                    <el-form-item
                      label=""
                      :prop="'outLadderList.' + index + '.endRange'"
                      :rules="{
                        required: true,
                        message: '请选择',
                        trigger: 'blur',
                      }"
                      :label-width="formLabelWidth"
                    >
                      <el-date-picker
                        v-if="
                          baseInfo.form.outLadderList[0].rangeType === 'month'
                        "
                        v-model="domains.endRange"
                        type="month"
                        placeholder="选择月"
                      ></el-date-picker>
                      <el-date-picker
                        v-else
                        v-model="domains.endRange"
                        type="year"
                        placeholder="选择年"
                      ></el-date-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :span="5">
                    <el-form-item
                      label=""
                      :prop="'outLadderList.' + index + '.rangeType'"
                      :rules="{
                        required: true,
                        message: '请选择',
                        trigger: 'blur',
                      }"
                      :label-width="formLabelWidth"
                    >
                      <el-select
                        v-model="baseInfo.form.outLadderList[0].rangeType"
                        placeholder="请选择"
                        style="width: 100%"
                        :disabled="index !== 0"
                        @change="outRangeTypeChange"
                      >
                        <el-option
                          v-for="item in rangeTypeNewList"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="9">
                    <el-form-item
                      label=""
                      :prop="'outLadderList.' + index + '.clearRatio'"
                      :rules="{
                        required: true,
                        message: '请输入',
                        trigger: 'blur',
                      }"
                      :label-width="formLabelWidth"
                    >
                      <span style="margin-right: 4px">分成比例：</span>
                      <el-input
                        v-model="domains.clearRatio"
                        placeholder="请输入"
                        maxLength="32"
                        style="width: 70%"
                      ></el-input>
                      <span style="margin-left: 4px">%</span>
                      <i
                        style="color: #217aff; margin-left: 4px"
                        class="el-icon-delete"
                        v-if="baseInfo.form.outLadderList.length > 1"
                        @click="outDeleteRatio(domains)"
                      ></i>
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="20">
                  <el-button
                    @click="addOutRatio"
                    style="margin: 0 10px 10px 10px; width: calc(100% - 20px)"
                  >
                    添加
                  </el-button>
                </el-row>
                <el-row :gutter="20">
                  <el-col :span="24">
                    <el-form-item
                      label=""
                      prop="outLadderCalType"
                      :label-width="formLabelWidth"
                    >
                      <el-radio-group v-model="baseInfo.form.outLadderCalType">
                        <el-radio
                          v-for="(item, index) in inLadderCalTypeList"
                          :label="item.value"
                          :key="index"
                        >
                          {{ item.label || '' }}
                        </el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-row>
            </div>
          </div>
        </div>
        <div class="form-division-formula">
          {{ divisionFormula() }}
        </div>
      </div>
      <!-- <div class="info-card">
        <div class="card-head card-head-bottom">
          <div class="before-icon"></div>
          <div class="card-head-text">场地租金配置</div>
        </div>
        <div class="form-wrap" style="padding-bottom: 0">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                label=""
                prop="operatorStatus"
                :label-width="formLabelWidth"
              >
                <el-radio-group v-model="baseInfo.form.operatorStatus">
                  <el-radio :label="1">固定场地租金</el-radio>
                  <el-radio :label="0">浮动场地租金</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-date-picker
                v-model="baseInfo.form.operatorCategoryList"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 80%"
              ></el-date-picker>
              <el-select
                style="width: 20%"
                v-model="baseInfo.form.operatorCategoryList"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in this.dict.type.ls_charging_operator_category"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-col>

            <el-col :span="12">
              <el-form-item
                label=""
                prop="operatorStatus"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-model="baseInfo.form.operatorName"
                  placeholder="请输入"
                  maxLength="32"
                  style="width: 70%"
                ></el-input>
                <el-select
                  style="width: 20%"
                  v-model="baseInfo.form.operatorCategoryList"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in this.dict.type.ls_charging_operator_category"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
                <i
                  style="color: #217aff; margin-left: 4px"
                  class="el-icon-delete"
                ></i>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-button
              style="margin: 0 10px 10px 10px; width: calc(100% - 20px)"
            >
              添加
            </el-button>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                label="场地租金"
                prop="operatorStatus"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-model="baseInfo.form.operatorName"
                  placeholder="请输入"
                  maxLength="32"
                  style="width: 90%"
                ></el-input>
                <span style="margin-left: 4px">元/月</span>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item
                label="分成周期"
                prop="operatorStatus"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.operatorCategoryList"
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in this.dict.type.ls_charging_operator_category"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="首次起始周期"
                prop="operatorStatus"
                :label-width="formLabelWidth"
              >
                <el-date-picker
                  v-model="baseInfo.form.operatorCategoryList"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  style="width: 100%"
                ></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
        <div class="form-division-formula" style="padding-top: 0">
          {{ divisionFormula() }}
        </div>
      </div> -->
      <!-- 
      <div class="info-card">
        <div class="card-head card-head-bottom">
          <div class="before-icon"></div>
          <div class="card-head-text">平台服务费配置</div>
        </div>
        <div class="form-wrap" style="padding-bottom: 0">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                label=""
                prop="operatorStatus"
                :label-width="formLabelWidth"
              >
                <el-radio-group v-model="baseInfo.form.operatorStatus">
                  <el-radio :label="1">按站</el-radio>
                  <el-radio :label="0">按桩</el-radio>
                  <el-radio :label="2">按充电服务费</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                label="充电服务费"
                prop="operatorStatus"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.operatorCategoryList"
                  placeholder="请选择"
                  style="width: 45%"
                >
                  <el-option
                    v-for="item in this.dict.type.ls_charging_operator_category"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
                <el-input
                  v-model="baseInfo.form.operatorName"
                  placeholder="请输入"
                  maxLength="32"
                  style="width: 45%"
                ></el-input>
                <span style="margin-left: 4px">元/度</span>
              </el-form-item>
            </el-col>

            <el-col :span="8">
              <el-form-item
                label="分成周期"
                prop="operatorStatus"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.operatorCategoryList"
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in this.dict.type.ls_charging_operator_category"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="首次起始周期"
                prop="operatorStatus"
                :label-width="formLabelWidth"
              >
                <el-date-picker
                  v-model="baseInfo.form.operatorCategoryList"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  style="width: 100%"
                ></el-date-picker>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="8">
              <el-form-item
                label="分成比例"
                prop="operatorStatus"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.operatorCategoryList"
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in this.dict.type.ls_charging_operator_category"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                label=""
                prop="operatorStatus"
                :label-width="formLabelWidth"
              >
                <el-radio-group v-model="baseInfo.form.operatorStatus">
                  <el-radio :label="1">按充电量</el-radio>
                  <el-radio :label="0">按合作时间</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-date-picker
                v-model="baseInfo.form.operatorCategoryList"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 80%"
              ></el-date-picker>
              <el-select
                style="width: 20%"
                v-model="baseInfo.form.operatorCategoryList"
                placeholder="请选择"
              >
                <el-option
                  v-for="item in this.dict.type.ls_charging_operator_category"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-col>

            <el-col :span="12">
              <el-form-item
                label=""
                prop="operatorStatus"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-model="baseInfo.form.operatorName"
                  placeholder="请输入"
                  maxLength="32"
                  style="width: 70%"
                ></el-input>
                <el-select
                  style="width: 20%"
                  v-model="baseInfo.form.operatorCategoryList"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in this.dict.type.ls_charging_operator_category"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
                <i
                  style="color: #217aff; margin-left: 4px"
                  class="el-icon-delete"
                ></i>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-button
              style="margin: 0 10px 10px 10px; width: calc(100% - 20px)"
            >
              添加
            </el-button>
          </el-row>
        </div>
      </div> -->
    </el-form>

    <!-- 底部按钮 -->
    <div class="bottom-button-wrap">
      <div>
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" :loading="submitLoading" @click="handleSave">
          确定
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { getOperatorsList } from '@/api/operator/index';
import {
  clearRuleDetail,
  clearRuleCreate,
  clearRuleUpdate,
} from '@/api/sortingManage/sortingRuleManage';
import { getToken } from '@/utils/auth';
import Upload from '@/components/Upload/index';
import moment from 'moment';

export default {
  name: 'InterconnectionCreate',
  dicts: [
    'ls_clear_to_obj_type',
    'ls_clear_space_owner',
    'ls_clear_space_operator',
  ],
  components: {
    Upload,
  },
  data() {
    return {
      uploadHeaders: {
        Authorization: 'Bearer ' + getToken(),
      },
      formLabelWidth: '120px',
      pageType: 'create',
      id: '',
      baseInfo: {
        form: {
          ruleName: '',
          toObjType: '',
          toObj: '',
          clearType: '',
          fromObj: '',
          effectDate: [],
          clearCostType: '',
          chargeServiceFeeType: '',
          chargeServiceFeePrice: '',
          clearPeriod: '',
          inOutSameFlag: 1,
          inMinClear: '',
          inClearRatioType: '',
          inClearFixedRatio: '',
          inLadderType: 'power',
          inLadderCalType: '',
          inLadderList: [
            {
              ladderType: 'in',
              begRange: '',
              endRange: '',
              clearRatio: '',
              rangeType: '',
              type: '1',
              idIndex: 1,
            },
          ],
          outMinClear: '',
          outClearRatioType: '',
          outClearFixedRatio: '',
          outLadderType: 'power',
          outLadderCalType: '',
          outLadderList: [
            {
              ladderType: 'out',
              begRange: '',
              endRange: '',
              clearRatio: '',
              rangeType: '',
              type: '1',
              idIndex: 1,
            },
          ],
          spaceRentType: '',
          spaceRentPeriod: '1',
        },
        rules: {
          ruleName: [
            { required: true, message: '请输入分成规则名称', trigger: 'blur' },
          ],
          toObjType: [
            { required: true, message: '请选择分成方类型', trigger: 'blur' },
          ],
          toObj: [
            {
              required: true,
              message: '请选择分成方',
              trigger: 'blur',
            },
          ],
          clearType: [
            {
              required: true,
              message: '请选择分成类型',
              trigger: 'blur',
            },
          ],
          fromObj: [
            {
              required: true,
              message: '请选择结算方',
              trigger: 'blur',
            },
          ],
          chargeServiceFeeType: [
            {
              required: true,
              message: '请选择充电服务费',
              trigger: 'blur',
            },
          ],
          effectDate: [
            { required: true, message: '请选择生效时间', trigger: 'blur' },
          ],
          clearCostType: [
            { required: true, message: '请选择分成费用', trigger: 'blur' },
          ],
          chargeServiceFeePrice: [
            {
              required: true,
              message: '请输入充电服务费单价',
              trigger: 'blur',
            },
          ],
          clearPeriod: [
            { required: true, message: '请选择分成周期', trigger: 'blur' },
          ],

          inClearRatioType: [
            {
              required: true,
              message: '请选择内部车辆分成方式',
              trigger: 'change',
            },
          ],
          inClearFixedRatio: [
            {
              required: true,
              message: '请输入内部车辆固定分成比例',
              trigger: 'blur',
            },
          ],
          inLadderType: [
            {
              required: true,
              message: '请选择内部阶梯方式',
              trigger: 'change',
            },
          ],
          inLadderCalType: [
            {
              required: true,
              message: '请选择内部阶梯计算方式',
              trigger: 'change',
            },
          ],
          outLadderType: [
            {
              required: true,
              message: '请选择外部阶梯方式',
              trigger: 'change',
            },
          ],
          outLadderCalType: [
            {
              required: true,
              message: '请选择外部阶梯计算方式',
              trigger: 'change',
            },
          ],
        },
      },
      clearTypeList: [
        { label: '服务费', value: 'service' },
        { label: '电费+服务费', value: 'elecAndService' },
        // { label: '场地租金', value: 'space' },
        // { label: '场地租金+服务费', value: 'spaceAndService' },
      ],
      clearPeriodList: [
        // { label: '月', value: 1 },
        { label: '自然月', value: 2 },
      ],
      inOutSameFlagList: [
        { label: '不一致', value: 0 },
        { label: '一致', value: 1 },
      ],
      clearCostTypeList: [
        { label: '充电服务费', value: '01' },
        { label: '充电服务费-费损', value: '02' },
        { label: '充电电费+充电服务费-供电电费-运维成本', value: '03' },
        {
          label: '充电电费+充电服务费-供电电费-运维成本-平台服务费',
          value: '04',
        },
      ],
      chargeServiceFeeTypeList: [
        { label: '固定服务费单价', value: 'fixed' },
        { label: '清分订单服务费', value: 'order' },
        { label: '清分实收服务费', value: 'real' },
      ],
      inClearRatioTypeList: [
        { label: '固定', value: 'fixed' },
        { label: '阶梯', value: 'ladder' },
      ],
      inLadderTypeList: [
        { label: '充电量', value: 'power' },
        { label: '按合作时间', value: 'time' },
      ],
      inLadderCalTypeList: [
        { label: '区间', value: 'interval' },
        { label: '阶梯', value: 'ladder' },
      ],
      rangeTypeList: [
        { label: '日', value: 'day' },
        { label: '月', value: 'month' },
      ],
      rangeTypeNewList: [
        { label: '月', value: 'month' },
        // { label: '季', value: 'quarter' },
        { label: '年', value: 'year' },
      ],
      typeList: [{ label: '平均充电量', value: '1' }],
      businessTypeList: [],
      contractList: [],
      operationLoading: false,
      operationUnitList: [],
      idMax: 10,
      submitLoading: false,
    };
  },
  mounted() {
    const id = this.$route.query.id;
    if (id) {
      this.pageType = 'edit';
      this.id = id;
      this.getClearRuleDetail();
    }
  },
  methods: {
    outRangeTypeChange(event) {
      this.baseInfo.form.outLadderList.forEach((item) => {
        item.rangeType = event;
      });
    },
    rangeTypeChange(event) {
      this.baseInfo.form.inLadderList.forEach((item) => {
        item.rangeType = event;
      });
    },
    outLadderTypeChange() {
      this.baseInfo.form.outLadderList = [
        {
          ladderType: 'out',
          begRange: '',
          endRange: '',
          clearRatio: '',
          rangeType: '',
          type: '1',
          idIndex: ++this.idMax,
        },
      ];
    },
    inLadderTypeChange() {
      this.baseInfo.form.inLadderList = [
        {
          ladderType: 'in',
          begRange: '',
          endRange: '',
          clearRatio: '',
          rangeType: '',
          type: '1',
          idIndex: ++this.idMax,
        },
      ];
    },
    outDeleteRatio(obj) {
      this.baseInfo.form.outLadderList =
        this.baseInfo.form.outLadderList.filter(
          (item) => item.idIndex !== obj.idIndex
        );
    },
    deleteRatio(obj) {
      this.baseInfo.form.inLadderList = this.baseInfo.form.inLadderList.filter(
        (item) => item.idIndex !== obj.idIndex
      );
    },
    addOutRatio() {
      this.baseInfo.form.outLadderList.push({
        ladderType: 'in',
        begRange: '',
        endRange: '',
        clearRatio: '',
        rangeType: this.baseInfo.form.outLadderList[0]?.rangeType || '',
        type: '1',
        idIndex: ++this.idMax,
      });
    },
    addinRatio() {
      this.baseInfo.form.inLadderList.push({
        ladderType: 'in',
        begRange: '',
        endRange: '',
        clearRatio: '',
        rangeType: this.baseInfo.form.inLadderList[0]?.rangeType || '',
        type: '1',
        idIndex: ++this.idMax,
      });
    },
    toObjTypeChange(val) {
      this.baseInfo.form.toObj = undefined;
      this.baseInfo.form.fromObj = val === '0' ? '省电动' : undefined;
    },
    async debouncedOperationSearch(query, type) {
      if (query !== '') {
        this.operationLoading = true;
        setTimeout(async () => {
          const params = {
            operatorName: query,
            pageNum: 1,
            pageSize: 50,
            linkBizTypeList: [],
            operatorCategoryList: [],
          };
          if (type) {
            delete params.operatorName;
            params.operatorNo = query;
          }

          const [err, res] = await getOperatorsList(params);

          if (err) return;
          this.operationLoading = false;
          this.operationUnitList = res.data.map((item) => ({
            label: item.operatorName,
            value: item.operatorNo,
          }));
        }, 200);
      } else {
        this.operationUnitList = [];
      }
    },
    divisionFormula() {
      let returnStr = '';

      const { clearType, chargeServiceFeeType } = this.baseInfo.form;
      if (clearType === 'service' && chargeServiceFeeType === 'order') {
        returnStr =
          '充电服务费=清分订单服务费：（否）承担费损（清分订单服务费）*分成比例；';
      } else if (clearType === 'service' && chargeServiceFeeType === 'real') {
        returnStr =
          '充电服务费=清分实收服务费：（否）承担费损（清分实收服务费）*分成比例；';
      } else if (clearType === 'service' && chargeServiceFeeType === 'fixed') {
        returnStr =
          '充电服务费=固定服务费单价：（否）承担费损（固定服务费单价*清分电量）*分成比例；';
      } else if (
        clearType === 'elecAndService' &&
        chargeServiceFeeType === 'order'
      ) {
        returnStr =
          '充电电费=清分实收电费，充电服务费=清分订单服务费：清分实收电费+清分订单服务费*分成比例；';
      } else if (
        clearType === 'elecAndService' &&
        chargeServiceFeeType === 'real'
      ) {
        returnStr =
          '充电电费=清分实收电费，充电服务费=清分实收服务费：清分实收电费+清分实收服务费*分成比例；';
      } else if (
        clearType === 'elecAndService' &&
        chargeServiceFeeType === 'fixed'
      ) {
        returnStr =
          '充电电费=清分实收电费，充电服务费=固定服务费单价：清分实收电费+（固定服务费单价*清分电量）*分成比例；';
      }
      return returnStr;
    },

    handleUpload(file) {
      console.log('上传中:', file);
    },
    handleUploadSuccess(file) {
      console.log('上传成功:', file);
    },
    handleExceed() {
      this.$message.warning('只能上传1张图片');
    },
    handleCancel() {
      this.$router.back();
    },
    async handleSave() {
      try {
        await Promise.all([this.$refs.baseInfoForm.validate()]);
        this.submitLoading = true;
        const params = { ...this.baseInfo.form };

        if (params.effectDate && params.effectDate.length) {
          params.begDate = moment(params.effectDate[0]).format('YYYY-MM-DD');
          params.endDate = moment(params.effectDate[1]).format('YYYY-MM-DD');
        }

        if (this.pageType === 'create') {
          const [err, res] = await clearRuleCreate(params);
          this.submitLoading = false;
          if (err) {
            return this.$message.error(err.message || '新增失败');
          }
          this.$message.success('新增成功');

          setTimeout(() => {
            this.$router.back();
          }, 2000);
        } else if (this.pageType === 'edit') {
          const [err, res] = await clearRuleUpdate({
            ...params,
            id: this.id,
          });
          this.submitLoading = false;

          if (err) {
            return this.$message.error(err.message || '修改失败');
          }
          this.$message.success('修改成功');

          setTimeout(() => {
            this.$router.back();
          }, 2000);
        }
      } catch (error) {
        console.log(error, 'error');
        this.$message.error('表单校验失败，请检查输入');
      }
    },

    // 获取运营商详情
    async getClearRuleDetail() {
      const [err, res] = await clearRuleDetail({
        id: this.id,
      });

      if (err) return;
      const {
        ruleName,
        toObjType,
        toObj,
        clearType,
        fromObj,
        clearCostType,
        chargeServiceFeePrice,
        clearPeriod,
        inOutSameFlag,
        inMinClear,
        inClearRatioType,
        inClearFixedRatio,
        inLadderType,
        inLadderCalType,
        inLadderList,
        outMinClear,
        outClearRatioType,
        outClearFixedRatio,
        outLadderType,
        outLadderCalType,
        outLadderList,
        spaceRentType,
        spaceRentPeriod,
        begDate,
        endDate,
        chargeServiceFeeType,
      } = res?.data || {};
      this.baseInfo.form = {
        ruleName,
        toObjType,
        toObj,
        clearType,
        fromObj,
        clearCostType,
        chargeServiceFeePrice,
        clearPeriod,
        inOutSameFlag,
        inMinClear,
        inClearRatioType,
        inClearFixedRatio,
        inLadderType,
        inLadderCalType,
        inLadderList: inLadderList
          ? inLadderList.map((item) => {
              return { ...item, idIndex: ++this.idMax };
            })
          : [],
        outMinClear,
        outClearRatioType,
        outClearFixedRatio,
        outLadderType,
        outLadderCalType,
        outLadderList: outLadderList
          ? outLadderList.map((item) => {
              return { ...item, idIndex: ++this.idMax };
            })
          : [],
        spaceRentType,
        spaceRentPeriod,
        chargeServiceFeeType,
        effectDate: [begDate, endDate],
      };
      this.debouncedOperationSearch(toObj, true);
    },
  },
};
</script>

<style lang="scss" scoped>
.info-card {
  margin: 16px;
  border-radius: 5px;
  border: 1px solid #fff;
  overflow: hidden;
  background-color: #fff;

  .card-head {
    height: 48px;
    padding: 0 15px;
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    .before-icon {
      width: 3px;
      height: 16px;
      background-image: url('~@/assets/station/consno-before.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin-right: 8px;
    }

    .card-head-text {
      flex: 1;
      font-weight: 500;
      font-size: 16px;
      color: #12151a;
    }
    .card-head-text-sing {
      font-weight: 400;
      color: #21252e;
    }
  }
  .card-head-bottom {
    border-bottom: 1px solid #e9ebf0;
  }

  .form-wrap {
    padding: 0 16px 16px 16px;
  }
}

.interface-section {
  margin: 16px 0;

  .el-checkbox-group {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 16px;
  }

  .el-checkbox {
    margin-right: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.bottom-button-wrap {
  height: 86px;
  margin-top: 16px;
  width: 100%;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: 32px;
  box-sizing: border-box;
}
.form-wrap-box {
  background: #f9f9fb;
}
.form-division-formula {
  padding: 24px;
  font-family: PingFang SC;
  font-weight: 500;
  font-size: 16px;
  line-height: 18px;
  color: #12151a;
}

::v-deep .el-input {
  width: 100%;
}
::v-deep .el-form-item__label {
  padding-bottom: 0;
}
</style>
