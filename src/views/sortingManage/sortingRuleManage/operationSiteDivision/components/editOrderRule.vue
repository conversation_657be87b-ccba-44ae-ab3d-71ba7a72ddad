<template>
  <el-dialog title="场站分成规则" :visible.sync="dialogVisible" width="952px">
    <div class="info-card">
      <div class="form-wrap" v-if="dialogVisible">
        <el-form
          :model="baseInfo.form"
          :rules="baseInfo.rules"
          ref="baseInfoForm"
          label-position="top"
        >
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item
                label="充电站："
                prop="stationId"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.stationId"
                  placeholder="请选择充电站"
                  style="width: 100%"
                  filterable
                  remote
                  :loading="stationLoading"
                  :remote-method="debouncedStationSearch"
                >
                  <el-option
                    v-for="item in stationNameList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="分成规则："
                prop="type"
                :label-width="formLabelWidth"
              >
                <!-- <el-select
                  v-model="baseInfo.form.ruleId"
                  placeholder="请选择分成规则"
                  style="width: 100%"
                  @change="ruleTypeChange"
                  filterable
                  remote
                  :loading="clearRuleLoading"
                  :remote-method="debouncedClearRulePageSearch"
                >
                  <el-option
                    v-for="item in clearRuleList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select> -->
                <el-select
                  v-model="baseInfo.form.ruleId"
                  placeholder="请选择分成规则"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in clearRuleList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item
                label="分成类型："
                prop="clearType"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.clearType"
                  placeholder="自动带出"
                  style="width: 100%"
                  :disabled="true"
                >
                  <el-option
                    v-for="item in clearTypeList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="分成周期："
                prop="spaceRentPeriod"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.spaceRentPeriod"
                  placeholder="自动带出"
                  style="width: 100%"
                  :disabled="true"
                >
                  <el-option
                    v-for="item in spaceRentPeriodList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="分成规则编号："
                prop="ruleId"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-model="baseInfo.form.ruleId"
                  placeholder="自动带出"
                  :disabled="true"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="分成方类型："
                prop="toObjType"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.toObjType"
                  placeholder="自动带出"
                  style="width: 100%"
                  :disabled="true"
                >
                  <el-option
                    v-for="item in dict.type.ls_clear_to_obj_type"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="分成方："
                prop="toObjName"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-model="baseInfo.form.toObjName"
                  placeholder="自动带出"
                  style="width: 100%"
                  :disabled="true"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="结算方："
                prop="fromObjName"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-model="baseInfo.form.fromObjName"
                  placeholder="自动带出"
                  style="width: 100%"
                  :disabled="true"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="分成条款："
                prop="ruleClause"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-model="baseInfo.form.ruleClause"
                  placeholder="请输入分成条款"
                ></el-input>
              </el-form-item>
            </el-col>
            <!-- <el-col :span="12">
              <el-form-item
                label="关联合同："
                prop="description"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.description"
                  placeholder="请选择关联合同"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in dict.type.ls_order_except_level"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col> -->
            <el-col :span="24">
              <el-form-item
                label="备注："
                prop="relaDesc"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-model="baseInfo.form.relaDesc"
                  placeholder="请输入"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <div class="bottom-wrap">
      <div>
        <el-button @click="handleCancel">取消</el-button>
        <el-button @click="handleSave" :loading="submitLoading" type="primary">
          提交
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>
<script>
import { getStationList } from '@/api/pile/index';
import {
  clearRulePage,
  stationClearRuleCreate,
  stationClearRuleUpdate,
  stationClearRuleDetail,
} from '@/api/sortingManage/sortingRuleManage';
export default {
  props: {
    type: {
      type: String,
      default: 'create',
    },
    detailObj: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  components: {},
  dicts: ['ls_clear_to_obj_type'],
  data() {
    return {
      dialogVisible: false,
      formLabelWidth: '120px',
      clearTypeList: [
        { label: '服务费', value: 'service' },
        { label: '电费+服务费', value: 'elecAndService' },
        { label: '场地租金', value: 'space' },
        { label: '场地租金+服务费', value: 'spaceAndService' },
      ],
      spaceRentPeriodList: [
        { label: '月', value: 1 },
        { label: '自然月', value: 2 },
      ],

      baseInfo: {
        form: {
          stationId: '',
          ruleId: '',
          clearType: '',
          spaceRentPeriod: '',
          toObjType: '',
          toObjName: '',
          fromObjName: '',
          ruleClause: '',
          relaDesc: '',
        },
        rules: {
          stationId: [
            { required: true, message: '请选择充电站', trigger: 'blur' },
          ],
          ruleId: [
            {
              required: true,
              message: '请选择分成规则',
              trigger: 'change',
            },
          ],
          ruleClause: [
            {
              required: true,
              message: '请输入分成条款',
              trigger: 'change',
            },
          ],
        },
      },
      submitLoading: false,
      stationLoading: false,
      stationNameList: [],
      clearRuleLoading: false,
      clearRuleList: [],
    };
  },
  watch: {
    dialogVisible(value) {
      if (!value) {
        this.baseInfo.form = {
          stationId: '',
          ruleId: '',
          clearType: '',
          spaceRentPeriod: '',
          toObjType: '',
          toObjName: '',
          fromObjName: '',
          ruleClause: '',
          relaDesc: '',
        };
      } else {
        if (Object.keys(this.detailObj)) {
          if (this.type !== 'create') {
            const {
              stationId,
              stationName,
              ruleId,
              ruleName,
              clearType,
              clearPeriod,
              toObjType,
              toObjName,
              fromObjName,
              ruleClause,
              relaDesc,
            } = this.detailObj;
            this.baseInfo.form = {
              stationId,
              ruleId,
              clearType,
              spaceRentPeriod: clearPeriod,
              toObjType,
              toObjName,
              fromObjName,
              ruleClause,
              relaDesc,
            };
            this.debouncedStationSearch(stationName);
            this.debouncedClearRulePageSearch(ruleName);
            this.queryStationClearRuleDetail();
          }
        }
      }
    },
  },
  computed: {},
  mounted() {
    this.debouncedClearRulePageSearch();
  },
  methods: {
    async queryStationClearRuleDetail() {
      const [err, res] = await stationClearRuleDetail({
        id: this?.detailObj?.id || '',
      });

      if (err) return;
      this.baseInfo.form.relaDesc = res?.data?.relaDesc || '';
    },
    async debouncedStationSearch(query) {
      if (query !== '') {
        this.stationLoading = true;
        setTimeout(async () => {
          const [err, res] = await getStationList({
            stationName: query,
          });

          if (err) return;
          this.stationLoading = false;
          this.stationNameList = res.data.map((item) => ({
            label: item.stationName,
            value: item.stationId,
          }));
        }, 200);
      } else {
        this.stationNameList = [];
      }
    },

    async debouncedClearRulePageSearch(query) {
      // if (query !== '') {
      this.clearRuleLoading = true;
      setTimeout(async () => {
        const [err, res] = await clearRulePage({
          ruleName: query,
        });

        if (err) return;
        this.clearRuleLoading = false;
        this.clearRuleList = res.data.map((item) => ({
          ...item,
          label: item.ruleName,
          value: item.ruleId,
        }));
      }, 200);
      // } else {
      //   this.clearRuleList = [];
      // }
    },

    ruleTypeChange(event) {
      const list = this.clearRuleList.filter((item) => {
        return item.ruleId === event;
      });
      if (list && list.length) {
        const {
          clearType,
          spaceRentPeriod,
          toObjType,
          toObjName,
          fromObjName,
        } = list[0];
        this.baseInfo.form = {
          ...this.baseInfo.form,
          clearType,
          spaceRentPeriod,
          toObjType,
          toObjName,
          fromObjName,
        };
      }
    },

    handleCancel() {
      this.dialogVisible = false;
    },
    // 保存
    handleSave() {
      this.$refs.baseInfoForm.validate(async (valid) => {
        if (valid) {
          this.submitLoading = true;
          if (this.type === 'create') {
            const [err, res] = await stationClearRuleCreate({
              ...this.baseInfo.form,
              agreeNo: '1',
            });
            this.submitLoading = false;
            if (err) return;
            this.$message({
              type: 'success',
              message: '新增成功!',
            });
            this.$emit('orderruleAdd', true);
            this.handleCancel();
          } else {
            const [err, res] = await stationClearRuleUpdate({
              id: this?.detailObj?.id || '',
              ...this.baseInfo.form,
              agreeNo: '1',
            });
            this.submitLoading = false;
            if (err) return;
            this.$message({
              type: 'success',
              message: '编辑成功!',
            });
            this.$emit('orderruleAdd', true);
            this.handleCancel();
          }
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.container-full {
  background-color: rgba(244, 246, 249, 1);
  padding-bottom: 16px;
  min-height: calc(100% - 80px);
  overflow-y: auto;
}

.info-card {
  margin: 16px;
  border-radius: 5px;
  border: 1px solid #fff;
  overflow: hidden;
  background-color: #fff;
  // min-height: 300px;
  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    .before-icon {
      width: 3px;
      height: 16px;
      background-image: url('~@/assets/station/consno-before.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin-right: 8px;
    }
    .card-head-text {
      flex: 1;
      font-weight: 500;
      font-size: 16px;
      color: #12151a;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
    .button-wrap {
      display: flex;
      .invite-btn {
        background-color: #1ab2ff;
        border-color: #1ab2ff;
      }
      ::v-deep .el-button--small {
        font-size: 14px;
      }
      .distribution {
        margin-left: 24px;
        margin-right: 24px;
        display: flex;
        align-items: center;
      }
    }
  }

  .people-table-wrap {
    ::v-deep .bd3001-table-select-box {
      display: none;
    }
    ::v-deep .bd3001-button {
      width: 100%;
    }
  }
  .form-wrap {
    padding: 0 16px 16px 16px;
    .custom-header {
      background: -webkit-gradient(
          linear,
          left top,
          left bottom,
          from(rgba(0, 149, 255, 0.5)),
          to(rgba(87, 152, 255, 0))
        ),
        #f5faff;
      background: linear-gradient(
          180deg,
          rgba(0, 149, 255, 0.5) 0%,
          rgba(87, 152, 255, 0) 100%
        ),
        #f5faff;
      background-repeat: no-repeat;
    }
  }
}

.container {
  position: relative;
  padding-bottom: 100px;
  box-sizing: border-box;
  .bottom-wrap {
    // position: fixed;
    // bottom: 0;
    // left: 0;
    // width: 100%;
    // height: 86px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background-color: #ffffff;
    padding-right: 32px;
    box-sizing: border-box;
  }
}
::v-deep .hidden-label .el-form-item__label:before {
  visibility: hidden;
}
</style>
