<template>
  <div class="container">
    <div class="table-wrap">
      <BuseCrud
        ref="crud"
        :loading="loading"
        :filterOptions="filterOptions"
        :tablePage="tablePage"
        :tableColumn="tableColumn"
        :tableData="tableData"
        :modalConfig="modalConfig"
         class="buse-wrap-station"
        @loadData="loadData"
        :tableOn="{
          'checkbox-change': handleCheckboxChange,
          'checkbox-all': handleCheckboxChange,
        }"
      >
        <template slot="defaultHeader">
          <div class="card-head">
            <div class="card-head-text">车网场站分成-运营商</div>
            <div class="top-button-wrap">
              <el-button type="primary" @click="handleAdd()">新增</el-button>
              <el-button type="primary" @click="handleOutput">导出</el-button>
            </div>
          </div>
          <div class="card-head-after"></div>
        </template>

        <!-- 操作列 -->
        <template slot="operate" slot-scope="{ row }">
          <div class="menu-box">
            <el-button type="primary" plain @click="handleDetail(row)">
              详情
            </el-button>
            <el-button type="primary" plain @click="handleSettle(row)">
              结算
            </el-button>
          </div>
        </template>
      </BuseCrud>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [{}],
      tableColumn: [
        { type: 'checkbox', width: 50 }, // 多选列
        { type: 'seq', title: '序号', minWidth: 60 },
        { field: 'divisionNo', title: '分成编号', minWidth: 140 },
        { field: 'divisionCycle', title: '分成周期', minWidth: 150 },
        { field: 'stationName', title: '充电站名称', minWidth: 160 },
        { field: 'city', title: '地市', minWidth: 120 },
        { field: 'settlementParty', title: '结算方', minWidth: 140 },
        { field: 'divisionParty', title: '分成方', minWidth: 140 },
        { field: 'divisionType', title: '分成类型', minWidth: 140 },
        {
          field: 'totalChargeEnergy',
          title: '充电总电量（KWH）',
          minWidth: 180,
        },
        {
          field: 'totalElectricityFee',
          title: '充电电费（元）',
          minWidth: 180,
        },
        {
          field: 'internalElectricityFee',
          title: '内部车辆充电电费（元）',
          minWidth: 220,
        },
        {
          field: 'externalElectricityFee',
          title: '外部车辆充电电费（元）',
          minWidth: 220,
        },
        { field: 'totalServiceFee', title: '充电服务费（元）', minWidth: 170 },
        {
          field: 'internalServiceFee',
          title: '内部车辆充电服务费（元）',
          minWidth: 240,
        },
        {
          field: 'externalServiceFee',
          title: '外部车辆充电服务费（元）',
          minWidth: 240,
        },
        { field: 'totalRevenue', title: '充电总收入（元）', minWidth: 170 },
        { field: 'supplyEnergy', title: '供电电量（KWH）', minWidth: 180 },
        {
          field: 'supplyElectricityFee',
          title: '供电电费（元）',
          minWidth: 160,
        },
        { field: 'lossEnergy', title: '电损电量（KWH）', minWidth: 180 },
        { field: 'lossMeteringType', title: '电损计量类型', minWidth: 160 },
        { field: 'lossElectricityFee', title: '电损电费（元）', minWidth: 160 },
        { field: 'operationCost', title: '运维成本（元）', minWidth: 160 },
        {
          field: 'platformServiceFee',
          title: '平台服务费（元）',
          minWidth: 180,
        },
        { field: 'rent', title: '场地租金（元）', minWidth: 160 },
        {
          field: 'lossElectricityFeeByParty',
          title: '分成方承担损耗电费（元）',
          minWidth: 240,
        },
        { field: 'pendingDivisionAmount', title: '待分成费用', minWidth: 160 },
        { field: 'divisionClause', title: '分成条款', minWidth: 160 },
        {
          field: 'settlementElectricityFee',
          title: '结算电费（元）',
          minWidth: 170,
        },
        {
          field: 'settlementServiceFee',
          title: '结算服务费（元）',
          minWidth: 190,
        },
        { field: 'settlementRent', title: '结算租金（元）', minWidth: 170 },
        {
          field: 'settlementTotalAmount',
          title: '结算金额（元）',
          minWidth: 170,
        },
        { field: 'billGenerationMethod', title: '账单生成方式', minWidth: 170 },
        { field: 'settlementStatus', title: '结算状态', minWidth: 160 },
        {
          slots: { default: 'operate' },
          title: '操作',
          width: 150,
          align: 'center',
          fixed: 'right',
        },
      ],
      // 下拉选项数据
      chargingStationOptions: [
        { label: '充电站A', value: 'station_a' },
        { label: '充电站B', value: 'station_b' },
      ],
      divisionPartyOptions: [
        { label: '运营商A', value: 'party_a' },
        { label: '运营商B', value: 'party_b' },
      ],
      divisionTypeOptions: [
        { label: '固定比例', value: 'fixed_ratio' },
        { label: '固定金额', value: 'fixed_amount' },
      ],
      billGenerationOptions: [
        { label: '自动', value: 'auto' },
        { label: '手动', value: 'manual' },
      ],
      cityOptions: [
        { label: '北京市', value: 'beijing' },
        { label: '上海市', value: 'shanghai' },
      ],
      divisionNoOptions: [
        { label: 'DN20240901', value: 'dn_20240901' },
        { label: 'DN20240902', value: 'dn_20240902' },
      ],
      params: {
        chargingStation: '',
        divisionParty: '',
        divisionType: '',
        billingCycle: [],
        billGeneration: '',
        city: '',
        divisionNo: '',
      },
      selectList: [],
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'chargingStation',
            title: '充电站',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.chargingStationOptions,
            },
          },
          {
            field: 'divisionParty',
            title: '分成方',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.divisionPartyOptions,
            },
          },
          {
            field: 'divisionType',
            title: '分成类型',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.divisionTypeOptions,
            },
          },
          {
            field: 'billingCycle',
            title: '分成周期',
            element: 'el-date-picker',
            props: {
              type: 'daterange',
              startPlaceholder: '开始时间',
              endPlaceholder: '结束时间',
            },
          },
          {
            field: 'billGeneration',
            title: '账单生成方式',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.billGenerationOptions,
            },
          },
          {
            field: 'city',
            title: '地市',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.cityOptions,
            },
          },
          {
            field: 'divisionNo',
            title: '分成编号',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.divisionNoOptions,
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  methods: {
    loadData(page = this.tablePage) {
      console.log('加载数据', page);
      this.loading = true;
      setTimeout(() => {
        this.tableData = [];
        this.tablePage.total = 0;
        this.loading = false;
      }, 500);
    },
    handleAdd() {
      this.$router.push({
        path: '/v2g-charging/sortingManage/stationDivision/operatorManagement/addView',
      });
    },
    handleOutput() {},
    // 勾选行
    handleCheckboxChange({ records }) {
      this.selectList = records;
    },
    handleDetail(row) {
      console.log('查看详情', row);
      this.$router.push({
        path: '/v2g-charging/sortingManage/stationDivision/operatorManagement/detailView',
      });
    },
    handleSettle(row) {
      console.log('执行结算', row);
      this.$router.push({
        path: '/v2g-charging/sortingManage/stationDivision/operatorManagement/settlementView',
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  padding: 20px;
}

.table-wrap {
  // background-color: #fff;
  // padding: 20px;
  // border-radius: 4px;

  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }

  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;

    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px;
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1);
      }
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }
}

::v-deep .bd3001-table-select-box {
  display: none !important;
}
</style>
