<template>
  <div class="container">
    <!-- 步骤导航 -->
    <div class="steps-container">
      <el-steps :active="activeStep" finish-status="success">
        <el-step title="开户申请"></el-step>
        <el-step title="开户审核"></el-step>
        <el-step title="发票上传"></el-step>
        <el-step title="完成"></el-step>
      </el-steps>
    </div>

    <!-- 表单卡片 -->
    <div class="form-card">
      <div class="card-title">申请开票</div>

      <el-form
        ref="applyForm"
        :model="formData"
        :rules="rules"
        label-width="120px"
        class="apply-form"
      >
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="交易单位" prop="tradingUnit">
              <el-select
                v-model="formData.tradingUnit"
                placeholder="请选择，可多选"
                multiple
                style="width: 100%"
              >
                <el-option label="选项1" value="1"></el-option>
                <el-option label="选项2" value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="开票电流类型">
              <el-input
                v-model="formData.currentType"
                placeholder="自动获取，不可输入"
                disabled
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="开票服务费金额">
              <el-input
                v-model="formData.serviceFee"
                placeholder="自动获取，不可输入"
                disabled
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="开票总金额">
              <el-input
                v-model="formData.totalAmount"
                placeholder="自动获取，不可输入"
                disabled
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="开票类型" prop="billingType">
              <el-select
                v-model="formData.billingType"
                placeholder="请选择"
                style="width: 100%"
              >
                <el-option label="增值税" value="1"></el-option>
                <el-option label="普通发票" value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="发票种类" prop="invoiceType">
              <el-select
                v-model="formData.invoiceType"
                placeholder="请选择"
                style="width: 100%"
              >
                <el-option label="增值税" value="1"></el-option>
                <el-option label="普通发票" value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="开票企业信息" prop="companyType">
              <el-select
                v-model="formData.companyType"
                placeholder="请选择"
                style="width: 100%"
              >
                <el-option label="一般纳税人" value="1"></el-option>
                <el-option label="小规模纳税人" value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="企业名称">
              <el-input
                v-model="formData.companyName"
                placeholder="系统自动带出"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="纳税人识别号">
              <el-input
                v-model="formData.taxNumber"
                placeholder="系统自动带出"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="开户银行">
              <el-input
                v-model="formData.bankName"
                placeholder="系统自动带出"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="银行账号">
              <el-input
                v-model="formData.bankAccount"
                placeholder="系统自动带出"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="注册地址及电话">
              <el-input
                v-model="formData.addressPhone"
                placeholder="系统自动带出"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="附注">
              <el-input
                v-model="formData.remark"
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="备注">
              <el-input
                v-model="formData.comment"
                placeholder="请输入"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <div class="bottom-wrap">
      <el-button @click="() => handleCancel()">取消</el-button>
      <el-button type="primary" @click="() => handleConfirm()">提交</el-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'OperatorApply',
  data() {
    return {
      activeStep: 0,
      formData: {
        tradingUnit: [],
        currentType: '自动获取，不可输入',
        serviceFee: '自动获取，不可输入',
        totalAmount: '自动获取，不可输入',
        billingType: '',
        invoiceType: '',
        companyType: '',
        companyName: '',
        taxNumber: '',
        bankName: '',
        bankAccount: '',
        addressPhone: '',
        remark: '',
        comment: '',
      },
      rules: {
        tradingUnit: [
          { required: true, message: '请选择交易单位', trigger: 'change' },
          {
            type: 'array',
            min: 1,
            message: '请至少选择一个交易单位',
            trigger: 'change',
          },
        ],
        billingType: [
          { required: true, message: '请选择开票类型', trigger: 'change' },
        ],
        invoiceType: [
          { required: true, message: '请选择发票种类', trigger: 'change' },
        ],
        companyType: [
          { required: true, message: '请选择开票企业类型', trigger: 'change' },
        ],
      },
    };
  },
  methods: {
    handleConfirm() {
      this.$refs.applyForm.validate((valid) => {
        if (valid) {
          // 表单验证通过，执行提交逻辑
          this.handleSubmit();
        } else {
          this.$message.error('请完善表单信息');
          return false;
        }
      });
    },
    handleSubmit() {
      // 显示加载状态
      const loading = this.$loading({
        lock: true,
        text: '提交中...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)',
      });

      // 模拟API调用
      setTimeout(() => {
        loading.close();
        this.$message.success('提交成功');
        this.activeStep = 1;

        // 实际项目中这里应该调用API
        // const params = {...this.formData};
        // submitInvoiceApply(params).then(res => {
        //   loading.close();
        //   if (res.code === 200) {
        //     this.$message.success('提交成功');
        //     this.activeStep = 1;
        //   } else {
        //     this.$message.error(res.msg || '提交失败');
        //   }
        // }).catch(err => {
        //   loading.close();
        //   this.$message.error('提交失败，请稍后重试');
        // });
      }, 1500);
    },
    handleCancel() {
      this.$refs.applyForm.resetFields();
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  padding: 20px;
  position: relative;
  padding-bottom: 100px;
}

.steps-container {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.form-card {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.card-title {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 20px;
  padding-left: 10px;
  border-left: 3px solid #409eff;
}

.apply-form {
  margin-top: 20px;
}

.form-actions {
  display: flex;
  justify-content: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

::v-deep .el-input.is-disabled .el-input__inner {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #909399;
}

::v-deep .el-form-item {
  margin-bottom: 22px;
}

::v-deep .el-form-item__error {
  padding-top: 2px;
}

::v-deep
  .el-form-item.is-required:not(.is-no-asterisk)
  .el-form-item__label:before {
  content: '*';
  color: #f56c6c;
  margin-right: 4px;
}

.bottom-wrap {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 86px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  background-color: #ffffff;
  padding-right: 32px;
  box-sizing: border-box;
  z-index: 5;
}
</style>
