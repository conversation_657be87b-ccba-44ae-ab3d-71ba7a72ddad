<template>
  <div class="container">
    <div class="info-card">
      <div class="card-head">
        <!-- <div class="before-icon"></div> -->
        <div class="card-head-text">清分排班报表（充电站）历史记录</div>
      </div>

      <div class="info-wrap">
        <el-row :gutter="20" style="width: 100%; margin: 0 0 10px 10px">
          <el-col :span="6">
            <span class="label">清分周期：</span>
            <span class="value">2024.9.1-2024.9.30</span>
          </el-col>
          <el-col :span="6">
            <span class="label">充电站名称：</span>
            <span class="value">充电站1</span>
          </el-col>
          <el-col :span="6">
            <span class="label">交易单位：</span>
            <span class="value">省电动</span>
          </el-col>
          <el-col :span="6">
            <span class="label">地市：</span>
            <span class="value">长沙</span>
          </el-col>
        </el-row>
      </div>

      <div class="table-wrap">
        <BuseCrud
          ref="crud"
          :tableColumn="tableColumn"
          :tableData="tableData"
          :modalConfig="{ addBtn: false, menu: false }"
        ></BuseCrud>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      tableColumn: [
        { type: 'seq', title: '序号', width: 60 },
        { field: 'modifyTime', title: '修订时间', minWidth: 150 },
        { field: 'modifyPerson', title: '修订人', minWidth: 100 },
        { field: 'transactionUnit', title: '交易单位', minWidth: 100 },
        {
          field: 'transactionUnitCode',
          title: '交易单位组织机构编码',
          minWidth: 200,
        },
        { field: 'stationAttribute', title: '修正前资产属性', minWidth: 150 },
        { field: 'stationProperty', title: '修正后资产属性', minWidth: 150 },
      ],
      tableData: [
        {
          modifyTime: '2024.9.1 12:12:12',
          modifyPerson: '张三',
          transactionUnit: '充电动',
          transactionUnitCode: '300030143008',
          stationAttribute: '主站',
          stationProperty: '直营站 自建',
        },
        {
          modifyTime: '2024.9.1 12:12:12',
          modifyPerson: '张三',
          transactionUnit: '充电动',
          transactionUnitCode: '300030143008',
          stationAttribute: '站点动力配送设施',
          stationProperty: '主站',
        },
      ],
    };
  },
};
</script>

<style lang="scss" scoped>
.container {
  padding: 0 0 100px 0;
}

.info-card {
  background-color: #fff;
  border-radius: 5px;
  margin: 16px;
}

.before-icon {
  width: 3px;
  height: 16px;
  background-color: #217aff;
  margin-right: 8px;
  border-radius: 2px;
}

.info-wrap {
  display: flex;
  flex-wrap: wrap;
  padding: 16px;

  .label {
    color: #606266;
    margin-right: 8px;
  }

  .value {
    color: #303133;
    font-weight: 500;
  }
}

.table-wrap {
  padding: 0 16px 16px;
}

::v-deep .bd3001-auto-filters-container {
  margin-bottom: 0px !important;
  box-shadow: none !important;
}

::v-deep .bd3001-content {
  padding-top: 0px !important;
}
</style>
