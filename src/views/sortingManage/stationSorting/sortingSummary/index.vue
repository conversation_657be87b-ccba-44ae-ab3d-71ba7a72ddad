<template>
  <div class="container">
    <div class="table-wrap">
      <div class="tabs">
        <div
          v-for="(item, index) in activeTab"
          :key="index"
          class="tab-item"
          :class="{ active: selectedItem === item.value }"
          @click="selectTab(item.value)"
        >
          {{ item.label }}
        </div>
      </div>
      <BuseCrud
        ref="crud"
        :loading="loading"
        :filterOptions="filterOptions"
        :tablePage="tablePage"
        :tableColumn="tableColumn"
        :tableData="tableData"
        :pagerProps="pagerProps"
        :modalConfig="modalConfig"
        @loadData="loadData"
        :tableOn="{
          'checkbox-change': handleCheckboxChange,
          'checkbox-all': handleCheckboxChange,
        }"
      >
        <template slot="defaultHeader">
          <div class="card-head">
            <div class="card-head-text">清分汇总报表</div>
            <div class="top-button-wrap">
              <el-button type="primary" @click="handleInvoicing">
                申请开票
              </el-button>
              <el-button type="primary" @click="handleOutput">
                导出报表
              </el-button>
            </div>
          </div>
          <div class="card-head-after"></div>
        </template>
        <template slot="operate" slot-scope="{ row }">
          <div class="menu-box">
            <el-button type="primary" plain @click="handleDetail(row)">
              清分明细
            </el-button>
            <el-button type="primary" plain @click="handleSettlement(row)">
              结算
            </el-button>
            <el-button type="primary" plain @click="handleHistory(row)">
              历史记录
            </el-button>
          </div>
        </template>
      </BuseCrud>
    </div>
    <el-dialog
      :title="upload.title"
      :visible.sync="upload.open"
      width="50%"
      append-to-body
      @close="handleCancel"
    >
      <el-form
        :model="upload.form"
        :rules="upload.rules"
        ref="uploadForm"
        label-position="top"
      >
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item
              label="对公转账时间"
              prop="transferTime"
              :label-width="formLabelWidth"
            >
              <el-date-picker
                type="datetime"
                v-model="upload.form.transferTime"
                value-format="yyyy-MM-dd HH:mm:ss"
                style="width: 100%"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="upload-title">
        <span style="color: red">*</span>
        上传文件
      </div>
      <el-upload
        ref="upload"
        :limit="1"
        accept=".xlsx, .xls"
        :headers="upload.headers"
        :action="upload.url"
        :disabled="upload.isUploading"
        :auto-upload="false"
        :data="upload.data"
        :on-exceed="handleExceed"
        :on-remove="handleRemove"
        :on-change="handleChange"
        :http-request="customUpload"
      >
        <div class="box el-upload__text">
          <img
            class="upload-icon"
            src="@/assets/icons/responseAndRegulation/upload.png"
          />
          选择要导入上传的文件
        </div>
      </el-upload>
      <el-form
        style="margin-top: 10px"
        :model="upload.form"
        :rules="upload.rules"
        ref="uploadForm2"
        label-position="top"
      >
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item
              label="备注"
              prop="remarks"
              :label-width="formLabelWidth"
            >
              <el-input
                v-model="upload.form.remarks"
                placeholder="请输入备注"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth';

export default {
  data() {
    return {
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [{}],
      tableColumn: [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
        },
        { type: 'seq', title: '序号', minWidth: 60 },
        { field: 'clearingCycle', title: '清分周期', minWidth: 150 },
        { field: 'assetType', title: '资产属性', minWidth: 140 },
        { field: 'totalEnergy', title: '充电总电量（KWH）', minWidth: 180 },
        {
          field: 'clearingElectricityFee',
          title: '清分充电电费（元）',
          minWidth: 170,
        },
        {
          field: 'clearingServiceFee',
          title: '清分充电服务费（元）',
          minWidth: 180,
        },
        {
          field: 'clearingTotalAmount',
          title: '清分充电总金额（元）',
          minWidth: 180,
        },
        { field: 'receiptAmount', title: '收款金额（元）', minWidth: 150 },
        { field: 'invoiceAmount', title: '开票金额（元）', minWidth: 150 },
      ],
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      // 检索参数
      params: {
        assetType: '', // 资产属性
        clearingCycleRange: [], // 清分周期时间范围
      },
      // 下拉选项数据
      assetTypeOptions: [
        { label: '省电动-自建', value: '1' },
        { label: '省电动-车网及建设', value: '2' },
        { label: '主业', value: '3' },
        { label: '三产', value: '4' },
        { label: '合资', value: '5' },
        { label: '其他', value: '6' },
      ],
      activeTab: [
        {
          label: '汇总',
          value: '1',
        },
        {
          label: '省电动-自建',
          value: '2',
        },
        {
          label: '主业',
          value: '3',
        },
        {
          label: '三产',
          value: '4',
        },
        {
          label: '省电动-车网及建设',
          value: '5',
        },
        {
          label: '合资',
          value: '6',
        },
        {
          label: '其他',
          value: '7',
        },
      ],
      selectList: [],
      selectedItem: '1',
      tradeUnitOptions: [
        { label: '单位A', value: '1' },
        { label: '单位B', value: '2' },
      ],
      invoiceStatusOptions: [
        { label: '待开票', value: '1' },
        { label: '已开票', value: '2' },
      ],
      receiptStatusOptions: [
        { label: '待收款', value: '1' },
        { label: '已收款', value: '2' },
      ],
      upload: {
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: '结算',
        // 是否禁用上传
        isUploading: false,
        // 设置上传的请求头部
        headers: { Authorization: 'Bearer ' + getToken() },
        // 上传的地址
        url: process.env.VUE_APP_BASE_API + '',
        form: {
          transferTime: '',
          remarks: '',
        },
        rules: {
          transferTime: [
            { required: true, message: '请选择对公转账时间', trigger: 'blur' },
          ],
        },
        fileList: [],
      },
      formLabelWidth: '120px',
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'assetType',
            title: '资产属性',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.assetTypeOptions,
            },
          },
          {
            field: 'clearingCycleRange',
            title: '清分周期',
            element: 'el-date-picker',
            props: {
              type: 'daterange',
              startPlaceholder: '开始时间',
              endPlaceholder: '结束时间',
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  mounted() {
    this.loadData();
  },
  methods: {
    loadData() {
      // TODO: 请求接口加载数据
      // console.log('加载数据', this.tablePage);
      // this.loading = true;
      // setTimeout(() => {
      //   this.tableData = [];
      //   this.tablePage.total = 0;
      //   this.loading = false;
      // }, 500);
    },
    // 申请开票
    handleInvoicing() {},
    // 导出报表
    handleOutput() {},
    // 表格勾选
    handleCheckboxChange({ records }) {
      console.log(records);
      this.selectList = records;
    },
    // 切换tab
    selectTab(value) {
      this.selectedItem = value;
      console.log(this.filterOptions);
      if (this.selectedItem == '1') {
        this.filterOptions.config = [
          {
            field: 'assetType',
            title: '资产属性',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.assetTypeOptions,
            },
          },
          {
            field: 'clearingCycleRange',
            title: '清分周期',
            element: 'el-date-picker',
            props: {
              type: 'daterange',
              startPlaceholder: '开始时间',
              endPlaceholder: '结束时间',
            },
          },
        ];
        this.tableColumn = [
          {
            type: 'checkbox',
            width: 50,
            fixed: 'left',
          },
          { type: 'seq', title: '序号', minWidth: 60 },
          { field: 'clearingCycle', title: '清分周期', minWidth: 150 },
          { field: 'assetType', title: '资产属性', minWidth: 140 },
          { field: 'totalEnergy', title: '充电总电量（KWH）', minWidth: 180 },
          {
            field: 'clearingElectricityFee',
            title: '清分充电电费（元）',
            minWidth: 170,
          },
          {
            field: 'clearingServiceFee',
            title: '清分充电服务费（元）',
            minWidth: 180,
          },
          {
            field: 'clearingTotalAmount',
            title: '清分充电总金额（元）',
            minWidth: 180,
          },
          { field: 'receiptAmount', title: '收款金额（元）', minWidth: 150 },
          { field: 'invoiceAmount', title: '开票金额（元）', minWidth: 150 },
        ];
      } else {
        this.filterOptions.config = [
          {
            field: 'tradeUnit',
            title: '交易单位',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.tradeUnitOptions,
            },
          },
          {
            field: 'invoiceStatus',
            title: '开票状态',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.invoiceStatusOptions,
            },
          },
          {
            field: 'receiptStatus',
            title: '收款状态',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.receiptStatusOptions,
            },
          },
        ];
        this.tableColumn = [
          {
            type: 'checkbox',
            width: 50,
            fixed: 'left',
          },
          { type: 'seq', title: '序号', minWidth: 60 },
          { field: 'clearingCycle', title: '清分周期', minWidth: 150 },
          { field: 'tradeUnit', title: '交易单位', minWidth: 140 },
          { field: 'totalEnergy', title: '充电总电量（KWH）', minWidth: 180 },
          {
            field: 'clearingElectricityFee',
            title: '清分充电电费（元）',
            minWidth: 170,
          },
          {
            field: 'clearingServiceFee',
            title: '清分充电服务费（元）',
            minWidth: 180,
          },
          {
            field: 'clearingTotalAmount',
            title: '清分充电总金额（元）',
            minWidth: 180,
          },
          {
            field: 'receiptStatus',
            title: '收款状态',
            minWidth: 130,
            slots: {
              default: ({ row }) => {
                return row.receiptStatus === 'received' ? '已收款' : '未收款';
              },
            },
          },
          {
            field: 'invoiceStatus',
            title: '开票状态',
            minWidth: 130,
            slots: {
              default: ({ row }) => {
                return row.invoiceStatus === 'invoiced' ? '已开票' : '未开票';
              },
            },
          },
          {
            slots: { default: 'operate' },
            title: '操作',
            width: 300,
            align: 'center',
            fixed: 'right',
          },
        ];
      }
    },
    // 清分明细
    handleDetail(row) {
      this.$router.push({
        path: '/v2g-charging-web/sortingManage/stationSorting/sortingSummary/details',
        // query: {
        //   status,
        // },
      });
    },
    // 结算
    handleSettlement(row) {
      this.upload.open = true;
    },
    // 用户选择的文件数量超过 limit 限制
    handleExceed(files, fileList) {
      console.log(files, fileList);
      // 清空已选文件列表
      this.$refs.upload.clearFiles();
      // 手动添加新选择的文件（首个文件）
      this.$refs.upload.handleStart(files[0]);
    },
    // 选择文件
    handleChange(file, fileList) {
      console.log('文件变更', file, fileList);
      this.upload.form.fileList = fileList;
    },
    // 移除文件
    handleRemove(file, fileList) {
      console.log('移除文件', file, fileList);
      this.upload.form.fileList = fileList;
    },
    async customUpload({ action, file, data }) {
      // this.upload.isUploading = true;
      // this.upload.open = false;
      // const formData = new FormData();
      // formData.append('file', file);
      // try {
      //   const [err, res] = await uploadClearingResult(formData);
      //   if (err) return;
      //   if (res.success) {
      //     // console.log('导入res', res);
      //     this.$message.success('导入成功');
      //     this.handleCancel();
      //     this.loadData();
      //   } else {
      //     this.$message.error(res.subMsg || '导入失败');
      //   }
      // } finally {
      //   this.upload.isUploading = false;
      // }
    },
    // 弹窗关闭
    handleCancel() {
      this.upload.open = false;
    },
    // 弹窗确定
    submitFileForm() {
      this.$refs.uploadForm.validate(async (valid) => {
        if (valid) {
          if (this.upload.fileList.length == 0) {
            this.$message.error(`请选择上传文件`);
            return;
          }
          this.$refs.upload.submit();
        }
      });
    },
    // 历史记录
    handleHistory(row) {
      this.$router.push({
        path: '/v2g-charging-web/sortingManage/stationSorting/sortingSummary/history',
        // query: {
        //   status,
        // },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  padding: 20px;
}

.table-wrap {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;

  ::v-deep .bd3001-table-select-box {
    display: none;
  }

  ::v-deep .bd3001-header {
    display: block;
  }

  ::v-deep .bd3001-button {
    display: block !important;
  }

  .tabs {
    display: flex;
    gap: 5px;
    justify-content: space-around;
    .tab-item {
      cursor: pointer;
      font-size: 18px;
      color: #606266; /* 默认颜色 */
      transition: color 0.3s; /* 平滑过渡 */
      padding: 0 16px;
      margin: 0 6px;
    }

    .tab-item.active {
      color: #409eff; /* 选中时的颜色 */
      border-bottom: 2px solid #409eff; /* 选中时的下划线 */
      padding-bottom: 4px; /* 调整下划线的位置 */
    }
  }

  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }
}

.upload-title {
  font-size: 14px;
  color: #606266;
  font-weight: 700;
  line-height: 28px;
  margin-bottom: 10px;
}
</style>
