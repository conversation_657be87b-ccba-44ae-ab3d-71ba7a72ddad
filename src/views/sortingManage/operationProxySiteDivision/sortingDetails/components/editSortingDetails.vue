<template>
  <el-dialog title="修改" :visible.sync="dialogVisible" width="1598px">
    <div class="edit-info-wrap">
      <div class="edit-info-item">
        <div class="info-wrap">
          <div class="info-item-wrap">
            <div class="info-title">订单编号：</div>
            <div class="info-detail">
              <a href="">{{ detailObj.orderNo || '' }}</a>
            </div>
          </div>

          <div class="info-item-wrap">
            <div class="info-title">充电站名称：</div>
            <div class="info-detail">
              {{ detailObj.stationName || '' }}
            </div>
          </div>

          <div class="info-item-wrap">
            <div class="info-title">地市：</div>
            <div class="info-detail">{{ detailObj.cityName || '' }}</div>
          </div>

          <div class="info-item-wrap">
            <div class="info-title">结算方：</div>
            <div class="info-detail">{{ detailObj.fromObj || '' }}</div>
          </div>

          <div class="info-item-wrap">
            <div class="info-title">分成方：</div>
            <div class="info-detail">{{ detailObj.toObj || '' }}</div>
          </div>

          <div class="info-item-wrap">
            <div class="info-title">分成类型：</div>
            <div class="info-detail">
              {{ clearTypeFilter(detailObj.clearType || '') }}
            </div>
          </div>

          <div class="info-item-wrap">
            <div class="info-title">订单下单时间：</div>
            <div class="info-detail">{{ detailObj.orderCreateTime || '' }}</div>
          </div>

          <div class="info-item-wrap">
            <div class="info-title">充电开始时间：</div>
            <div class="info-detail">{{ detailObj.chargeBegTime || '' }}</div>
          </div>

          <div class="info-item-wrap">
            <div class="info-title">充电结束时间：</div>
            <div class="info-detail">{{ detailObj.chargeEndTime || '' }}</div>
          </div>

          <div class="info-item-wrap">
            <div class="info-title">充电电量：</div>
            <div class="info-detail">
              {{ detailObj.chargePower || '0' }} kwh
            </div>
          </div>

          <div class="info-item-wrap">
            <div class="info-title">分成条款：</div>
            <div class="info-detail">{{ detailObj.ruleClause || '' }}</div>
          </div>
        </div>
      </div>
      <el-form :model="baseInfo.form" ref="baseInfoForm" label-position="top">
        <el-descriptions title="" direction="vertical" :column="6" border>
          <el-descriptions-item label="充电电费(元)">
            <el-form-item
              label=""
              prop="chargeFee"
              :label-width="formLabelWidth"
            >
              <el-input-number
                size="medium"
                v-model="baseInfo.form.chargeFee"
                placeholder=""
              ></el-input-number>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="内部车辆充电电费(元)">
            <el-form-item
              label=""
              prop="inCarChargeFee"
              :label-width="formLabelWidth"
            >
              <el-input-number
                size="medium"
                v-model="baseInfo.form.inCarChargeFee"
                placeholder=""
              ></el-input-number>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="外部车辆充电电费(元)">
            <el-form-item
              label=""
              prop="outCarChargeFee"
              :label-width="formLabelWidth"
            >
              <el-input-number
                size="medium"
                v-model="baseInfo.form.outCarChargeFee"
                placeholder=""
              ></el-input-number>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="充电服务费(元)">
            <el-form-item
              label=""
              prop="serviceFee"
              :label-width="formLabelWidth"
            >
              <el-input-number
                size="medium"
                v-model="baseInfo.form.serviceFee"
                placeholder=""
              ></el-input-number>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="内部车辆充电服务费(元)">
            <el-form-item
              label=""
              prop="inServiceFee"
              :label-width="formLabelWidth"
            >
              <el-input-number
                size="medium"
                v-model="baseInfo.form.inServiceFee"
                placeholder=""
              ></el-input-number>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="外部车辆充电服务费(元)">
            <el-form-item
              label=""
              prop="outServiceFee"
              :label-width="formLabelWidth"
            >
              <el-input-number
                size="medium"
                v-model="baseInfo.form.outServiceFee"
                placeholder=""
              ></el-input-number>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item label="充电总金额(元)">
            {{ detailObj.chargeIncome || '0' }}
          </el-descriptions-item>
          <el-descriptions-item label="待分成费用">
            {{ detailObj.clearFee || '0' }}
          </el-descriptions-item>
          <el-descriptions-item label="结算电费(元)">
            {{ detailObj.settleElecFee || '0' }}
          </el-descriptions-item>
          <el-descriptions-item label="结算服务费(元)">
            {{ detailObj.settleServiceFee || '0' }}
          </el-descriptions-item>
          <el-descriptions-item label="结算金额(元)">
            {{ detailObj.totalSettleFee || '0' }}
          </el-descriptions-item>
          <el-descriptions-item label=""></el-descriptions-item>
        </el-descriptions>
      </el-form>
    </div>
    <div class="bottom-wrap">
      <div>
        <el-button @click="handleCancel">取消</el-button>
        <el-button @click="handleSave" :loading="submitLoading" type="primary">
          确定
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>
<script>
import { clearDetailUpdate } from '@/api/sortingManage/operationProxySiteDivision';
export default {
  props: {
    detailObj: {
      type: Object,
      default: () => {},
    },
  },
  components: {},
  dicts: [],
  data() {
    return {
      formLabelWidth: '0',
      dialogVisible: false,
      clearTypeList: [
        { label: '服务费', value: 'service' },
        { label: '电费+服务费', value: 'elecAndService' },
        { label: '场地租金', value: 'space' },
        { label: '场地租金+服务费', value: 'spaceAndService' },
      ],
      submitLoading: false,
      baseInfo: {
        form: {
          chargeFee: '',
          inCarChargeFee: '',
          outCarChargeFee: '',
          serviceFee: '',
          inServiceFee: '',
          outServiceFee: '',
        },
        rules: {},
      },
    };
  },
  watch: {
    dialogVisible(value) {
      if (value) {
        this.baseInfo.form = {
          chargeFee: this.detailObj.chargeFee || '0',
          inCarChargeFee: this.detailObj.inCarChargeFee || '0',
          outCarChargeFee: this.detailObj.outCarChargeFee || '0',
          serviceFee: this.detailObj.serviceFee || '0',
          inServiceFee: this.detailObj.inServiceFee || '0',
          outServiceFee: this.detailObj.outServiceFee || '0',
        };
      }
    },
  },
  computed: {},
  mounted() {},
  methods: {
    clearTypeFilter(cellValue) {
      this.selectDictLabel(this.clearTypeList, cellValue);
    },
    handleCancel() {
      this.dialogVisible = false;
    },
    async handleSave() {
      this.submitLoading = true;

      const [err, res] = await clearDetailUpdate({
        clearDetailId: this.detailObj?.clearDetailId || '',
        ...this.baseInfo.form,
      });

      this.submitLoading = false;

      if (err) return;
      this.$message({
        type: 'success',
        message: '更新成功!',
      });
      this.$emit('sortDetailSave', this.baseInfo.form);
      this.dialogVisible = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.edit-info-wrap {
  min-height: 520px;
  .edit-info-item {
    .edit-info-item-title-after {
      display: flex;
      height: 88px;
      width: 100%;
      background: linear-gradient(180deg, #d9ecff 0%, #ffffff 100%);
      box-sizing: border-box;
      align-items: center;
      font-weight: 500;
      font-size: 24px;
      .edit-detail-title-icon {
        width: 32px;
        height: 32px;
        background-image: url('~@/assets/station/compare-detail-title-icon.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        margin-right: 12px;
      }
    }
    .title-wrap {
      height: 18px;
      padding: 0 0 0 24px;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      font-weight: 500;
      font-size: 18px;
      margin-bottom: 24px;
      color: #12151a;
      .before-icon {
        width: 3px;
        height: 16px;
        background-image: url('~@/assets/station/consno-before.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        margin-right: 8px;
      }
    }
    .info-wrap {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      margin-bottom: 24px;

      .info-item-wrap {
        display: flex;
        width: 33.33%;
        margin-bottom: 24px;
        .info-title {
          font-weight: 400;
          font-size: 16px;
          line-height: 16px;
          color: #505363;
        }
        .info-detail {
          font-weight: 400;
          font-size: 16px;
          line-height: 16px;
          color: #292b33;
          .price-number {
            color: #ff8d24;
            font-weight: 500;
          }
          a {
            color: #217aff;
            text-decoration: underline;
          }
        }
      }
    }
  }
}
.bottom-wrap {
  // position: fixed;
  // bottom: 0;
  // left: 0;
  // width: 100%;
  // height: 86px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  background-color: #ffffff;
  padding-right: 32px;
  box-sizing: border-box;
}
::v-deep .is-bordered-label {
  background: #ebf3ff;
  height: 56px;
  color: #21252e;
  font-size: 16px;
  text-align: center !important;
  font-weight: 500;
}
::v-deep .el-descriptions-item__content {
  height: 56px;
  color: #292b33;
  font-size: 16px;
  text-align: center !important;
  font-weight: 400;
}

::v-deep .bd3001-content {
  padding: 0 !important;
}
::v-deep .el-form-item {
  margin-bottom: 0 !important;
}
::v-deep .el-input__inner {
  text-align: center;
}
</style>
