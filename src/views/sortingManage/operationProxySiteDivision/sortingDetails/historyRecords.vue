<template>
  <div class="container container-float" style="padding: 0 0 100px 0">
    <div class="device-head">
      <img
        src="@/assets/order/abnormal-detail-icon.png"
        class="device-head-icon"
      />

      <div class="device-info-wrap">
        <div class="device-title-wrap">
          <div class="device-title">
            订单编号：{{ detailObj.orderNo || '' }}
          </div>
          <div class="device-status">
            {{ detailObj.settleStatus === '0' ? '未清分' : '已清分' }}
          </div>
        </div>
        <div class="device-info-wrap">
          <el-row>
            <el-col :span="8">
              <span class="label">充电站名称：</span>
              <span class="value">{{ detailObj.stationName || '' }}</span>
            </el-col>
            <el-col :span="8">
              <span class="label">结算方：</span>
              <span class="value">{{ detailObj.fromObjName || '' }}</span>
            </el-col>
            <el-col :span="8">
              <span class="label">分成方：</span>
              <span class="value">{{ detailObj.toObjName || '' }}</span>
            </el-col>
          </el-row>
        </div>
      </div>

      <el-button type="primary" @click="goBack">返回</el-button>
    </div>

    <div class="info-card">
      <div class="form-wrap">
        <el-row :gutter="20">
          <el-col :span="8" style="margin-bottom: 24px">
            <div style="display: flex">
              <div class="info-title">分成类型：</div>
              <div class="info-detail">
                {{ clearTypeFilter(detailObj.clearType || '') }}
              </div>
            </div>
          </el-col>
          <el-col :span="8" style="margin-bottom: 24px">
            <div style="display: flex">
              <div class="info-title">订单下单时间：</div>
              <div class="info-detail">
                {{ detailObj.orderCreateTime || '' }}
              </div>
            </div>
          </el-col>
          <el-col :span="8" style="margin-bottom: 24px">
            <div style="display: flex">
              <div class="info-title">充电开始时间：</div>
              <div class="info-detail">{{ detailObj.chargeBegTime || '' }}</div>
            </div>
          </el-col>
          <el-col :span="8" style="margin-bottom: 24px">
            <div style="display: flex">
              <div class="info-title">充电结束时间：</div>
              <div class="info-detail">{{ detailObj.chargeEndTime || '' }}</div>
            </div>
          </el-col>
          <el-col :span="8" style="margin-bottom: 24px">
            <div style="display: flex">
              <div class="info-title">分成条款：</div>
              <div class="info-detail">{{ detailObj.ruleClause || '' }}</div>
            </div>
          </el-col>
        </el-row>
      </div>
      <BuseCrud
        style="margin-bottom: 16px; padding: 0 24px"
        ref="periodTableInfo"
        :tablePage="tablePage"
        :tableColumn="tableColumn"
        :tableData="tableData"
        :modalConfig="{ addBtn: false, menu: false }"
        :pagerProps="pagerProps"
        @loadData="queryList"
      ></BuseCrud>
    </div>
  </div>
</template>

<script>
import {
  clearDetailChangePage,
  clearDetailInfo,
} from '@/api/sortingManage/operationProxySiteDivision';

export default {
  components: {},
  dicts: [],
  data() {
    return {
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      clearDetailId: '',
      tableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
        },
        {
          field: 'chargePower',
          title: '充电电量（KWH）',
          minWidth: 120,
        },
        {
          field: 'chargeFee',
          title: '充电电费（元）',
          minWidth: 150,
        },
        {
          field: 'inCarChargeFee',
          title: '内部车辆充电电费（元）',
          align: 'center',
          minWidth: 150,
        },
        {
          field: 'outCarChargeFee',
          title: '外部车辆充电电费（元）',
          align: 'center',
          minWidth: 150,
        },
        {
          field: 'serviceFee',
          title: '充电服务费（元）',
          minWidth: 150,
        },
        {
          field: 'inServiceFee',
          title: '内部车辆充电服务费（元）',
          align: 'center',
          minWidth: 150,
        },
        {
          field: 'outServiceFee',
          title: '外部车辆充电服务费（元）',
          align: 'center',
          minWidth: 150,
        },
        {
          field: 'chargeIncome',
          title: '充电总金额（元）',
          minWidth: 150,
        },

        {
          field: 'clearFee',
          title: '待分成费用',
          minWidth: 150,
        },
        {
          field: 'settleElecFee',
          title: '结算电费（元）',
          minWidth: 150,
        },
        {
          field: 'settleServiceFee',
          title: '结算服务费（元）',
          minWidth: 150,
        },
        {
          field: 'totalSettleFee',
          title: '结算金额（元）',
          minWidth: 150,
        },
        {
          field: 'updateTime',
          title: '修订时间',
          minWidth: 180,
        },
        {
          field: 'updateBy',
          title: '操作人',
          minWidth: 150,
        },
      ],
      tableData: [],
      detailObj: {},
      clearTypeList: [
        { label: '服务费', value: 'service' },
        { label: '电费+服务费', value: 'elecAndService' },
        { label: '场地租金', value: 'space' },
        { label: '场地租金+服务费', value: 'spaceAndService' },
      ],
    };
  },

  computed: {},
  mounted() {
    this.clearDetailId = this.$route.query.clearDetailId;
    this.queryDeatil();
    this.queryList();
  },
  methods: {
    clearTypeFilter(cellValue) {
      return this.selectDictLabel(this.clearTypeList, cellValue);
    },
    goBack() {
      this.$router.back();
    },

    // 获取详情
    async queryDeatil() {
      const [err, res] = await clearDetailInfo({
        id: this.clearDetailId || '',
      });

      if (err) return;
      this.detailObj = res?.data || {};
    },
    // 获取详情列表
    async queryList() {
      const [err, res] = await clearDetailChangePage({
        clearDetailId: this.clearDetailId || '',
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      });

      if (err) return;
      this.tableData = res?.data || [];
      this.tablePage.total = res?.total || 0;
    },
  },
};
</script>

<style lang="scss" scoped>
.container-full {
  background-color: rgba(244, 246, 249, 1);
  padding-bottom: 16px;
  min-height: calc(100% - 80px);
  overflow-y: auto;
}

.device-head {
  background-color: #fff;

  display: flex;
  height: 112px;
  display: flex;
  align-items: center;
  padding: 0 24px;
  box-sizing: border-box;
  .device-head-icon {
    width: 60px;
    height: 60px;
    margin-right: 24px;
  }
  .device-info-wrap {
    flex: 1;
    .device-title-wrap {
      height: 32px;
      display: flex;
      align-items: center;
      .device-title {
        font-weight: 500;
        font-size: 24px;
        color: #12151a;
      }
      .device-status {
        // width: 50px;
        padding: 0 10px;
        height: 24px;
        border-radius: 10px 0 10px 0;
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;
        text-align: center;
        color: #fff;
        background: linear-gradient(321.01deg, #ffb624 8.79%, #ff8d24 100.27%);
        margin-left: 12px;
      }
    }
    .device-info-wrap {
      height: 16px;
      margin-top: 16px;
      font-size: 16px;
      font-weight: 400;
      color: #292b33;
    }
  }
  .device-status-wrap {
    display: flex;
    align-items: center;
    .device-status-item-wrap {
      width: 150px;
      .device-status-item-title {
        font-weight: 400;
        font-size: 14px;
        line-height: 14px;
        color: #505363;
        margin: 0 auto 12px auto;
        text-align: center;
      }
      .device-status {
        width: 86px;
        height: 34px;
        border-radius: 4px;
        display: flex;
        margin: 0 auto;
        align-items: center;
        justify-content: center;
        background-color: #ebf3ff;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 18px;
        color: #217aff;
      }
      .device-status-success {
        width: 86px;
        height: 34px;
        border-radius: 4px;
        display: flex;
        margin: 0 auto;
        align-items: center;
        justify-content: center;
        background-color: #fff7e6;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 18px;
        color: #ff8d24;
      }
    }
    .device-status-split {
      width: 1px;
      height: 36px;
      background-color: #e9ebf0;
    }
  }
}

.info-card {
  margin: 16px;
  border-radius: 5px;
  border: 1px solid #fff;
  overflow: hidden;
  background-color: #fff;
  // min-height: 300px;
  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    background: linear-gradient(180deg, #e9f2ff 0%, #ffffff 100%);
    .before-icon {
      width: 3px;
      height: 16px;
      background-image: url('~@/assets/station/consno-before.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin-right: 8px;
    }
    .card-head-text {
      flex: 1;
      font-weight: 500;
      font-size: 16px;
      color: #12151a;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
    .button-wrap {
      display: flex;
      .invite-btn {
        background-color: #1ab2ff;
        border-color: #1ab2ff;
      }
      ::v-deep .el-button--small {
        font-size: 14px;
      }
      .distribution {
        margin-left: 24px;
        margin-right: 24px;
        display: flex;
        align-items: center;
      }
    }
  }

  .people-table-wrap {
    ::v-deep .bd3001-table-select-box {
      display: none;
    }
    ::v-deep .bd3001-button {
      width: 100%;
    }
  }
  .form-wrap {
    padding: 16px;
    .info-title {
      font-weight: 400;
      font-size: 16px;
      line-height: 16px;
      color: #505363;
    }
    .info-detail {
      font-weight: 400;
      font-size: 16px;
      line-height: 16px;
      color: #292b33;
    }
    .info-amount {
      height: 28px;
      background-color: #fff7e6;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 8px;
      font-weight: 400;
      font-size: 20px;
      color: #fe8921;
      margin-top: -6px;
      margin-right: 4px;
    }
    .info-img {
      width: 140px;
      height: 140px;
    }
  }
}
</style>
