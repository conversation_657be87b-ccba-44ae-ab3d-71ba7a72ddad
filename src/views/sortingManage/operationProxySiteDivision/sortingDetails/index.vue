<template>
  <div class="container container-float" style="padding: 0 0 100px 0">
    <!-- 异常规则管理标签栏内容 -->

    <div class="table-wrap">
      <BuseCrud
        ref="crud"
        :loading="manageLoading"
        :filterOptions="manageFilterOptions"
        :tablePage="manageTablePage"
        :tableColumn="manageTableColumn"
        :tableData="manageTableData"
        :pagerProps="pagerProps"
        :modalConfig="modalConfig"
        @loadData="loadManageData"
      >
        <template slot="defaultHeader">
          <div>
            <div class="card-head">
              <div class="card-head-text">清分明细表</div>

              <div class="top-button-wrap">
                <!-- <el-button type="primary">新增</el-button> -->
                <el-button type="primary" @click="() => handleExport()">
                  导出报表
                </el-button>
              </div>
            </div>
          </div>
        </template>
        <template slot="orderNo" slot-scope="{ row }">
          <el-button type="text">{{ row.orderNo || '' }}</el-button>
        </template>
        <template slot="operate" slot-scope="{ row }">
          <div class="menu-box">
            <el-button @click="handEdit(row)">修改</el-button>
            <el-button @click="goHistory(row)">修改记录</el-button>
          </div>
        </template>
      </BuseCrud>
    </div>
    <editSortingDetails
      ref="editSortingDetails"
      :detailObj="selectOrderRule"
      @sortDetailSave="loadManageData"
    />
  </div>
</template>

<script>
import editSortingDetails from './components/editSortingDetails.vue';
import { clearDetailPage } from '@/api/sortingManage/operationProxySiteDivision';
import { getAreaList } from '@/api/electricPricePeriod/index';
export default {
  components: {
    editSortingDetails,
  },
  dicts: [
    'ls_order_except_level', // 异常级别
    'ls_order_except_type', // 异常类型
    'ls_order_except_measures', // 处理措施
    'ls_order_except_status', // 异常状态
  ],
  data() {
    return {
      activeName: 'manage',
      manageLoading: false,
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      params: {
        clearNo: '',
        orderNo: '',
        toObj: '',
        clearType: '',
        clearDate: [],
        stationId: '',
        settleStatus: '',
        city: '',
      },
      manageTablePage: { total: 0, currentPage: 1, pageSize: 10 },
      manageTableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
          fixed: 'left',
        },
        {
          field: 'clearNo',
          title: '分成编号',
          minWidth: 190,
        },
        {
          field: 'orderNo',
          title: '订单编号',
          slots: { default: 'orderNo' },
          minWidth: 190,
        },

        {
          field: 'stationName',
          title: '充电站名称',
          minWidth: 190,
        },
        {
          field: 'cityName',
          title: '地市',
          minWidth: 100,
        },
        {
          field: 'fromObj',
          title: '结算方',
          minWidth: 100,
        },
        {
          field: 'toObj',
          title: '分成方',
          minWidth: 150,
        },
        {
          field: 'clearType',
          title: '分成类型',
          minWidth: 120,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(this.clearTypeList, cellValue);
          },
        },
        {
          field: 'orderCreateTime',
          title: '订单下单时间',
          minWidth: 180,
        },
        {
          field: 'chargeBegTime',
          title: '充电开始时间',
          minWidth: 180,
        },
        {
          field: 'chargeEndTime',
          title: '充电结束时间',
          align: 'center',
          minWidth: 180,
        },
        {
          field: 'chargePower',
          title: '充电电量（KWH）',
          minWidth: 120,
        },
        {
          field: 'chargeFee',
          title: '充电电费（元）',
          minWidth: 150,
        },
        {
          field: 'inCarChargeFee',
          title: '内部车辆充电电费（元）',
          align: 'center',
          minWidth: 150,
        },
        {
          field: 'outCarChargeFee',
          title: '外部车辆充电电费（元）',
          align: 'center',
          minWidth: 150,
        },
        {
          field: 'serviceFee',
          title: '充电服务费（元）',
          minWidth: 150,
        },
        {
          field: 'inServiceFee',
          title: '内部车辆充电服务费（元）',
          align: 'center',
          minWidth: 150,
        },
        {
          field: 'outServiceFee',
          title: '外部车辆充电服务费（元）',
          align: 'center',
          minWidth: 150,
        },
        {
          field: 'chargeIncome',
          title: '充电总金额（元）',
          minWidth: 150,
        },
        {
          field: 'clearFee',
          title: '待分成费用',
          minWidth: 150,
        },
        {
          field: 'ruleClause',
          title: '分成条款',
          minWidth: 150,
        },
        {
          field: 'settleElecFee',
          title: '结算电费（元）',
          minWidth: 150,
        },
        {
          field: 'settleServiceFee',
          title: '结算服务费（元）',
          minWidth: 150,
        },

        {
          field: 'totalSettleFee',
          title: '结算金额（元）',
          minWidth: 150,
        },
        {
          field: 'settleStatus',
          title: '清分状态',
          minWidth: 120,
          fixed: 'right',
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(this.settleStatusList, cellValue);
          },
        },

        {
          title: '操作',
          slots: { default: 'operate' },
          width: 200,
          align: 'center',
          fixed: 'right',
        },
      ],
      manageTableData: [],
      selectOrderRule: {},
      clearTypeList: [
        { label: '服务费', value: 'service' },
        { label: '电费+服务费', value: 'elecAndService' },
        { label: '场地租金', value: 'space' },
        { label: '场地租金+服务费', value: 'spaceAndService' },
      ],
      settleStatusList: [
        { label: '未清分', value: '0' },
        { label: '已清分', value: '1' },
      ],
      orderRuleType: 'create',
      id: '',
      areaList: [],
    };
  },

  computed: {
    manageFilterOptions() {
      return {
        config: [
          {
            field: 'clearNo',
            title: '分成编号：',
            element: 'el-input',
          },
          {
            field: 'orderNo',
            title: '订单编号：',
            element: 'el-input',
          },
          {
            field: 'toObj',
            title: '分成方',
            element: 'el-select',
            props: {
              placeholder: '请选择分成方',
              options: [],
            },
          },
          {
            field: 'clearType',
            title: '分成类型',
            element: 'el-select',
            props: {
              placeholder: '请选择分成类型',
              options: this.clearTypeList,
            },
          },
          {
            field: 'clearDate',
            title: '订单下单时间：',
            element: 'el-date-picker',
            props: {
              type: 'daterange',
              options: [],
              valueFormat: 'yyyy-MM-dd',
            },
          },
          {
            field: 'stationId',
            title: '充电站编号',
            element: 'el-input',
          },

          {
            field: 'city',
            title: '地市',
            element: 'el-select',
            props: {
              placeholder: '请选择地市',
              options: this.areaList,
            },
          },
          {
            field: 'settleStatus',
            title: '清分状态：',
            element: 'el-select',
            props: {
              placeholder: '请选择账单生成方式',
              options: this.settleStatusList,
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  mounted() {
    this.id = this.$route.query?.id || '';
    this.loadManageData();
    this.queryAreaList();
  },
  methods: {
    async queryAreaList() {
      const [err, res] = await getAreaList({
        areaLevel: '03',
        huNanOnly: true,
      });
      if (err) return;
      const { data } = res;
      const list = [];
      data.forEach((item) => {
        list.push({
          label: item.areaName,
          value: item.areaCode,
        });
      });
      this.areaList = list;
    },
    goHistory(row) {
      this.$router.push({
        path: `/v2g-charging-web/sortingManage/operationProxySiteDivision/historyRecords?clearDetailId=${row?.clearDetailId}`,
      });
    },

    handleExport() {
      const params = {
        ...this.manageFilterOptions.params,
      };
      if (params.clearDate && params.clearDate.length > 0) {
        params.begDate = params.clearDate[0];
        params.endDate = params.clearDate[1];
      }

      this.download(
        '/vehicle-charging-admin/clearDetail/export',
        {
          ...params,
        },
        `清分明细列表.xlsx`
      );
    },
    // 获取管理列表数据
    async loadManageData() {
      const params = {
        ...this.manageFilterOptions.params,
      };
      if (params.clearDate && params.clearDate.length > 0) {
        params.begDate = params.clearDate[0];
        params.endDate = params.clearDate[1];
      }

      this.manageLoading = true;
      const [err, res] = await clearDetailPage({
        id: this.id,
        ...params,
        pageNum: this.manageTablePage.currentPage,
        pageSize: this.manageTablePage.pageSize,
      });

      this.manageLoading = false;

      if (err) return;
      const { data, total } = res;

      this.manageTableData = data;
      this.manageTablePage.total = total;
    },

    // 编辑异常规则
    handEdit(row) {
      this.selectOrderRule = row;
      this.$refs.editSortingDetails.dialogVisible = true;
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-tabs {
  margin-top: 16px;
  background-color: #f5f6f9;
  .el-tabs__header {
    padding-left: 0;
    display: flex;
    justify-content: center;
    text-align: center;
    margin-bottom: 1px;
    .el-tabs__item {
      padding: 0;
      width: 164px;
      font-size: 18px;
      font-weight: 400;
      background-color: #fff;
    }
    .el-tabs__item.is-active {
      background-color: #1677fe;
      color: #fff;
    }
    .el-tabs__nav-scroll {
      border-radius: 25px;
      border: solid 1px #dfe1e5;
    }
    .el-tabs__active-bar {
      display: none;
    }
    .el-tabs__nav-wrap::after {
      width: 0;
    }
  }
}

.table-wrap {
  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }
  margin: 16px;

  .card-head {
    // position: relative;
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    margin-top: -20px;
    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }
  .info-wrap {
    margin-top: 16px;
    display: flex;
    flex-wrap: wrap;
    gap: 16px;

    .info-item {
      background-color: #fafbfc;
      flex: 1 1 0;
      // min-width: 180px;

      border-radius: 5px;
      padding: 8px 24px;
      box-sizing: border-box;
      // margin-right: 16px;
      display: flex;
      .info-icon {
        width: 42px;
        height: 42px;
      }
      .info-right-wrap {
        flex: 1;
        margin-left: 8px;
        .info-title {
          font-weight: 400;
          font-size: 14px;
          line-height: 14px;
          margin-bottom: 8px;
        }
        .info-number {
          font-size: 20px;
          font-weight: 500;
          .info-unit {
            font-size: 14px;
            font-weight: 400;
          }
        }
      }
    }
    .info-item:last-child {
      margin-right: 0;
    }
  }

  .top-button-wrap {
    display: flex;
    margin: 16px 0;
  }
}
::v-deep .menu-box .el-button {
  color: #217aff;
  border-color: #217aff;
}
</style>
