<template>
  <div class="container container-float" style="padding: 0 0 100px 0">
    <!-- 头部信息 -->
    <div class="device-head">
      <img
        src="@/assets/stationDivision/settlement-icon.png"
        class="device-head-icon"
      />

      <div class="device-info-wrap">
        <div class="device-title-wrap">
          <div class="device-title">
            分成编号：{{ detailObj.clearNo || '' }}
          </div>
          <div class="device-status">
            {{ detailObj.settleStatus === '0' ? '未结算' : '已结算' }}
          </div>
        </div>
        <div class="device-info-wrap">
          <el-row>
            <el-col :span="8">
              <span class="label">充电站：</span>
              <span class="value">{{ detailObj.stationName || '' }}</span>
            </el-col>
            <el-col :span="8">
              <span class="label">地市：</span>
              <span class="value">{{ detailObj.cityName || '' }}</span>
            </el-col>
            <el-col :span="8">
              <span class="label">分成周期：</span>
              <span class="value">
                {{ detailObj.clearBegDate || '' }} -
                {{ detailObj.clearEndDate || '' }}
              </span>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>

    <!-- 基础信息 -->
    <div class="info-card">
      <div class="card-head" style="margin-bottom: 8px">
        <div class="before-icon"></div>
        <div class="card-head-text">
          基础信息
          <div class="top-button-wrap">
            <!-- <el-button type="primary" @click="handleOutput">
              下载结算表
            </el-button> -->
          </div>
        </div>
      </div>

      <div class="form-wrap">
        <el-row :gutter="20" style="margin-bottom: 24px">
          <el-col :span="8">
            <div style="display: flex">
              <div class="info-title">结算方：</div>
              <div class="info-detail">{{ detailObj.fromObjName || '' }}</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div style="display: flex">
              <div class="info-title">分成方：</div>
              <div class="info-detail">{{ detailObj.toObjName || '' }}</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div style="display: flex">
              <div class="info-title">分成类型：</div>
              <div class="info-detail">
                {{ clearTypeFilter(detailObj.clearType || '') }}
              </div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20" style="margin-bottom: 24px">
          <el-col :span="24">
            <div style="display: flex">
              <div class="info-title">分成条款：</div>
              <div class="info-detail">{{ detailObj.ruleClause || '' }}</div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 结算数据 -->
    <div class="info-card">
      <div class="card-head" style="margin-bottom: 8px">
        <div class="before-icon"></div>
        <div class="card-head-text">结算数据</div>
      </div>

      <div class="form-wrap">
        <div class="table-container">
          <BuseCrud
            ref="settlementCrud"
            :tableColumn="settlementTableColumn"
            :tableData="tableData"
            :modalConfig="{ addBtn: false, menu: false }"
          ></BuseCrud>
        </div>
      </div>
    </div>

    <!-- 公众号回单 -->
    <div class="info-card">
      <div class="card-head" style="margin-bottom: 8px">
        <div class="before-icon"></div>
        <div class="card-head-text">结算信息</div>
      </div>

      <div class="form-wrap">
        <el-form
          :model="formData"
          :rules="formRules"
          ref="uploadForm"
          label-position="top"
        >
          <el-form-item
            prop="transferDate"
            label="对公转账日期："
            :label-width="formLabelWidth"
          >
            <el-date-picker
              v-model="formData.transferDate"
              type="date"
              placeholder="请选择对公转账日期"
              style="width: 80%; margin-bottom: 20px"
              value-format="yyyy-MM-dd"
            ></el-date-picker>
          </el-form-item>

          <el-form-item
            prop="fileList"
            label="上传结算凭证："
            :label-width="formLabelWidth"
          >
            <div class="upload-section">
              <Upload
                v-model="formData.fileList"
                :limit="1"
                @input="handleUpload"
                @success="handleUploadSuccess"
              />
            </div>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="bottom-wrap">
      <el-button @click="goBack">取消</el-button>
      <el-button type="primary" @click="handleSave">保存</el-button>
      <el-button type="primary" @click="handleConfirm">确认结算</el-button>
    </div>

    <SettlementDoc ref="settlementDoc" />
  </div>
</template>

<script>
import SettlementDoc from './components/settlementDoc';
import {
  clearInfoDetail,
  clearInfoUpdate,
  clearInfoSettle,
} from '@/api/sortingManage/operationProxySiteDivision';
import Upload from '@/components/Upload/index';
export default {
  name: 'SettlementView',
  components: {
    SettlementDoc,
    Upload,
  },
  data() {
    return {
      settlementTableColumn: [
        {
          field: 'totalChargePower',
          title: '充电总电量（KWH）',
          minWidth: 120,
        },
        {
          field: 'totalChargeFee',
          title: '充电电费（元）',
          minWidth: 150,
        },
        {
          field: 'inCarChargeFee',
          title: '内部车辆充电电费（元）',
          align: 'center',
          minWidth: 150,
        },
        {
          field: 'outCarChargeFee',
          title: '外部车辆充电电费（元）',
          align: 'center',
          minWidth: 150,
        },
        {
          field: 'totalServiceFee',
          title: '充电服务费（元）',
          minWidth: 150,
        },
        {
          field: 'inServiceFee',
          title: '内部车辆充电服务费（元）',
          align: 'center',
          minWidth: 150,
        },
        {
          field: 'outServiceFee',
          title: '外部车辆充电服务费（元）',
          align: 'center',
          minWidth: 150,
        },
        {
          field: 'totalChargeIncome',
          title: '充电总收入（元）',
          minWidth: 150,
        },
        {
          field: 'totalSupplyPower',
          title: ' 供电电量（KWH）',
          minWidth: 180,
        },
        {
          field: 'totalSupplyFee',
          title: ' 供电电费（元）',
          minWidth: 150,
        },
        {
          field: 'lossPower',
          title: '电损电量（KWH）',
          minWidth: 180,
        },
        // {
        //   field: 'electricalType',
        //   title: '电损计量类型',
        //   minWidth: 150,
        //   formatter: ({ cellValue }) => {
        //     return this.selectDictLabel(
        //       this.dict.type.ls_order_except_measures,
        //       cellValue
        //     );
        //   },
        // },
        // {
        //   field: 'lossFee',
        //   title: '电损电费（元）',
        //   minWidth: 150,
        // },
        // {
        //   field: 'operationCost',
        //   title: '运维成本（元）',
        //   minWidth: 150,
        // },
        // {
        //   field: 'platformServiceFee',
        //   title: '平台服务费（元）',
        //   minWidth: 150,
        // },
        {
          field: 'settleSpaceRentFee',
          title: '场地租金（元）',
          minWidth: 150,
        },
        // {
        //   field: 'shareLossFee',
        //   title: '分成方承担损耗电费（元）',
        //   align: 'center',
        //   minWidth: 150,
        // },
        // {
        //   field: 'pendingCostSharing',
        //   title: '待分成费用',
        //   minWidth: 150,
        // },
        {
          field: 'ruleClause',
          title: '分成条款',
          minWidth: 150,
        },
        {
          field: 'settleElecFee',
          title: '结算电费（元）',
          minWidth: 150,
        },
        {
          field: 'settleServiceFee',
          title: '结算服务费（元）',
          minWidth: 150,
        },
        {
          field: 'settleSpaceRentFee',
          title: '结算租金（元）',
          minWidth: 150,
        },
        {
          field: 'totalSettleFee',
          title: '结算金额（元）',
          minWidth: 150,
        },
      ],
      // 结算数据
      tableData: [],
      // 公众号回单表单数据
      formData: {
        transferDate: '',
        fileList: [],
      },
      // 表单验证规则
      formRules: {
        transferDate: [
          { required: true, message: '请输入对公转账日期', trigger: 'blur' },
        ],
        fileList: [
          {
            required: true,
            validator: this.validateFileList,
            trigger: 'change',
          },
        ],
      },
      uploadProgress: 10,
      formLabelWidth: '120px',
      id: '',
      detailObj: {},
      clearTypeList: [
        { label: '服务费', value: 'service' },
        { label: '电费+服务费', value: 'elecAndService' },
        { label: '场地租金', value: 'space' },
        { label: '场地租金+服务费', value: 'spaceAndService' },
      ],
    };
  },
  created() {
    // 获取路由参数
    this.id = this.$route.query?.id || '';
    if (this.id) {
      this.queryDeatil();
    }
  },
  methods: {
    handleUpload(file) {},
    handleUploadSuccess(file) {},
    clearTypeFilter(cellValue) {
      return this.selectDictLabel(this.clearTypeList, cellValue);
    },
    async queryDeatil() {
      const [err, res] = await clearInfoDetail({
        id: this.id || '',
      });

      if (err) return;
      this.detailObj = res?.data || {};
      const { transferDate, clearVoucher } = res?.data || {};
      this.formData.transferDate = transferDate || '';
      if (clearVoucher) {
        this.formData.fileList = [{ url: clearVoucher }];
      }
      this.tableData = [this.detailObj];
    },

    // 下载结算表
    handleOutput() {
      this.$refs.settlementDoc.loadData();
      this.$refs.settlementDoc.dialogVisible = true;
    },
    // 验证文件上传
    validateFileList(rule, value, callback) {
      if (this.formData.fileList.length === 0) {
        callback(new Error('请上传结算凭证'));
      } else {
        callback();
      }
    },
    // 用户选择的文件数量超过 limit 限制
    handleExceed(files, fileList) {
      console.log(files, fileList);
      // 清空已选文件列表
      this.$refs.upload.clearFiles();
      // 手动添加新选择的文件（首个文件）
      this.$refs.upload.handleStart(files[0]);
    },
    // 文件上传相关方法
    handleFileChange(file, fileList) {
      this.formData.fileList = fileList;
      // 模拟上传进度
      if (fileList.length > 0) {
        this.uploadProgress = 10;
        const timer = setInterval(() => {
          this.uploadProgress += 10;
          if (this.uploadProgress >= 100) {
            clearInterval(timer);
          }
        }, 300);
      }
    },
    handleFileRemove(file, fileList) {
      this.formData.fileList = fileList;
      if (fileList.length === 0) {
        this.uploadProgress = 0;
      }
    },
    // 返回上一页
    goBack() {
      this.$router.go(-1);
    },
    // 保存
    handleSave() {
      this.$refs.uploadForm.validate(async (valid) => {
        if (valid) {
          const [err, res] = await clearInfoUpdate({
            id: this.id || '',
            transferDate: this.formData.transferDate,
            clearVoucher: this.formData.fileList[0]?.url,
          });

          if (err) return;
          this.$message.success('保存成功');
          this.goBack();
        } else {
          this.$message.error('请完善表单信息');
        }
      });
    },
    // 确认结算
    handleConfirm() {
      this.$refs.uploadForm.validate((valid) => {
        if (valid) {
          this.$confirm('确认进行结算操作?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
          })
            .then(async () => {
              const [err, res] = await clearInfoSettle({
                id: this.id || '',
                transferDate: this.formData.transferDate,
                clearVoucher: this.formData.fileList[0]?.url,
                stationId: this.detailObj?.stationId || '',
                settleStatus: '1',
              });

              if (err) return;
              this.$message.success('结算成功');
              this.goBack();
            })
            .catch(() => {
              // 取消操作
            });
        } else {
          this.$message.error('请完善表单信息');
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.container-float {
  background-color: rgba(244, 246, 249, 1);
  padding-bottom: 16px;
  min-height: calc(100% - 80px);
  overflow-y: auto;
}

.device-head {
  background-color: #fff;
  display: flex;
  height: 112px;
  display: flex;
  align-items: center;
  padding: 0 24px;
  box-sizing: border-box;

  .device-head-icon {
    width: 48px;
    height: 48px;
    margin-right: 24px;
  }

  .device-info-wrap {
    flex: 1;

    .device-title-wrap {
      height: 32px;
      display: flex;
      align-items: center;

      .device-title {
        font-weight: 500;
        font-size: 24px;
        color: #12151a;
      }

      .device-status {
        padding: 0 10px;
        height: 24px;
        border-radius: 10px 0 10px 0;
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;
        text-align: center;
        color: #fff;
        background: linear-gradient(321.01deg, #ffb624 8.79%, #ff8d24 100.27%);
        margin-left: 12px;
      }
    }

    .device-info-wrap {
      height: 16px;
      margin-top: 16px;
      font-size: 16px;
      font-weight: 400;
      color: #292b33;

      .label {
        color: #505363;
        margin-right: 8px;
      }

      .value {
        color: #292b33;
      }
    }
  }
}

.info-card {
  margin: 16px;
  border-radius: 5px;
  border: 1px solid #fff;
  overflow: hidden;
  background-color: #fff;

  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    background: linear-gradient(180deg, #e9f2ff 0%, #ffffff 100%);

    .before-icon {
      width: 3px;
      height: 16px;
      background-image: url('~@/assets/station/consno-before.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin-right: 8px;
    }

    .card-head-text {
      flex: 1;
      font-weight: 500;
      font-size: 16px;
      color: #12151a;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }

  .form-wrap {
    padding: 0 16px 16px 16px;

    .info-title {
      font-weight: 400;
      font-size: 16px;
      line-height: 16px;
      color: #505363;
      margin-right: 8px;
    }

    .info-detail {
      font-weight: 400;
      font-size: 16px;
      line-height: 16px;
      color: #292b33;
    }
  }
}

.table-container {
  width: 100%;
  overflow-x: auto;
}

.upload-section {
  margin-top: 20px;

  .upload-title {
    font-size: 16px;
    color: #505363;
    margin-bottom: 10px;
  }

  .upload-area {
    width: 100%;
    border-radius: 6px;

    .upload-tip {
      font-size: 12px;
      color: #909399;
      margin-top: 8px;
    }
  }
}

.bottom-wrap {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 86px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  background-color: #ffffff;
  padding-right: 32px;
  box-sizing: border-box;
  z-index: 100;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
}

::v-deep .el-table {
  .cell {
    padding: 8px 5px;
  }

  th.is-leaf {
    background-color: #f5f7fa;
    font-weight: 500;
  }
}

::v-deep .el-upload-dragger {
  width: 100%;
  height: 180px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .el-icon-upload {
    font-size: 40px;
    color: #c0c4cc;
    margin-bottom: 10px;
  }

  .el-upload__text {
    font-size: 14px;
    text-align: center;
    color: #606266;
  }
}

// .form-label {
//   font-size: 16px;
//   color: #505363;
//   display: inline-block;
//   margin-bottom: 10px;

//   &.required::before {
//     content: '*';
//     color: #f56c6c;
//     margin-right: 4px;
//   }
// }
</style>
