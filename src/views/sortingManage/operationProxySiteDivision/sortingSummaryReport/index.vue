<template>
  <div class="container container-float" style="padding: 0">
    <!-- 异常规则管理标签栏内容 -->

    <div class="table-wrap">
      <BuseCrud
        ref="crud"
        :loading="manageLoading"
        :filterOptions="manageFilterOptions"
        :tablePage="manageTablePage"
        :tableColumn="manageTableColumn"
        :tableData="manageTableData"
        :pagerProps="pagerProps"
        :modalConfig="modalConfig"
        class="buse-wrap-station"
        @loadData="loadData"
      >
        <template slot="defaultHeader">
          <div>
            <div class="card-head">
              <div class="card-head-text">清分汇总报表</div>

              <div class="top-button-wrap">
                <!-- <el-button type="primary">新增</el-button> -->
                <el-button plain @click="() => handleExport()">导出</el-button>
              </div>
            </div>
          </div>
        </template>

        <template slot="operate" slot-scope="{ row }">
          <div class="menu-box">
            <el-button @click="goSortingDetails(row)">明细</el-button>
            <el-button @click="goSettlementView(row)">详情</el-button>
            <el-button v-if="row.settleStatus === '0'" @click="goFeedback(row)">
              问题反馈
            </el-button>
            <el-button
              v-if="row.settleStatus === '0'"
              @click="goSettlement(row)"
            >
              结算
            </el-button>
          </div>
        </template>
      </BuseCrud>
    </div>
    <summaryReportHistory
      ref="summaryReportHistory"
      :detailObj="selectOrderRule"
      @orderruleAdd="loadData"
    />
  </div>
</template>

<script>
import summaryReportHistory from './components/summaryReportHistory.vue';
import { clearInfoPage } from '@/api/sortingManage/operationProxySiteDivision';
import { getStationList } from '@/api/pile/index';
import { getOperatorsList } from '@/api/operator/index';
import { getAreaList } from '@/api/electricPricePeriod/index';
import moment from 'moment';
export default {
  components: {
    summaryReportHistory,
  },
  dicts: [
    'ls_clear_to_obj_type',
    'ls_clear_space_owner',
    'ls_clear_space_operator',
  ],
  data() {
    return {
      activeName: 'manage',
      manageLoading: false,
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      params: {
        stationId: '',
        toObjType: undefined,
        toObj: undefined,
        clearType: undefined,
        clearDate: [],
        city: undefined,
        clearNo: '',
      },
      manageTablePage: { total: 0, currentPage: 1, pageSize: 10 },
      manageTableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
          fixed: 'left',
        },
        {
          field: 'clearNo',
          title: '分成编号',
          minWidth: 190,
        },
        {
          field: 'clearDate',
          title: '分成周期',
          minWidth: 190,
        },
        {
          field: 'stationName',
          title: '充电站名称',
          minWidth: 190,
        },
        {
          field: 'cityName',
          title: '地市',
          minWidth: 100,
        },
        {
          field: 'fromObjName',
          title: '结算方',
          minWidth: 100,
        },
        {
          field: 'toObjName',
          title: '分成方',
          minWidth: 150,
        },
        {
          field: 'clearType',
          title: '分成类型',
          minWidth: 120,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(this.clearTypeList, cellValue);
          },
        },
        {
          field: 'totalChargePower',
          title: '充电总电量（KWH）',
          minWidth: 120,
        },
        {
          field: 'totalChargeFee',
          title: '充电电费（元）',
          minWidth: 150,
        },
        {
          field: 'inCarChargeFee',
          title: '内部车辆充电电费（元）',
          align: 'center',
          minWidth: 150,
        },
        {
          field: 'outCarChargeFee',
          title: '外部车辆充电电费（元）',
          align: 'center',
          minWidth: 150,
        },
        {
          field: 'totalServiceFee',
          title: '充电服务费（元）',
          minWidth: 150,
        },
        {
          field: 'inServiceFee',
          title: '内部车辆充电服务费（元）',
          align: 'center',
          minWidth: 150,
        },
        {
          field: 'outServiceFee',
          title: '外部车辆充电服务费（元）',
          align: 'center',
          minWidth: 150,
        },
        {
          field: 'totalChargeIncome',
          title: '充电总收入（元）',
          minWidth: 150,
        },
        {
          field: 'totalSupplyPower',
          title: ' 供电电量（KWH）',
          minWidth: 180,
        },
        {
          field: 'totalSupplyFee',
          title: ' 供电电费（元）',
          minWidth: 150,
        },
        {
          field: 'lossPower',
          title: '电损电量（KWH）',
          minWidth: 180,
        },
        // {
        //   field: 'electricalType',
        //   title: '电损计量类型',
        //   minWidth: 150,
        //   formatter: ({ cellValue }) => {
        //     return this.selectDictLabel(
        //       this.dict.type.ls_order_except_measures,
        //       cellValue
        //     );
        //   },
        // },
        // {
        //   field: 'lossFee',
        //   title: '电损电费（元）',
        //   minWidth: 150,
        // },
        // {
        //   field: 'operationCost',
        //   title: '运维成本（元）',
        //   minWidth: 150,
        // },
        // {
        //   field: 'platformServiceFee',
        //   title: '平台服务费（元）',
        //   minWidth: 150,
        // },
        {
          field: 'settleSpaceRentFee',
          title: '场地租金（元）',
          minWidth: 150,
        },
        // {
        //   field: 'shareLossFee',
        //   title: '分成方承担损耗电费（元）',
        //   align: 'center',
        //   minWidth: 150,
        // },
        // {
        //   field: 'pendingCostSharing',
        //   title: '待分成费用',
        //   minWidth: 150,
        // },
        {
          field: 'ruleClause',
          title: '分成条款',
          minWidth: 150,
        },
        {
          field: 'settleElecFee',
          title: '结算电费（元）',
          minWidth: 150,
        },
        {
          field: 'settleServiceFee',
          title: '结算服务费（元）',
          minWidth: 150,
        },
        {
          field: 'settleSpaceRentFee',
          title: '结算租金（元）',
          minWidth: 150,
        },
        {
          field: 'totalSettleFee',
          title: '结算金额（元）',
          minWidth: 150,
        },
        {
          field: 'orderMethod',
          title: '账单生成方式',
          minWidth: 120,
        },
        {
          field: 'settleStatus',
          title: '结算状态',
          minWidth: 120,
          fixed: 'right',
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(this.settleStatusList, cellValue);
          },
        },

        {
          title: '操作',
          slots: { default: 'operate' },
          width: 330,
          align: 'center',
          fixed: 'right',
        },
      ],
      manageTableData: [],
      selectOrderRule: {},
      orderRuleType: 'create',
      settleStatusList: [
        { label: '未结算', value: '0' },
        { label: '已结算', value: '1' },
      ],
      clearTypeList: [
        { label: '服务费', value: 'service' },
        { label: '电费+服务费', value: 'elecAndService' },
        { label: '场地租金', value: 'space' },
        { label: '场地租金+服务费', value: 'spaceAndService' },
      ],
      areaList: [],
      stationLoading: false,
      stationNameList: [],
      operationLoading: false,
      operationUnitList: [],
    };
  },

  computed: {
    manageFilterOptions() {
      return {
        config: [
          {
            field: 'stationId',
            title: '充电站',
            element: 'el-select',
            props: {
              options: this.stationNameList,
              filterable: true,
              remote: true,
              remoteMethod: this.debouncedStationSearch,
              loading: this.stationLoading,
            },
          },

          {
            field: 'toObjType',
            title: '分成方类型',
            element: 'el-select',
            props: {
              placeholder: '请选择分成方类型',
              options: this.dict.type.ls_clear_to_obj_type,
            },
            on: {
              change: (val) => {
                this.params.toObj = undefined;
              },
            },
          },
          {
            field: 'toObj',
            title: '分成方',
            element: 'el-select',
            props: {
              placeholder: '请输入分成方',
              options: this.operationUnitList,
              filterable: true,
              remote: true,
              remoteMethod: this.debouncedOperationSearch,
              loading: this.operationLoading,
            },
            show: this.params.toObjType === '0',
          },
          {
            field: 'toObj',
            title: '分成方',
            element: 'el-select',
            props: {
              placeholder: '请选择分成方',
              options: this.dict.type.ls_clear_space_owner,
            },
            show: this.params.toObjType === '1',
          },
          {
            field: 'clearType',
            title: '分成类型',
            element: 'el-select',
            props: {
              placeholder: '请选择分成类型',
              options: this.clearTypeList,
            },
          },
          {
            field: 'clearDate',
            title: '分成周期',
            element: 'el-date-picker',
            props: {
              type: 'daterange',
              options: [],
              valueFormat: 'yyyy-MM-dd',
            },
          },
          // {
          //   field: 'orderType',
          //   title: '账单生成方式',
          //   element: 'el-select',
          //   props: {
          //     placeholder: '请选择账单生成方式',
          //     options: [],
          //   },
          // },
          {
            field: 'city',
            title: '地市',
            element: 'el-select',
            props: {
              placeholder: '请选择地市',
              options: this.areaList,
            },
          },
          {
            field: 'clearNo',
            title: '分成编号',
            element: 'el-input',
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  mounted() {
    this.loadData();
    this.queryAreaList();
  },
  methods: {
    async debouncedStationSearch(query) {
      if (query !== '') {
        this.stationLoading = true;
        setTimeout(async () => {
          const [err, res] = await getStationList({
            stationName: query,
          });

          if (err) return;
          this.stationLoading = false;
          this.stationNameList = res.data.map((item) => ({
            label: item.stationName,
            value: item.stationId,
          }));
        }, 200);
      } else {
        this.stationNameList = [];
      }
    },
    async debouncedOperationSearch(query) {
      if (query !== '') {
        this.operationLoading = true;
        setTimeout(async () => {
          const [err, res] = await getOperatorsList({
            operatorName: query,
            pageNum: 1,
            pageSize: 50,
            linkBizTypeList: [],
            operatorCategoryList: [],
          });

          if (err) return;
          this.operationLoading = false;
          this.operationUnitList = res.data.map((item) => ({
            label: item.operatorName,
            value: item.operatorNo,
          }));
        }, 200);
      } else {
        this.operationUnitList = [];
      }
    },

    // 获取适用地市
    async queryAreaList() {
      const [err, res] = await getAreaList({
        areaLevel: '03',
        huNanOnly: true,
      });
      if (err) return;
      const { data } = res;
      const list = [];
      data.forEach((item) => {
        list.push({
          label: item.areaName,
          value: item.areaCode,
        });
      });
      this.areaList = list;
    },
    goFeedback(row) {
      this.$router.push({
        path: `/v2g-charging/sortingManage/operationProxySiteDivision/feedbackDetail?id=${
          row?.infoId || ''
        }&feedbackId=${row?.feedbackId || ''}`,
      });
    },
    goSortingDetails(row) {
      this.$router.push({
        path: `/v2g-charging/sortingManage/operationProxySiteDivision/sortingDetails?id=${
          row?.infoId || ''
        }&allSettleStatus=${row?.settleStatus || ''}`,
      });
    },
    goSettlementView(row) {
      this.$router.push({
        path: `/v2g-charging/sortingManage/operationProxySiteDivision/settlementView?id=${
          row?.infoId || ''
        }`,
      });
    },
    goSettlement(row) {
      this.$router.push({
        path: `/v2g-charging/sortingManage/operationProxySiteDivision/settlement?id=${
          row?.infoId || ''
        }`,
      });
    },
    handleExport() {
      const params = {
        ...this.manageFilterOptions.params,
      };
      if (params.clearDate && params.clearDate.length > 0) {
        params.begDate = params.clearDate[0];
        params.endDate = params.clearDate[1];
      }

      this.download(
        '/vehicle-charging-admin/clearInfo/export',
        {
          ...params,
        },
        `清分汇总列表.xlsx`
      );
    },
    // 获取管理列表数据
    async loadData() {
      const params = {
        ...this.manageFilterOptions.params,
      };

      if (params.clearDate && params.clearDate.length > 0) {
        params.begDate = params.clearDate[0];
        params.endDate = params.clearDate[1];
      }

      this.manageLoading = true;
      const [err, res] = await clearInfoPage({
        ...params,
        pageNum: this.manageTablePage.currentPage,
        pageSize: this.manageTablePage.pageSize,
      });

      this.manageLoading = false;

      if (err) return;
      const { data, total } = res;

      this.manageTableData = data;
      this.manageTablePage.total = total;
    },

    // 编辑异常规则
    handleSummaryHistory(row) {
      this.selectOrderRule = row;
      this.$refs.summaryReportHistory.dialogVisible = true;
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-tabs {
  margin-top: 16px;
  background-color: #f5f6f9;
  .el-tabs__header {
    padding-left: 0;
    display: flex;
    justify-content: center;
    text-align: center;
    margin-bottom: 1px;
    .el-tabs__item {
      padding: 0;
      width: 164px;
      font-size: 18px;
      font-weight: 400;
      background-color: #fff;
    }
    .el-tabs__item.is-active {
      background-color: #1677fe;
      color: #fff;
    }
    .el-tabs__nav-scroll {
      border-radius: 25px;
      border: solid 1px #dfe1e5;
    }
    .el-tabs__active-bar {
      display: none;
    }
    .el-tabs__nav-wrap::after {
      width: 0;
    }
  }
}

.table-wrap {
  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }
  margin: 16px;

  .card-head {
    // position: relative;
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    margin-top: -20px;
    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }
  .info-wrap {
    margin-top: 16px;
    display: flex;
    flex-wrap: wrap;
    gap: 16px;

    .info-item {
      background-color: #fafbfc;
      flex: 1 1 0;
      // min-width: 180px;

      border-radius: 5px;
      padding: 8px 24px;
      box-sizing: border-box;
      // margin-right: 16px;
      display: flex;
      .info-icon {
        width: 42px;
        height: 42px;
      }
      .info-right-wrap {
        flex: 1;
        margin-left: 8px;
        .info-title {
          font-weight: 400;
          font-size: 14px;
          line-height: 14px;
          margin-bottom: 8px;
        }
        .info-number {
          font-size: 20px;
          font-weight: 500;
          .info-unit {
            font-size: 14px;
            font-weight: 400;
          }
        }
      }
    }
    .info-item:last-child {
      margin-right: 0;
    }
  }

  .top-button-wrap {
    display: flex;
    margin: 16px 0;
  }
}
::v-deep .menu-box .el-button {
  color: #217aff;
  border-color: #217aff;
}
</style>
