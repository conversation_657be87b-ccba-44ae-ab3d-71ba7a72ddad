<template>
  <el-dialog
    title="清分明细订单选择"
    :visible.sync="dialogVisible"
    width="1598px"
  >
    <div class="edit-info-wrap">
      <BuseCrud
        style="margin-bottom: 16px; padding: 0 24px"
        ref="periodTableInfo"
        :filterOptions="manageFilterOptions"
        :tablePage="tablePage"
        :tableColumn="tableColumn"
        :tableData="tableData"
        :modalConfig="{ addBtn: false, menu: false }"
        :tableOn="{
          'checkbox-change': handleCheckboxChange,
          'checkbox-all': handleCheckboxChange,
        }"
        :pagerProps="pagerProps"
        @loadData="loadData"
      ></BuseCrud>
    </div>
    <div class="bottom-wrap">
      <div>
        <el-button @click="handleCancel">取消</el-button>
        <el-button @click="handleSave" type="primary">提交</el-button>
      </div>
    </div>
  </el-dialog>
</template>
<script>
import { clearDetailPage } from '@/api/sortingManage/operationProxySiteDivision';
export default {
  props: {
    selectOrderRule: {
      type: Object,
      default: () => {},
    },
    id: {
      type: String,
      default: '',
    },
  },
  components: {},
  dicts: [],
  data() {
    return {
      checkedOrderList: [],
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      dialogVisible: false,
      settleStatusList: [
        { label: '未清分', value: '0' },
        { label: '已清分', value: '1' },
      ],
      tableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
        },
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
        },
        {
          field: 'orderNo',
          title: '订单编号',
          minWidth: 190,
        },
        {
          field: 'orderCreateTime',
          title: '订单下单时间',
          minWidth: 190,
        },
        {
          field: 'chargeBegTime',
          title: '充电开始时间',
          minWidth: 150,
        },
        {
          field: 'chargeEndTime',
          title: '充电结束时间',
          minWidth: 120,
        },
        {
          field: 'chargePower',
          title: '充电电量（KWH）',
          minWidth: 100,
        },
        {
          field: 'chargeFee',
          title: '充电电费（元）',
          minWidth: 150,
        },
        {
          field: 'inCarChargeFee',
          title: '内部车辆充电电费（元）',
          align: 'center',
          minWidth: 150,
        },
        {
          field: 'outCarChargeFee',
          title: '外部车辆充电电费（元）',
          align: 'center',
          minWidth: 150,
        },
        {
          field: 'serviceFee',
          title: '充电服务费（元）',
          minWidth: 150,
        },
        {
          field: 'inServiceFee',
          title: '内部车辆充电服务费（元）',
          align: 'center',
          minWidth: 150,
        },
        {
          field: 'outServiceFee',
          title: '外部车辆充电服务费（元）',
          align: 'center',
          minWidth: 150,
        },
        {
          field: 'chargeIncome',
          title: '充电总金额（元）',
          minWidth: 150,
        },
        {
          field: 'clearFee',
          title: '待分成费用',
          minWidth: 150,
        },
        {
          field: 'settleElecFee',
          title: '结算电费（元）',
          minWidth: 150,
        },
        {
          field: 'settleServiceFee',
          title: '结算服务费（元）',
          minWidth: 150,
        },

        {
          field: 'totalSettleFee',
          title: '结算金额（元）',
          minWidth: 150,
        },
        {
          field: 'settleStatus',
          title: '清分状态',
          minWidth: 120,
          fixed: 'right',
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(this.settleStatusList, cellValue);
          },
        },
      ],

      tableData: [],
      params: {
        orderNo: '',
        clearDate: [],
        chargeDate: [],
      },
    };
  },
  watch: {
    dialogVisible(value) {
      if (value) {
        this.loadData();
      }
    },
  },
  computed: {
    manageFilterOptions() {
      return {
        config: [
          {
            field: 'orderNo',
            title: '订单编号：',
            element: 'el-input',
          },

          {
            field: 'clearDate',
            title: '订单下单时间：',
            element: 'el-date-picker',
            props: {
              type: 'daterange',
              options: [],
              valueFormat: 'yyyy-MM-dd',
            },
          },
          {
            field: 'chargeDate',
            title: '充电时间：',
            element: 'el-date-picker',
            props: {
              type: 'daterange',
              options: [],
              valueFormat: 'yyyy-MM-dd',
            },
          },
        ],
        params: this.params,
      };
    },
  },
  mounted() {},
  methods: {
    handleCancel() {
      this.dialogVisible = false;
    },
    // 保存
    handleSave() {
      this.$emit('chooseOrderSave', this.checkedOrderList);
      this.dialogVisible = false;
    },
    handleCheckboxChange({ records }) {
      this.checkedOrderList = records;
    },
    // 获取管理列表数据
    async loadData() {
      const params = {
        ...this.manageFilterOptions.params,
      };
      if (params.clearDate && params.clearDate.length > 0) {
        params.begDate = params.clearDate[0];
        params.endDate = params.clearDate[1];
      }
      if (params.chargeDate && params.chargeDate.length > 0) {
        params.chargeBegStartDate = params.chargeDate[0];
        params.chargeEndEndDate = params.chargeDate[1];
      }

      const [err, res] = await clearDetailPage({
        id: this.id,
        ...params,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      });

      if (err) return;
      const { data, total } = res;

      this.tableData = data;
      this.tablePage.total = total;
    },
  },
};
</script>

<style lang="scss" scoped>
.edit-info-wrap {
  min-height: 520px;
  .edit-info-item {
    .edit-info-item-title-after {
      display: flex;
      height: 88px;
      width: 100%;
      background: linear-gradient(180deg, #d9ecff 0%, #ffffff 100%);
      padding: 0 0 0 24px;
      box-sizing: border-box;
      align-items: center;
      font-weight: 500;
      font-size: 24px;
      .edit-detail-title-icon {
        width: 32px;
        height: 32px;
        background-image: url('~@/assets/station/compare-detail-title-icon.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        margin-right: 12px;
      }
    }
    .title-wrap {
      height: 18px;
      padding: 0 0 0 24px;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      font-weight: 500;
      font-size: 18px;
      margin-bottom: 24px;
      color: #12151a;
      .before-icon {
        width: 3px;
        height: 16px;
        background-image: url('~@/assets/station/consno-before.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        margin-right: 8px;
      }
    }
    .info-wrap {
      padding: 0 0 0 24px;
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      margin-bottom: 24px;

      .info-item-wrap {
        display: flex;
        flex: 1 1 25%;
        margin-bottom: 24px;
        .info-title {
          font-weight: 400;
          font-size: 16px;
          line-height: 16px;
          color: #505363;
        }
        .info-detail {
          font-weight: 400;
          font-size: 16px;
          line-height: 16px;
          color: #292b33;
          .price-number {
            color: #ff8d24;
            font-weight: 500;
          }
          a {
            color: #217aff;
            text-decoration: underline;
          }
        }
      }
    }
  }
}
.bottom-wrap {
  // position: fixed;
  // bottom: 0;
  // left: 0;
  // width: 100%;
  // height: 86px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  background-color: #ffffff;
  padding-right: 32px;
  box-sizing: border-box;
}
::v-deep .bd3001-content {
  padding: 0 !important;
}
</style>
