<template>
  <div class="container container-float" style="padding: 0 0 100px 0">
    <!-- 异常规则管理标签栏内容 -->

    <div class="table-wrap">
      <BuseCrud
        ref="crud"
        :loading="manageLoading"
        :filterOptions="manageFilterOptions"
        :tablePage="manageTablePage"
        :tableColumn="manageTableColumn"
        :tableData="manageTableData"
        :pagerProps="pagerProps"
        :modalConfig="modalConfig"
        @loadData="loadManageData"
      >
        <template slot="defaultHeader">
          <div>
            <div class="card-head">
              <div class="card-head-text">问题反馈管理列表</div>
            </div>
          </div>
        </template>
        <template slot="divideIntoNumbers" slot-scope="{ row }">
          <el-button type="text">1111111111</el-button>
        </template>
        <template slot="operate" slot-scope="{ row }">
          <div class="menu-box">
            <el-button @click="goFeedbackDetail(row)">详情</el-button>
            <el-button @click="goFeedbackDetail(row)">核查</el-button>
          </div>
        </template>
      </BuseCrud>
    </div>
  </div>
</template>

<script>
import { getExruleList, exruleEnable } from '@/api/order/index';
import moment from 'moment';
export default {
  components: {},
  dicts: [
    'ls_order_except_level', // 异常级别
    'ls_order_except_type', // 异常类型
    'ls_order_except_measures', // 处理措施
    'ls_order_except_status', // 异常状态
  ],
  data() {
    return {
      activeName: 'manage',
      manageLoading: false,
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      params: {
        exceptId: '',
        name: '',
        level: '',
        type: '',
        measures: '',
        creatorTime: [],
        status: '',
      },
      manageTablePage: { total: 0, currentPage: 1, pageSize: 10 },
      manageTableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
          fixed: 'left',
        },
        {
          field: 'feedbackId',
          title: '问题反馈编号',
          minWidth: 190,
        },
        {
          field: 'divideIntoNumbers',
          title: '分成编号',
          minWidth: 190,
          slots: { default: 'divideIntoNumbers' },
        },
        {
          field: 'stationName',
          title: '充电站名称',
          minWidth: 190,
        },
        {
          field: 'region',
          title: '问题订单数',
          minWidth: 150,
        },

        {
          field: 'splittingType',
          title: '状态',
          minWidth: 120,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_order_except_type,
              cellValue
            );
          },
        },
        {
          field: 'totalCapacity',
          title: '反馈人',
          minWidth: 120,
        },

        {
          field: 'externalServiceFee',
          title: '分成方',
          align: 'center',
          minWidth: 150,
        },
        {
          field: 'createTime',
          title: '创建时间',
          align: 'center',
          minWidth: 180,
        },
        {
          title: '操作',
          slots: { default: 'operate' },
          width: 220,
          align: 'center',
          fixed: 'right',
        },
      ],
      manageTableData: [],
      selectOrderRule: {},
      orderRuleType: 'create',
    };
  },

  computed: {
    manageFilterOptions() {
      return {
        config: [
          {
            field: 'divideIntoNumbers',
            title: '分成编号',
            element: 'el-input',
          },
          {
            field: 'stationId',
            title: '充电站',
            element: 'el-select',
            props: {
              placeholder: '请选择充电站',
              options: this.dict.type.ls_order_except_level,
            },
          },
          {
            field: 'splittingParty',
            title: '分成方',
            element: 'el-input',
          },
          {
            field: 'feedbackUser',
            title: '反馈人',
            element: 'el-input',
          },
          {
            field: 'splittingTime',
            title: '创建时间',
            element: 'el-date-picker',
            props: {
              type: 'daterange',
              options: [],
              valueFormat: 'yyyy-MM-dd',
            },
          },
          {
            field: 'billGenerationMethod',
            title: '状态',
            element: 'el-select',
            props: {
              placeholder: '请选择状态',
              options: this.dict.type.ls_order_except_level,
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  mounted() {
    this.loadManageData();
  },
  methods: {
    handleExport() {
      const params = {
        ...this.manageFilterOptions.params,
      };

      this.download(
        '/vehicle-charging-admin/station/stationExport',
        {
          ...params,
        },
        `充电站列表.xlsx`
      );
    },
    // 获取管理列表数据
    async loadManageData() {
      const { exceptId, name, level, type, measures, creatorTime, status } =
        this.manageFilterOptions.params;

      let createTimeStart = '';
      let createTimeEnd = '';
      if (creatorTime && creatorTime.length > 0) {
        createTimeStart = moment(creatorTime[0]).format('YYYY-MM-DD');
        createTimeEnd = moment(creatorTime[1]).format('YYYY-MM-DD');
      }

      this.manageLoading = true;
      const [err, res] = await getExruleList({
        exceptId: exceptId,
        name: name,
        type: type,
        level: level,
        measures: measures,
        status: status,
        createTimeStart,
        createTimeEnd,
        pageNum: this.manageTablePage.currentPage,
        pageSize: this.manageTablePage.pageSize,
      });

      this.manageLoading = false;
      if (err) return;
      const { data, total } = res;
      this.manageTableData = data;
      this.manageTablePage.total = total;
    },

    // 编辑异常规则
    goFeedbackDetail(row) {
      this.$router.push({
        path: '/v2g-charging-web/sortingManage/operationProxySiteDivision/feedbackDetail',
      });
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-tabs {
  margin-top: 16px;
  background-color: #f5f6f9;
  .el-tabs__header {
    padding-left: 0;
    display: flex;
    justify-content: center;
    text-align: center;
    margin-bottom: 1px;
    .el-tabs__item {
      padding: 0;
      width: 164px;
      font-size: 18px;
      font-weight: 400;
      background-color: #fff;
    }
    .el-tabs__item.is-active {
      background-color: #1677fe;
      color: #fff;
    }
    .el-tabs__nav-scroll {
      border-radius: 25px;
      border: solid 1px #dfe1e5;
    }
    .el-tabs__active-bar {
      display: none;
    }
    .el-tabs__nav-wrap::after {
      width: 0;
    }
  }
}

.table-wrap {
  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }
  margin: 16px;

  .card-head {
    // position: relative;
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    margin-top: -20px;
    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }
  .info-wrap {
    margin-top: 16px;
    display: flex;
    flex-wrap: wrap;
    gap: 16px;

    .info-item {
      background-color: #fafbfc;
      flex: 1 1 0;
      // min-width: 180px;

      border-radius: 5px;
      padding: 8px 24px;
      box-sizing: border-box;
      // margin-right: 16px;
      display: flex;
      .info-icon {
        width: 42px;
        height: 42px;
      }
      .info-right-wrap {
        flex: 1;
        margin-left: 8px;
        .info-title {
          font-weight: 400;
          font-size: 14px;
          line-height: 14px;
          margin-bottom: 8px;
        }
        .info-number {
          font-size: 20px;
          font-weight: 500;
          .info-unit {
            font-size: 14px;
            font-weight: 400;
          }
        }
      }
    }
    .info-item:last-child {
      margin-right: 0;
    }
  }

  .top-button-wrap {
    display: flex;
    margin: 16px 0;
  }
}
::v-deep .menu-box .el-button {
  color: #217aff;
  border-color: #217aff;
}
</style>
