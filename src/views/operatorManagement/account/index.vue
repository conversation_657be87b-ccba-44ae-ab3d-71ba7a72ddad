<template>
  <div class="container">
    <div class="account-info">
      <div class="info-item">
        <span class="label">账户编码：</span>
        <span class="value">*********</span>
      </div>
      <div class="info-item">
        <span class="label">账户余额：</span>
        <span class="value">97686.00</span>
      </div>
      <div class="info-item">
        <el-button type="primary">绑定银行卡</el-button>
        <el-button type="primary">提现</el-button>
        <el-button type="primary">充值</el-button>
      </div>
    </div>
    <div class="table-wrap">
      <BuseCrud
        ref="crud"
        :loading="loading"
        :filterOptions="filterOptions"
        :tablePage="tablePage"
        :tableColumn="tableColumn"
        :tableData="tableData"
        :pagerProps="pagerProps"
        :modalConfig="modalConfig"
        @loadData="loadData"
      >
        <template slot="defaultHeader">
          <el-button
            class="btn-export"
            type="primary"
            icon="el-icon-plus"
            @click="handleExport"
          >
            导出
          </el-button>
        </template>
      </BuseCrud>
    </div>
  </div>
</template>

<script>
export default {
  name: 'OperatorAccount',
  data() {
    return {
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [
        {
          processNo: '***********',
          operationType: '充值',
          settlementNo: '城市公共',
          withdrawalNo: '自营',
          transactionTime: '2016-09-19 08:50:08',
          amount: '+80.00',
          balance: '123.00',
          operator: '管理员张三',
          status: '已交易',
        },
      ],
      tableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
        },
        {
          field: 'processNo',
          title: '流程号',
          minWidth: 120,
        },
        {
          field: 'operationType',
          title: '操作类型',
          minWidth: 100,
        },
        {
          field: 'settlementNo',
          title: '关联汇总清分编号',
          minWidth: 100,
        },
        {
          field: 'withdrawalNo',
          title: '关联提现申请编号',
          minWidth: 100,
        },
        {
          field: 'transactionTime',
          title: '交易时间',
          minWidth: 160,
        },
        {
          field: 'amount',
          title: '支付金额',
          minWidth: 100,
        },
        {
          field: 'balance',
          title: '账户余额',
          minWidth: 100,
        },
        {
          field: 'operator',
          title: '操作员',
          minWidth: 120,
        },
        {
          field: 'status',
          title: '交易状态',
          minWidth: 100,
          fixed: 'right',
        },
      ],
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      params: {
        processNo: '',
        transactionType: '',
        operationType: '',
        transactionTime: [],
      },
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'processNo',
            title: '流程号',
            element: 'el-input',
            placeholder: '请输入',
          },
          {
            field: 'operationType',
            title: '操作类型',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
          {
            field: 'transactionTime',
            title: '交易时间',
            element: 'el-date-picker',
            props: {
              type: 'daterange',
              startPlaceholder: '开始日期',
              endPlaceholder: '结束日期',
              valueFormat: 'yyyy-MM-dd',
            },
          },
          {
            field: 'transactionType',
            title: '交易类型',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  methods: {
    async loadData(params) {
      this.loading = true;
      try {
        // TODO: 调用获取账户列表接口
        // const { data } = await getOperatorAccountList({
        //   ...params,
        //   operatorId: this.$route.query.operatorId
        // });
        // this.tableData = data.list;
        // this.tablePage.total = data.total;
      } catch (error) {
        console.error('获取账户列表失败:', error);
      } finally {
        this.loading = false;
      }
    },
    handleExport() {},
  },
  created() {
    this.loadData(this.params);
  },
};
</script>

<style lang="scss" scoped>
.account-info {
  background: #fff;
  padding: 16px 24px;
  margin-bottom: 16px;
  border-radius: 4px;
  display: flex;
  align-items: center;

  .info-item {
    display: flex;
    align-items: center;
    margin-right: 48px;

    &:last-child {
      margin-left: auto;
      margin-right: 0;
    }

    .label {
      color: #606266;
      margin-right: 8px;
    }

    .value {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }

    .el-button {
      margin-left: 16px;

      &:first-child {
        margin-left: 0;
      }
    }
  }
}
.table-wrap {
  background: #fff;
  padding: 20px;
  border-radius: 4px;

  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }

  .btn-export {
    margin-bottom: 16px;
  }
}
</style>
