<template>
  <div class="container container-float" style="padding: 0 ">
    <div class="device-head">
            <img
                src="@/assets/user/operator-account-icon.png"
                class="device-head-icon"
            />

            <div class="device-info-wrap">
                <div class="device-title-wrap">
                    <div class="device-title">账户编码：{{ accountCode }}</div>
                </div>
                <div class="device-detail-wrap">
                    <el-row>
                        <el-col :span="6">
                            <span class="label">账户余额：</span>
                            <span class="value">{{ balance }}</span>
                        </el-col>
                    </el-row>
                </div>
            </div>

            
            <div class="button-wrap">
                <div class="status-wrap">
                  <div class="status-wrap-title">
                    账户状态
                  </div>
                  <div class="status-info-wrap  ">
                    <el-switch
                      disabled
                      v-model="status"
                    ></el-switch>
                    <div class="status-text">{{ status ? '启用' : '禁用' }}</div>
                  </div>
                </div>
                <el-button
                    type="primary"
                    class="set-btn"
                    @click="handleBindCard"
                >
                    <svg-icon iconClass="a-set"></svg-icon>
                   
                    绑定银行卡
                </el-button>

                <el-button
                    type="primary"
                    class="set-btn"
                    @click="handleWithdrawal"
                >
                    <svg-icon iconClass="a-withdrawal"></svg-icon>
                    提现
                </el-button>

                <el-button
                    type="primary"
                    @click="handleRecharge"
                >
                    <svg-icon iconClass="a-recharge"></svg-icon>
                    充值
                </el-button>

                
            </div>

    </div>
    <div class="table-wrap">
      <BuseCrud
        ref="crud"
        :loading="loading"
        :filterOptions="filterOptions"
        :tablePage="tablePage"
        :tableColumn="tableColumn"
        :tableData="tableData"
        :pagerProps="pagerProps"
        :modalConfig="modalConfig"
          class="buse-wrap-account"
        @loadData="loadData"
      >
        <template slot="defaultHeader">
          <el-button
            class="btn-export"
            type="primary"
            icon="el-icon-plus"
            @click="handleExport"
          >
            导出
          </el-button>
        </template>

        <template slot="operationType" slot-scope="{ row }">
          <div class="operation-type-0" v-if="row.operationType === '0'">充值</div>
          <div class="operation-type-1" v-if="row.operationType === '1'">提现</div>
        </template>

        <template slot="amount" slot-scope="{ row }">
          <div style="font-weight: 500;">{{ row.operationType === '0' ? '+' : '-' }}{{ row.amount }}</div>
        </template>
      </BuseCrud>
    </div>

    <BindCardModal ref="bindCardModal"  @loadData="getOperatorsAccount"/>
    <RechargeModal ref="rechargeModal"  @loadData="getOperatorsAccount"/>
  </div>
</template>

<script>

import BindCardModal from './components/bindCardModal.vue'
import RechargeModal from './components/rechargeModal.vue'
import StatusDot from '@/components/Business/StatusDot';


import {
  getOperatorsAccount,
  getOperatorsFlow,
} from '@/api/operator/index'


export default {
  name: 'OperatorAccount',
  components: {
    BindCardModal,
    RechargeModal,
    StatusDot,
  },
  data() {
    return {
      operatorNo: '',
      operatorName: '',
      operatorAccountId: '',
      accountCode: '',
      balance: '',
      
      status: true,
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [],
      tableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
        },
        {
          field: 'serialNumber',
          title: '流水号',
          minWidth: 120,
          slots: {
                  default: ({ row }) => [
                      <el-tooltip 
                          content={row.serialNumber} 
                          placement="top" 
                          disabled={!row.serialNumber || row.serialNumber.length < 10}
                      >
                          <span class="ellipsis-text">{row.serialNumber}</span>
                      </el-tooltip>
                  ]
              }
        },
        {
          field: 'operationType',
          title: '操作类型',
          minWidth: 80,
          slots: { default: 'operationType' },
          
        },
        {
          field: 'clearNo',
          title: '分成编号',
          minWidth: 100,
          slots: {
                  default: ({ row }) => [
                      <el-tooltip 
                          content={row.clearNo} 
                          placement="top" 
                          disabled={!row.clearNo || row.clearNo.length < 10}
                      >
                          <span class="ellipsis-text">{row.clearNo}</span>
                      </el-tooltip>
                  ]
              }
        },
        {
          field: 'payId',
          title: '提现编号',
          minWidth: 100,
          slots: {
                  default: ({ row }) => [
                      <el-tooltip 
                          content={row.payId} 
                          placement="top" 
                          disabled={!row.payId || row.payId.length < 10}
                      >
                          <span class="ellipsis-text">{row.payId}</span>
                      </el-tooltip>
                  ]
              }
        },
        {
          field: 'arrivalTime',
          title: '交易时间',
          minWidth: 160,
        },
        {
          field: 'amount',
          title: '支付金额',
          minWidth: 100,
          slots: { default: 'amount' },
        },
        {
          field: 'realAmount',
          title: '账户余额',
          minWidth: 100,
        },
        {
          field: 'createBy',
          title: '操作员',
          minWidth: 120,
        },
        {
          field: 'status',
          title: '交易状态',
          minWidth: 100,
          fixed: 'right',
          slots: {
                  // 自定义render函数
                  default: ({ row }) => {
                    return (
                      <StatusDot
                        value={row.status}
                        dictValue={this.transactionTypeList}
                        colors={['warning', 'success', 'danger',]}
                      ></StatusDot>
                    );
                  },
                },
        },
      ],
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      params: {
        processNo: '',
        transactionType: '',
        operationType: '',
        transactionTime: [],
      },

      operationTypeList: [
        {
          label: '充值',
          value: '0',
        },
        {
          label: '提现',
          value: '1',
        }
      ],
      transactionTypeList:[
        {
          label: '交易中',
          value: '0',
        },
        {
          label: '交易成功',
          value: '1',
        },
        {
          label: '交易失败',
          value: '2',
        }
      ]
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'processNo',
            title: '流水号',
            element: 'el-input',
            placeholder: '请输入',
          },
          {
            field: 'operationType',
            title: '操作类型',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.operationTypeList,
            },
          },
          {
            field: 'transactionTime',
            title: '交易时间',
            element: 'el-date-picker',
            props: {
              type: 'daterange',
              startPlaceholder: '开始日期',
              endPlaceholder: '结束日期',
              valueFormat: 'yyyy-MM-dd',
            },
          },
          {
            field: 'transactionType',
            title: '交易状态',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.transactionTypeList,
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  methods: {
    // 运营商账户查询
    async getOperatorsAccount(){
      const [err, res] = await getOperatorsAccount({
        operatorNo: this.operatorNo
      })

      if (err) return 
      const  {
        operatorAccountId,
        operatorName,
        operatorBalance,
        accountNo,
        status,
      } = res.data

      this.accountCode = accountNo
      this.balance = operatorBalance
      this.operatorName = operatorName
      this.operatorAccountId = operatorAccountId

      this.status = (status === '1' || !status)  ? true : false

      this.tablePage = { total: 0, currentPage: 1, pageSize: 10 },

      this.loadData();



      
    },

    async loadData() {
      
      const  {
        processNo,
        transactionType,
        operationType,
        transactionTime,
      } = this.params;

      let tradeTimeBeg = ''
      let tradeTimeEnd = ''
      if(transactionTime && transactionTime.length > 0) {
        tradeTimeBeg = transactionTime[0]
        tradeTimeEnd = transactionTime[1]
      }

      

      const params = {
        serialNumber: processNo,
        operationType: operationType,
        status: transactionType,
        tradeTimeBeg,
        tradeTimeEnd,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      }


      this.loading = true;

      const [err, res] = await getOperatorsFlow(params)

      this.loading = false;
      
      if (err) return 
        const { data, total } = res;


        this.tableData = data;
        this.tablePage.total = total;
    },
    handleExport() {
      const  {
        processNo,
        transactionType,
        operationType,
        transactionTime,
      } = this.params;

      let tradeTimeBeg = ''
      let tradeTimeEnd = ''
      if(transactionTime && transactionTime.length > 0) {
        tradeTimeBeg = transactionTime[0]
        tradeTimeEnd = transactionTime[1]
      }

      

      const params = {
        serialNumber: processNo,
        operationType: operationType,
        status: transactionType,
        tradeTimeBeg,
        tradeTimeEnd,
      }

      this.download(
          '/vehicle-charging-admin/withdrawal/export ',
          {
            ...params,
          },
          `运营商流水列表.xlsx`
        );
    },

    handleBindCard() {
      this.$refs.bindCardModal.operatorAccountId = this.operatorAccountId;
      this.$refs.bindCardModal.dialogVisible = true;
    },

    handleRecharge() {
      this.$refs.rechargeModal.operatorName = this.operatorName;
      this.$refs.rechargeModal.operatorNo = this.operatorNo;
      this.$refs.rechargeModal.operatorAccountId = this.operatorAccountId;
      this.$refs.rechargeModal.balance = this.balance;
      this.$refs.rechargeModal.dialogVisible = true;
    },

    handleWithdrawal() {
      this.$router.push({
        path: '/v2g-charging/baseInfo/operator/withdrawal',
        query: {
          operatorNo: this.operatorNo,

        }
      });
    },
    
   
  },
  mounted() {
    const operatorNo = this.$route.query.operatorNo;
    this.operatorNo = operatorNo;

    this.getOperatorsAccount();

   

  },
  
};
</script>

<style lang="scss" scoped>
.account-info {
  padding: 16px 24px;
  margin-bottom: 16px;
  border-radius: 4px;
  display: flex;
  align-items: center;

  .info-item {
    display: flex;
    align-items: center;
    margin-right: 48px;

    &:last-child {
      margin-left: auto;
      margin-right: 0;
    }

    .label {
      color: #606266;
      margin-right: 8px;
    }

    .value {
      font-size: 16px;
      font-weight: 500;
      color: #303133;
    }

    .el-button {
      margin-left: 16px;

      &:first-child {
        margin-left: 0;
      }
    }
  }
}
.table-wrap {
  background: #fff;
  margin: 16px;
  border-radius: 4px;

  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }

  .btn-export {
    margin-bottom: 16px;
  }
}


.device-head {
  background-color: #fff;

  display: flex;
  height: 112px;
  display: flex;
  align-items: center;
  padding: 0 24px;
  box-sizing: border-box;
  .device-head-icon {
    width: 48px;
    height: 48px;
    margin-right: 24px;
  }
  .device-info-wrap {
    flex: 1;
    .device-title-wrap {
      height: 32px;
      display: flex;
      align-items: center;
      .device-title {
        font-weight: 500;
        font-size: 24px;
        color: #12151a;
      }
      .device-status {
        // width: 50px;
        padding: 0 10px;
        height: 24px;
        border-radius: 10px 0 10px 0;
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;
        text-align: center;
        color: #fff;
        background: linear-gradient(321.01deg, #217AFF 8.79%,  #21C8FF 100.27%);
        margin-left: 12px;
      }
    }
    .device-detail-wrap {
      height: 16px;
      margin-top: 16px;
      font-size: 16px;
      font-weight: 400;
      color: #292b33;

      .label {
        font-weight: 400;
        font-size: 16px;
        color: #505363;
      }
      .value {
        font-weight: 400;
        font-size: 20px;
        color:  #292B33;
      }
    }
  }
  
  .button-wrap {
    display: flex;
    align-items: center;
    .set-btn {
        background-color: #FFFFFF;
        color: #292B33;
        border-color: #DFE1E5;
    }
    .status-wrap {
      margin-right: 32px;
      .status-wrap-title {
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 16px;
        line-height: 16px;
        margin-bottom: 16px;
        color: #505363;
      }
      .status-info-wrap {
        display: flex;
        align-items: center;
        height: 24px;
        .status-text {
          font-weight: 400;
          font-size: 16px;
          line-height: 16px;
          color: #505363;
          margin-left: 8px;
        }
      }
    }

  }
}

::v-deep .bd3001-auto-filters-container {
  box-shadow: none !important;
}

::v-deep .bd3001-content {
  box-shadow: none !important;
  padding-top: 0 !important;
}

.ellipsis-text {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }

.operation-type-0 {
  width: 52px;

  height: 28px;
  border-radius: 2px;
  padding: 0 10px;
  box-sizing: border-box;
  color: #217AFF;
  background-color: #EBF3FF;
  display: flex;
  align-items: center;
  justify-content: center;

}
.operation-type-1 {
  width: 52px;
  height: 28px;
  border-radius: 2px;
  padding: 0 10px;
  box-sizing: border-box;
  color: #FF8D24;
  background-color: #FFF7E6;
  display: flex;
  align-items: center;
  justify-content: center;

}
</style>
