<template>
    <el-dialog title="账户提现" :visible.sync="dialogVisible" width="630px">
        <el-form :model="form" :rules="rules" ref="accountForm"  label-position="top">
            <div class="company-wrap" style="margin-bottom: 24px;">
                <div class="company-brief">当前账户余额(元)：</div>
                <div class="company-name">{{ useableBalance }}</div>
            </div>
            
            <el-radio-group
                v-model="widthdrawalType"
                style="margin-bottom: 16px;"
                @change="changeWidthdrawalType"
            >
                <el-radio
                    v-for="dict in widthdrawalTypeList"
                    :key="dict.value"
                    :label="dict.value"
                >
                    {{ dict.label }}
                </el-radio>
            </el-radio-group>

            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item
                        label="提现金额："
                        prop="widthdrawalAmount"
                        :label-width="formLabelWidth"
                    >
                    <div style="display: flex;">
                        <el-input-number
                            style="width: 100%;"
                            v-model="form.widthdrawalAmount"
                            :min="0"
                            :max="Number(useableBalance)"
                            :precision="2"
                            :step="0.01"
                            :controls="false"
                            placeholder="请输入提现金额"
                            @change="handleWidthdrawalAmountChange"
                        ></el-input-number>
                        <div style="margin-left: 8px;">元</div>
                    </div>
                    </el-form-item>
                </el-col>

                <el-col :span="24">
                    <div class="balance-wrap-after">
                        <div class="balance-item">
                            <div class="balance-label">提现后账户余额（元）：</div>
                            <div class="balance-value">{{  afterUseableBalance }}</div>
                        </div>

                    </div>
                </el-col>
               

                <el-col :span="24">
                    <el-form-item
                        label="备注"
                        prop="remark"
                        :label-width="formLabelWidth"
                    >
                        <el-input
                            v-model="form.remark"
                            type="textarea"
                            :rows="3"
                            placeholder="请输入备注"
                        ></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form> 

        <div slot="footer" class="dialog-footer">
            <el-button @click="handleCancel">取 消</el-button>
            <el-button type="primary" @click="handleConfirm">确 定</el-button>
        </div>
    </el-dialog>
   
</template>
<script>  

import  Upload from '@/components/Upload/index'
import _ from 'lodash';


import {
    addWithdrawal
} from '@/api/operator/index'
  export default {
    props: {
        editInfo: {
            type: Object,
            default: () => {},
        },
    },
    dicts: [
        'ls_order_except_level',
        'ls_order_except_type',
    ],
    components: {
        Upload,
    },
    data() {
        return {
            dialogVisible: false,
            operatorNo: '',
            operatorAccountId: '',

            useableBalance: '',
            afterUseableBalance: '',

            widthdrawalType: '1',

            formLabelWidth: '120px',
            form: {
                widthdrawalAmount: '',
                remark: '',
            },
            rules: {
                widthdrawalAmount: [
                    { required: true, message: '请输入提现金额', trigger: 'blur' },
                ],

            },
            widthdrawalTypeList: [
                {
                    label: '部分提现',
                    value: '1',
                },
                {
                    label: '全额提现',
                    value: '2',
                }
            ]
        };
    },
    watch: {
        
    },
    computed: {
        
    },
    mounted() {
       this.handleWidthdrawalAmountChange();    
    },
    methods: {
        // 新增按钮防抖        
        handleConfirm: _.debounce(function() {
            this.$refs.accountForm.validate(async (valid) => {
                if (valid) {
                    console.log(this.form, 'this.form')
                    const {
                        widthdrawalAmount,
                        remark,
                    } = this.form;
                    
                    const params = {
                        operatorNo: this.operatorNo,
                        operatorAccountId: this.operatorAccountId,
                        amount: widthdrawalAmount,
                        remark
                    }
                    const [err, res] = await addWithdrawal(params);
                    if (err) return;
                    
                    this.$message.success('提现成功');
                    this.dialogVisible = false;
                    this.handleResetData()
                    this.$emit('loadData');
                    
                }
            });
        }, 300) ,
        handleCancel() {
            this.dialogVisible = false;
            this.handleResetData()
        },

        handleWidthdrawalAmountChange() {
            this.afterUseableBalance = Number(this.useableBalance) - (Number(this.form.widthdrawalAmount)?Number(this.form.widthdrawalAmount):0);
            
        },

        handleResetData() {
            this.form =  {
                widthdrawalAmount: '',
                remark: '',
            }
            this. operatorNo =  '',
            this.operatorAccountId = '',
            this.useableBalance= '',
            this.afterUseableBalance= ''
        },

        changeWidthdrawalType() {
            if(this.widthdrawalType === '1') {
                this.form.widthdrawalAmount = ''
                this.handleWidthdrawalAmountChange() 
            } else if (this.widthdrawalType === '2') {
                this.form.widthdrawalAmount = Number(this.useableBalance)
                this.handleWidthdrawalAmountChange()    
            }
        }
    },
};
  </script>

<style lang="scss" scoped>
.bottom-button-wrap {
    height: 86px;
    // margin-top: 46px;
    width: 100%;
    background-color: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    // padding-right: 32px;
    box-sizing: border-box;
}

.company-wrap {
    height: 20px;
    display: flex;
    align-items: center;
    font-weight: 400;
    font-size: 20px;
    .company-brief {
        color: #505363
    }
    .company-name {
        color: #12151A;
    }
}
.balance-wrap {
    height: 40px;
    width: 100%;
    margin-top: 24px;
    padding: 0 16px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    background-color: #EBF3FF;
    margin-bottom: 24px;
    .balance-item {
        flex: 1;
        display: flex;
        align-items: center;
        .balance-label {
            font-weight: 400;
            font-size: 16px;
            color: #505363;
        }
        .balance-value {
            font-weight: 400;
            font-size: 16px;
            color: #12151A  ;
        }
    }
   

}

.balance-wrap-after {
    height: 40px;
    margin: 12px 0;
    width: 100%;
    padding: 0 16px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    background-color: #EBF3FF;
    margin-bottom: 24px;
    .balance-item {
        flex: 1;
        display: flex;
        align-items: center;
        .balance-label {
            font-weight: 400;
            font-size: 16px;
            color: #505363;
        }
        .balance-value {
            font-weight: 400;
            font-size: 16px;
            color: #217AFF  ;
        }
    }
   

}
  </style>
  