<template>
    <el-dialog title="账户设置" :visible.sync="dialogVisible"  width="630px">
        <el-form :model="form" :rules="rules" ref="accountForm"  label-position="top">
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item
                        label="银行卡开户行："
                        prop="openBank"
                        :label-width="formLabelWidth"
                    >
                        <el-input
                            v-model="form.openBank"
                            placeholder="请输入"
                            style="width:100%"
                        ></el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="24">
                    <el-form-item
                        label="银行卡账户："
                        prop="bankAccount"
                        :label-width="formLabelWidth"
                    >
                        <el-input
                            v-model="form.bankAccount"
                            placeholder="请输入数字"
                            @input="formatBankCard"
                            maxlength="23"
                        ></el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="24">
                    <el-form-item
                        label="开户行支行："
                        prop="subBranch"
                        :label-width="formLabelWidth"
                    >
                        <el-input
                            v-model="form.subBranch"
                            placeholder="请输入"
                            style="width:100%"
                        ></el-input>
                    </el-form-item>
                </el-col>

                

                <el-col :span="24">
                    <el-form-item
                        label="备注"
                        prop="remark"
                        :label-width="formLabelWidth"
                    >
                        <el-input
                            v-model="form.remark"
                            type="textarea"
                            :rows="3"
                            placeholder="请输入备注"
                        ></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form> 

        <div slot="footer" class="dialog-footer">
            <el-button @click="handleCancel">取 消</el-button>
            <el-button type="primary" @click="handleConfirm">确 定</el-button>
        </div>
    </el-dialog>
   
</template>
<script>  

import _ from 'lodash';

import {bindBankCard} from '@/api/operator/index'

import {
    enterpriseSet
} from '@/api/user/enterprise'

  export default {
    props: {
        editInfo: {
            type: Object,
            default: () => {},
        },
    },
    dicts: [
        'ls_order_except_level',
        'ls_order_except_type',
    ],
    components: {
        
    },
    data() {
         // 验证规则
        const validateBankCard = (rule, value, callback) => {
            const cleanValue = value.replace(/\s+/g, ''); // 移除所有空格
            if (!/^\d{16,19}$/.test(cleanValue)) {
                callback(new Error('请输入有效的16-19位银行卡号'));
            } else {
                callback();
            }
        };
        return {
            dialogVisible: false,
            operatorAccountId: '',

            formLabelWidth: '120px',
            form: {
                openBank: '',
                bankAccount: '',
                subBranch: '',
                remark: '',
            },
            rules: {
                openBank: [
                    { required: true, message: '请选择银行卡开户行', trigger: 'blur' },
                ],
                bankAccount: [
                    { required: true, message: '请输入银行卡账号', trigger: 'blur' },
                    { validator: validateBankCard, trigger: 'blur' }
                ],
                subBranch: [
                    { required: true, message: '请选择开户行支行', trigger: 'blur' },
                ]
            }
        };
    },
    watch: {
        
    },
    computed: {
        
    },
    mounted() {
       
    },
    methods: {
        formatBankCard() {
            // 1. 移除非数字字符
            let cleanValue = this.form.bankAccount.replace(/\D/g, '');
            
            // 2. 每4位添加空格
            let formatted = cleanValue.replace(/(\d{4})(?=\d)/g, '$1 ');
            
            // 3. 更新绑定值（自动处理光标位置）
            this.form.bankAccount = formatted;
        },

        // 新增按钮防抖        
        handleConfirm: _.debounce(function() {
            this.$refs.accountForm.validate(async (valid) => {
                if (valid) {
                    console.log(this.form, 'this.form')
                    const {
                        openBank,
                        bankAccount,
                        subBranch,
                        remark,
                    } = this.form;
                    
                    const params = {
                        operatorAccountId: this.operatorAccountId,
                        cardBank: openBank,
                        cardBankBranch: subBranch,
                        cardNo: bankAccount.replaceAll(' ', ''),
                        remark,
                    }
                    const [err, res] = await bindBankCard(params);
                    if (err) return;
                    
                    this.$message.success('绑定成功');
                    this.dialogVisible = false;
                    this.handleResetData()
                    this.$emit('loadData');
                    
                }
            });
        }, 300) ,

        handleCancel() {
            this.dialogVisible = false;
            this.handleResetData()
        },

        handleResetData() {
            this.form =  {
                openBank: '',
                bankAccount: '',
                subBranch: '',
                remark: '',
            }
            this.operatorAccountId = ''
        }

    },
  };
  </script>

<style lang="scss" scoped>
.bottom-button-wrap {
    height: 86px;
    // margin-top: 46px;
    width: 100%;
    background-color: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    // padding-right: 32px;
    box-sizing: border-box;
}

  </style>
  