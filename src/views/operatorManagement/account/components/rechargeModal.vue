<template>
    <el-dialog title="账户提现" :visible.sync="dialogVisible" width="630px">
        <el-form :model="form" :rules="rules" ref="accountForm"  label-position="top">
            <div class="company-wrap">
                <div class="company-brief">运营商名称：</div>
                <div class="company-name">{{ operatorName }}</div>
            </div>
            <div class="balance-wrap">
                <div class="balance-item">
                    <div class="balance-label">当前账户余额（元）：</div>
                    <div class="balance-value">{{  balance }}</div>
                </div>
            </div>

            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item
                        label="充值金额："
                        prop="rechargeAmount"
                        :label-width="formLabelWidth"
                    >
                    <div style="display: flex;">
                        <el-input-number
                            style="width: 100%;"
                            v-model="form.rechargeAmount"
                            :min="0"
                            :precision="2"
                            :step="0.01"
                            :controls="false"
                            placeholder="请输入数字"
                            @change="handleWidthdrawalAmountChange"
                        ></el-input-number>
                        <div style="margin-left: 8px;">元</div>
                    </div>
                    </el-form-item>
                </el-col>

                <el-col :span="24">
                    <div class="balance-wrap-after">
                        <div class="balance-item">
                            <div class="balance-label">充值后帐户余额（元）：</div>
                            <div class="balance-value">{{  afterBalance }}</div>
                        </div>
                       
                    </div>
                </el-col>


                <el-col :span="24">
                    <el-form-item label="分成编号" prop="number">
                        <el-input
                            v-model="form.number"
                            placeholder="请输入汇总清分编号"
                        ></el-input>
                    </el-form-item>
                </el-col>

                <el-col :span="24">
                    <el-form-item
                        label="备注"
                        prop="remark"
                        :label-width="formLabelWidth"
                    >
                        <el-input
                            v-model="form.remark"
                            type="textarea"
                            :rows="3"
                            placeholder="请输入备注"
                        ></el-input>
                    </el-form-item>
                </el-col>
            </el-row>
        </el-form> 

        <div slot="footer" class="dialog-footer">
            <el-button @click="handleCancel">取 消</el-button>
            <el-button type="primary" @click="handleConfirm">确 定</el-button>
        </div>
    </el-dialog>
   
</template>
<script>  

import {
    rechargeOperators
} from '@/api/operator/index'

import _ from 'lodash';

import {
    enterpriseRecharge
} from '@/api/user/enterprise'
  export default {
    props: {
        editInfo: {
            type: Object,
            default: () => {},
        },
    },
    dicts: [
        'ls_order_except_level',
        'ls_order_except_type',
    ],
    components: {

    },
    data() {
        return {
            dialogVisible: false,
            operatorName: '',
            operatorNo: '',
            operatorAccountId: '',
            balance: '',
            afterBalance: '',

            formLabelWidth: '120px',
            form: {
                rechargeAmount: '',
                number:'',
                remark: '',
            },
            rules: {
                rechargeAmount: [
                    { required: true, message: '请输入充值金额', trigger: 'blur' },
                ],

            }
        };
    },
    watch: {
        
    },
    computed: {
        
    },
    mounted() {
       this.handleWidthdrawalAmountChange();    
    },
    methods: {
        // 新增按钮防抖        
        handleConfirm: _.debounce(function() {
            this.$refs.accountForm.validate(async (valid) => {
                if (valid) {
                    console.log(this.form, 'this.form')
                    const {
                        rechargeAmount,
                        number,
                        remark,
                    } = this.form;
                    
                    const params = {
                        operatorNo: this.operatorNo,
                        operatorAccountId: this.operatorAccountId,
                        amount: rechargeAmount,
                        remark: remark,
                        clearNo: number,
                    }
                    const [err, res] = await rechargeOperators(params);
                    if (err) return;
                    
                    this.$message.success('充值成功');
                    this.dialogVisible = false;
                    this.userId  = '';
                    this.handleResetData()
                    this.$emit('loadData');
                    
                }
            });
        }, 300) ,
        handleCancel() {
            this.dialogVisible = false;
            this.handleResetData()
        },

        handleWidthdrawalAmountChange() {
            this.afterBalance = Number(this.balance) + (Number(this.form.rechargeAmount)?Number(this.form.rechargeAmount):0);
            
        },


        handleResetData() {
            this.form =  {
                rechargeAmount: '',
                number:'',
                remark: '',
            }
            this.operatorName = '',
            this.operatorNo = '',
            this.operatorAccountId = '',
            this.balance = '',
            this.afterBalance = ''
            
        }
    },
};
  </script>

<style lang="scss" scoped>
.bottom-button-wrap {
    height: 86px;
    // margin-top: 46px;
    width: 100%;
    background-color: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    // padding-right: 32px;
    box-sizing: border-box;
}

.company-wrap {
    height: 20px;
    display: flex;
    align-items: center;
    font-weight: 400;
    font-size: 20px;
    .company-brief {
        color: #505363
    }
    .company-name {
        color: #12151A;
    }
}
.balance-wrap {
    height: 40px;
    width: 100%;
    margin-top: 24px;
    padding: 0 16px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    background-color: #EBF3FF;
    margin-bottom: 24px;
    .balance-item {
        flex: 1;
        display: flex;
        align-items: center;
        .balance-label {
            font-weight: 400;
            font-size: 16px;
            color: #505363;
        }
        .balance-value {
            font-weight: 400;
            font-size: 16px;
            color: #12151A  ;
        }
    }
   

}

.balance-wrap-after {
    height: 40px;
    margin: 12px 0;
    width: 100%;
    padding: 0 16px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    background-color: #EBF3FF;
    margin-bottom: 24px;
    .balance-item {
        flex: 1;
        display: flex;
        align-items: center;
        .balance-label {
            font-weight: 400;
            font-size: 16px;
            color: #505363;
        }
        .balance-value {
            font-weight: 400;
            font-size: 16px;
            color: #217AFF  ;
        }
    }
   

}
  </style>
  