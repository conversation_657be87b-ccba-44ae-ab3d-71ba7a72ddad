<template>
    <div class="container container-float " style="padding: 0 0 100px 0;">
        <div class="device-head">
            <img src="@/assets/operator/operator-detail-top-icon.png" class="device-head-icon">
            
            <div class="device-info-wrap">
                <div class="device-title-wrap">
                    <div class="device-title">{{ operatorName }}</div>
                    <div class="device-status">{{ operatorStatus }}</div>
                </div>
                <div class="device-info-wrap">
                    <el-row>
                        <el-col :span="8">
                            <span class="label">运营商简称：</span>
                            <span class="value">{{shortname}}</span>
                        </el-col>
                     
                    </el-row>
                </div>
            </div>
        </div>

        <div class="info-card" >
            <div class="card-head" >
                <div class="before-icon"></div>
                <div class="card-head-text">基础信息</div>
            </div>

            <div class="form-wrap">
                <el-row :gutter="20">
                    <el-col :span="8" v-for="(item, key) in baseInfo" :key="key" style="margin-bottom: 24px;">
                        <div style="display: flex;">
                            <div class="info-title">{{labels.baseInfo[key]}}：</div>
                            <div class="info-detail">{{ item }}</div>
                        </div>
                    </el-col>

                    <el-col :span="24" style="margin-bottom: 24px;">
                        <div style="display: flex;">
                            <div class="info-title">注册地址：</div>
                            <div class="info-detail info-detail-overflow">{{ address }}</div>
                        </div>
                    </el-col>

                    <el-col :span="24" style="margin-bottom: 24px;">
                        <div style="display: flex;">
                            <div class="info-title">备注说明：</div>
                            <div class="info-detail info-detail-overflow">{{ remark }}</div>
                        </div>
                    </el-col>

                    <el-col  style="margin-bottom: 24px;" >
                        <div style="display: flex;">
                            <div class="info-title"> 资质图片：</div>
                            <Preview
                                :src="businessLicensePicture"
                            ></Preview>
                           
                        </div>
                    </el-col>
                </el-row>
            </div>
        </div>

        <div class="info-card" >
            <div class="card-head" >
                <div class="before-icon"></div>
                <div class="card-head-text">接入信息</div>
            </div>

            <div class="form-wrap">
                <el-row :gutter="20">
                    <el-col :span="8" v-for="(item, key) in platformInfo" :key="key" style="margin-bottom: 24px;">
                        <div style="display: flex;">
                            <div class="info-title">{{labels.platformInfo[key]}}：</div>
                            <div class="info-detail">{{ item }}</div>
                        </div>
                    </el-col>
                </el-row>
            </div>
        </div>

         


    </div>
    
  </template>
  
  <script>

import {
  getOperatorsDetail,
} from '@/api/operator/index'

import Preview from '@/components/Preview/index.vue'
  
    export default {
    components: {
        Preview
    },
    dicts: [
        'ls_charging_operator_category',
        'ls_charging_operator_link_biz',
        'ls_charging_operator_status',
    ],
    data() {
      return {
        operatorName: '',
        operatorStatus: '',
        shortname: '',

        labels: {
            baseInfo: {
                socialCreditCode: '统一社会信用代码',
                legalRepresentative: '法人',
                legalRepresentativeCode: '法人身份证',
                contact: '联系人',
                contactTel: '联系电话',
                // address: '注册地址',
                // remark: '备注说明',
            },
            platformInfo: {
                operatorCategoryList:'运营商类型',
                linkBizTypeList: '接入业务类型',
                contractId: '关联合同',
            }
        },

        baseInfo: {
            socialCreditCode: '',
            legalRepresentative: '',
            legalRepresentativeCode: '',
            contact: '',
            contactTel: '',
            address: '',
            remark: '',
        },

        platformInfo: {
            operatorCategoryList: '',
            linkBizTypeList: '',
            contractId: '',
        },
        businessLicensePicture: '',
        remark: '',
        address: '',
      };
    },

    computed: {
    },
    mounted() {
        const operatorId = this.$route.query.operatorId;
        this.getOperatorDetail(operatorId);
    },
    methods: {
        // 获取运营商详情
    async getOperatorDetail(operatorId) {
      const [err, res] = await getOperatorsDetail({
        operatorId,
      })

      if (err) return

      // 基本信息

      const {
        operatorName,
        shortname,
        socialCreditCode,
        legalRepresentative,
        legalRepresentativeCode,
        contact,
        contactTel,
        address,
        operatorStatus,
        businessLicensePicture,
        remark, 
        operatorCategoryList,
        linkBizTypeList,
        contractId, 
      } = res.data

      this.operatorName = operatorName
      this.operatorStatus = this.selectDictLabel(
                    this.dict.type.ls_charging_operator_status,
                    operatorStatus
                  );
      this.shortname = shortname

      this.baseInfo = {
        socialCreditCode,
        legalRepresentative,
        legalRepresentativeCode,
        contact,
        contactTel,
        // address,
        // remark,
      }

      const operator = []
      operatorCategoryList.forEach(element => {
        operator.push(
            this.selectDictLabel(
                    this.dict.type.ls_charging_operator_category,
                    element
                  )
        )
      });

      const link = []
      linkBizTypeList.forEach(element => {
        link.push(
            this.selectDictLabel(
                    this.dict.type.ls_charging_operator_link_biz,
                    element
                  )
        )
      });

      this.platformInfo = {
        operatorCategoryList: operator.join(' '),
        linkBizTypeList: link.join(' '),
        contractId, 
      }

      this.remark = remark
      this.address = address

      this.businessLicensePicture  = businessLicensePicture

    },
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .container-full {
    background-color: rgba(244, 246, 249, 1);
    padding-bottom: 16px;
    min-height: calc(100% - 80px);
    overflow-y: auto;
  }

  .device-head {
    background-color: #fff;
    
    display: flex;
    height: 112px;
    display: flex;
    align-items: center;
    padding: 0 24px;
    box-sizing: border-box;
    .device-head-icon {
        width: 48px;
        height: 48px;
        margin-right: 24px;
    }
    .device-info-wrap {
        flex: 1;
        .device-title-wrap {
            height: 32px;
            display: flex;
            align-items: center;
            .device-title {
                font-weight: 500;
                font-size: 24px;
                color: #12151A;
            }
            .device-status {
                // width: 50px;
                padding: 0 10px;
                box-sizing: border-box;
                height: 24px;
                border-radius: 10px 0 10px 0;
                font-size: 16px;
                font-weight: 400;
                line-height: 24px;
                text-align: center;
                color: #fff;
                background: linear-gradient(321.01deg, #00C864 8.79%, #38F3CA 100.27%);
                margin-left: 12px;
            }
        }
        .device-info-wrap {
            height: 16px;
            margin-top: 16px;
            font-size: 16px;
            font-weight: 400;
            color: #292B33;
        }
    }
    
  
  }

  .info-card {
    margin: 16px;
    border-radius: 5px;
    border: 1px solid #fff;
    overflow: hidden;
    background-color: #fff;
    // min-height: 300px;
    .card-head {
        height: 56px;
        padding: 0 16px;
        display: flex;
        align-items: center;
        background: linear-gradient(180deg, #E9F2FF 0%, #FFFFFF 100%);
        .before-icon {
            width: 3px;
            height: 16px;
            background-image: url('~@/assets/station/consno-before.png');
            background-size: 100% 100%;
            background-repeat: no-repeat;
            margin-right: 8px;
        }
        .card-head-text {
            flex:1;
            font-weight: 500;
            font-size: 16px;
            color: #12151A;

        &::before {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            left: -3px; /* 调整这个值来改变边框的宽度 */
            width: 0;
            border-top: 3px solid transparent;
            border-bottom: 3px solid transparent;
            border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
        }

    
        }

    }


    .form-wrap {
      padding: 0 24px 0 24px;
      .info-title {
        font-weight: 400;
        font-size: 16px;
        line-height: 16px;
        color: #505363;
      }
      .info-detail {
        font-weight: 400;
        font-size: 16px;
        line-height: 16px;
        color: #292B33;
      }
      .info-detail-overflow {
        max-width: 90%;
        word-wrap: break-word;
        word-break: break-all;
      }
      .info-amount {
        height: 28px;
        background-color: #FFF7E6;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 8px;
        font-weight: 400;
        font-size: 20px;
        color: #FE8921;
        margin-top: -6px;
        margin-right: 4px;

      }

    }

    .table-wrap {
        padding: 8px 24px 24px 24px;
    }

  }
  </style>
  