<template>
  <div class="container container-float" style="padding: 0">
    <!-- 基本信息 -->
    <div class="info-card">
      <div class="card-head">
        <div class="before-icon"></div>
        <div class="card-head-text">基本信息</div>
      </div>
      <div class="form-wrap">
        <el-form
          :model="baseInfo.form"
          :rules="baseInfo.rules"
          ref="baseInfoForm"
          label-position="top"
        >
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                label="运营商名称"
                prop="operatorName"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-model="baseInfo.form.operatorName"
                  placeholder="请输入"
                  maxLength="32"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="运营商简称"
                prop="shortname"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-model="baseInfo.form.shortname"
                  placeholder="请输入"
                  maxLength="16"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="统一社会信用代码"
                prop="socialCreditCode"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-model="baseInfo.form.socialCreditCode"
                  placeholder="请输入"
                   maxLength="18"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                label="法人"
                prop="legalRepresentative"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-model="baseInfo.form.legalRepresentative"
                  placeholder="请输入"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="法人身份证"
                prop="legalRepresentativeCode"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-model="baseInfo.form.legalRepresentativeCode"
                  placeholder="请输入"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="联系人"
                prop="contactPerson"
                :label-width="formLabelWidth" 
              >
                <el-input
                  v-model="baseInfo.form.contactPerson"
                  placeholder="请输入"
                  maxLength="16"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="联系电话"
                prop="contactTel"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-model="baseInfo.form.contactTel"
                  placeholder="请输入"
                  maxLength="11"
                ></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="16">
              <el-form-item
                label="注册地址"
                prop="registeredAddress"
                :label-width="formLabelWidth"
              >
                <!-- <el-cascader
                  v-model="baseInfo.form.registeredAddress"
                  :options="[]"
                  placeholder="请选择"
                  style="width: 40%; margin-right: 10px"
                ></el-cascader> -->
                <!-- <el-input
                  v-model="baseInfo.form.detailAddress"
                  placeholder="请输入详细地址"
                  style="width: 58%"
                ></el-input> -->
                <el-input
                  v-model="baseInfo.form.registeredAddress"
                  placeholder="请输入详细地址"
                  maxLength="256"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
           
            <el-col :span="8">
              <el-form-item
                label="运营商状态"
                prop="operatorStatus"
                :label-width="formLabelWidth"
              >
                <el-radio-group v-model="baseInfo.form.operatorStatus">
                  <el-radio
                    v-for="item in dict.type.ls_charging_operator_status"
                    :key="item.value"
                    :label="item.value"
                  >{{ item.label }}</el-radio>
                  
                  <!-- <el-radio :label="1">正常</el-radio>
                  <el-radio :label="0">冻结</el-radio> -->
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item
                label="资质图片"
                prop="businessLicensePicture"
                :label-width="formLabelWidth"
              >
                <Upload
                  v-model="baseInfo.form.businessLicensePicture"
                  @input="handleUpload"
                  @success="handleUploadSuccess"
                />
                <!-- <el-upload
                  class="upload-demo"
                  :action="'/vehicle-grid-system/upload/single'"
                   :headers="uploadHeaders"
                  :limit="1"
                  :on-exceed="handleExceed"
                  :file-list="fileList"
                >
                  <div class="upload-area">
                    <i class="el-icon-upload"></i>
                    <div class="el-upload__text">
                      点击或将文件拖拽到这里上传
                    </div>
                    <div class="el-upload__tip">
                      支持格式：jpg/png/jpeg，大小不超过5M
                    </div>
                  </div>
                </el-upload> -->

              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item
                label="备注说明"
                prop="remark"
                :label-width="formLabelWidth"
              >
                <el-input
                  type="textarea"
                  v-model="baseInfo.form.remark"
                  :rows="4"
                  placeholder="请输入备注说明"
                  maxLength="255"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <!-- 接入信息 -->
    <div class="info-card">
      <div class="card-head">
        <div class="before-icon"></div>
        <div class="card-head-text">接入信息</div>
      </div>
      <div class="form-wrap">
        <el-form
          :model="platformInfo.form"
          :rules="platformInfo.rules"
          ref="platformInfoForm"
          label-position="top"
        >
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item
                label="运营商类型"
                prop="operatorCategoryList"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="platformInfo.form.operatorCategoryList"
                  placeholder="请选择"
                  style="width: 100%"
                  :multiple="true"
                >

                  <el-option
                      v-for="item in this.dict.type.ls_charging_operator_category"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="接入业务类型"
                prop="linkBizTypeList"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="platformInfo.form.linkBizTypeList"
                  placeholder="请选择"
                  style="width: 100%"
                   :multiple="true"
                >
                  <el-option
                    v-for="item in dict.type.ls_charging_operator_link_biz"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item
                label="关联合同"
                prop="contractId"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="platformInfo.form.contractId"
                  placeholder="请选择"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in contractList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="bottom-button-wrap">
      <div>
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSave">确定</el-button>
      </div>
    </div>
  </div>
</template>

<script>

import {
  createOperators,
  getOperatorsDetail,
  updateOperators,
  
} from '@/api/operator/index'

import { getToken } from "@/utils/auth";

import  Upload from '@/components/Upload/index'

export default {
  name: 'InterconnectionCreate',
  dicts: [
    'ls_charging_operator_category',
    'ls_charging_operator_link_biz',
    'ls_charging_operator_status',
  ],
  components: {
    Upload,
  },
  data() {
    return {
      uploadHeaders: {
        Authorization: 'Bearer ' + getToken(),
      },
      formLabelWidth: '120px',
      pageType: 'create',
      operatorId: '',
      baseInfo: {
        form: {
          operatorName: '',
          shortname: '',
          socialCreditCode: '',
          legalRepresentative: '',
          legalRepresentativeCode: '',
          contactPerson: '',
          contactTel: '',
          registeredAddress: '',
          // registeredAddress: [],
          // detailAddress: '',
          operatorStatus: 1,
          businessLicensePicture: [],
          remark: '',
        },
        rules: {
          operatorName: [
            { required: true, message: '请输入运营商名称', trigger: 'blur' },
          ],
          shortname: [
            { required: true, message: '请输入运营商简称', trigger: 'blur' },
          ],
          socialCreditCode: [
            {
              required: true,
              message: '请输入统一社会信用代码',
              trigger: 'blur',
            },
          ],
          legalRepresentative: [
            { required: true, message: '请输入法人', trigger: 'blur' },
          ],
          legalRepresentativeCode: [
            { required: true, message: '请输入法人身份证号', trigger: 'blur' },
          ],
          contactPerson: [
            { required: true, message: '请输入联系人', trigger: 'blur' },
          ],
          contactTel: [
            { required: true, message: '请输入联系电话', trigger: 'blur' },
          ],
          registeredAddress: [
            { required: true, message: '请输入地址', trigger: 'change' },
          ],
          // detailAddress: [
          //   { required: true, message: '请输入详细地址', trigger: 'blur' },
          // ],
          operatorStatus: [
            { required: true, message: '请选择运营商状态', trigger: 'change' },
          ],
          businessLicensePicture: [
            { required: true, message: '请上传资质图片', trigger: 'change' },
          ]
        },
      },
      platformInfo: {
        form: {
          // operatorId: '',
          operatorCategoryList: [],
          linkBizTypeList: [],
          contractId: '',
        },
        rules: {
          // operatorId: [
          //   { required: true, message: '请输入运营商ID', trigger: 'blur' },
          // ],
          operatorCategoryList: [
            { required: true, message: '请选择运营商类型', trigger: 'change' },
          ],
          linkBizTypeList: [
            {
              required: true,
              message: '请选择接入业务类型',
              trigger: 'change',
            },
          ],
          // contractId: [
          //   { required: true, message: '请选择关联合同', trigger: 'change' },
          // ],
        },
      },
      operatorTypeList: [],
      businessTypeList: [],
      contractList: [],
    };
  },
  mounted() {
    const operatorId = this.$route.query.operatorId;
    if (operatorId) {
      this.pageType = 'edit';
      this.operatorId = operatorId;
      this.getOperatorDetail();
    }
  },
  methods: {
    handleUpload(file) {
      console.log('上传中:', file)
    },
    handleUploadSuccess(file) {
      console.log('上传成功:', file)
    },
    handleExceed() {
      this.$message.warning('只能上传1张图片');
    },
    handleCancel() {
      this.$router.back();
    },
    async handleSave() {
      try {
        await Promise.all([
          this.$refs.baseInfoForm.validate(),
          this.$refs.platformInfoForm.validate(),
        ]);

        const {
          operatorName,
          shortname,
          socialCreditCode,
          legalRepresentative,
          legalRepresentativeCode,
          contactPerson,
          contactTel,
          registeredAddress,
          operatorStatus,
          businessLicensePicture,
          remark,
        } = this.baseInfo.form;

        const {
          operatorCategoryList,
          linkBizTypeList,
          contractId,
        } = this.platformInfo.form;

        let picUrl = ''
        if(businessLicensePicture.length) {
          picUrl = businessLicensePicture[0].url
        }

        const params = {
          operatorName,
          shortname,
          socialCreditCode,
          legalRepresentative,
          legalRepresentativeCode,
          contact: contactPerson,
          contactTel,
          address: registeredAddress,
          operatorStatus,
          businessLicensePicture: picUrl,
          remark,
          operatorCategoryList,
          linkBizTypeList,
          contractId,
        }

        console.log(params,this.pageType, 'params');

        if(this.pageType === 'create') {
          const [err,res] = await createOperators(
            params
          )

          if (err){ 
            return this.$message.error(err.message || '新增运营商失败');
          }
          this.$message.success('新增成功');
          
          setTimeout(() => {
            this.$router.back();
          }, 2000)

        } else if(this.pageType === 'edit') { 
          const [err,res] = await updateOperators(
            {
              ...params,
              operatorId: this.operatorId
            }
          )

          if (err){ 
            return this.$message.error(err.message || '修改运营商失败');
          }
          this.$message.success('修改成功');
          
          setTimeout(() => {
            this.$router.back();
          }, 2000)
        }


      } catch (error) {
        console.log(error, 'error');
        this.$message.error('表单校验失败，请检查输入');
      }
    },

    // 获取运营商详情
    async getOperatorDetail() {
      const [err, res] = await getOperatorsDetail({
        operatorId: this.operatorId,
      })

      if (err) return

      // 基本信息

      const {
        operatorName,
        shortname,
        socialCreditCode,
        legalRepresentative,
        legalRepresentativeCode,
        contact,
        contactTel,
        address,
        operatorStatus,
        businessLicensePicture,
        remark, 
      } = res.data

      this.baseInfo.form = {
        operatorName,
          shortname,
          socialCreditCode,
          legalRepresentative,
          legalRepresentativeCode,
          contactPerson: contact,
          contactTel,
          registeredAddress: address,
          operatorStatus,
          businessLicensePicture: [
            {url: businessLicensePicture,}
          ],
          remark,
      }

      // 接入信息
      const {
        operatorCategoryList,
        linkBizTypeList,
        contractId, 
      }  = res.data
    
      this.platformInfo.form = {
        operatorCategoryList,
        linkBizTypeList,
        contractId, 
      }

    }
  },
};
</script>

<style lang="scss" scoped>
.info-card {
  margin: 16px;
  border-radius: 5px;
  border: 1px solid #fff;
  overflow: hidden;
  background-color: #fff;

  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;

    .before-icon {
      width: 3px;
      height: 16px;
      background-image: url('~@/assets/station/consno-before.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin-right: 8px;
    }

    .card-head-text {
      flex: 1;
      font-weight: 500;
      font-size: 16px;
      color: #12151a;
    }
  }

  .form-wrap {
    padding: 0 16px 16px 16px;
  }
}

.interface-section {
  margin: 16px 0;

  .el-checkbox-group {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 16px;
  }

  .el-checkbox {
    margin-right: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.bottom-button-wrap {
  height: 86px;
  margin-top: 16px;
  width: 100%;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: 32px;
  box-sizing: border-box;
}

::v-deep .el-input {
  width: 100%;
}
</style>
