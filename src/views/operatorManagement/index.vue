<template>
  <div class="container">
    <div class="table-wrap">
      <BuseCrud
        ref="crud"
        :loading="loading"
        :filterOptions="filterOptions"
        :tablePage="tablePage"
        :tableColumn="tableColumn"
        :tableData="tableData"
        :pagerProps="pagerProps"
        :modalConfig="modalConfig"
        @loadData="loadData"
      >
        <template slot="defaultHeader">
          <div class="card-head">
            <div class="card-head-text">运营商管理列表</div>
            <div class="top-button-wrap">
              <el-button type="primary" icon="el-icon-plus" @click="handleAdd">
                新增运营商信息
              </el-button>
            </div>
          </div>
          <div class="card-head-after"></div>
        </template>

        <template slot="operate" slot-scope="{ row }">
          <div class="menu-box">
            <el-button type="primary" plain @click="handleDetail(row)">
              详情
            </el-button>
            <el-button type="primary" plain @click="handleEdit(row)">
              修改
            </el-button>
            <!-- <el-button
              type="primary"
              plain
              @click="handleAccountManagement(row)"
            >
              账户管理
            </el-button> -->
            <el-button type="danger" plain @click="handleDelete(row)">
              删除
            </el-button>
          </div>
        </template>
      </BuseCrud>
    </div>
  </div>
</template>

<script>

import {
  getOperatorsList
} from '@/api/operator/index'
export default {
  name: 'InterconnectionManagement',
  dicts: [
    'ls_charging_operator_category',
    'ls_charging_operator_link_biz',
    'ls_charging_operator_status',

  ],
  data() {
    return {
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [],
      tableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
        },
        {
          field: 'operatorCode',
          title: '运营商ID',
          minWidth: 120,
        },
        {
          field: 'operatorName',
          title: '运营商名称',
          minWidth: 120,
        },
        {
          field: 'stationNum',
          title: '充电站数量',
          minWidth: 120,
        },
        {
          field: 'pileNum',
          title: '充电桩数量',
          minWidth: 120,
        },
        {
          field: 'totalRatePower',
          title: '总额定功率\n(kW)',
          minWidth: 120,
        },
        {
          field: 'operatorCategoryName',
          title: '运营商类型',
          minWidth: 120,
        },
        {
          field: 'linkBizTypeName',
          title: '接入业务类型',
          minWidth: 120,
        },
        {
          field: 'operatorStatus',
          title: '运营商状态',
          minWidth: 120,
          formatter: ({ cellValue }) => {
                  return this.selectDictLabel(
                    this.dict.type.ls_charging_operator_status,
                    cellValue
                  );
                },
        },
        {
          field: 'contractInfo', //  todo 更新字段
          title: '关联合同',
          minWidth: 120,
        },
        {
          field: 'updateTime',
          title: '更新时间',
          minWidth: 200,
        },
        {
          title: '操作',
          slots: { default: 'operate' },
          width: 300,
          align: 'center',
          fixed: 'right',
        },
      ],
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      params: {
        operatorId: '',
        operatorName: '',
        operatorType: '',
        businessType: '',
        updateTime: [],
      },
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'operatorId',
            title: '运营商ID',
            element: 'el-input',
            placeholder: '请输入',
          },
          {
            field: 'operatorName',
            title: '运营商名称',
            element: 'el-input',
            placeholder: '请输入',
          },
          {
            field: 'operatorType',
            title: '运营商类型',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_charging_operator_category,
            },
          },
          {
            field: 'businessType',
            title: '接入业务类型',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.dict.type.ls_charging_operator_link_biz,
            },
          },
          {
            field: 'updateTime',
            title: '更新时间',
            element: 'el-date-picker',
            props: {
              type: 'daterange',
              startPlaceholder: '开始日期',
              endPlaceholder: '结束日期',
              valueFormat: 'yyyy-MM-dd',
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  mounted() {
    this.loadData()
  },
  methods: {
    async loadData() {
      const  {
        operatorId,
        operatorName,
        operatorType,
        businessType,
        updateTime,
      } = this.params;

      let updateTimeLeft = ''
      let updateTimeRight = ''
      if(updateTime && updateTime.length > 0) {
          updateTimeLeft = updateTime[0]
          updateTimeRight = updateTime[1]
      }

      let operatorCategoryList = []
      if(operatorType) {
        operatorCategoryList.push(operatorType)
      }

      let linkBizTypeList = []
      if(businessType) {
        linkBizTypeList.push(businessType)
      }

      const params = {
        operatorCode: operatorId,
        operatorName,
        operatorCategoryList,
        linkBizTypeList,
        updateTimeLeft,
        updateTimeRight,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      }


      this.loading = true;

      const [err, res] = await getOperatorsList(params)

      this.loading = false;
      
      if (err) return 
        const { data, total } = res;


        this.tableData = data;
        this.tablePage.total = total;

    },

    handleAdd() {
      this.$router.push({
        path: '/v2g-charging-web/baseInfo/operator/create',
      });
    },

    handleEdit(row) {
      const {
        operatorId
      } = row;
      this.$router.push({
        path: '/v2g-charging-web/baseInfo/operator/create',
        query: {
          operatorId
        }
      });
    },

    handleDetail(row) {
      const {
        operatorId
      } = row;
      this.$router.push({
        path: '/v2g-charging-web/baseInfo/operator/detail',
        query: {
          operatorId
        }
      });
    },

    handleAccountManagement(row) {
      this.$router.push({
        path: '/v2g-charging-web/baseInfo/operator/account',
      });
    },

    handleDelete(row) {
      
    },
  },
};
</script>

<style lang="scss" scoped>
.table-wrap {
  background: #fff;
  padding: 20px;
  border-radius: 4px;

  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }

  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }

  .menu-box {
    .el-button {
      padding: 5px;
    }
  }
}
</style>
