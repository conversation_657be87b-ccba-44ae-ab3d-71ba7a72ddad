<template>
  <div class="container">
    <div class="table-wrap">
      <BuseCrud
        ref="crud"
        :loading="loading"
        :filterOptions="filterOptions"
        :tablePage="tablePage"
        :tableColumn="tableColumn"
        :tableData="tableData"
        :pagerProps="pagerProps"
        :modalConfig="modalConfig"
        @loadData="loadData"
      ></BuseCrud>
    </div>
  </div>
</template>

<script>
import { getStationList } from '@/api/pile/index';

export default {
  data() {
    return {
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [],
      tableColumn: [
        {
          type: 'seq',
          title: '序号',
          minWidth: 60, // 最小宽度
        },
        {
          field: '',
          title: '预警编号',
          minWidth: 120, // 最小宽度
        },
        {
          field: '',
          title: '预警时间',
          minWidth: 120, // 最小宽度
        },
        {
          field: '',
          title: '预警通知内容',
          minWidth: 120, // 最小宽度
        },
        {
          field: '',
          title: '充电桩编号',
          minWidth: 120, // 最小宽度
        },
        {
          field: '',
          title: '充电桩名称',
          minWidth: 120, // 最小宽度
        },

        {
          field: '',
          title: '所属充电站',
          minWidth: 120, // 最小宽度
        },
        {
          field: '',
          title: '预警通知人',
          minWidth: 120, // 最小宽度
        },
        {
          field: '',
          title: '计费编号',
          minWidth: 120, // 最小宽度
        },
        {
          field: '',
          title: '下发人',
          minWidth: 120, // 最小宽度
        },
        {
          field: '',
          title: '下发时间',
          minWidth: 120, // 最小宽度
        },
      ],
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      params: {
        warningTime: '',
        issuedBy: '',
        warningNotifier: '',
        billingNumber: '',
        chargingPileNumber: '',
        chargingPileName: '',
        stationId: '',
      },
      stationList: [],
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'warningTime',
            title: '预警时间',
            element: 'el-date-picker',
            props: {
              type: 'datetimerange',
              rangeSeparator: '至',
              startPlaceholder: '开始日期',
              endPlaceholder: '结束日期',
              valueFormat: 'YYYY-MM-DD HH:mm:ss',
            },
          },
          {
            field: 'issuedBy',
            title: '下发人',
            element: 'el-input',
          },
          {
            field: 'warningNotifier',
            title: '预警通知人',
            element: 'el-input',
          },
          {
            field: 'billingNumber',
            title: '计费编号',
            element: 'el-input',
          },
          {
            field: 'chargingPileNumber',
            title: '充电桩编号',
            element: 'el-input',
          },
          {
            field: 'chargingPileName',
            title: '充电桩名称',
            element: 'el-input',
          },
          {
            field: 'stationId',
            title: '所属充电站',
            element: 'el-select',
            props: {
              options: this.stationList,
              filterable: true,
              remote: true,
              remoteMethod: this.debouncedStationSearch,
              loading: this.stationLoading,
            },
          },
          {
            field: 'warningNumber',
            title: '预警编号',
            element: 'el-input',
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  mounted() {
    this.loadData();
  },
  methods: {
    async debouncedStationSearch(query) {
      if (query !== '') {
        this.stationLoading = true;
        setTimeout(async () => {
          const [err, res] = await getStationList({
            stationName: query,
          });

          if (err) return;
          this.stationLoading = false;
          this.stationList = res.data.map((item) => ({
            label: item.stationName,
            value: item.stationId,
          }));
        }, 200);
      } else {
        this.stationList = [];
      }
    },
    loadData() {},
  },
};
</script>

<style lang="scss" scoped>
.table-wrap {
  background: #fff;
  padding: 20px;
  border-radius: 4px;

  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }

  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }
}
</style>
