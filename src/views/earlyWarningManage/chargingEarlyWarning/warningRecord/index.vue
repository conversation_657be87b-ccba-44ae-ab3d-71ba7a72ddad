<template>
  <div class="container">
    <div class="table-wrap">
      <BuseCrud
        ref="crud"
        :loading="loading"
        :filterOptions="filterOptions"
        :tablePage="tablePage"
        :tableColumn="tableColumn"
        :tableData="tableData"
        :pagerProps="pagerProps"
        :modalConfig="modalConfig"
        class="buse-wrap-station"
        @loadData="loadData"
      >
        <template slot="defaultHeader">
          <div class="card-head">
            <div class="card-head-text">计费预警记录</div>
            <div class="top-button-wrap">
              <el-button type="primary" @click="handleOutput">导出</el-button>
            </div>
          </div>
          <div class="card-head-after"></div>
        </template>
        <template slot="warningConfigName" slot-scope="{ row }">
          <div
            style="color: rgb(64, 158, 255); cursor: pointer; color: blue"
            @click="handleDetail(row)"
          >
            {{ row.warningConfigName }}
          </div>
        </template>
      </BuseCrud>
      <el-dialog
        :title="warningConfigDetail.title"
        :visible.sync="warningConfigDetail.open"
        width="60%"
        append-to-body
        @close="handleCancel"
      >
        <div>
          <el-table :data="warningConfigDetail.tableData" style="width: 100%">
            <el-table-column
              prop="notifyChannelName"
              label="通知渠道"
              width="200"
            ></el-table-column>
            <el-table-column
              prop="notifyContent"
              label="预警通知内容"
            ></el-table-column>
            <el-table-column
              prop="notifyUserName"
              label="通知渠道"
              width="180"
            ></el-table-column>
          </el-table>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { getStationList } from '@/api/pile/index';
import {
  billingPage,
  recordDetail,
  orderExport,
} from '@/api/earlyWarningManage/ruleConfiguration';

export default {
  dicts: [
    'ls_charging_warning_notify_channel', // 计费预警通知渠道
  ],
  data() {
    return {
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [],
      tableColumn: [
        {
          type: 'seq',
          title: '序号',
          minWidth: 60, // 最小宽度
        },
        {
          field: 'notifyRecordBillingId',
          title: '预警编号',
          minWidth: 120, // 最小宽度
        },
        {
          field: 'warningTime',
          title: '预警时间',
          minWidth: 160, // 最小宽度
        },
        {
          title: '预警规则名称',
          minWidth: 240, // 最小宽度
          slots: {
            default: 'warningConfigName',
          },
        },
        {
          field: 'pileNo',
          title: '充电桩编号',
          minWidth: 160, // 最小宽度
        },
        {
          field: 'pileName',
          title: '充电桩名称',
          minWidth: 160, // 最小宽度
        },

        {
          field: 'stationName',
          title: '所属充电站',
          minWidth: 120, // 最小宽度
        },
        {
          field: 'chcNo',
          title: '计费编号',
          minWidth: 120, // 最小宽度
        },
        {
          field: 'issueUser',
          title: '下发人',
          minWidth: 120, // 最小宽度
        },
        {
          field: 'issueTime',
          title: '下发时间',
          minWidth: 120, // 最小宽度
        },
      ],
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      params: {
        warningTime: '',
        issueUser: '',
        chcNo: '',
        pileNo: '',
        pileName: '',
        stationId: '',
        notifyRecordBillingId: '',
      },
      stationList: [],
      warningConfigDetail: {
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: '预警规则详情',
        tableData: [],
      },
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'warningTime',
            title: '预警时间',
            element: 'el-date-picker',
            props: {
              type: 'datetimerange',
              rangeSeparator: '至',
              startPlaceholder: '开始日期',
              endPlaceholder: '结束日期',
              valueFormat: 'yyyy-MM-dd HH:mm:ss',
              options: [],
              defaultTime: ['00:00:00', '23:59:59'],
            },
          },
          {
            field: 'issueUser',
            title: '下发人',
            element: 'el-input',
          },
          {
            field: 'chcNo',
            title: '计费编号',
            element: 'el-input',
          },
          {
            field: 'pileNo',
            title: '充电桩编号',
            element: 'el-input',
          },
          {
            field: 'pileName',
            title: '充电桩名称',
            element: 'el-input',
          },
          {
            field: 'stationId',
            title: '所属充电站',
            element: 'el-select',
            props: {
              options: this.stationList,
              filterable: true,
              remote: true,
              remoteMethod: this.debouncedStationSearch,
              loading: this.stationLoading,
            },
          },
          {
            field: 'notifyRecordBillingId',
            title: '预警编号',
            element: 'el-input',
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  mounted() {
    this.loadData();
  },
  methods: {
    async debouncedStationSearch(query) {
      if (query !== '') {
        this.stationLoading = true;
        setTimeout(async () => {
          const [err, res] = await getStationList({
            stationName: query,
          });

          if (err) return;
          this.stationLoading = false;
          this.stationList = res.data.map((item) => ({
            label: item.stationName,
            value: item.stationId,
          }));
        }, 200);
      } else {
        this.stationList = [];
      }
    },
    async loadData() {
      console.log(this.params.warningTime);
      this.loading = true;
      let params = {
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
        ...this.params,
        warningTimeLeft:
          this.params.warningTime?.length > 0 ? this.params.warningTime[0] : '',
        warningTimeRight:
          this.params.warningTime?.length > 0 ? this.params.warningTime[1] : '',
      };
      const [err, res] = await billingPage(params);
      this.loading = false;
      if (err) {
        return;
      }
      const { data, total } = res;
      this.tableData = data;
      this.tablePage.total = total;
    },
    // 打开详情弹窗
    async handleDetail(row) {
      console.log(row);
      let params = {
        warningBizType: '',
        warningNotifyRecordId: row.notifyRecordBillingId,
      };
      if (row.warningBizType === '计费预警') {
        params.warningBizType = '01';
      } else if (row.warningBizType === '订单预警') {
        params.warningBizType = '02';
      }
      const [err, res] = await recordDetail(params);
      if (err) {
        return;
      }
      // console.log('计费预警记录详情', res);
      this.warningConfigDetail.tableData = res.data;
      let notificationChannelsOptions =
        this.dict.type.ls_charging_warning_notify_channel;
      // console.log('notificationChannelsOptions', notificationChannelsOptions);
      notificationChannelsOptions.forEach((item) => {
        this.warningConfigDetail.tableData.forEach((it) => {
          if (it.notifyChannel == item.value) {
            it.notifyChannelName = item.label;
          }
        });
      });
      this.warningConfigDetail.open = true;
    },
    // 弹窗关闭
    handleCancel() {
      this.warningConfigDetail.open = false;
    },
    // 导出
    handleOutput() {
      this.download(
        '/vehicle-charging-admin/warning/record/billing/export',
        {},
        `计费预警记录.xlsx`
      );
    },
  },
};
</script>

<style lang="scss" scoped>
.table-wrap {
  // background: #fff;
  // padding: 20px;
  // border-radius: 4px;

  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }

  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }
}
</style>
