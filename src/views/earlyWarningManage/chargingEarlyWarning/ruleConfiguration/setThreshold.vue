<template>
  <div class="container">
    <div class="table-wrap">
      <div class="table-wrap">
        <BuseCrud
          ref="crud"
          :loading="loading"
          :filterOptions="filterOptions"
          :tablePage="tablePage"
          :tableColumn="tableColumn"
          :tableData="tableData"
          :pagerProps="pagerProps"
          :modalConfig="modalConfig"
          :tableOn="{
            'checkbox-change': handleCheckboxChange,
            'checkbox-all': handleCheckboxChange,
          }"
          @loadData="loadData"
        >
          <template slot="defaultHeader">
            <div class="card-head">
              <div class="card-head-text">计费预警阈值设置</div>
              <div class="top-button-wrap">
                <el-button type="primary" @click="handleAdd">
                  新增阈值设置
                </el-button>
                <el-button type="primary" @click="handleEdit">
                  批量修改阈值
                </el-button>
              </div>
            </div>
            <div class="card-head-after"></div>
          </template>
          <template #sharpServiceThreshold="{ row, column }">
            <el-input
              v-model="row[column.property]"
              size="small"
              @change="handleCellValueChange(row, column)"
              type="number"
            ></el-input>
          </template>
          <template #peakServiceThreshold="{ row, column }">
            <el-input
              v-model="row[column.property]"
              size="small"
              @change="handleCellValueChange(row, column)"
              type="number"
            ></el-input>
          </template>
          <template #flatServiceThreshold="{ row, column }">
            <el-input
              v-model="row[column.property]"
              size="small"
              @change="handleCellValueChange(row, column)"
              type="number"
            ></el-input>
          </template>
          <template #valleyServiceThreshold="{ row, column }">
            <el-input
              v-model="row[column.property]"
              size="small"
              @change="handleCellValueChange(row, column)"
              type="number"
            ></el-input>
          </template>
        </BuseCrud>
      </div>
    </div>
    <EditModal ref="editModal" @closeModal="closeEditModal" />
  </div>
</template>

<script>
import EditModal from './components/editModal';

export default {
  components: {
    EditModal,
  },
  data() {
    return {
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [
        {
          data6: '1',
          data7: '2',
          data8: '3',
          data9: '4',
        },
        {},
        {},
      ],
      tableColumn: [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
        },
        {
          type: 'seq',
          title: '序号',
          minWidth: 60,
        },
        {
          field: 'data1',
          title: '充电站名称',
          minWidth: 200,
        },
        {
          field: 'data2',
          title: '充电站编号',
          minWidth: 200,
        },
        {
          field: 'data3',
          title: '地市',
          minWidth: 200,
        },
        {
          field: 'data4',
          title: '资产属性',
          minWidth: 200,
        },
        {
          field: 'data5',
          title: '站点类型',
          minWidth: 200,
        },
        {
          field: 'data6',
          title: '尖服务费单价阈值',
          minWidth: 200,
          slots: {
            default: 'sharpServiceThreshold',
          },
        },
        {
          field: 'data7',
          title: '峰服务费单价阈值',
          minWidth: 200,
          slots: {
            default: 'peakServiceThreshold',
          },
        },
        {
          field: 'data8',
          title: '平服务费单价阈值',
          minWidth: 200,
          slots: {
            default: 'flatServiceThreshold', // 自定义单元格插槽
          },
        },
        {
          field: 'data9',
          title: '谷服务费单价阈值',
          minWidth: 200,
          slots: {
            default: 'valleyServiceThreshold',
          },
        },
        {
          field: 'data10',
          title: '更新人',
          minWidth: 200,
        },
        {
          field: 'data11',
          title: '更新时间',
          minWidth: 200,
        },
      ],
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      params: {
        chargingStationNumber: '',
        chargingStation: '',
        assetAttributes: '',
        region: '',
        siteType: '',
      },
      batchList: [],
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'chargingStationNumber',
            title: '充电站编号',
          },
          {
            field: 'chargingStation',
            title: '充电站',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
          {
            field: 'assetAttributes',
            title: '资产属性',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
          {
            field: 'region',
            title: '地市',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
          {
            field: 'siteType',
            title: '站点类型',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: [],
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  mounted() {
    this.loadData();
  },
  methods: {
    loadData() {
      console.log(this.params);
    },
    // 勾选
    handleCheckboxChange({ records }) {
      console.log('records', records);
      this.batchList = records;
    },
    // 处理单元格值变化
    handleCellValueChange(row, column) {
      console.log('单元格值变化:', row, column);
      // 可以在这里添加保存或验证逻辑
    },
    // 新增
    handleAdd() {
      this.$router.push({
        path: '/v2g-charging-web/earlyWarningManage/chargingEarlyWarning/ruleConfiguration/addThreshold',
      });
    },
    // 编辑
    handleEdit() {
      console.log('this.batchList', this.batchList);
      if (this.batchList.length > 0) {
        this.$refs.editModal.dialogVisible = true;
      } else {
        this.$message.error(`请勾选需要修改的数据!`);
      }
    },
    // 关闭编辑弹窗
    closeEditModal() {
      this.$refs.editModal.dialogVisible = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.table-wrap {
  background: #fff;
  padding: 20px;
  border-radius: 4px;

  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }

  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }
}
</style>
