<template>
  <div class="container">
    <BuseCrud
      ref="crud"
      :loading="table.loading"
      :tablePage="table.page"
      :tableColumn="tableColumn"
      :tableData="table.data"
      :pagerProps="pagerProps"
      :modalConfig="modalConfig"
      @loadData="getTablePage"
    >
      <template slot="defaultHeader">
        <div>
          <div class="card-head">
            <div class="card-head-text">新增阈值设置</div>
          </div>
          <div class="card-head-after"></div>

          <div class="top-button-wrap">
            <el-button type="primary" @click="() => handleChooseStation()">
              充电站选择
            </el-button>

            <div class="choose-info-wrap">
              已选择
              <div class="choose-number">{{ stationNumber }}</div>
              个充电站，
              <div class="choose-number">{{ pileNumber }}</div>

              个充电桩
            </div>
          </div>
        </div>
      </template>

      <template slot="operate" slot-scope="{ row }">
        <div class="menu-box">
          <el-button
            type="danger"
            plain
            @click="() => handleDelete(row)"
            v-if="row.stationId !== stationId"
          >
            删除
          </el-button>
        </div>
      </template>
    </BuseCrud>

    <div class="info-card">
      <div class="form-wrap">
        <el-form
          :model="addForm"
          :rules="rules"
          ref="addForm"
          label-position="top"
        >
          <el-row :gutter="20" style="margin-top: 20px">
            <el-col :span="12">
              <el-form-item
                prop="pointedServiceCharge"
                :label-width="formLabelWidth"
                label="尖服务费阈值"
              >
                <el-input
                  placeholder="请输入内容"
                  v-model="addForm.pointedServiceCharge"
                  :min="0"
                  :max="100"
                  controls-position="right"
                  type="number"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                prop="peakServiceCharge"
                :label-width="formLabelWidth"
                label="峰服务费阈值"
              >
                <el-input
                  placeholder="请输入内容"
                  v-model="addForm.peakServiceCharge"
                  :min="0"
                  :max="100"
                  controls-position="right"
                  type="number"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                prop="flatServiceCharge"
                :label-width="formLabelWidth"
                label="平服务费阈值"
              >
                <el-input
                  placeholder="请输入内容"
                  v-model="addForm.flatServiceCharge"
                  :min="0"
                  :max="100"
                  controls-position="right"
                  type="number"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                prop="valleyServiceCharge"
                :label-width="formLabelWidth"
                label="谷服务费阈值"
              >
                <el-input
                  placeholder="请输入内容"
                  v-model="addForm.valleyServiceCharge"
                  :min="0"
                  :max="100"
                  controls-position="right"
                  type="number"
                ></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <div class="bottom-wrap">
      <el-button @click="() => handleCancel()">取消</el-button>
      <el-button type="primary" @click="() => handleConfirm()">提交</el-button>
    </div>
    <ChooseStationModal
      ref="chooseStationModal"
      @confirm="handleChooseStationConfirm"
    />
  </div>
</template>

<script>
import ChooseStationModal from './components/chooseStationModal';

export default {
  props: {
    dialogTitle: {
      type: String,
      default: '新增阈值设置',
    },
  },
  components: {
    ChooseStationModal,
  },
  data() {
    return {
      dialogVisible: false,
      table: {
        loading: false,
        page: {
          total: 0,
          currentPage: 1,
          pageSize: 10,
        },
        dataTotal: [],
        data: [],
      },
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      tableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60, // 最小宽度
        },
        {
          field: 'stationNo',
          title: '充电站编号',
          minWidth: 120, // 最小宽度
        },
        {
          field: 'stationName',
          title: '充电站名称',
          minWidth: 150, // 最小宽度
        },
        {
          field: 'stationType',
          title: '站点类型',
          minWidth: 100, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_station_type,
              cellValue
            );
          },
        },
        {
          field: 'operationMode',
          title: '运营模式',
          minWidth: 100, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_operation_mode,
              cellValue
            );
          },
        },
        {
          field: 'assetProperty',
          title: '资产属性',
          minWidth: 100, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_asset_property,
              cellValue
            );
          },
        },
        {
          field: 'stationAccessType',
          title: '接入方式',
          minWidth: 100, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_station_access_type,
              cellValue
            );
          },
        },
        {
          field: 'pileNum',
          title: '充电桩数量',
          minWidth: 120, // 最小宽度
        },
        {
          field: 'isAdjustable',
          title: '是否参与调控',
          minWidth: 120, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_status,
              cellValue
            );
          },
        },
        {
          field: 'adjustableType',
          title: '可控类型',
          minWidth: 100, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_adjustable_type,
              cellValue
            );
          },
        },
        {
          field: 'unifiedConstruction',
          title: '是否统建统服',
          minWidth: 120, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_status,
              cellValue
            );
          },
        },
        {
          field: 'construction',
          title: '建设场所',
          minWidth: 120, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_construction,
              cellValue
            );
          },
        },
        {
          field: 'areaType',
          title: '所属区域',
          minWidth: 120, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_area_type,
              cellValue
            );
          },
        },
        {
          title: '资产单位',
          field: 'assetUnitName',
          minWidth: 120, // 最小宽度
        },
        {
          title: '运营单位',
          field: 'operatingUnitName',
          minWidth: 120, // 最小宽度
        },
        {
          title: '运维单位',
          field: 'maintenanceUnitName',
          minWidth: 120, // 最小宽度
        },
        {
          title: '签约单位',
          field: 'contractedUnit',
          minWidth: 120, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_contracted_unit,
              cellValue
            );
          },
        },
        {
          title: '数据来源',
          field: 'stationSource',
          minWidth: 120, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_station_source,
              cellValue
            );
          },
        },

        {
          title: '操作',
          slots: { default: 'operate' },
          width: 200,
          align: 'center',
          fixed: 'right',
        },
      ],
      stationNumber: '0',
      pileNumber: '0',
      addForm: {
        pointedServiceCharge: '',
        peakServiceCharge: '',
        flatServiceCharge: '',
        valleyServiceCharge: '',
      },
      rules: {
        pointedServiceCharge: [
          { required: true, message: '请输入尖服务费', trigger: 'blur' },
        ],
        peakServiceCharge: [
          { required: true, message: '请输入峰服务费', trigger: 'blur' },
        ],
        flatServiceCharge: [
          { required: true, message: '请输入平服务费', trigger: 'blur' },
        ],
        valleyServiceCharge: [
          { required: true, message: '请输入谷服务费', trigger: 'blur' },
        ],
      },
      formLabelWidth: '120px',
    };
  },
  computed: {
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  mounted() {
    this.getTablePage();
  },
  methods: {
    getTablePage() {
      this.table.data = this.table.dataTotal.slice(
        (this.table.page.currentPage - 1) * this.table.page.pageSize,
        this.table.page.currentPage * this.table.page.pageSize
      );
    },
    // 取消
    handleCancel() {
      this.$router.back();
    },
    // 确定
    handleConfirm() {
      this.$refs.addForm.validate(async (valid) => {
        if (valid) {
          console.log('addForm', this.addForm);
          this.handleCancel();
        }
      });
    },
    // 选择充电站
    handleChooseStation() {
      console.log('123', this.$refs.chooseStationModal.filterOptions.params);
      (this.$refs.chooseStationModal.params = {
        stationName: '',
        stationType: '',
        operationMode: '',
        assetAttribute: '',
        assetType: '',
        controlType: '',
        constructionSite: '',
        dataSources: '',
      }),
        this.$refs.chooseStationModal.loadData();
      this.$refs.chooseStationModal.dialogVisible = true;
    },
    // 选择充电站确认
    handleChooseStationConfirm(stationList) {
      console.log('stationList', stationList);
      let list = null;
      if (this.table.data.length == 0) {
        list = this.table.dataTotal.concat(stationList);
      } else if (this.table.data.length > 0) {
        list = this.table.dataTotal.concat(...this.table.data, ...stationList);
      }
      const uniqueArray = Array.from(
        new Map(list.map((item) => [item.stationId, item])).values()
      );
      this.table.dataTotal = uniqueArray;
      this.stationNumber = this.table.dataTotal.length;
      let number = 0;
      this.table.dataTotal.forEach((item) => {
        number += Number(item.pileNum);
      });
      this.pileNumber = 0;
      this.pileNumber = number;
      this.table.page.currentPage = 1;
      this.table.page.total = this.table.dataTotal.length;
      this.getTablePage();
    },
  },
};
</script>

<style lang="scss" scoped>
.title-box {
  padding: 10px 0 20px;
  display: flex;
  // justify-content: space-between;
  .add-btn {
    margin-left: 20px;
  }
}
.menu-box {
  display: flex;
}
::v-deep .bd3001-table-select-box {
  display: none;
}
::v-deep .bd3001-header {
  display: block !important;
}
::v-deep .bd3001-button {
  display: block !important;
}
.card-head {
  // position: relative;
  height: 56px;
  padding: 0 16px;
  display: flex;
  align-items: center;
  margin-top: -20px;
  .card-head-text {
    flex: 1;
    width: 520px;
    height: 26px;
    background-image: url('~@/assets/images/bg-title.png');
    background-size: 520px 26px;
    background-repeat: no-repeat;
    font-size: 20px;
    font-style: normal;
    font-weight: 500;
    line-height: 16px;
    padding-left: 36px;
    color: #21252e;
    &::before {
      content: '';
      position: absolute;
      top: 0;
      bottom: 0;
      left: -3px; /* 调整这个值来改变边框的宽度 */
      width: 0;
      border-top: 3px solid transparent;
      border-bottom: 3px solid transparent;
      border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
    }
  }
}
.card-head-after {
  width: 100%;
  height: 1px;
  background-color: #dcdee2;
  margin-bottom: 16px;
}
.top-button-wrap {
  display: flex;
  align-items: center;
  height: 34px;
  align-items: center;
  margin-bottom: 14px;
  .choose-info-wrap {
    border-radius: 2px;
    height: 34px;
    padding: 0 10px;
    display: flex;
    align-items: center;
    background-color: #ebf3ff;
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 14px;
    margin-left: 16px;
    color: #217aff;
    .choose-number {
      font-size: 16px;
      font-weight: 500;
      margin: 0 4px;
    }
  }
}
.info-card {
  margin: 16px 0 16px 0;
  border-radius: 5px;
  border: 1px solid #fff;
  overflow: hidden;
  background-color: #fff;
  .form-wrap {
    padding: 0 16px 16px 16px;
    .custom-header {
      background: -webkit-gradient(
          linear,
          left top,
          left bottom,
          from(rgba(0, 149, 255, 0.5)),
          to(rgba(87, 152, 255, 0))
        ),
        #f5faff;
      background: linear-gradient(
          180deg,
          rgba(0, 149, 255, 0.5) 0%,
          rgba(87, 152, 255, 0) 100%
        ),
        #f5faff;
      background-repeat: no-repeat;
    }
  }
}
.container {
  position: relative;
  padding-bottom: 100px;
  box-sizing: border-box;
  .bottom-wrap {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 86px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background-color: #ffffff;
    padding-right: 32px;
    box-sizing: border-box;
    z-index: 5;
  }
}
</style>
