<template>
  <div
    class="container container-float"
    style="padding: 0 0 100px 0; position: relative"
  >
    <div class="info-card">
      <div class="card-head">
        <div class="before-icon"></div>
        <div class="card-head-text">预警规则信息</div>
      </div>
      <div class="form-wrap">
        <el-form :model="form" :rules="rules" ref="form" label-position="top">
          <!-- 计费下发未成功预警 -->
          <el-row :gutter="20">
            <el-col :span="24" class="flex-box">
              <el-checkbox
                v-model="form.failedToIssue.isWarning"
                style="margin: 5px 0px 0px 0px"
                @change="handleIsWarningChange"
              ></el-checkbox>
              当计费下发
              <el-input-number
                v-model="form.failedToIssue.warningTime"
                :min="1"
                :max="30"
              />
              <el-select v-model="form.failedToIssue.warningTimeUnit">
                <el-option
                  v-for="item in timeUnitOptions"
                  :key="item.value"
                  :value="item.label"
                >
                  {{ item.label }}
                </el-option>
              </el-select>
              后仍未下发成功，触发预警
            </el-col>
            <el-col :span="24">
              <el-form-item label="通知渠道：" prop="notificationChannels1">
                <el-checkbox-group
                  v-model="form.failedToIssue.notificationChannels"
                >
                  <el-checkbox
                    v-for="item in notificationChannelsOptions"
                    :key="item.value"
                    :label="item.label"
                  />
                </el-checkbox-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="web推送模版：" prop="webTemplate1">
                <el-select
                  v-model="form.failedToIssue.webTemplate"
                  style="width: 80%"
                >
                  <el-option
                    v-for="item in webTemplateOptions"
                    :key="item.value"
                    :value="item.label"
                  >
                    {{ item.label }}
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="短信推送模版：" prop="textMessageTemplate1">
                <el-select
                  v-model="form.failedToIssue.textMessageTemplate"
                  style="width: 80%"
                >
                  <el-option
                    v-for="item in textMessageTemplateOptions"
                    :key="item.value"
                    :value="item.label"
                  >
                    {{ item.label }}
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 计费下发成功，订单仍按原价计费预警 -->
          <el-row :gutter="20">
            <el-col :span="24" class="flex-box">
              <el-checkbox
                v-model="form.originalPriceBilling.isWarning"
                style="margin: 5px 0px 0px 0px"
                @change="handleIsWarningChange"
              ></el-checkbox>
              当计费下发成功后，订单仍按原价计费，触发预警
            </el-col>
            <el-col :span="24">
              <el-form-item label="通知渠道：" prop="notificationChannels2">
                <el-checkbox-group
                  v-model="form.originalPriceBilling.notificationChannels"
                >
                  <el-checkbox
                    v-for="item in notificationChannelsOptions"
                    :key="item.value"
                    :label="item.label"
                  />
                </el-checkbox-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="web推送模版：" prop="webTemplate2">
                <el-select
                  v-model="form.originalPriceBilling.webTemplate"
                  style="width: 80%"
                >
                  <el-option
                    v-for="item in webTemplateOptions"
                    :key="item.value"
                    :value="item.label"
                  >
                    {{ item.label }}
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="短信推送模版：" prop="textMessageTemplate2">
                <el-select
                  v-model="form.originalPriceBilling.textMessageTemplate"
                  style="width: 80%"
                >
                  <el-option
                    v-for="item in textMessageTemplateOptions"
                    :key="item.value"
                    :value="item.label"
                  >
                    {{ item.label }}
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 计费电价与营销电价不一致预警 -->
          <el-row :gutter="20">
            <el-col :span="24" class="flex-box" style="align-items: flex-start">
              <el-checkbox
                v-model="form.inconsistentBilling.isWarning"
                style="margin: 5px 0px 0px 0px"
                @change="handleIsWarningChange"
              ></el-checkbox>
              当电费计价类型、计费模式（分时/不分时）及计费电价与营销电价不一致时，触发预警（计费模版内已审批通过计费电价与营销电价不一致的情况不在预警范围内）
            </el-col>
            <el-col :span="24">
              <el-form-item label="通知渠道：" prop="notificationChannels3">
                <el-checkbox-group
                  v-model="form.inconsistentBilling.notificationChannels"
                >
                  <el-checkbox
                    v-for="item in notificationChannelsOptions"
                    :key="item.value"
                    :label="item.label"
                  />
                </el-checkbox-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="web推送模版：" prop="webTemplate3">
                <el-select
                  v-model="form.inconsistentBilling.webTemplate"
                  style="width: 80%"
                >
                  <el-option
                    v-for="item in webTemplateOptions"
                    :key="item.value"
                    :value="item.label"
                  >
                    {{ item.label }}
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="短信推送模版：" prop="textMessageTemplate3">
                <el-select
                  v-model="form.inconsistentBilling.textMessageTemplate"
                  style="width: 80%"
                >
                  <el-option
                    v-for="item in textMessageTemplateOptions"
                    :key="item.value"
                    :value="item.label"
                  >
                    {{ item.label }}
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 电费、服务费计价不一致预警 -->
          <el-row :gutter="20">
            <el-col :span="24" class="flex-box">
              <el-checkbox
                v-model="form.inconsistentCosts.isWarning"
                style="margin: 5px 0px 0px 0px"
                @change="handleIsWarningChange"
              ></el-checkbox>
              当同一充电站充电桩电费、服务费计价不一致时，触发预警
            </el-col>
            <el-col :span="24">
              <el-form-item label="通知渠道：" prop="notificationChannels4">
                <el-checkbox-group
                  v-model="form.inconsistentCosts.notificationChannels"
                >
                  <el-checkbox
                    v-for="item in notificationChannelsOptions"
                    :key="item.value"
                    :label="item.label"
                  />
                </el-checkbox-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="web推送模版：" prop="webTemplate4">
                <el-select
                  v-model="form.inconsistentCosts.webTemplate"
                  style="width: 80%"
                >
                  <el-option
                    v-for="item in webTemplateOptions"
                    :key="item.value"
                    :value="item.label"
                  >
                    {{ item.label }}
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="短信推送模版：" prop="textMessageTemplate4">
                <el-select
                  v-model="form.inconsistentCosts.textMessageTemplate"
                  style="width: 80%"
                >
                  <el-option
                    v-for="item in textMessageTemplateOptions"
                    :key="item.value"
                    :value="item.label"
                  >
                    {{ item.label }}
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 服务费计价低于阈值预警 -->
          <el-row :gutter="20">
            <el-col :span="24" class="flex-box">
              <el-checkbox
                v-model="form.belowThreshold.isWarning"
                style="margin: 5px 0px 0px 0px"
                @change="handleIsWarningChange"
              ></el-checkbox>
              当充电站充电桩服务费计价低于阈值时，触发预警
              <i
                class="el-icon-s-tools"
                style="margin: 5px 0px 0px 10px"
                @click="goSetThreshold()"
              ></i>
            </el-col>
            <el-col :span="24">
              <el-form-item label="通知渠道：" prop="notificationChannels5">
                <el-checkbox-group
                  v-model="form.belowThreshold.notificationChannels"
                >
                  <el-checkbox
                    v-for="item in notificationChannelsOptions"
                    :key="item.value"
                    :label="item.label"
                  />
                </el-checkbox-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="web推送模版：" prop="webTemplate5">
                <el-select
                  v-model="form.belowThreshold.webTemplate"
                  style="width: 80%"
                >
                  <el-option
                    v-for="item in webTemplateOptions"
                    :key="item.value"
                    :value="item.label"
                  >
                    {{ item.label }}
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="短信推送模版：" prop="textMessageTemplate5">
                <el-select
                  v-model="form.belowThreshold.textMessageTemplate"
                  style="width: 80%"
                >
                  <el-option
                    v-for="item in textMessageTemplateOptions"
                    :key="item.value"
                    :value="item.label"
                  >
                    {{ item.label }}
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
    <div class="bottom-wrap">
      <el-button type="primary" @click="() => handleConfirm()">保存</el-button>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      form: {
        // 计费下发未成功预警
        failedToIssue: {
          isWarning: false, // 是否预警
          warningTime: '', // 下发预警时间
          warningTimeUnit: '分钟', // 下发预警时间单位
          notificationChannels: [], // 通知渠道
          webTemplate: '', // web推送模版
          textMessageTemplate: '', // 短信推送模版
        },
        // 计费下发成功，订单仍按原价计费预警
        originalPriceBilling: {
          isWarning: false, // 是否预警
          notificationChannels: [], // 通知渠道
          webTemplate: '', // web推送模版
          textMessageTemplate: '', // 短信推送模版
        },
        // 计费电价与营销电价不一致预警
        inconsistentBilling: {
          isWarning: false, // 是否预警
          notificationChannels: [], // 通知渠道
          webTemplate: '', // web推送模版
          textMessageTemplate: '', // 短信推送模版
        },
        // 电费、服务费计价不一致预警
        inconsistentCosts: {
          isWarning: false, // 是否预警
          notificationChannels: [], // 通知渠道
          webTemplate: '', // web推送模版
          textMessageTemplate: '', // 短信推送模版
        },
        // 服务费计价低于阈值预警
        belowThreshold: {
          isWarning: false, // 是否预警
          notificationChannels: [], // 通知渠道
          webTemplate: '', // web推送模版
          textMessageTemplate: '', // 短信推送模版
        },
      },
      rules: {
        // 计费下发未成功预警
        notificationChannels1: [
          { required: false, message: '请选择通知渠道', trigger: 'blur' },
        ],
        webTemplate1: [
          { required: false, message: '请选择web推送模版', trigger: 'blur' },
        ],
        textMessageTemplate1: [
          { required: false, message: '请选择短信推送模版', trigger: 'blur' },
        ],
        // 计费下发成功，订单仍按原价计费预警
        notificationChannels2: [
          { required: false, message: '请选择通知渠道', trigger: 'blur' },
        ],
        webTemplate2: [
          { required: false, message: '请选择web推送模版', trigger: 'blur' },
        ],
        textMessageTemplate2: [
          { required: false, message: '请选择短信推送模版', trigger: 'blur' },
        ],
        // 计费电价与营销电价不一致预警
        notificationChannels3: [
          { required: false, message: '请选择通知渠道', trigger: 'blur' },
        ],
        webTemplate3: [
          { required: false, message: '请选择web推送模版', trigger: 'blur' },
        ],
        textMessageTemplate3: [
          { required: false, message: '请选择短信推送模版', trigger: 'blur' },
        ],
        // 电费、服务费计价不一致预警
        notificationChannels4: [
          { required: false, message: '请选择通知渠道', trigger: 'blur' },
        ],
        webTemplate4: [
          { required: false, message: '请选择web推送模版', trigger: 'blur' },
        ],
        textMessageTemplate4: [
          { required: false, message: '请选择短信推送模版', trigger: 'blur' },
        ],
        // 服务费计价低于阈值预警
        notificationChannels5: [
          { required: false, message: '请选择通知渠道', trigger: 'blur' },
        ],
        webTemplate5: [
          { required: false, message: '请选择web推送模版', trigger: 'blur' },
        ],
        textMessageTemplate5: [
          { required: false, message: '请选择短信推送模版', trigger: 'blur' },
        ],
      },
      timeUnitOptions: [
        { label: '分钟', value: 'min' },
        { label: '小时', value: 'hour' },
      ],
      notificationChannelsOptions: [
        { label: 'wab', value: 'wab' },
        { label: 'app', value: 'app' },
        { label: '微信小程序', value: 'wx' },
        { label: '短信', value: 'textMessage' },
      ],
      webTemplateOptions: [{ label: '模板1', value: '1' }],
      textMessageTemplateOptions: [{ label: '模板1', value: '1' }],
    };
  },
  mounted() {},
  methods: {
    // 是否预警改变
    handleIsWarningChange() {
      console.log(this.form.failedToIssue.isWarning);
      // 计费下发未成功预警
      if (this.form.failedToIssue.isWarning) {
        this.rules.notificationChannels1[0].required = true;
        this.rules.webTemplate1[0].required = true;
        this.rules.textMessageTemplate1[0].required = true;
      } else {
        this.rules.notificationChannels1[0].required = false;
        this.rules.webTemplate1[0].required = false;
        this.rules.textMessageTemplate1[0].required = false;
      }
      // 计费下发成功，订单仍按原价计费预警
      if (this.form.originalPriceBilling.isWarning) {
        this.rules.notificationChannels2[0].required = true;
        this.rules.webTemplate2[0].required = true;
        this.rules.textMessageTemplate2[0].required = true;
      } else {
        this.rules.notificationChannels2[0].required = false;
        this.rules.webTemplate2[0].required = false;
        this.rules.textMessageTemplate2[0].required = false;
      }
      // 计费电价与营销电价不一致预警
      if (this.form.inconsistentBilling.isWarning) {
        this.rules.notificationChannels3[0].required = true;
        this.rules.webTemplate3[0].required = true;
        this.rules.textMessageTemplate3[0].required = true;
      } else {
        this.rules.notificationChannels3[0].required = false;
        this.rules.webTemplate3[0].required = false;
        this.rules.textMessageTemplate3[0].required = false;
      }
      // 电费、服务费计价不一致预警
      if (this.form.inconsistentCosts.isWarning) {
        this.rules.notificationChannels4[0].required = true;
        this.rules.webTemplate4[0].required = true;
        this.rules.textMessageTemplate4[0].required = true;
      } else {
        this.rules.notificationChannels4[0].required = false;
        this.rules.webTemplate4[0].required = false;
        this.rules.textMessageTemplate4[0].required = false;
      }
      // 服务费计价低于阈值预警
      if (this.form.belowThreshold.isWarning) {
        this.rules.notificationChannels5[0].required = true;
        this.rules.webTemplate5[0].required = true;
        this.rules.textMessageTemplate5[0].required = true;
      } else {
        this.rules.notificationChannels5[0].required = false;
        this.rules.webTemplate5[0].required = false;
        this.rules.textMessageTemplate5[0].required = false;
      }
    },
    // 保存按钮
    handleConfirm() {
      this.$refs.form.validate(async (valid) => {
        if (valid) {
        }
      });
    },
    // 阈值设置
    goSetThreshold() {
      this.$router.push(
        '/v2g-charging-web/earlyWarningManage/chargingEarlyWarning/ruleConfiguration/setThreshold'
      );
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  position: relative;
  padding-bottom: 100px;
  box-sizing: border-box;
  .info-card {
    margin: 16px;
    border-radius: 5px;
    border: 1px solid #fff;
    background-color: #fff;

    .card-head {
      height: 56px;
      padding: 0 16px;
      display: flex;
      align-items: center;

      .before-icon {
        width: 3px;
        height: 16px;
        background-image: url('~@/assets/station/consno-before.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        margin-right: 8px;
      }

      .card-head-text {
        font-weight: 500;
        font-size: 16px;
        color: #12151a;
      }
    }

    .form-wrap {
      padding: 16px;
      .flex-box {
        display: flex;
        gap: 6px;
        align-items: center;
        margin-bottom: 20px;
      }
    }
  }
  .bottom-wrap {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 86px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background-color: #ffffff;
    padding-right: 32px;
    box-sizing: border-box;
  }
}
</style>
