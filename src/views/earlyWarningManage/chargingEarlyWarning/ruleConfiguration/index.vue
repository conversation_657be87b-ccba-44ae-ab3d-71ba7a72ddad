<template>
  <div
    class="container container-float"
    style="padding: 0 0 100px 0; position: relative"
  >
    <div class="info-card">
      <div class="card-head">
        <div class="before-icon"></div>
        <div class="card-head-text">预警规则信息</div>
      </div>
      <div class="form-wrap">
        <el-form :model="form" :rules="rules" ref="form" label-position="top">
          <!-- 计费下发未成功预警 -->
          <el-row
            :gutter="20"
            type="flex"
            justify="start"
            style="flex-wrap: wrap; flex-direction: row"
          >
            <el-col :span="24" class="flex-box">
              <el-checkbox
                v-model="form.failedToIssue.enabled"
                style="margin: 5px 0px 0px 0px"
                @change="handleIsWarningChange"
              ></el-checkbox>
              当计费下发
              <el-input-number
                v-model="form.failedToIssue.paramMap.value"
                :min="1"
                :max="30"
              />
              <el-select
                v-model="form.failedToIssue.paramMap.unitName"
                clearable
                @change="selectChange"
              >
                <el-option
                  v-for="item in timeUnitOptions"
                  :key="item.value"
                  :value="item.label"
                >
                  {{ item.label }}
                </el-option>
              </el-select>
              后仍未下发成功，触发预警
            </el-col>
            <el-col :span="24">
              <el-form-item
                label="通知渠道："
                prop="failedToIssue.notificationChannels"
              >
                <el-checkbox-group
                  v-model="form.failedToIssue.notificationChannels"
                  @change="checkChange('01')"
                >
                  <el-checkbox
                    v-for="item in notificationChannelsOptions"
                    :key="item.value"
                    :label="item.label"
                  />
                </el-checkbox-group>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="form.showWebTemplate1">
              <el-form-item
                label="web推送模版："
                prop="failedToIssue.webTemplate"
              >
                <el-select
                  v-model="form.failedToIssue.webTemplate"
                  style="width: 80%"
                  clearable
                  @change="selectChange"
                >
                  <el-option
                    v-for="item in webTemplateOptions"
                    :key="item.templateId"
                    :value="item.templateId"
                    :label="item.name"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="form.showAppTemplate1">
              <el-form-item
                label="app推送模版："
                prop="failedToIssue.appTemplate"
              >
                <el-select
                  v-model="form.failedToIssue.appTemplate"
                  style="width: 80%"
                  clearable
                  @change="selectChange"
                >
                  <el-option
                    v-for="item in appTemplateOptions"
                    :key="item.templateId"
                    :value="item.templateId"
                    :label="item.name"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="form.showWxTemplate1">
              <el-form-item
                label="微信推送模版："
                prop="failedToIssue.wxTemplate"
              >
                <el-select
                  v-model="form.failedToIssue.wxTemplate"
                  style="width: 80%"
                  clearable
                  @change="selectChange"
                >
                  <el-option
                    v-for="item in wxTemplateOptions"
                    :key="item.templateId"
                    :value="item.templateId"
                    :label="item.name"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="form.showTextMessageTemplate1">
              <el-form-item
                label="短信推送模版："
                prop="failedToIssue.textMessageTemplate"
              >
                <el-select
                  v-model="form.failedToIssue.textMessageTemplate"
                  style="width: 80%"
                  clearable
                  @change="selectChange"
                >
                  <el-option
                    v-for="item in textMessageTemplateOptions"
                    :key="item.templateId"
                    :value="item.templateId"
                    :label="item.name"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 计费下发成功，订单仍按原价计费预警 -->
          <el-row
            :gutter="20"
            type="flex"
            justify="start"
            style="flex-wrap: wrap; flex-direction: row"
          >
            <el-col :span="24" class="flex-box">
              <el-checkbox
                v-model="form.originalPriceBilling.enabled"
                style="margin: 5px 0px 0px 0px"
                @change="handleIsWarningChange"
              ></el-checkbox>
              当计费下发成功后，订单仍按原价计费，触发预警
            </el-col>
            <el-col :span="24">
              <el-form-item
                label="通知渠道："
                prop="originalPriceBilling.notificationChannels"
              >
                <el-checkbox-group
                  v-model="form.originalPriceBilling.notificationChannels"
                  @change="checkChange('02')"
                >
                  <el-checkbox
                    v-for="item in notificationChannelsOptions"
                    :key="item.value"
                    :label="item.label"
                  />
                </el-checkbox-group>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="form.showWebTemplate2">
              <el-form-item
                label="web推送模版："
                prop="originalPriceBilling.webTemplate"
              >
                <el-select
                  v-model="form.originalPriceBilling.webTemplate"
                  style="width: 80%"
                  clearable
                  @change="selectChange"
                >
                  <el-option
                    v-for="item in webTemplateOptions"
                    :key="item.templateId"
                    :value="item.templateId"
                    :label="item.name"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="form.showAppTemplate2">
              <el-form-item
                label="app推送模版："
                prop="originalPriceBilling.appTemplate"
              >
                <el-select
                  v-model="form.originalPriceBilling.appTemplate"
                  style="width: 80%"
                  clearable
                  @change="selectChange"
                >
                  <el-option
                    v-for="item in appTemplateOptions"
                    :key="item.templateId"
                    :value="item.templateId"
                    :label="item.name"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="form.showWxTemplate2">
              <el-form-item
                label="微信推送模版："
                prop="originalPriceBilling.wxTemplate"
              >
                <el-select
                  v-model="form.originalPriceBilling.wxTemplate"
                  style="width: 80%"
                  clearable
                  @change="selectChange"
                >
                  <el-option
                    v-for="item in wxTemplateOptions"
                    :key="item.templateId"
                    :value="item.templateId"
                    :label="item.name"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="form.showTextMessageTemplate2">
              <el-form-item
                label="短信推送模版："
                prop="originalPriceBilling.textMessageTemplate"
              >
                <el-select
                  v-model="form.originalPriceBilling.textMessageTemplate"
                  style="width: 80%"
                  clearable
                  @change="selectChange"
                >
                  <el-option
                    v-for="item in textMessageTemplateOptions"
                    :key="item.templateId"
                    :value="item.templateId"
                    :label="item.name"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 计费电价与营销电价不一致预警 -->
          <el-row
            :gutter="20"
            type="flex"
            justify="start"
            style="flex-wrap: wrap; flex-direction: row"
          >
            <el-col :span="24" class="flex-box" style="align-items: flex-start">
              <el-checkbox
                v-model="form.inconsistentBilling.enabled"
                style="margin: 5px 0px 0px 0px"
                @change="handleIsWarningChange"
              ></el-checkbox>
              当电费计价类型、计费模式（分时/不分时）及计费电价与营销电价不一致时，触发预警（计费模版内已审批通过计费电价与营销电价不一致的情况不在预警范围内）
            </el-col>
            <el-col :span="24">
              <el-form-item
                label="通知渠道："
                prop="inconsistentBilling.notificationChannels"
              >
                <el-checkbox-group
                  v-model="form.inconsistentBilling.notificationChannels"
                  @change="checkChange('03')"
                >
                  <el-checkbox
                    v-for="item in notificationChannelsOptions"
                    :key="item.value"
                    :label="item.label"
                  />
                </el-checkbox-group>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="form.showWebTemplate3">
              <el-form-item
                label="web推送模版："
                prop="inconsistentBilling.webTemplate"
              >
                <el-select
                  v-model="form.inconsistentBilling.webTemplate"
                  style="width: 80%"
                  clearable
                  @change="selectChange"
                >
                  <el-option
                    v-for="item in webTemplateOptions"
                    :key="item.templateId"
                    :value="item.templateId"
                    :label="item.name"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="form.showAppTemplate3">
              <el-form-item
                label="app推送模版："
                prop="inconsistentBilling.appTemplate"
              >
                <el-select
                  v-model="form.inconsistentBilling.appTemplate"
                  style="width: 80%"
                  clearable
                  @change="selectChange"
                >
                  <el-option
                    v-for="item in appTemplateOptions"
                    :key="item.templateId"
                    :value="item.templateId"
                    :label="item.name"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="form.showWxTemplate3">
              <el-form-item
                label="微信推送模版："
                prop="inconsistentBilling.wxTemplate"
              >
                <el-select
                  v-model="form.inconsistentBilling.wxTemplate"
                  style="width: 80%"
                  clearable
                  @change="selectChange"
                >
                  <el-option
                    v-for="item in wxTemplateOptions"
                    :key="item.templateId"
                    :value="item.templateId"
                    :label="item.name"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="form.showTextMessageTemplate3">
              <el-form-item
                label="短信推送模版："
                prop="inconsistentBilling.textMessageTemplate"
              >
                <el-select
                  v-model="form.inconsistentBilling.textMessageTemplate"
                  style="width: 80%"
                  clearable
                  @change="selectChange"
                >
                  <el-option
                    v-for="item in textMessageTemplateOptions"
                    :key="item.templateId"
                    :value="item.templateId"
                    :label="item.name"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 电费、服务费计价不一致预警 -->
          <el-row
            :gutter="20"
            type="flex"
            justify="start"
            style="flex-wrap: wrap; flex-direction: row"
          >
            <el-col :span="24" class="flex-box">
              <el-checkbox
                v-model="form.inconsistentCosts.enabled"
                style="margin: 5px 0px 0px 0px"
                @change="handleIsWarningChange"
              ></el-checkbox>
              当同一充电站充电桩电费、服务费计价不一致时，触发预警
            </el-col>
            <el-col :span="24">
              <el-form-item
                label="通知渠道："
                prop="inconsistentCosts.notificationChannels"
              >
                <el-checkbox-group
                  v-model="form.inconsistentCosts.notificationChannels"
                  @change="checkChange('04')"
                >
                  <el-checkbox
                    v-for="item in notificationChannelsOptions"
                    :key="item.value"
                    :label="item.label"
                  />
                </el-checkbox-group>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="form.showWebTemplate4">
              <el-form-item
                label="web推送模版："
                prop="inconsistentCosts.webTemplate"
              >
                <el-select
                  v-model="form.inconsistentCosts.webTemplate"
                  style="width: 80%"
                  clearable
                  @change="selectChange"
                >
                  <el-option
                    v-for="item in webTemplateOptions"
                    :key="item.templateId"
                    :value="item.templateId"
                    :label="item.name"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="form.showAppTemplate4">
              <el-form-item
                label="app推送模版："
                prop="inconsistentCosts.appTemplate"
              >
                <el-select
                  v-model="form.inconsistentCosts.appTemplate"
                  style="width: 80%"
                  clearable
                  @change="selectChange"
                >
                  <el-option
                    v-for="item in appTemplateOptions"
                    :key="item.templateId"
                    :value="item.templateId"
                    :label="item.name"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="form.showWxTemplate4">
              <el-form-item
                label="微信推送模版："
                prop="inconsistentCosts.wxTemplate"
              >
                <el-select
                  v-model="form.inconsistentCosts.wxTemplate"
                  style="width: 80%"
                  clearable
                  @change="selectChange"
                >
                  <el-option
                    v-for="item in wxTemplateOptions"
                    :key="item.templateId"
                    :value="item.templateId"
                    :label="item.name"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="form.showTextMessageTemplate4">
              <el-form-item
                label="短信推送模版："
                prop="inconsistentCosts.textMessageTemplate"
              >
                <el-select
                  v-model="form.inconsistentCosts.textMessageTemplate"
                  style="width: 80%"
                  clearable
                  @change="selectChange"
                >
                  <el-option
                    v-for="item in textMessageTemplateOptions"
                    :key="item.templateId"
                    :value="item.templateId"
                    :label="item.name"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- 服务费计价低于阈值预警 -->
          <el-row
            :gutter="20"
            type="flex"
            justify="start"
            style="flex-wrap: wrap; flex-direction: row"
          >
            <el-col :span="24" class="flex-box">
              <el-checkbox
                v-model="form.belowThreshold.enabled"
                style="margin: 5px 0px 0px 0px"
                @change="handleIsWarningChange"
              ></el-checkbox>
              当充电站充电桩服务费计价低于阈值时，触发预警
              <i
                class="el-icon-s-tools"
                style="margin: 5px 0px 0px 10px"
                @click="goSetThreshold()"
              ></i>
            </el-col>
            <el-col :span="24">
              <el-form-item
                label="通知渠道："
                prop="belowThreshold.notificationChannels"
              >
                <el-checkbox-group
                  v-model="form.belowThreshold.notificationChannels"
                  @change="checkChange('05')"
                >
                  <el-checkbox
                    v-for="item in notificationChannelsOptions"
                    :key="item.value"
                    :label="item.label"
                  />
                </el-checkbox-group>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="form.showWebTemplate5">
              <el-form-item
                label="web推送模版："
                prop="belowThreshold.webTemplate"
              >
                <el-select
                  v-model="form.belowThreshold.webTemplate"
                  style="width: 80%"
                  clearable
                  @change="selectChange"
                >
                  <el-option
                    v-for="item in webTemplateOptions"
                    :key="item.templateId"
                    :value="item.templateId"
                    :label="item.name"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="form.showAppTemplate5">
              <el-form-item
                label="app推送模版："
                prop="belowThreshold.appTemplate"
              >
                <el-select
                  v-model="form.belowThreshold.appTemplate"
                  style="width: 80%"
                  clearable
                  @change="selectChange"
                >
                  <el-option
                    v-for="item in appTemplateOptions"
                    :key="item.templateId"
                    :value="item.templateId"
                    :label="item.name"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="form.showWxTemplate5">
              <el-form-item
                label="微信推送模版："
                prop="belowThreshold.wxTemplate"
              >
                <el-select
                  v-model="form.belowThreshold.wxTemplate"
                  style="width: 80%"
                  clearable
                  @change="selectChange"
                >
                  <el-option
                    v-for="item in wxTemplateOptions"
                    :key="item.templateId"
                    :value="item.templateId"
                    :label="item.name"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="form.showTextMessageTemplate5">
              <el-form-item
                label="短信推送模版："
                prop="belowThreshold.textMessageTemplate"
              >
                <el-select
                  v-model="form.belowThreshold.textMessageTemplate"
                  style="width: 80%"
                  clearable
                  @change="selectChange"
                >
                  <el-option
                    v-for="item in textMessageTemplateOptions"
                    :key="item.templateId"
                    :value="item.templateId"
                    :label="item.name"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>
    <div class="bottom-wrap">
      <el-button type="primary" @click="() => handleConfirm()">保存</el-button>
    </div>
  </div>
</template>

<script>
import {
  getBillingConfigDetail,
  templateList,
  billingConfig,
} from '@/api/earlyWarningManage/ruleConfiguration';

export default {
  dicts: [
    'ls_charging_warning_notify_channel', // 计费预警通知渠道
    'ls_charging_warning_time_type', // 预警时间周期
  ],
  data() {
    return {
      form: {
        // 计费下发未成功预警
        failedToIssue: {
          enabled: false, // 是否预警
          paramMap: { value: 0, unit: '', unitName: '' }, // 规则参数，仅规则类型为01时传入
          notificationChannels: [], // 通知渠道(页面用)
          notifyChannels: [], // 通知渠道(接口用)
          webTemplate: '', // web推送模版
          appTemplate: '', // app推送模版
          wxTemplate: '', // 微信推送模版
          textMessageTemplate: '', // 短信推送模版
        },
        showWebTemplate1: false,
        showAppTemplate1: false,
        showWxTemplate1: false,
        showTextMessageTemplate1: false,
        // 计费下发成功，订单仍按原价计费预警
        originalPriceBilling: {
          enabled: false, // 是否预警
          notificationChannels: [], // 通知渠道(页面用)
          notifyChannels: [], // 通知渠道(接口用)
          webTemplate: '', // web推送模版
          appTemplate: '', // app推送模版
          wxTemplate: '', // 微信推送模版
          textMessageTemplate: '', // 短信推送模版
        },
        showWebTemplate2: false,
        showAppTemplate2: false,
        showWxTemplate2: false,
        showTextMessageTemplate2: false,
        // 计费电价与营销电价不一致预警
        inconsistentBilling: {
          enabled: false, // 是否预警
          notificationChannels: [], // 通知渠道(页面用)
          notifyChannels: [], // 通知渠道(接口用)
          webTemplate: '', // web推送模版
          appTemplate: '', // app推送模版
          wxTemplate: '', // 微信推送模版
          textMessageTemplate: '', // 短信推送模版
        },
        showWebTemplate3: false,
        showAppTemplate3: false,
        showWxTemplate3: false,
        showTextMessageTemplate3: false,
        // 电费、服务费计价不一致预警
        inconsistentCosts: {
          enabled: false, // 是否预警
          notificationChannels: [], // 通知渠道(页面用)
          notifyChannels: [], // 通知渠道(接口用)
          webTemplate: '', // web推送模版
          appTemplate: '', // app推送模版
          wxTemplate: '', // 微信推送模版
          textMessageTemplate: '', // 短信推送模版
        },
        showWebTemplate4: false,
        showAppTemplate4: false,
        showWxTemplate4: false,
        showTextMessageTemplate4: false,
        // 服务费计价低于阈值预警
        belowThreshold: {
          enabled: false, // 是否预警
          notificationChannels: [], // 通知渠道(页面用)
          notifyChannels: [], // 通知渠道(接口用)
          webTemplate: '', // web推送模版
          appTemplate: '', // app推送模版
          wxTemplate: '', // 微信推送模版
          textMessageTemplate: '', // 短信推送模版
        },
        showWebTemplate5: false,
        showAppTemplate5: false,
        showWxTemplate5: false,
        showTextMessageTemplate5: false,
      },
      rules: {
        // 计费下发未成功预警
        failedToIssue: {
          notificationChannels: [
            { required: false, message: '请选择通知渠道', trigger: 'blur' },
          ],
          webTemplate: [
            { required: false, message: '请选择web推送模版', trigger: 'blur' },
          ],
          appTemplate: [
            { required: false, message: '请选择app推送模版', trigger: 'blur' },
          ],
          wxTemplate: [
            { required: false, message: '请选择微信推送模版', trigger: 'blur' },
          ],
          textMessageTemplate: [
            { required: false, message: '请选择短信推送模版', trigger: 'blur' },
          ],
        },
        // 计费下发成功，订单仍按原价计费预警
        originalPriceBilling: {
          notificationChannels: [
            { required: false, message: '请选择通知渠道', trigger: 'blur' },
          ],
          webTemplate: [
            { required: false, message: '请选择web推送模版', trigger: 'blur' },
          ],
          appTemplate: [
            { required: false, message: '请选择app推送模版', trigger: 'blur' },
          ],
          wxTemplate: [
            { required: false, message: '请选择微信推送模版', trigger: 'blur' },
          ],
          textMessageTemplate: [
            { required: false, message: '请选择短信推送模版', trigger: 'blur' },
          ],
        },
        // 计费电价与营销电价不一致预警
        inconsistentBilling: {
          notificationChannels: [
            { required: false, message: '请选择通知渠道', trigger: 'blur' },
          ],
          webTemplate: [
            { required: false, message: '请选择web推送模版', trigger: 'blur' },
          ],
          appTemplate: [
            { required: false, message: '请选择app推送模版', trigger: 'blur' },
          ],
          wxTemplate: [
            { required: false, message: '请选择微信推送模版', trigger: 'blur' },
          ],
          textMessageTemplate: [
            { required: false, message: '请选择短信推送模版', trigger: 'blur' },
          ],
        },
        // 电费、服务费计价不一致预警
        inconsistentCosts: {
          notificationChannels: [
            { required: false, message: '请选择通知渠道', trigger: 'blur' },
          ],
          webTemplate: [
            { required: false, message: '请选择web推送模版', trigger: 'blur' },
          ],
          appTemplate: [
            { required: false, message: '请选择app推送模版', trigger: 'blur' },
          ],
          wxTemplate: [
            { required: false, message: '请选择微信推送模版', trigger: 'blur' },
          ],
          textMessageTemplate: [
            { required: false, message: '请选择短信推送模版', trigger: 'blur' },
          ],
        },
        // 服务费计价低于阈值预警
        belowThreshold: {
          notificationChannels: [
            { required: false, message: '请选择通知渠道', trigger: 'blur' },
          ],
          webTemplate: [
            { required: false, message: '请选择web推送模版', trigger: 'blur' },
          ],
          appTemplate: [
            { required: false, message: '请选择app推送模版', trigger: 'blur' },
          ],
          wxTemplate: [
            { required: false, message: '请选择微信推送模版', trigger: 'blur' },
          ],
          textMessageTemplate: [
            { required: false, message: '请选择短信推送模版', trigger: 'blur' },
          ],
        },
      },
      timeUnitOptions: [],
      notificationChannelsOptions: [
        // { label: 'wab', value: 'wab' },
        // { label: 'app', value: 'app' },
        // { label: '微信小程序', value: 'wx' },
        // { label: '短信', value: 'textMessage' },
      ],
      webTemplateOptions: [],
      appTemplateOptions: [],
      wxTemplateOptions: [],
      textMessageTemplateOptions: [],
    };
  },
  async mounted() {
    console.log('字典', this.dict.type.ls_charging_warning_notify_channel);
    this.notificationChannelsOptions =
      this.dict.type.ls_charging_warning_notify_channel;
    this.timeUnitOptions = this.dict.type.ls_charging_warning_time_type;
    await this.getData();
    await this.getTemplates('web');
    await this.getTemplates('app');
    await this.getTemplates('wechat');
    await this.getTemplates('sms');
  },
  methods: {
    // 是否预警改变
    handleIsWarningChange() {
      console.log('===', this.form.failedToIssue.notificationChannels);
      // 计费下发未成功预警
      this.form.showWebTemplate1 = false;
      this.form.showAppTemplate1 = false;
      this.form.showWxTemplate1 = false;
      this.form.showTextMessageTemplate1 = false;
      this.form.failedToIssue.notificationChannels.forEach((item) => {
        if (item == 'web') {
          this.form.showWebTemplate1 = true;
        }
        if (item == 'APP') {
          this.form.showAppTemplate1 = true;
        }
        if (item == '微信小程序') {
          this.form.showWxTemplate1 = true;
        }
        if (item == '短信') {
          this.form.showTextMessageTemplate1 = true;
        }
      });
      if (
        this.form.failedToIssue.enabled == 1 ||
        this.form.failedToIssue.enabled
      ) {
        this.rules.failedToIssue.notificationChannels[0].required = true;
        this.form.failedToIssue.notificationChannels.forEach((item) => {
          if (item == 'web') {
            this.rules.failedToIssue.webTemplate[0].required = true;
          }
          if (item == 'APP') {
            this.rules.failedToIssue.appTemplate[0].required = true;
          }
          if (item == '微信小程序') {
            this.rules.failedToIssue.wxTemplate[0].required = true;
          }
          if (item == '短信') {
            this.rules.failedToIssue.textMessageTemplate[0].required = true;
          }
        });
      } else {
        this.rules.failedToIssue.notificationChannels[0].required = false;
        this.rules.failedToIssue.webTemplate[0].required = false;
        this.rules.failedToIssue.appTemplate[0].required = false;
        this.rules.failedToIssue.wxTemplate[0].required = false;
        this.rules.failedToIssue.textMessageTemplate[0].required = false;
      }
      // 计费下发成功，订单仍按原价计费预警
      this.form.showWebTemplate2 = false;
      this.form.showAppTemplate2 = false;
      this.form.showWxTemplate2 = false;
      this.form.showTextMessageTemplate2 = false;
      this.form.originalPriceBilling.notificationChannels.forEach((item) => {
        if (item == 'web') {
          this.form.showWebTemplate2 = true;
        }
        if (item == 'APP') {
          this.form.showAppTemplate2 = true;
        }
        if (item == '微信小程序') {
          this.form.showWxTemplate2 = true;
        }
        if (item == '短信') {
          this.form.showTextMessageTemplate2 = true;
        }
      });
      if (
        this.form.originalPriceBilling.enabled == 1 ||
        this.form.originalPriceBilling.enabled
      ) {
        this.rules.originalPriceBilling.notificationChannels[0].required = true;
        this.form.originalPriceBilling.notificationChannels.forEach((item) => {
          if (item == 'web') {
            this.rules.originalPriceBilling.webTemplate[0].required = true;
          }
          if (item == 'APP') {
            this.rules.originalPriceBilling.appTemplate[0].required = true;
          }
          if (item == '微信小程序') {
            this.rules.originalPriceBilling.wxTemplate[0].required = true;
          }
          if (item == '短信') {
            this.rules.originalPriceBilling.textMessageTemplate[0].required = true;
          }
        });
      } else {
        this.rules.originalPriceBilling.notificationChannels[0].required = false;
        this.rules.originalPriceBilling.webTemplate[0].required = false;
        this.rules.originalPriceBilling.appTemplate[0].required = false;
        this.rules.originalPriceBilling.wxTemplate[0].required = false;
        this.rules.originalPriceBilling.textMessageTemplate[0].required = false;
      }
      // 计费电价与营销电价不一致预警
      this.form.showWebTemplate3 = false;
      this.form.showAppTemplate3 = false;
      this.form.showWxTemplate3 = false;
      this.form.showTextMessageTemplate3 = false;
      this.form.inconsistentBilling.notificationChannels.forEach((item) => {
        if (item == 'web') {
          this.form.showWebTemplate3 = true;
        }
        if (item == 'APP') {
          this.form.showAppTemplate3 = true;
        }
        if (item == '微信小程序') {
          this.form.showWxTemplate3 = true;
        }
        if (item == '短信') {
          this.form.showTextMessageTemplate3 = true;
        }
      });
      if (
        this.form.inconsistentBilling.enabled == 1 ||
        this.form.inconsistentBilling.enabled
      ) {
        this.rules.inconsistentBilling.notificationChannels[0].required = true;
        this.form.inconsistentBilling.notificationChannels.forEach((item) => {
          if (item == 'web') {
            this.rules.inconsistentBilling.webTemplate[0].required = true;
          }
          if (item == 'APP') {
            this.rules.inconsistentBilling.appTemplate[0].required = true;
          }
          if (item == '微信小程序') {
            this.rules.inconsistentBilling.wxTemplate[0].required = true;
          }
          if (item == '短信') {
            this.rules.inconsistentBilling.textMessageTemplate[0].required = true;
          }
        });
      } else {
        this.rules.inconsistentBilling.notificationChannels[0].required = false;
        this.rules.inconsistentBilling.webTemplate[0].required = false;
        this.rules.inconsistentBilling.appTemplate[0].required = false;
        this.rules.inconsistentBilling.wxTemplate[0].required = false;
        this.rules.inconsistentBilling.textMessageTemplate[0].required = false;
      }
      // 电费、服务费计价不一致预警
      this.form.showWebTemplate4 = false;
      this.form.showAppTemplate4 = false;
      this.form.showWxTemplate4 = false;
      this.form.showTextMessageTemplate4 = false;
      this.form.inconsistentCosts.notificationChannels.forEach((item) => {
        if (item == 'web') {
          this.form.showWebTemplate4 = true;
        }
        if (item == 'APP') {
          this.form.showAppTemplate4 = true;
        }
        if (item == '微信小程序') {
          this.form.showWxTemplate4 = true;
        }
        if (item == '短信') {
          this.form.showTextMessageTemplate4 = true;
        }
      });
      if (
        this.form.inconsistentCosts.enabled == 1 ||
        this.form.inconsistentCosts.enabled
      ) {
        this.rules.inconsistentCosts.notificationChannels[0].required = true;
        this.form.inconsistentCosts.notificationChannels.forEach((item) => {
          if (item == 'web') {
            this.rules.inconsistentCosts.webTemplate[0].required = true;
          }
          if (item == 'APP') {
            this.rules.inconsistentCosts.appTemplate[0].required = true;
          }
          if (item == '微信小程序') {
            this.rules.inconsistentCosts.wxTemplate[0].required = true;
          }
          if (item == '短信') {
            this.rules.inconsistentCosts.textMessageTemplate[0].required = true;
          }
        });
      } else {
        this.rules.inconsistentCosts.notificationChannels[0].required = false;
        this.rules.inconsistentCosts.webTemplate[0].required = false;
        this.rules.inconsistentCosts.appTemplate[0].required = false;
        this.rules.inconsistentCosts.wxTemplate[0].required = false;
        this.rules.inconsistentCosts.textMessageTemplate[0].required = false;
      }
      // 服务费计价低于阈值预警
      this.form.showWebTemplate5 = false;
      this.form.showAppTemplate5 = false;
      this.form.showWxTemplate5 = false;
      this.form.showTextMessageTemplate5 = false;
      this.form.belowThreshold.notificationChannels.forEach((item) => {
        if (item == 'web') {
          this.form.showWebTemplate5 = true;
        }
        if (item == 'APP') {
          this.form.showAppTemplate5 = true;
        }
        if (item == '微信小程序') {
          this.form.showWxTemplate5 = true;
        }
        if (item == '短信') {
          this.form.showTextMessageTemplate5 = true;
        }
      });
      if (
        this.form.belowThreshold.enabled == 1 ||
        this.form.belowThreshold.enabled
      ) {
        this.rules.belowThreshold.notificationChannels[0].required = true;
        this.form.belowThreshold.notificationChannels.forEach((item) => {
          if (item == 'web') {
            this.rules.belowThreshold.webTemplate[0].required = true;
          }
          if (item == 'APP') {
            this.rules.belowThreshold.appTemplate[0].required = true;
          }
          if (item == '微信小程序') {
            this.rules.belowThreshold.wxTemplate[0].required = true;
          }
          if (item == '短信') {
            this.rules.belowThreshold.textMessageTemplate[0].required = true;
          }
        });
      } else {
        this.rules.belowThreshold.notificationChannels[0].required = false;
        this.rules.belowThreshold.webTemplate[0].required = false;
        this.rules.belowThreshold.appTemplate[0].required = false;
        this.rules.belowThreshold.wxTemplate[0].required = false;
        this.rules.belowThreshold.textMessageTemplate[0].required = false;
      }
      this.$forceUpdate();
    },
    // 选择通知渠道
    checkChange(str) {
      if (str == '01') {
        console.log(this.form.failedToIssue);
        this.rules.failedToIssue.webTemplate[0].required = false;
        this.rules.failedToIssue.appTemplate[0].required = false;
        this.rules.failedToIssue.wxTemplate[0].required = false;
        this.rules.failedToIssue.textMessageTemplate[0].required = false;
        this.form.failedToIssue.notificationChannels.forEach((item) => {
          if (item == 'web') {
            this.rules.failedToIssue.webTemplate[0].required = true;
          }
          if (item == 'APP') {
            this.rules.failedToIssue.appTemplate[0].required = true;
          }
          if (item == '微信小程序') {
            this.rules.failedToIssue.wxTemplate[0].required = true;
          }
          if (item == '短信') {
            this.rules.failedToIssue.textMessageTemplate[0].required = true;
          }
        });
      } else if (str == '02') {
        console.log(this.form.originalPriceBilling);
        this.rules.originalPriceBilling.webTemplate[0].required = false;
        this.rules.originalPriceBilling.appTemplate[0].required = false;
        this.rules.originalPriceBilling.wxTemplate[0].required = false;
        this.rules.originalPriceBilling.textMessageTemplate[0].required = false;
        this.form.originalPriceBilling.notificationChannels.forEach((item) => {
          if (item == 'web') {
            this.rules.originalPriceBilling.webTemplate[0].required = true;
          }
          if (item == 'APP') {
            this.rules.originalPriceBilling.appTemplate[0].required = true;
          }
          if (item == '微信小程序') {
            this.rules.originalPriceBilling.wxTemplate[0].required = true;
          }
          if (item == '短信') {
            this.rules.originalPriceBilling.textMessageTemplate[0].required = true;
          }
        });
      } else if (str == '03') {
        console.log(this.form.inconsistentBilling);
        this.rules.inconsistentBilling.webTemplate[0].required = false;
        this.rules.inconsistentBilling.appTemplate[0].required = false;
        this.rules.inconsistentBilling.wxTemplate[0].required = false;
        this.rules.inconsistentBilling.textMessageTemplate[0].required = false;
        this.form.inconsistentBilling.notificationChannels.forEach((item) => {
          if (item == 'web') {
            this.rules.inconsistentBilling.webTemplate[0].required = true;
          }
          if (item == 'APP') {
            this.rules.inconsistentBilling.appTemplate[0].required = true;
          }
          if (item == '微信小程序') {
            this.rules.inconsistentBilling.wxTemplate[0].required = true;
          }
          if (item == '短信') {
            this.rules.inconsistentBilling.textMessageTemplate[0].required = true;
          }
        });
      } else if (str == '04') {
        console.log(this.form.inconsistentCosts);
        this.rules.inconsistentCosts.webTemplate[0].required = false;
        this.rules.inconsistentCosts.appTemplate[0].required = false;
        this.rules.inconsistentCosts.wxTemplate[0].required = false;
        this.rules.inconsistentCosts.textMessageTemplate[0].required = false;
        this.form.inconsistentCosts.notificationChannels.forEach((item) => {
          if (item == 'web') {
            this.rules.inconsistentCosts.webTemplate[0].required = true;
          }
          if (item == 'APP') {
            this.rules.inconsistentCosts.appTemplate[0].required = true;
          }
          if (item == '微信小程序') {
            this.rules.inconsistentCosts.wxTemplate[0].required = true;
          }
          if (item == '短信') {
            this.rules.inconsistentCosts.textMessageTemplate[0].required = true;
          }
        });
      } else if (str == '05') {
        console.log(this.form.belowThreshold);
        this.rules.belowThreshold.webTemplate[0].required = false;
        this.rules.belowThreshold.appTemplate[0].required = false;
        this.rules.belowThreshold.wxTemplate[0].required = false;
        this.rules.belowThreshold.textMessageTemplate[0].required = false;
        this.form.belowThreshold.notificationChannels.forEach((item) => {
          if (item == 'web') {
            this.rules.belowThreshold.webTemplate[0].required = true;
          }
          if (item == 'APP') {
            this.rules.belowThreshold.appTemplate[0].required = true;
          }
          if (item == '微信小程序') {
            this.rules.belowThreshold.wxTemplate[0].required = true;
          }
          if (item == '短信') {
            this.rules.belowThreshold.textMessageTemplate[0].required = true;
          }
        });
      }
      this.$nextTick(() => {
        this.handleIsWarningChange();
      });
      this.$forceUpdate();
    },
    // 保存按钮
    handleConfirm() {
      this.$nextTick(() => {
        console.log('===', this.form);
        this.$refs.form.validate(async (valid) => {
          if (valid) {
            this.form.failedToIssue.notifyChannels = [];
            this.form.originalPriceBilling.notifyChannels = [];
            this.form.inconsistentBilling.notifyChannels = [];
            this.form.inconsistentCosts.notifyChannels = [];
            this.form.belowThreshold.notifyChannels = [];
            this.form.failedToIssue.notificationChannels.forEach((item) => {
              this.notificationChannelsOptions.forEach((it) => {
                if (item === it.label) {
                  let notifyTemplateId = '';
                  if (item == 'web') {
                    notifyTemplateId = this.form.failedToIssue.webTemplate;
                  } else if (item == 'APP') {
                    notifyTemplateId = this.form.failedToIssue.appTemplate;
                  } else if (item == '微信小程序') {
                    notifyTemplateId = this.form.failedToIssue.wxTemplate;
                  } else if (item == '短信') {
                    notifyTemplateId =
                      this.form.failedToIssue.textMessageTemplate;
                  }
                  this.form.failedToIssue.notifyChannels.push({
                    notifyChannelType: it.value,
                    notifyTemplateId: notifyTemplateId,
                  });
                }
              });
              this.timeUnitOptions.forEach((it) => {
                if (it.label == this.form.failedToIssue.paramMap.unitName) {
                  this.form.failedToIssue.paramMap.unit = it.value;
                }
              });
            });
            this.form.originalPriceBilling.notificationChannels.forEach(
              (item) => {
                this.notificationChannelsOptions.forEach((it) => {
                  if (item === it.label) {
                    let notifyTemplateId = '';
                    if (item == 'web') {
                      notifyTemplateId =
                        this.form.originalPriceBilling.webTemplate;
                    } else if (item == 'APP') {
                      notifyTemplateId =
                        this.form.originalPriceBilling.appTemplate;
                    } else if (item == '微信小程序') {
                      notifyTemplateId =
                        this.form.originalPriceBilling.wxTemplate;
                    } else if (item == '短信') {
                      notifyTemplateId =
                        this.form.originalPriceBilling.textMessageTemplate;
                    }
                    this.form.originalPriceBilling.notifyChannels.push({
                      notifyChannelType: it.value,
                      notifyTemplateId: notifyTemplateId,
                    });
                  }
                });
              }
            );
            this.form.inconsistentBilling.notificationChannels.forEach(
              (item) => {
                this.notificationChannelsOptions.forEach((it) => {
                  if (item === it.label) {
                    let notifyTemplateId = '';
                    if (item == 'web') {
                      notifyTemplateId =
                        this.form.inconsistentBilling.webTemplate;
                    } else if (item == 'APP') {
                      notifyTemplateId =
                        this.form.inconsistentBilling.appTemplate;
                    } else if (item == '微信小程序') {
                      notifyTemplateId =
                        this.form.inconsistentBilling.wxTemplate;
                    } else if (item == '短信') {
                      notifyTemplateId =
                        this.form.inconsistentBilling.textMessageTemplate;
                    }
                    this.form.inconsistentBilling.notifyChannels.push({
                      notifyChannelType: it.value,
                      notifyTemplateId: notifyTemplateId,
                    });
                  }
                });
              }
            );
            this.form.inconsistentCosts.notificationChannels.forEach((item) => {
              this.notificationChannelsOptions.forEach((it) => {
                if (item === it.label) {
                  let notifyTemplateId = '';
                  if (item == 'web') {
                    notifyTemplateId = this.form.inconsistentCosts.webTemplate;
                  } else if (item == 'APP') {
                    notifyTemplateId = this.form.inconsistentCosts.appTemplate;
                  } else if (item == '微信小程序') {
                    notifyTemplateId = this.form.inconsistentCosts.wxTemplate;
                  } else if (item == '短信') {
                    notifyTemplateId =
                      this.form.inconsistentCosts.textMessageTemplate;
                  }
                  this.form.inconsistentCosts.notifyChannels.push({
                    notifyChannelType: it.value,
                    notifyTemplateId: notifyTemplateId,
                  });
                }
              });
            });
            this.form.belowThreshold.notificationChannels.forEach((item) => {
              this.notificationChannelsOptions.forEach((it) => {
                if (item === it.label) {
                  let notifyTemplateId = '';
                  if (item == 'web') {
                    notifyTemplateId = this.form.belowThreshold.webTemplate;
                  } else if (item == 'APP') {
                    notifyTemplateId = this.form.belowThreshold.appTemplate;
                  } else if (item == '微信小程序') {
                    notifyTemplateId = this.form.belowThreshold.wxTemplate;
                  } else if (item == '短信') {
                    notifyTemplateId =
                      this.form.belowThreshold.textMessageTemplate;
                  }
                  this.form.belowThreshold.notifyChannels.push({
                    notifyChannelType: it.value,
                    notifyTemplateId: notifyTemplateId,
                  });
                }
              });
            });
            let params = [
              {
                id: this.form.failedToIssue.id
                  ? this.form.failedToIssue.id
                  : '',
                ruleType: '01',
                enabled: this.form.failedToIssue.enabled ? 1 : 0,
                paramMap: {
                  value: this.form.failedToIssue.paramMap.value,
                  unit: this.form.failedToIssue.paramMap.unit,
                },
                notifyChannels: this.form.failedToIssue.notifyChannels,
              },
              {
                id: this.form.originalPriceBilling.id
                  ? this.form.originalPriceBilling.id
                  : '',
                ruleType: '02',
                enabled: this.form.originalPriceBilling.enabled ? 1 : 0,
                notifyChannels: this.form.originalPriceBilling.notifyChannels,
              },
              {
                id: this.form.inconsistentBilling.id
                  ? this.form.inconsistentBilling.id
                  : '',
                ruleType: '03',
                enabled: this.form.inconsistentBilling.enabled ? 1 : 0,
                notifyChannels: this.form.inconsistentBilling.notifyChannels,
              },
              {
                id: this.form.inconsistentCosts.id
                  ? this.form.inconsistentCosts.id
                  : '',
                ruleType: '04',
                enabled: this.form.inconsistentCosts.enabled ? 1 : 0,
                notifyChannels: this.form.inconsistentCosts.notifyChannels,
              },
              {
                id: this.form.belowThreshold.id
                  ? this.form.belowThreshold.id
                  : '',
                ruleType: '05',
                enabled: this.form.belowThreshold.enabled ? 1 : 0,
                notifyChannels: this.form.belowThreshold.notifyChannels,
              },
            ];
            // console.log('params', params);
            const [err, res] = await billingConfig(params);
            if (err) return;
            console.log('res', res.data);
            this.$message.success('保存成功');
            await this.getData();
          }
        });
      });
    },
    // 阈值设置
    goSetThreshold() {
      this.$router.push(
        '/v2g-charging/earlyWarningManage/chargingEarlyWarning/ruleConfiguration/setThreshold'
      );
    },
    // 获取配置详情内容
    async getData() {
      const [err, res] = await getBillingConfigDetail({});
      this.loading = false;
      if (err) return;
      // console.log('配置详情内容', res.data);
      res.data.forEach((item) => {
        if (item.ruleType == '01') {
          // 计费下发未成功预警
          this.form.failedToIssue = { ...item };
          this.timeUnitOptions.forEach((it) => {
            if (this.form.failedToIssue.paramMap.unit == it.value) {
              this.form.failedToIssue.paramMap.unitName = it.label;
            }
          });
          this.form.failedToIssue.notificationChannels = [];
          if (this.form.failedToIssue.enabled == 1) {
            this.form.failedToIssue.enabled = true;
          } else {
            this.form.failedToIssue.enabled = false;
          }
          this.form.failedToIssue.notifyChannels.forEach((it) => {
            this.notificationChannelsOptions.forEach((i) => {
              if (i.value == it.notifyChannelType) {
                this.form.failedToIssue.notificationChannels.push(i.label);
              }
            });
            if (
              it.notifyChannelType == this.notificationChannelsOptions[0].value
            ) {
              this.form.failedToIssue.webTemplate = it.notifyTemplateId;
            }
            if (
              it.notifyChannelType == this.notificationChannelsOptions[1].value
            ) {
              this.form.failedToIssue.appTemplate = it.notifyTemplateId;
            }
            if (
              it.notifyChannelType == this.notificationChannelsOptions[2].value
            ) {
              this.form.failedToIssue.wxTemplate = it.notifyTemplateId;
            }
            if (
              it.notifyChannelType == this.notificationChannelsOptions[3].value
            ) {
              this.form.failedToIssue.textMessageTemplate = it.notifyTemplateId;
            }
          });
        } else if (item.ruleType == '02') {
          // 计费下发成功，订单仍按原价计费预警
          this.form.originalPriceBilling = { ...item };
          this.form.originalPriceBilling.notificationChannels = [];
          if (this.form.originalPriceBilling.enabled == 1) {
            this.form.originalPriceBilling.enabled = true;
          } else {
            this.form.originalPriceBilling.enabled = false;
          }
          this.form.originalPriceBilling.notifyChannels.forEach((it) => {
            this.notificationChannelsOptions.forEach((i) => {
              if (i.value == it.notifyChannelType) {
                this.form.originalPriceBilling.notificationChannels.push(
                  i.label
                );
              }
            });
            if (
              it.notifyChannelType == this.notificationChannelsOptions[0].value
            ) {
              this.form.originalPriceBilling.webTemplate = it.notifyTemplateId;
            }
            if (
              it.notifyChannelType == this.notificationChannelsOptions[1].value
            ) {
              this.form.originalPriceBilling.appTemplate = it.notifyTemplateId;
            }
            if (
              it.notifyChannelType == this.notificationChannelsOptions[2].value
            ) {
              this.form.originalPriceBilling.wxTemplate = it.notifyTemplateId;
            }
            if (
              it.notifyChannelType == this.notificationChannelsOptions[3].value
            ) {
              this.form.originalPriceBilling.textMessageTemplate =
                it.notifyTemplateId;
            }
          });
        } else if (item.ruleType == '03') {
          // 计费电价与营销电价不一致预警
          this.form.inconsistentBilling = { ...item };
          this.form.inconsistentBilling.notificationChannels = [];
          if (this.form.inconsistentBilling.enabled == 1) {
            this.form.inconsistentBilling.enabled = true;
          } else {
            this.form.inconsistentBilling.enabled = false;
          }
          this.form.inconsistentBilling.notifyChannels.forEach((it) => {
            this.notificationChannelsOptions.forEach((i) => {
              if (i.value == it.notifyChannelType) {
                this.form.inconsistentBilling.notificationChannels.push(
                  i.label
                );
              }
            });
            if (
              it.notifyChannelType == this.notificationChannelsOptions[0].value
            ) {
              this.form.inconsistentBilling.webTemplate = it.notifyTemplateId;
            }
            if (
              it.notifyChannelType == this.notificationChannelsOptions[1].value
            ) {
              this.form.inconsistentBilling.appTemplate = it.notifyTemplateId;
            }
            if (
              it.notifyChannelType == this.notificationChannelsOptions[2].value
            ) {
              this.form.inconsistentBilling.wxTemplate = it.notifyTemplateId;
            }
            if (
              it.notifyChannelType == this.notificationChannelsOptions[3].value
            ) {
              this.form.inconsistentBilling.textMessageTemplate =
                it.notifyTemplateId;
            }
          });
        } else if (item.ruleType == '04') {
          // 电费、服务费计价不一致预警
          this.form.inconsistentCosts = { ...item };
          this.form.inconsistentCosts.notificationChannels = [];
          if (this.form.inconsistentCosts.enabled == 1) {
            this.form.inconsistentCosts.enabled = true;
          } else {
            this.form.inconsistentCosts.enabled = false;
          }
          this.form.inconsistentCosts.notifyChannels.forEach((it) => {
            this.notificationChannelsOptions.forEach((i) => {
              if (i.value == it.notifyChannelType) {
                this.form.inconsistentCosts.notificationChannels.push(i.label);
              }
            });
            if (
              it.notifyChannelType == this.notificationChannelsOptions[0].value
            ) {
              this.form.inconsistentCosts.webTemplate = it.notifyTemplateId;
            }
            if (
              it.notifyChannelType == this.notificationChannelsOptions[1].value
            ) {
              this.form.inconsistentCosts.appTemplate = it.notifyTemplateId;
            }
            if (
              it.notifyChannelType == this.notificationChannelsOptions[2].value
            ) {
              this.form.inconsistentCosts.wxTemplate = it.notifyTemplateId;
            }
            if (
              it.notifyChannelType == this.notificationChannelsOptions[3].value
            ) {
              this.form.inconsistentCosts.textMessageTemplate =
                it.notifyTemplateId;
            }
          });
        } else if (item.ruleType == '05') {
          // 服务费计价低于阈值预警
          this.form.belowThreshold = { ...item };
          this.form.belowThreshold.notificationChannels = [];
          if (this.form.belowThreshold.enabled == 1) {
            this.form.belowThreshold.enabled = true;
          } else {
            this.form.belowThreshold.enabled = false;
          }
          this.form.belowThreshold.notifyChannels.forEach((it) => {
            this.notificationChannelsOptions.forEach((i) => {
              if (i.value == it.notifyChannelType) {
                this.form.belowThreshold.notificationChannels.push(i.label);
              }
            });
            if (
              it.notifyChannelType == this.notificationChannelsOptions[0].value
            ) {
              this.form.belowThreshold.webTemplate = it.notifyTemplateId;
            }
            if (
              it.notifyChannelType == this.notificationChannelsOptions[1].value
            ) {
              this.form.belowThreshold.appTemplate = it.notifyTemplateId;
            }
            if (
              it.notifyChannelType == this.notificationChannelsOptions[2].value
            ) {
              this.form.belowThreshold.wxTemplate = it.notifyTemplateId;
            }
            if (
              it.notifyChannelType == this.notificationChannelsOptions[3].value
            ) {
              this.form.belowThreshold.textMessageTemplate =
                it.notifyTemplateId;
            }
          });
        }
      });
      this.$nextTick(() => {
        this.handleIsWarningChange();
      });
    },
    // 获取模版列表
    async getTemplates(str) {
      const [err, res] = await templateList({ notifyChannel: str });
      this.loading = false;
      if (err) return;
      if (str == 'web') {
        this.webTemplateOptions = res.data;
      } else if (str == 'app') {
        this.appTemplateOptions = res.data;
      } else if (str == 'wechat') {
        this.wxTemplateOptions = res.data;
      } else if (str == 'sms') {
        this.textMessageTemplateOptions = res.data;
      }
    },
    selectChange() {
      console.log('selectChange');
      this.$forceUpdate();
    },
  },
};
</script>

<style lang="scss" scoped>
.container {
  position: relative;
  padding-bottom: 100px;
  box-sizing: border-box;
  .info-card {
    margin: 16px;
    border-radius: 5px;
    border: 1px solid #fff;
    background-color: #fff;

    .card-head {
      height: 56px;
      padding: 0 16px;
      display: flex;
      align-items: center;

      .before-icon {
        width: 3px;
        height: 16px;
        background-image: url('~@/assets/station/consno-before.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        margin-right: 8px;
      }

      .card-head-text {
        font-weight: 500;
        font-size: 16px;
        color: #12151a;
      }
    }

    .form-wrap {
      padding: 16px;
      .flex-box {
        display: flex;
        gap: 6px;
        align-items: center;
        margin-bottom: 20px;
      }
    }
  }
  .bottom-wrap {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 86px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background-color: #ffffff;
    padding-right: 32px;
    box-sizing: border-box;
  }
}
</style>
