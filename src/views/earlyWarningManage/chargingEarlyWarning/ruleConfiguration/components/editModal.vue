<template>
  <el-dialog
    title="批量修改阈值"
    :visible.sync="dialogVisible"
    @close="handleCancel"
    width="80%"
    :destroy-on-close="true"
  >
    <el-form
      :model="editForm"
      :rules="rules"
      ref="editForm"
      label-position="top"
    >
      <el-row :gutter="20" style="margin-top: 20px">
        <el-col :span="12">
          <el-form-item
            prop="serviceTopThreshold"
            :label-width="formLabelWidth"
            label="尖服务费阈值"
          >
            <el-input
              placeholder="请输入内容"
              v-model="editForm.serviceTopThreshold"
              :min="0"
              :max="100"
              controls-position="right"
              type="number"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            prop="servicePeakThreshold"
            :label-width="formLabelWidth"
            label="峰服务费阈值"
          >
            <el-input
              placeholder="请输入内容"
              v-model="editForm.servicePeakThreshold"
              :min="0"
              :max="100"
              controls-position="right"
              type="number"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            prop="serviceFlatThreshold"
            :label-width="formLabelWidth"
            label="平服务费阈值"
          >
            <el-input
              placeholder="请输入内容"
              v-model="editForm.serviceFlatThreshold"
              :min="0"
              :max="100"
              controls-position="right"
              type="number"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            prop="serviceValleyThreshold"
            :label-width="formLabelWidth"
            label="谷服务费阈值"
          >
            <el-input
              placeholder="请输入内容"
              v-model="editForm.serviceValleyThreshold"
              :min="0"
              :max="100"
              controls-position="right"
              type="number"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" @click="handleConfirm">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { batchUpdatePriceThreshold } from '@/api/earlyWarningManage/ruleConfiguration';

export default {
  data() {
    return {
      dialogVisible: false,
      editForm: {
        serviceTopThreshold: '',
        servicePeakThreshold: '',
        serviceFlatThreshold: '',
        serviceValleyThreshold: '',
      },
      rules: {
        serviceTopThreshold: [
          { required: true, message: '请输入尖服务费', trigger: 'blur' },
        ],
        servicePeakThreshold: [
          { required: true, message: '请输入峰服务费', trigger: 'blur' },
        ],
        serviceFlatThreshold: [
          { required: true, message: '请输入平服务费', trigger: 'blur' },
        ],
        serviceValleyThreshold: [
          { required: true, message: '请输入谷服务费', trigger: 'blur' },
        ],
      },
      formLabelWidth: '120px',
      stationIds: [],
    };
  },
  computed: {},
  mounted() {},
  methods: {
    // 取消
    handleCancel() {
      this.$emit('closeModal');
    },
    // 确定
    handleConfirm() {
      // console.log('this.stationIds', this.stationIds);
      this.$refs.editForm.validate(async (valid) => {
        if (valid) {
          let params = { ...this.editForm, ids: [] };
          this.stationIds.forEach((item) => {
            params.ids.push(item.id);
          });
          // console.log('params', params);
          const [err, res] = await batchUpdatePriceThreshold(params);
          if (err) return;
          // console.log('批量修改价格阈值', res);
          if (res.code == '10000') {
            this.$message.success('编辑成功');
            this.handleCancel();
          }
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped></style>
