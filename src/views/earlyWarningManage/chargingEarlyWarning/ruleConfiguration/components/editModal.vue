<template>
  <el-dialog
    title="批量修改阈值"
    :visible.sync="dialogVisible"
    @close="handleCancel"
    width="80%"
    :destroy-on-close="true"
  >
    <el-form
      :model="editForm"
      :rules="rules"
      ref="editForm"
      label-position="top"
    >
      <el-row :gutter="20" style="margin-top: 20px">
        <el-col :span="12">
          <el-form-item
            prop="pointedServiceCharge"
            :label-width="formLabelWidth"
            label="尖服务费阈值"
          >
            <el-input
              placeholder="请输入内容"
              v-model="editForm.pointedServiceCharge"
              :min="0"
              :max="100"
              controls-position="right"
              type="number"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            prop="peakServiceCharge"
            :label-width="formLabelWidth"
            label="峰服务费阈值"
          >
            <el-input
              placeholder="请输入内容"
              v-model="editForm.peakServiceCharge"
              :min="0"
              :max="100"
              controls-position="right"
              type="number"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            prop="flatServiceCharge"
            :label-width="formLabelWidth"
            label="平服务费阈值"
          >
            <el-input
              placeholder="请输入内容"
              v-model="editForm.flatServiceCharge"
              :min="0"
              :max="100"
              controls-position="right"
              type="number"
            ></el-input>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            prop="valleyServiceCharge"
            :label-width="formLabelWidth"
            label="谷服务费阈值"
          >
            <el-input
              placeholder="请输入内容"
              v-model="editForm.valleyServiceCharge"
              :min="0"
              :max="100"
              controls-position="right"
              type="number"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取 消</el-button>
      <el-button type="primary" @click="handleConfirm">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      dialogVisible: false,
      editForm: {
        pointedServiceCharge: '',
        peakServiceCharge: '',
        flatServiceCharge: '',
        valleyServiceCharge: '',
      },
      rules: {
        pointedServiceCharge: [
          { required: true, message: '请输入尖服务费', trigger: 'blur' },
        ],
        peakServiceCharge: [
          { required: true, message: '请输入峰服务费', trigger: 'blur' },
        ],
        flatServiceCharge: [
          { required: true, message: '请输入平服务费', trigger: 'blur' },
        ],
        valleyServiceCharge: [
          { required: true, message: '请输入谷服务费', trigger: 'blur' },
        ],
      },
      formLabelWidth: '120px',
    };
  },
  computed: {},
  mounted() {},
  methods: {
    // 取消
    handleCancel() {
      this.$emit('closeModal');
    },
    // 确定
    handleConfirm() {},
  },
};
</script>

<style lang="scss" scoped></style>
