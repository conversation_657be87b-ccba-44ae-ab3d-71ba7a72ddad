<template>
  <el-dialog title="异常预警规则" :visible.sync="dialogVisible" width="952px">
    <div class="info-card">
      <div class="form-wrap" v-if="dialogVisible">
        <el-form
          :model="baseInfo.form"
          :rules="baseInfo.rules"
          ref="baseInfoForm"
          label-position="top"
          :disabled="type !== 'create'"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item
                label="预警规则名称："
                prop="warningName"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-model="baseInfo.form.warningName"
                  placeholder="请输入"
                ></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item
                label="订单异常规则："
                prop="type"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.type"
                  placeholder="请选择订单异常类型"
                  style="width: 100%"
                  @change="orderTypeChange"
                >
                  <el-option
                    v-for="item in dict.type.ls_order_except_type"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label=" "
                prop="name"
                class="hidden-label"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.name"
                  placeholder="请选择订单异常名称"
                  style="width: 100%"
                  :disabled="!baseInfo.form.type"
                  @change="orderChange"
                >
                  <el-option
                    v-for="item in dict.type.ls_order_except_level"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item
                label="异常订单编号："
                prop="exceptId"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-model="baseInfo.form.exceptId"
                  placeholder="自动带出"
                  :disabled="true"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item
                label="订单异常描述："
                prop="description"
                :label-width="formLabelWidth"
              >
                <el-input
                  :disabled="true"
                  v-model="baseInfo.form.description"
                  placeholder="自动带出"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item
                label="订单异常等级："
                prop="level"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.level"
                  placeholder="自动带出"
                  style="width: 100%"
                  :disabled="true"
                >
                  <el-option
                    v-for="item in dict.type.ls_order_except_level"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item
                label="通知渠道："
                prop="notificationChannels"
                :label-width="formLabelWidth"
              >
                <el-checkbox-group v-model="baseInfo.form.notificationChannels">
                  <el-checkbox
                    v-for="(item, index) in channelList"
                    :key="index"
                    :label="item.value"
                  >
                    {{ item.label }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item
                label="web推送模版："
                prop="webPushTemplate"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.webPushTemplate"
                  placeholder="请选择web推送模版"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in webPushTemplateList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="短信推送模版："
                prop="smsPushTemplate"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.smsPushTemplate"
                  placeholder="请选择短信推送模版"
                  style="width: 100%"
                >
                  <el-option
                    v-for="item in smsPushTemplateList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <div class="bottom-wrap">
      <div>
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          @click="handleSave"
          :loading="submitLoading"
          type="primary"
          v-if="type === 'create'"
        >
          提交
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>
<script>
export default {
  props: {
    type: {
      type: String,
      default: 'create',
    },
    detailObj: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  components: {},
  dicts: [
    'ls_order_except_level', // 异常级别
    'ls_order_except_type', // 异常类型
    'ls_order_except_measures', // 处理措施
    'ls_order_except_status', // 异常状态
    'ls_except_rule_item', // 异常规则
  ],
  data() {
    return {
      dialogVisible: false,
      formLabelWidth: '120px',
      channelList: [
        { label: 'web', value: 'web' },
        { label: 'APP', value: 'APP' },
        { label: '微信小程序', value: '微信小程序' },
        { label: '短信', value: '短信' },
      ],
      webPushTemplateList: [
        { label: 'web推送模版1', value: '1' },
        { label: 'web推送模版2', value: '2' },
      ],
      smsPushTemplateList: [
        { label: '短信推送模版1', value: '1' },
        { label: '短信推送模版2', value: '2' },
      ],

      baseInfo: {
        form: {
          warningName: '',
          type: '',
          name: '',
          exceptId: '',
          description: '',
          level: '',
          notificationChannels: [],
          webPushTemplate: '',
          smsPushTemplate: '',
        },
        rules: {
          warningName: [
            { required: true, message: '请输入预警规则名称', trigger: 'blur' },
          ],
          type: [
            {
              required: true,
              message: '请选择订单异常类型',
              trigger: 'change',
            },
          ],
          name: [
            {
              required: true,
              message: '请选择订单异常名称',
              trigger: 'change',
            },
          ],
          notificationChannels: [
            { required: true, message: '请选择通知渠道', trigger: 'change' },
          ],
          webPushTemplate: [
            { required: true, message: '请选择web推送模版', trigger: 'change' },
          ],
          smsPushTemplate: [
            {
              required: true,
              message: '请选择短信推送模版',
              trigger: 'change',
            },
          ],
        },
      },
      submitLoading: false,
    };
  },
  watch: {
    dialogVisible(value) {
      console.log(value, 888);
      if (!value) {
        this.baseInfo.form = {
          warningName: '',
          type: '',
          name: '',
          exceptId: '',
          description: '',
          level: '',
          notificationChannels: [],
          webPushTemplate: '',
          smsPushTemplate: '',
        };
      } else {
        if (Object.keys(this.detailObj)) {
          this.baseInfo.form = { ...this.baseInfo.form, ...this.detailObj };
        }
      }
    },
  },
  computed: {},
  mounted() {},
  methods: {
    orderTypeChange(event) {
      console.log(event, 88);
    },
    orderChange(event) {
      console.log(event, 88);
    },
    handleCancel() {
      this.dialogVisible = false;
    },
    // 保存
    handleSave() {
      this.$refs.baseInfoForm.validate(async (valid) => {
        if (valid) {
          this.$emit('orderruleAdd', true);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.container-full {
  background-color: rgba(244, 246, 249, 1);
  padding-bottom: 16px;
  min-height: calc(100% - 80px);
  overflow-y: auto;
}

.info-card {
  margin: 16px;
  border-radius: 5px;
  border: 1px solid #fff;
  overflow: hidden;
  background-color: #fff;
  // min-height: 300px;
  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    .before-icon {
      width: 3px;
      height: 16px;
      background-image: url('~@/assets/station/consno-before.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin-right: 8px;
    }
    .card-head-text {
      flex: 1;
      font-weight: 500;
      font-size: 16px;
      color: #12151a;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
    .button-wrap {
      display: flex;
      .invite-btn {
        background-color: #1ab2ff;
        border-color: #1ab2ff;
      }
      ::v-deep .el-button--small {
        font-size: 14px;
      }
      .distribution {
        margin-left: 24px;
        margin-right: 24px;
        display: flex;
        align-items: center;
      }
    }
  }

  .people-table-wrap {
    ::v-deep .bd3001-table-select-box {
      display: none;
    }
    ::v-deep .bd3001-button {
      width: 100%;
    }
  }
  .form-wrap {
    padding: 0 16px 16px 16px;
    .custom-header {
      background: -webkit-gradient(
          linear,
          left top,
          left bottom,
          from(rgba(0, 149, 255, 0.5)),
          to(rgba(87, 152, 255, 0))
        ),
        #f5faff;
      background: linear-gradient(
          180deg,
          rgba(0, 149, 255, 0.5) 0%,
          rgba(87, 152, 255, 0) 100%
        ),
        #f5faff;
      background-repeat: no-repeat;
    }
  }
}

.container {
  position: relative;
  padding-bottom: 100px;
  box-sizing: border-box;
  .bottom-wrap {
    // position: fixed;
    // bottom: 0;
    // left: 0;
    // width: 100%;
    // height: 86px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background-color: #ffffff;
    padding-right: 32px;
    box-sizing: border-box;
  }
}
::v-deep .hidden-label .el-form-item__label:before {
  visibility: hidden;
}
</style>
