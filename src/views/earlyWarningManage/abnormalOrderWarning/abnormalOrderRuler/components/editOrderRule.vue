<template>
  <el-dialog title="异常预警规则" :visible.sync="dialogVisible" width="952px">
    <div class="info-card">
      <div class="form-wrap" v-if="dialogVisible">
        <el-form
          :model="baseInfo.form"
          :rules="baseInfo.rules"
          ref="baseInfoForm"
          label-position="top"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item
                label="预警规则名称："
                prop="ruleName"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-model="baseInfo.form.ruleName"
                  placeholder="请输入"
                  :disabled="type !== 'create'"
                ></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="12">
              <el-form-item
                label="订单异常规则："
                prop="type"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.type"
                  placeholder="请选择订单异常类型"
                  style="width: 100%"
                  @change="orderTypeChange"
                  :disabled="type !== 'create'"
                >
                  <el-option
                    v-for="item in dict.type.ls_order_except_type"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label=" "
                prop="name"
                class="hidden-label"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.name"
                  placeholder="请选择订单异常名称"
                  style="width: 100%"
                  :disabled="
                    (!baseInfo.form.type && type == 'create') ||
                    type != 'create'
                  "
                  @change="orderChange"
                >
                  <el-option
                    v-for="item in exceptionNameOptions"
                    :key="item.exceptId"
                    :label="item.name"
                    :value="item.exceptId"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item
                label="异常订单编号："
                prop="exceptId"
                :label-width="formLabelWidth"
              >
                <el-input
                  v-model="baseInfo.form.exceptId"
                  placeholder="自动带出"
                  :disabled="true"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item
                label="订单异常描述："
                prop="description"
                :label-width="formLabelWidth"
              >
                <el-input
                  :disabled="true"
                  v-model="baseInfo.form.description"
                  placeholder="自动带出"
                ></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item
                label="订单异常等级："
                prop="level"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.level"
                  placeholder="自动带出"
                  style="width: 100%"
                  :disabled="true"
                >
                  <el-option
                    v-for="item in dict.type.ls_order_except_level"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item
                label="通知渠道："
                prop="notificationChannels"
                :label-width="formLabelWidth"
              >
                <el-checkbox-group
                  v-model="baseInfo.form.notificationChannels"
                  @change="checkChange()"
                  :disabled="type == 'detail'"
                >
                  <el-checkbox
                    v-for="(item, index) in channelList"
                    :key="index"
                    :label="item.value"
                  >
                    {{ item.label }}
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item
                label="web推送模版："
                prop="webPushTemplate"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.webPushTemplate"
                  placeholder="请选择web推送模版"
                  style="width: 100%"
                  :disabled="type == 'detail'"
                  clearable
                >
                  <el-option
                    v-for="item in webPushTemplateList"
                    :key="item.templateId"
                    :label="item.name"
                    :value="item.templateId"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="APP推送模版："
                prop="appPushTemplate"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.appPushTemplate"
                  placeholder="请选择APP推送模版"
                  style="width: 100%"
                  :disabled="type == 'detail'"
                  clearable
                >
                  <el-option
                    v-for="item in appPushTemplateList"
                    :key="item.templateId"
                    :label="item.name"
                    :value="item.templateId"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item
                label="微信小程序推送模版："
                prop="wxPushTemplate"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.wxPushTemplate"
                  placeholder="请选择微信小程序推送模版"
                  style="width: 100%"
                  :disabled="type == 'detail'"
                  clearable
                >
                  <el-option
                    v-for="item in wxPushTemplateList"
                    :key="item.templateId"
                    :label="item.name"
                    :value="item.templateId"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="短信推送模版："
                prop="smsPushTemplate"
                :label-width="formLabelWidth"
              >
                <el-select
                  v-model="baseInfo.form.smsPushTemplate"
                  placeholder="请选择短信推送模版"
                  style="width: 100%"
                  :disabled="type == 'detail'"
                  clearable
                >
                  <el-option
                    v-for="item in smsPushTemplateList"
                    :key="item.templateId"
                    :label="item.name"
                    :value="item.templateId"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <div class="bottom-wrap">
      <div>
        <el-button @click="handleCancel">取消</el-button>
        <el-button
          @click="handleSave"
          :loading="submitLoading"
          type="primary"
          v-if="type !== 'detail'"
        >
          提交
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>
<script>
import {
  listQuery,
  abnormalRuleDetail,
  templateList,
  orderAdd,
  orderUpdate,
} from '@/api/earlyWarningManage/abnormalOrderWarning';

export default {
  props: {
    type: {
      type: String,
      default: 'create',
    },
    detailObj: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  components: {},
  dicts: [
    'ls_order_except_level', // 异常级别
    'ls_order_except_type', // 异常类型
    'ls_order_except_measures', // 处理措施
    'ls_order_except_status', // 异常状态
    'ls_except_rule_item', // 异常规则
    'ls_charging_warning_notify_channel', // 计费预警通知渠道
  ],
  data() {
    return {
      dialogVisible: false,
      formLabelWidth: '120px',
      channelList: [],
      webPushTemplateList: [],
      appPushTemplateList: [],
      wxPushTemplateList: [],
      smsPushTemplateList: [],

      baseInfo: {
        form: {
          ruleName: '',
          type: '',
          name: '',
          exceptId: '',
          description: '',
          level: '',
          notificationChannels: [],
          webPushTemplate: '',
          appPushTemplate: '',
          wxPushTemplate: '',
          smsPushTemplate: '',
        },
        rules: {
          ruleName: [
            { required: true, message: '请输入预警规则名称', trigger: 'blur' },
          ],
          type: [
            {
              required: true,
              message: '请选择订单异常类型',
              trigger: 'change',
            },
          ],
          name: [
            {
              required: true,
              message: '请选择订单异常名称',
              trigger: 'change',
            },
          ],
          notificationChannels: [
            { required: true, message: '请选择通知渠道', trigger: 'change' },
          ],
          webPushTemplate: [
            {
              required: false,
              message: '请选择web推送模版',
              trigger: 'change',
            },
          ],
          appPushTemplate: [
            {
              required: false,
              message: '请选择APP推送模版',
              trigger: 'change',
            },
          ],
          wxPushTemplate: [
            {
              required: false,
              message: '请选择APP推送模版',
              trigger: 'change',
            },
          ],
          smsPushTemplate: [
            {
              required: false,
              message: '请选择短信推送模版',
              trigger: 'change',
            },
          ],
        },
      },
      submitLoading: false,
      exception: '',
      exceptionName: '',
      exceptionNameOptions: [],
    };
  },
  watch: {
    dialogVisible(value) {
      console.log(value, 888);
      if (!value) {
        this.baseInfo.form = {
          ruleName: '',
          type: '',
          name: '',
          exceptId: '',
          description: '',
          level: '',
          notificationChannels: [],
          webPushTemplate: '',
          appPushTemplate: '',
          wxPushTemplate: '',
          smsPushTemplate: '',
        };
      } else {
        if (Object.keys(this.detailObj)) {
          this.baseInfo.form = { ...this.baseInfo.form, ...this.detailObj };
        }
      }
    },
  },
  computed: {},
  async mounted() {
    this.channelList = this.dict.type.ls_charging_warning_notify_channel;
    const [err1, res1] = await templateList({ notifyChannel: 'web' });
    if (err1) return;
    this.webPushTemplateList = res1.data;
    const [err2, res2] = await templateList({ notifyChannel: 'app' });
    if (err2) return;
    this.appPushTemplateList = res2.data;
    const [err3, res3] = await templateList({ notifyChannel: 'wechat' });
    if (err3) return;
    this.wxPushTemplateList = res3.data;
    const [err4, res4] = await templateList({ notifyChannel: 'sms' });
    if (err4) return;
    this.smsPushTemplateList = res4.data;
  },
  methods: {
    async orderTypeChange(event) {
      console.log(event, 'exception');
      this.exception = event;
      await this.getNameList();
      if (this.exception && this.exceptionName) {
        this.getExceptionRuleDetail(this.exceptionName);
      }
    },
    async orderChange(event) {
      console.log(event, 'exceptionName');
      this.exceptionName = event;
      if (this.exception && this.exceptionName) {
        this.getExceptionRuleDetail(this.exceptionName);
      }
    },
    handleCancel() {
      this.dialogVisible = false;
    },
    // 保存
    handleSave() {
      this.$refs.baseInfoForm.validate(async (valid) => {
        if (valid) {
          let params = {
            ruleName: this.baseInfo.form.ruleName,
            abnormalId: this.baseInfo.form.exceptId,
            notifyChannels: [],
          };
          this.baseInfo.form.notificationChannels.forEach((item) => {
            if (item == 'web') {
              params.notifyChannels.push({
                notifyChannelType: item,
                notifyTemplateId: this.baseInfo.form.webPushTemplate,
              });
            } else if (item == 'app') {
              params.notifyChannels.push({
                notifyChannelType: item,
                notifyTemplateId: this.baseInfo.form.appPushTemplate,
              });
            } else if (item == 'wechat') {
              params.notifyChannels.push({
                notifyChannelType: item,
                notifyTemplateId: this.baseInfo.form.wxPushTemplate,
              });
            } else if (item == 'sms') {
              params.notifyChannels.push({
                notifyChannelType: item,
                notifyTemplateId: this.baseInfo.form.smsPushTemplate,
              });
            }
          });
          // console.log(params);
          if (this.type == 'create') {
            const [err, res] = await orderAdd(params);
            if (err) return;
            // console.log('res', res);
            this.handleCancel();
            this.$emit('orderruleAdd', true);
          } else if (this.type == 'edit') {
            params.id = this.detailObj.id;
            const [err, res] = await orderUpdate(params);
            if (err) return;
            this.handleCancel();
            this.$emit('orderruleAdd', true);
          }
        }
      });
    },
    // 选择通知渠道
    async checkChange() {
      this.baseInfo.rules.webPushTemplate[0].required = false;
      this.baseInfo.rules.appPushTemplate[0].required = false;
      this.baseInfo.rules.wxPushTemplate[0].required = false;
      this.baseInfo.rules.smsPushTemplate[0].required = false;
      this.baseInfo.form.notificationChannels.forEach(async (item) => {
        if (item == 'web') {
          this.baseInfo.rules.webPushTemplate[0].required = true;
        }
        if (item == 'app') {
          this.baseInfo.rules.appPushTemplate[0].required = true;
        }
        if (item == 'wechat') {
          this.baseInfo.rules.wxPushTemplate[0].required = true;
        }
        if (item == 'sms') {
          this.baseInfo.rules.smsPushTemplate[0].required = true;
        }
      });
    },
    // 获取名称列表
    async getNameList() {
      let params = {};
      const [err, res] = await listQuery(params);
      if (err) {
        return;
      }
      // console.log('res', res);
      this.exceptionNameOptions = res.data;
    },
    // 获取异常规则详情
    async getExceptionRuleDetail(id) {
      const [err, res] = await abnormalRuleDetail({
        exceptId: id,
      });
      if (err) {
        return;
      }
      // console.log('res1', res1);
      this.baseInfo.form.exceptId = res.data.exceptId;
      this.baseInfo.form.description = res.data.description;
      this.baseInfo.form.level = res.data.level;
    },
  },
};
</script>

<style lang="scss" scoped>
.container-full {
  background-color: rgba(244, 246, 249, 1);
  padding-bottom: 16px;
  min-height: calc(100% - 80px);
  overflow-y: auto;
}

.info-card {
  margin: 16px;
  border-radius: 5px;
  border: 1px solid #fff;
  overflow: hidden;
  background-color: #fff;
  // min-height: 300px;
  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    .before-icon {
      width: 3px;
      height: 16px;
      background-image: url('~@/assets/station/consno-before.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin-right: 8px;
    }
    .card-head-text {
      flex: 1;
      font-weight: 500;
      font-size: 16px;
      color: #12151a;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
    .button-wrap {
      display: flex;
      .invite-btn {
        background-color: #1ab2ff;
        border-color: #1ab2ff;
      }
      ::v-deep .el-button--small {
        font-size: 14px;
      }
      .distribution {
        margin-left: 24px;
        margin-right: 24px;
        display: flex;
        align-items: center;
      }
    }
  }

  .people-table-wrap {
    ::v-deep .bd3001-table-select-box {
      display: none;
    }
    ::v-deep .bd3001-button {
      width: 100%;
    }
  }
  .form-wrap {
    padding: 0 16px 16px 16px;
    .custom-header {
      background: -webkit-gradient(
          linear,
          left top,
          left bottom,
          from(rgba(0, 149, 255, 0.5)),
          to(rgba(87, 152, 255, 0))
        ),
        #f5faff;
      background: linear-gradient(
          180deg,
          rgba(0, 149, 255, 0.5) 0%,
          rgba(87, 152, 255, 0) 100%
        ),
        #f5faff;
      background-repeat: no-repeat;
    }
  }
}

.container {
  position: relative;
  padding-bottom: 100px;
  box-sizing: border-box;
  .bottom-wrap {
    // position: fixed;
    // bottom: 0;
    // left: 0;
    // width: 100%;
    // height: 86px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background-color: #ffffff;
    padding-right: 32px;
    box-sizing: border-box;
  }
}
::v-deep .hidden-label .el-form-item__label:before {
  visibility: hidden;
}
</style>
