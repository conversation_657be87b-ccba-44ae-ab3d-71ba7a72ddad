<template>
  <div class="container container-float" style="padding: 0">
    <!-- 异常规则管理标签栏内容 -->

    <div class="table-wrap">
      <BuseCrud
        ref="crud"
        :loading="manageLoading"
        :filterOptions="manageFilterOptions"
        :tablePage="manageTablePage"
        :tableColumn="manageTableColumn"
        :tableData="manageTableData"
        :pagerProps="pagerProps"
        :modalConfig="modalConfig"
        class="buse-wrap-station"
        @loadData="loadManageData"
      >
        <template slot="defaultHeader">
          <div>
            <div class="card-head">
              <div class="card-head-text">异常订单预警记录列表</div>
              <div class="top-button-wrap">
                <el-button type="primary" @click="handleOutput">导出</el-button>
              </div>
            </div>
          </div>
        </template>

        <template slot="orderId" slot-scope="{ row }">
          <div style="color: rgb(64, 158, 255); cursor: pointer; color: blue">
            {{ row.orderId }}
          </div>
        </template>
        <template slot="abnormalDescription" slot-scope="{ row }">
          <div
            style="color: rgb(64, 158, 255); cursor: pointer; color: blue"
            @click="handleDetail(row)"
          >
            {{ row.abnormalDescription }}
          </div>
        </template>
      </BuseCrud>
    </div>

    <el-dialog
      :title="warningConfigDetail.title"
      :visible.sync="warningConfigDetail.open"
      width="60%"
      append-to-body
      @close="handleCancel"
    >
      <div>
        <el-table :data="warningConfigDetail.tableData" style="width: 100%">
          <el-table-column
            prop="notifyChannelName"
            label="通知渠道"
            width="200"
          ></el-table-column>
          <el-table-column
            prop="notifyContent"
            label="预警通知内容"
          ></el-table-column>
          <el-table-column
            prop="notifyUserName"
            label="通知人"
            width="180"
          ></el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  orderWarningRecordPage,
  recordDetail,
} from '@/api/earlyWarningManage/abnormalOrderWarning';
import moment from 'moment';
export default {
  components: {},
  dicts: [
    'ls_order_except_level', // 异常级别
    'ls_order_except_type', // 异常类型
    'ls_order_except_status', // 异常状态
    'ls_charging_warning_notify_channel', // 计费预警通知渠道
  ],
  data() {
    return {
      activeName: 'manage',
      manageLoading: false,
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      params: {
        abnormalId: '',
        abnormalName: '',
        abnormalLevel: '',
        abnormalType: '',
        measures: '',
        warningTime: [],
      },
      manageTablePage: { total: 0, currentPage: 1, pageSize: 10 },
      manageTableColumn: [
        {
          type: 'seq',
          title: '序号',
          minWidth: 60,
          fixed: 'left',
        },
        {
          field: 'notifyRecordOrderId',
          title: '异常通知编号',
          minWidth: 180,
        },
        {
          // field: 'orderId',
          title: '订单编号',
          minWidth: 180,
          slots: { default: 'orderId' },
        },
        {
          field: 'abnormalId',
          title: '异常编号',
          minWidth: 180,
        },
        {
          field: 'abnormalLevel',
          title: '异常等级',
          minWidth: 100,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_order_except_level,
              cellValue
            );
          },
        },
        {
          field: 'abnormalType',
          title: '异常类型',
          minWidth: 120,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_order_except_type,
              cellValue
            );
          },
        },
        {
          field: 'abnormalName',
          title: '异常名称',
          minWidth: 200,
        },
        {
          // field: 'abnormalDescription',
          title: '异常描述',
          minWidth: 240,
          slots: { default: 'abnormalDescription' },
        },
        {
          field: 'warningTime',
          title: '预警时间',
          minWidth: 160,
        },
      ],
      manageTableData: [],
      warningConfigDetail: {
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: '预警规则详情',
        tableData: [],
      },
    };
  },

  computed: {
    manageFilterOptions() {
      return {
        config: [
          {
            field: 'abnormalId',
            title: '异常编号',
            element: 'el-input',
          },
          {
            field: 'abnormalName',
            title: '异常名称',
            element: 'el-input',
          },
          {
            field: 'abnormalLevel',
            title: '异常等级',
            element: 'el-select',
            props: {
              placeholder: '请选择异常等级',
              options: this.dict.type.ls_order_except_level,
            },
          },
          {
            field: 'abnormalType',
            title: '异常类型',
            element: 'el-select',
            props: {
              placeholder: '请选择异常类型',
              options: this.dict.type.ls_order_except_type,
            },
          },
          {
            field: 'warningTime',
            title: '预警时间',
            element: 'el-date-picker',
            props: {
              type: 'datetimerange',
              rangeSeparator: '至',
              startPlaceholder: '开始日期',
              endPlaceholder: '结束日期',
              valueFormat: 'yyyy-MM-dd HH:mm:ss',
              options: [],
              defaultTime: ['00:00:00', '23:59:59'],
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  mounted() {
    this.loadManageData();
  },
  methods: {
    // 获取管理列表数据
    async loadManageData() {
      const {
        abnormalId,
        abnormalName,
        abnormalLevel,
        abnormalType,
        measures,
        warningTime,
      } = this.manageFilterOptions.params;

      this.manageLoading = true;
      const [err, res] = await orderWarningRecordPage({
        abnormalId: abnormalId,
        abnormalName: abnormalName,
        abnormalType: abnormalType,
        abnormalLevel: abnormalLevel,
        measures: measures,
        warningTimeLeft: warningTime?.length > 0 ? warningTime[0] : '',
        warningTimeRight: warningTime?.length > 0 ? warningTime[1] : '',
        pageNum: this.manageTablePage.currentPage,
        pageSize: this.manageTablePage.pageSize,
      });

      this.manageLoading = false;

      if (err) return;
      const { data, total } = res;

      this.manageTableData = data;
      this.manageTablePage.total = total;
    },
    // 编辑异常规则
    handleRuleEdit(row) {
      const { abnormalId } = row;
      this.$router.push({
        path: '/v2g-charging/operatorManage/orderManage/abnormalRule/create',
        query: {
          abnormalId,
        },
      });
    },
    // 打开详情弹窗
    async handleDetail(row) {
      console.log(row);
      let params = {
        warningBizType: '',
        warningNotifyRecordId: row.notifyRecordOrderId,
      };
      if (row.warningBizType === '计费预警') {
        params.warningBizType = '01';
      } else if (row.warningBizType === '订单预警') {
        params.warningBizType = '02';
      }
      const [err, res] = await recordDetail(params);
      if (err) {
        return;
      }
      // console.log('计费预警记录详情', res);
      this.warningConfigDetail.tableData = res.data;
      let notificationChannelsOptions =
        this.dict.type.ls_charging_warning_notify_channel;
      // console.log('notificationChannelsOptions', notificationChannelsOptions);
      notificationChannelsOptions.forEach((item) => {
        this.warningConfigDetail.tableData.forEach((it) => {
          if (it.notifyChannel == item.value) {
            it.notifyChannelName = item.label;
          }
        });
      });
      this.warningConfigDetail.open = true;
    },
    // 弹窗关闭
    handleCancel() {
      this.warningConfigDetail.open = false;
    },
    // 导出
    handleOutput() {
      this.download(
        '/vehicle-charging-admin/warning/record/order/export',
        {},
        `订单预警记录.xlsx`
      );
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-tabs {
  margin-top: 16px;
  background-color: #f5f6f9;
  .el-tabs__header {
    padding-left: 0;
    display: flex;
    justify-content: center;
    text-align: center;
    margin-bottom: 1px;
    .el-tabs__item {
      padding: 0;
      width: 164px;
      font-size: 18px;
      font-weight: 400;
      background-color: #fff;
    }
    .el-tabs__item.is-active {
      background-color: #1677fe;
      color: #fff;
    }
    .el-tabs__nav-scroll {
      border-radius: 25px;
      border: solid 1px #dfe1e5;
    }
    .el-tabs__active-bar {
      display: none;
    }
    .el-tabs__nav-wrap::after {
      width: 0;
    }
  }
}

.table-wrap {
  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }
  margin: 16px;

  .card-head {
    // position: relative;
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    margin-top: -20px;
    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }
  .info-wrap {
    margin-top: 16px;
    display: flex;
    flex-wrap: wrap;
    gap: 16px;

    .info-item {
      background-color: #fafbfc;
      flex: 1 1 0;
      // min-width: 180px;

      border-radius: 5px;
      padding: 8px 24px;
      box-sizing: border-box;
      // margin-right: 16px;
      display: flex;
      .info-icon {
        width: 42px;
        height: 42px;
      }
      .info-right-wrap {
        flex: 1;
        margin-left: 8px;
        .info-title {
          font-weight: 400;
          font-size: 14px;
          line-height: 14px;
          margin-bottom: 8px;
        }
        .info-number {
          font-size: 20px;
          font-weight: 500;
          .info-unit {
            font-size: 14px;
            font-weight: 400;
          }
        }
      }
    }
    .info-item:last-child {
      margin-right: 0;
    }
  }

  .top-button-wrap {
    display: flex;
    margin: 16px 0;
  }
}
</style>
