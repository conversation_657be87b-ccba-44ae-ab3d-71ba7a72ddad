<template>
    <div class="container container-float " style="padding: 0;">
        <div class="info-card">
            <div class="card-head">
                <div class="before-icon"></div>
                <div class="card-head-text">线损预警规则</div>
            </div>
            <div class="card-head-split"></div>
            <div class="form-wrap">
                <el-form
                    :model="baseInfo.form"
                    :rules="baseInfo.rules"
                    ref="baseInfoForm"
                    label-position="top"
                >
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item label="线损预警规则名称" prop="rulerName">
                                <el-input 
                                    v-model="baseInfo.form.rulerName"
                                    placeholder="请输入线损预警规则名称"
                                ></el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="12">
                            <el-form-item label="充电站" prop="station">
                                <el-select
                                    v-model="baseInfo.form.station"
                                    placeholder="请选择充电站"
                                    style="width: 100%"
                                    multiple
                                >
                                    <el-option
                                        v-for="item in stationList"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="12">
                            <el-form-item label="预警周期" prop="cycle">
                                <el-select
                                    v-model="baseInfo.form.cycle"
                                    placeholder="请选择预警周期"
                                    style="width: 100%"
                                    @change="changeCycle"
                                >
                                    <el-option
                                        v-for="item in cycleList"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="12">
                            <el-form-item label="预警状态" prop="warningStatus">
                                <el-select
                                    v-model="baseInfo.form.warningStatus"
                                    placeholder="请选择预警状态"
                                    style="width: 100%"
                                >
                                    <el-option
                                        v-for="item in warningStatusList"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>

                <div class="ruler-wrap">
                    <el-form
                        :model="rulerInfo.form"
                        :rules="rulerInfo.rules"
                        ref="rulerInfoForm"
                        label-position="top"
                    >
                        <el-row :gutter="20">
                            <el-col :span="24">
                                <el-form-item label="预警规则" prop="rulerType">
                                    <el-radio-group
                                        v-model="rulerInfo.form.rulerType"
                                    >
                                        <el-radio
                                            v-for="dict in rulerTypeList"
                                            :key="dict.value"
                                            :label="dict.value"
                                        >
                                            {{ dict.label }}
                                        </el-radio>
                                    </el-radio-group>

                                </el-form-item>
                            </el-col>

                            <el-col :span="24">
                                <el-form-item label="组合方式" prop="rulerList">
                                    <div class="rule" v-for="(item,index) in rulerInfo.form.rulerList" :key="index">
                                        <div class="rule-item-wrap" >
                                            <el-select
                                                v-model="item.time"
                                                placeholder="请选择"
                                                style="width: 15%"
                                            >
                                                <el-option
                                                    v-for="item in timeList"
                                                    :key="item.value"
                                                    :label="item.label"
                                                    :value="item.value"
                                                ></el-option>
                                            </el-select>

                                            <el-select
                                                v-model="rate"
                                                placeholder=""
                                                style="width: 35%; margin-left: 8px"
                                                disabled
                                            >
                                                <el-option
                                                    v-for="item in rateList"
                                                    :key="item.value"
                                                    :label="item.label"
                                                    :value="item.value"
                                                ></el-option>
                                            </el-select>

                                            <el-select
                                                v-model="item.size"
                                                placeholder="请选择"
                                                style="width: 15%; margin-left: 8px"
                                            >
                                                <el-option
                                                    v-for="item in sizeList"
                                                    :key="item.value"
                                                    :label="item.label"
                                                    :value="item.value"
                                                ></el-option>
                                            </el-select>

                                            <el-select
                                                v-model="item.percent"
                                                placeholder="请选择"
                                                style="flex: 1; margin-left: 8px"
                                                v-if="item.percent !== '16'"
                                            >
                                                <el-option
                                                    v-for="item in percentList"
                                                    :key="item.value"
                                                    :label="item.label"
                                                    :value="item.value"
                                                ></el-option>
                                            </el-select>

                                            <el-select
                                                v-model="item.percent"
                                                placeholder="请选择"
                                                style="width: 15%; margin-left: 8px"
                                                v-if="item.percent === '16'"
                                            >
                                                <el-option
                                                    v-for="item in percentList"
                                                    :key="item.value"
                                                    :label="item.label"
                                                    :value="item.value"
                                                ></el-option>
                                            </el-select>

                                            <el-input 
                                                v-if="item.percent === '16'"
                                                v-model="item.inputPercent"
                                                style="flex: 1; margin-left: 8px"
                                                placeholder="请输入"
                                            ></el-input>
                                            <div v-if="item.percent === '16'">%</div>
                                            
                                        </div>

                                        <div class="delete-btn" v-if="rulerInfo.form.rulerList.length>1" @click="deleteItem(index)"></div>

                                    </div>

                                    <div class="add-btn" @click="addItem">+ 添加</div>
                                   
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>

                </div>

                <el-radio-group v-model="warningType" v-if="baseInfo.form.cycle === '1'" size="small" style="margin: 12px 0;">
                    <el-radio-button label="1">日预警</el-radio-button>
                    <el-radio-button label="2">周预警</el-radio-button>
                </el-radio-group>
                
                
                <div class="ruler-wrap" v-if="baseInfo.form.cycle !== '1'" style="margin: 24px 0;">
                    <el-form
                        :model="warnInfo.form"
                        :rules="warnInfo.rules"
                        ref="warnInfoForm"
                        label-position="top"
                    >
                        <el-row :gutter="20">
                            <el-col :span="24">
                                <el-form-item
                                    label="处理措施"
                                    prop="method"
                                >
                                    <el-checkbox-group
                                    v-model="warnInfo.form.method"
                                    >
                                        <el-checkbox
                                            v-for="item in methodList"
                                            :key="item.value"
                                            :label="item.value"
                                        >{{ item.label }}</el-checkbox>
                                    </el-checkbox-group>
                                </el-form-item>
                            </el-col>

                            <el-col :span="24">
                                <el-form-item
                                    label="通知渠道"
                                    prop="channel"
                                >
                                    <el-checkbox-group
                                        v-model="warnInfo.form.channel"
                                    >
                                        <el-checkbox
                                            v-for="item in channelList"
                                            :key="item.value"
                                            :label="item.value"
                                            
                                        >{{ item.label }}</el-checkbox>
                                    </el-checkbox-group>
                                </el-form-item>
                            </el-col>

                            <el-col :span="12" v-if="warnInfo.form.channel.includes('1')">
                                <el-form-item
                                    label="web推送模版"
                                    prop="webTemplate"
                                >
                                    <el-select 
                                        v-model="warnInfo.form.webTemplate"
                                        placeholder="请选择"
                                        style="width: 100%"
                                    >
                                        <el-option
                                            v-for="item in webTemplateList"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value"
                                        />
                                    </el-select>
                                </el-form-item>
                            </el-col>

                            <el-col :span="12" v-if="warnInfo.form.channel.includes('2')">
                                <el-form-item
                                    label="小程序推送模版"
                                    prop="wxTemplate"
                                >
                                    <el-select 
                                        v-model="warnInfo.form.wxTemplate"
                                        placeholder="请选择"
                                        style="width: 100%"
                                    >
                                        <el-option
                                            v-for="item in wxTemplateList"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value"
                                        />
                                    </el-select>
                                </el-form-item>
                            </el-col>

                            <el-col :span="12" v-if="warnInfo.form.channel.includes('3')">
                                <el-form-item
                                    label="app推送模版"
                                    prop="appTemplate"
                                >
                                    <el-select 
                                        v-model="warnInfo.form.appTemplate"
                                        placeholder="请选择"
                                        style="width: 100%"
                                    >
                                        <el-option
                                            v-for="item in appTemplateList"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value"
                                        />
                                    </el-select>
                                </el-form-item>
                            </el-col>

                            <el-col :span="12" v-if="warnInfo.form.channel.includes('4')">
                                <el-form-item
                                    label="短信推送模版"
                                    prop="messageTemplate"
                                >
                                    <el-select 
                                        v-model="warnInfo.form.messageTemplate"
                                        placeholder="请选择"
                                        style="width: 100%"
                                    >
                                        <el-option
                                            v-for="item in messageTemplateList"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value"
                                        />
                                    </el-select>
                                </el-form-item>
                            </el-col>

                            <el-col :span="24">
                                <el-form-item
                                    label="派单类型"
                                    prop="dispatchType"
                                >
                                    <el-radio-group v-model="warnInfo.form.dispatchType">
                                        <el-radio
                                            v-for="dict in dispatchTypeList"
                                            :key="dict.value"
                                            :label="dict.value"
                                        >
                                            {{ dict.label }}
                                        </el-radio>
                                    </el-radio-group>
                                </el-form-item>
                            </el-col>

                            <el-col :span="24">
                                <el-form-item
                                    label="派单目标"
                                    prop="dispatchTarget"
                                >
                                   <div style="display: flex; align-items: center;">
                                        <el-select 
                                            v-model="warnInfo.form.operatorRole"
                                            placeholder="请选择运营角色"
                                            style="width: 50%; margin-right: 8px;"
                                        >
                                            <el-option
                                                v-for="item in operatorRoleList"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value"
                                            />
                                        </el-select>
                                        <el-select 
                                            v-model="warnInfo.form.operator"
                                            placeholder="请选择运营人员"
                                            style="width: 50%"
                                        >
                                            <el-option
                                                v-for="item in operatorList"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value"
                                            />
                                        </el-select>
                                   </div>
                                    
                                </el-form-item>
                            </el-col>

                            <el-col :span="24">
                                <el-form-item
                                    label="派单时间"
                                    prop="dispatchTime"
                                >
                                    <div style="display: flex; align-items: center;">
                                        <el-select 
                                            v-model="warnInfo.form.dispatchTimeType"
                                            placeholder="请选择"
                                            style="width: 25%; margin-right: 8px;"
                                        >
                                            <el-option
                                                v-for="item in dispatchTimeTypeList"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value"
                                            />
                                        </el-select>


                                        <el-select 
                                            v-if="warnInfo.form.dispatchTimeType === '2'"
                                            style="width: 25%; margin-right: 8px"
                                            disabled
                                            v-model="rate"
                                        >
                                            <el-option key="1" label="预警后" value="1" />
                                        </el-select>

                                        <el-input 
                                            v-if="warnInfo.form.dispatchTimeType === '2'"
                                            v-model="warnInfo.form.dispatchTime"
                                            placeholder="请输入"
                                            style="width: 25%; margin-right: 8px"
                                        ></el-input>

                                        <el-select 
                                            v-if="warnInfo.form.dispatchTimeType === '2'"
                                            v-model="warnInfo.form.dispatchTimeUnit"
                                            placeholder="请选择"
                                            style="width: 25%;"
                                        >
                                            <el-option
                                                v-for="item in dispatchTimeUnitList"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value"
                                            />
                                        </el-select>

                                    </div>
                                </el-form-item>
                            </el-col>


                            <el-col :span="24">
                                <el-form-item
                                    label="计划完成时间"
                                    prop="planTime"
                                >
                                    <div style="display: flex; align-items: center;">
                                        <el-select 
                                            style="width: 33%; margin-right: 8px"
                                            disabled
                                            v-model="rate"
                                        >
                                            <el-option key="1" label="预警后" value="1" />
                                        </el-select>

                                        <el-input 
                                            v-model="warnInfo.form.planTime"
                                            placeholder="请输入"
                                            style="width: 33%; margin-right: 8px"
                                        ></el-input>

                                        <el-select 
                                            v-model="warnInfo.form.planTimeUnit"
                                            placeholder="请选择"
                                            style="width: 33%;"
                                        >
                                            <el-option
                                                v-for="item in planTimeUnitList"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value"
                                            />
                                        </el-select>
                                    </div>
                                </el-form-item>

                                
                            </el-col>

                            <el-col :span="24">
                                <el-form-item
                                    label="紧急程度"
                                    prop="level"
                                >
                                    <el-select 
                                            v-model="warnInfo.form.level"
                                            placeholder="请选择"
                                            style="width:100%"
                                        >
                                            <el-option
                                                v-for="item in levelList"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value"
                                            />
                                        </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </div>

                <div class="ruler-wrap" v-else>
                    <el-form
                        v-if="warningType === '1'"
                        :model="warnInfo.daily"
                        :rules="warnInfo.dailyRules"
                        ref="warnInfoDailyForm"
                        label-position="top"
                    >
                        <el-row :gutter="20">
                            <el-col :span="24">
                                <el-form-item
                                    label="处理措施"
                                    prop="method"
                                >
                                    <el-checkbox-group
                                    v-model="warnInfo.daily.method"
                                    >
                                        <el-checkbox
                                            v-for="item in methodList"
                                            :key="item.value"
                                            :label="item.value"
                                        >{{ item.label }}</el-checkbox>
                                    </el-checkbox-group>
                                </el-form-item>
                            </el-col>

                            <el-col :span="24">
                                <el-form-item
                                    label="通知渠道"
                                    prop="channel"
                                >
                                    <el-checkbox-group
                                        v-model="warnInfo.daily.channel"
                                    >
                                        <el-checkbox
                                            v-for="item in channelList"
                                            :key="item.value"
                                            :label="item.value"
                                            
                                        >{{ item.label }}</el-checkbox>
                                    </el-checkbox-group>
                                </el-form-item>
                            </el-col>

                            <el-col :span="12" v-if="warnInfo.daily.channel.includes('1')">
                                <el-form-item
                                    label="web推送模版"
                                    prop="webTemplate"
                                >
                                    <el-select 
                                        v-model="warnInfo.daily.webTemplate"
                                        placeholder="请选择"
                                        style="width: 100%"
                                    >
                                        <el-option
                                            v-for="item in webTemplateList"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value"
                                        />
                                    </el-select>
                                </el-form-item>
                            </el-col>

                            <el-col :span="12" v-if="warnInfo.daily.channel.includes('2')">
                                <el-form-item
                                    label="小程序推送模版"
                                    prop="wxTemplate"
                                >
                                    <el-select 
                                        v-model="warnInfo.daily.wxTemplate"
                                        placeholder="请选择"
                                        style="width: 100%"
                                    >
                                        <el-option
                                            v-for="item in wxTemplateList"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value"
                                        />
                                    </el-select>
                                </el-form-item>
                            </el-col>

                            <el-col :span="12" v-if="warnInfo.daily.channel.includes('3')">
                                <el-form-item
                                    label="app推送模版"
                                    prop="appTemplate"
                                >
                                    <el-select 
                                        v-model="warnInfo.daily.appTemplate"
                                        placeholder="请选择"
                                        style="width: 100%"
                                    >
                                        <el-option
                                            v-for="item in appTemplateList"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value"
                                        />
                                    </el-select>
                                </el-form-item>
                            </el-col>

                            <el-col :span="12" v-if="warnInfo.daily.channel.includes('4')">
                                <el-form-item
                                    label="短信推送模版"
                                    prop="messageTemplate"
                                >
                                    <el-select 
                                        v-model="warnInfo.daily.messageTemplate"
                                        placeholder="请选择"
                                        style="width: 100%"
                                    >
                                        <el-option
                                            v-for="item in messageTemplateList"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value"
                                        />
                                    </el-select>
                                </el-form-item>
                            </el-col>

                            <el-col :span="24">
                                <el-form-item
                                    label="派单类型"
                                    prop="dispatchType"
                                >
                                    <el-radio-group v-model="warnInfo.daily.dispatchType">
                                        <el-radio
                                            v-for="dict in dispatchTypeList"
                                            :key="dict.value"
                                            :label="dict.value"
                                        >
                                            {{ dict.label }}
                                        </el-radio>
                                    </el-radio-group>
                                </el-form-item>
                            </el-col>

                            <el-col :span="24">
                                <el-form-item
                                    label="派单目标"
                                    prop="dispatchTarget"
                                >
                                   <div style="display: flex; align-items: center;">
                                        <el-select 
                                            v-model="warnInfo.daily.operatorRole"
                                            placeholder="请选择运营角色"
                                            style="width: 50%; margin-right: 8px;"
                                        >
                                            <el-option
                                                v-for="item in operatorRoleList"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value"
                                            />
                                        </el-select>
                                        <el-select 
                                            v-model="warnInfo.daily.operator"
                                            placeholder="请选择运营人员"
                                            style="width: 50%"
                                        >
                                            <el-option
                                                v-for="item in operatorList"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value"
                                            />
                                        </el-select>
                                   </div>
                                    
                                </el-form-item>
                            </el-col>

                            <el-col :span="24">
                                <el-form-item
                                    label="派单时间"
                                    prop="dispatchTime"
                                >
                                    <div style="display: flex; align-items: center;">
                                        <el-select 
                                            v-model="warnInfo.daily.dispatchTimeType"
                                            placeholder="请选择"
                                            style="width: 25%; margin-right: 8px;"
                                        >
                                            <el-option
                                                v-for="item in dispatchTimeTypeList"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value"
                                            />
                                        </el-select>


                                        <el-select 
                                            v-if="warnInfo.daily.dispatchTimeType === '2'"
                                            style="width: 25%; margin-right: 8px"
                                            disabled
                                            v-model="rate"
                                        >
                                            <el-option key="1" label="预警后" value="1" />
                                        </el-select>

                                        <el-input 
                                            v-if="warnInfo.daily.dispatchTimeType === '2'"
                                            v-model="warnInfo.daily.dispatchTime"
                                            placeholder="请输入"
                                            style="width: 25%; margin-right: 8px"
                                        ></el-input>

                                        <el-select 
                                            v-if="warnInfo.daily.dispatchTimeType === '2'"
                                            v-model="warnInfo.daily.dispatchTimeUnit"
                                            placeholder="请选择"
                                            style="width: 25%;"
                                        >
                                            <el-option
                                                v-for="item in dispatchTimeUnitList"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value"
                                            />
                                        </el-select>

                                    </div>
                                </el-form-item>
                            </el-col>


                            <el-col :span="24">
                                <el-form-item
                                    label="计划完成时间"
                                    prop="planTime"
                                >
                                    <div style="display: flex; align-items: center;">
                                        <el-select 
                                            style="width: 33%; margin-right: 8px"
                                            disabled
                                            v-model="rate"
                                        >
                                            <el-option key="1" label="预警后" value="1" />
                                        </el-select>

                                        <el-input 
                                            v-model="warnInfo.daily.planTime"
                                            placeholder="请输入"
                                            style="width: 33%; margin-right: 8px"
                                        ></el-input>

                                        <el-select 
                                            v-model="warnInfo.daily.planTimeUnit"
                                            placeholder="请选择"
                                            style="width: 33%;"
                                        >
                                            <el-option
                                                v-for="item in planTimeUnitList"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value"
                                            />
                                        </el-select>
                                    </div>
                                </el-form-item>

                                
                            </el-col>

                            <el-col :span="24">
                                <el-form-item
                                    label="紧急程度"
                                    prop="level"
                                >
                                    <el-select 
                                            v-model="warnInfo.daily.level"
                                            placeholder="请选择"
                                            style="width:100%"
                                        >
                                            <el-option
                                                v-for="item in levelList"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value"
                                            />
                                        </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>


                    <el-form
                        v-if="warningType === '2'"
                        :model="warnInfo.weekly"
                        :rules="warnInfo.weeklyRules"
                        ref="warnInfoWeeklyForm"
                        label-position="top"
                    >
                        <el-row :gutter="20">
                            <el-col :span="24">
                                <el-form-item
                                    label="处理措施"
                                    prop="method"
                                >
                                    <el-checkbox-group
                                    v-model="warnInfo.weekly.method"
                                    >
                                        <el-checkbox
                                            v-for="item in methodList"
                                            :key="item.value"
                                            :label="item.value"
                                        >{{ item.label }}</el-checkbox>
                                    </el-checkbox-group>
                                </el-form-item>
                            </el-col>

                            <el-col :span="24">
                                <el-form-item
                                    label="通知渠道"
                                    prop="channel"
                                >
                                    <el-checkbox-group
                                        v-model="warnInfo.weekly.channel"
                                    >
                                        <el-checkbox
                                            v-for="item in channelList"
                                            :key="item.value"
                                            :label="item.value"
                                            
                                        >{{ item.label }}</el-checkbox>
                                    </el-checkbox-group>
                                </el-form-item>
                            </el-col>

                            <el-col :span="12" v-if="warnInfo.weekly.channel.includes('1')">
                                <el-form-item
                                    label="web推送模版"
                                    prop="webTemplate"
                                >
                                    <el-select 
                                        v-model="warnInfo.weekly.webTemplate"
                                        placeholder="请选择"
                                        style="width: 100%"
                                    >
                                        <el-option
                                            v-for="item in webTemplateList"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value"
                                        />
                                    </el-select>
                                </el-form-item>
                            </el-col>

                            <el-col :span="12" v-if="warnInfo.weekly.channel.includes('2')">
                                <el-form-item
                                    label="小程序推送模版"
                                    prop="wxTemplate"
                                >
                                    <el-select 
                                        v-model="warnInfo.weekly.wxTemplate"
                                        placeholder="请选择"
                                        style="width: 100%"
                                    >
                                        <el-option
                                            v-for="item in wxTemplateList"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value"
                                        />
                                    </el-select>
                                </el-form-item>
                            </el-col>

                            <el-col :span="12" v-if="warnInfo.weekly.channel.includes('3')">
                                <el-form-item
                                    label="app推送模版"
                                    prop="appTemplate"
                                >
                                    <el-select 
                                        v-model="warnInfo.weekly.appTemplate"
                                        placeholder="请选择"
                                        style="width: 100%"
                                    >
                                        <el-option
                                            v-for="item in appTemplateList"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value"
                                        />
                                    </el-select>
                                </el-form-item>
                            </el-col>

                            <el-col :span="12" v-if="warnInfo.weekly.channel.includes('4')">
                                <el-form-item
                                    label="短信推送模版"
                                    prop="messageTemplate"
                                >
                                    <el-select 
                                        v-model="warnInfo.weekly.messageTemplate"
                                        placeholder="请选择"
                                        style="width: 100%"
                                    >
                                        <el-option
                                            v-for="item in messageTemplateList"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value"
                                        />
                                    </el-select>
                                </el-form-item>
                            </el-col>

                            <el-col :span="24">
                                <el-form-item
                                    label="派单类型"
                                    prop="dispatchType"
                                >
                                    <el-radio-group v-model="warnInfo.weekly.dispatchType">
                                        <el-radio
                                            v-for="dict in dispatchTypeList"
                                            :key="dict.value"
                                            :label="dict.value"
                                        >
                                            {{ dict.label }}
                                        </el-radio>
                                    </el-radio-group>
                                </el-form-item>
                            </el-col>

                            <el-col :span="24">
                                <el-form-item
                                    label="派单目标"
                                    prop="dispatchTarget"
                                >
                                   <div style="display: flex; align-items: center;">
                                        <el-select 
                                            v-model="warnInfo.weekly.operatorRole"
                                            placeholder="请选择运营角色"
                                            style="width: 50%; margin-right: 8px;"
                                        >
                                            <el-option
                                                v-for="item in operatorRoleList"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value"
                                            />
                                        </el-select>
                                        <el-select 
                                            v-model="warnInfo.weekly.operator"
                                            placeholder="请选择运营人员"
                                            style="width: 50%"
                                        >
                                            <el-option
                                                v-for="item in operatorList"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value"
                                            />
                                        </el-select>
                                   </div>
                                    
                                </el-form-item>
                            </el-col>

                            <el-col :span="24">
                                <el-form-item
                                    label="派单时间"
                                    prop="dispatchTime"
                                >
                                    <div style="display: flex; align-items: center;">
                                        <el-select 
                                            v-model="warnInfo.weekly.dispatchTimeType"
                                            placeholder="请选择"
                                            style="width: 25%; margin-right: 8px;"
                                        >
                                            <el-option
                                                v-for="item in dispatchTimeTypeList"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value"
                                            />
                                        </el-select>


                                        <el-select 
                                            v-if="warnInfo.weekly.dispatchTimeType === '2'"
                                            style="width: 25%; margin-right: 8px"
                                            disabled
                                            v-model="rate"
                                        >
                                            <el-option key="1" label="预警后" value="1" />
                                        </el-select>

                                        <el-input 
                                            v-if="warnInfo.weekly.dispatchTimeType === '2'"
                                            v-model="warnInfo.weekly.dispatchTime"
                                            placeholder="请输入"
                                            style="width: 25%; margin-right: 8px"
                                        ></el-input>

                                        <el-select 
                                            v-if="warnInfo.weekly.dispatchTimeType === '2'"
                                            v-model="warnInfo.weekly.dispatchTimeUnit"
                                            placeholder="请选择"
                                            style="width: 25%;"
                                        >
                                            <el-option
                                                v-for="item in dispatchTimeUnitList"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value"
                                            />
                                        </el-select>

                                    </div>
                                </el-form-item>
                            </el-col>


                            <el-col :span="24">
                                <el-form-item
                                    label="计划完成时间"
                                    prop="planTime"
                                >
                                    <div style="display: flex; align-items: center;">
                                        <el-select 
                                            style="width: 33%; margin-right: 8px"
                                            disabled
                                            v-model="rate"
                                        >
                                            <el-option key="1" label="预警后" value="1" />
                                        </el-select>

                                        <el-input 
                                            v-model="warnInfo.weekly.planTime"
                                            placeholder="请输入"
                                            style="width: 33%; margin-right: 8px"
                                        ></el-input>

                                        <el-select 
                                            v-model="warnInfo.weekly.planTimeUnit"
                                            placeholder="请选择"
                                            style="width: 33%;"
                                        >
                                            <el-option
                                                v-for="item in planTimeUnitList"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value"
                                            />
                                        </el-select>
                                    </div>
                                </el-form-item>

                                
                            </el-col>

                            <el-col :span="24">
                                <el-form-item
                                    label="紧急程度"
                                    prop="level"
                                >
                                    <el-select 
                                            v-model="warnInfo.weekly.level"
                                            placeholder="请选择"
                                            style="width:100%"
                                        >
                                            <el-option
                                                v-for="item in levelList"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value"
                                            />
                                        </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </div>
            </div>
        </div>

        <div class="bottom-button-wrap">
            <div>
                <el-button @click="handleCancel">取消</el-button>
                <el-button type="primary" @click="handleSave">保存</el-button>
            </div>
        </div>
    </div>
    
  </template>
  
  <script>
  
    export default {
    components: {
        
    },
    dicts: [],
    data() {
      return {
        baseInfo: {
            form: {
                rulerName: '',
                station: [],
                cycle: '1',
                warningStatus: '',
            },
            rules: {
                rulerName: [
                    { required: true, message: '请输入线损预警规则名称', trigger: 'blur' },
                ],
                station: [
                    { required: true, message: '请选择充电站', trigger: 'change' },
                ],
                cycle: [
                    { required: true, message: '请选择预警周期', trigger: 'change' },
                ],
                warningStatus: [
                    { required: true, message: '请选择预警状态', trigger: 'change' },
                ]
            },
            
        },
        stationList: [
            { label: '充电站1', value: '1' },
            { label: '充电站2', value: '2' },
            { label: '充电站3', value: '3' },
        ],
        cycleList: [
            { label: '日预警;周预警', value: '1' },
            { label: '周预警', value: '2' },
            { label: '月预警', value: '3' },
            { label: '季预警', value: '4' },


        ],
        warningStatusList: [
            { label: '启用', value: '1' },
            { label: '停用', value: '0' },
        ],
        rulerInfo: {
            form: {
                rulerType: '',
                rulerList: [
                    {
                        time: '1',
                        size: '1',
                        percent: '',
                        inputPercent: '',
                    },
                ],
            },
            rules: {
            },
        },
        rulerTypeList: [
            { label: '同时满足所有条件', value: '1' },
            { label: '满足任一条件', value: '2' },
        ],
        timeList: [
            { label: '日', value: '1' },
            { label: '周', value: '2' },
            { label: '月', value: '3' },
            { label: '季', value: '4' },
        ],
        sizeList:[
            { label: '≤', value: '1' },
            { label: '≥', value: '2' },
            { label: '=', value: '3' },
            { label: '＜', value: '4' },
            { label: '＞', value: '5' },
        ],
        percentList: [
            { label: '-20%', value: '1' },
            { label: '-10%', value: '2' },
            { label: '0%', value: '3' },
            { label: '10%', value: '4' },
            { label: '20%', value: '5' },
            { label: '30%', value: '6' },
            { label: '40%', value: '7' },
            { label: '50%', value: '8' },
            { label: '60%', value: '9' },
            { label: '70%', value: '10' },
            { label: '80%', value: '11' },
            { label: '90%', value: '12' },
            { label: '100%', value: '13' },
            { label: '110%', value: '14' },
            { label: '120%', value: '15' },
            { label: '自定义', value: '16' },
        ],
        rate: '1',
        rateList:[
            { label: '线损率', value: '1' },
        ],

        warningType: '1',

        warnInfo: {
            daily: {
                method: [],
                channel: [],
                webTemplate: '',
                wxTemplate: '',
                appTemplate: '',
                messageTemplate: '',
                dispatchType: '',
                operatorRole: '',
                operator: '',
                dispatchTimeType: '',
                dispatchTime: '',
                dispatchTimeUnit: '',
                planTime: '',
                planTimeUnit: '',
                level: '',
            },

            dailyRules: {
                method: [
                    { required: true, message: '请选择预警措施', trigger: 'change' }
                ],
                channel:[
                    { required: true, message: '请选择通知渠道', trigger: 'change' }
                ],
                webTemplate: [
                    { required: true, message: '请选择web推送模板', trigger: 'change' }
                ],
                wxTemplate: [
                    { required: true, message: '请选择微信推送模板', trigger: 'change' }
                ],
                appTemplate: [
                    { required: true, message: '请选择app推送模板', trigger: 'change' }
                ],
                messageTemplate: [
                    { required: true, message: '请选择短信推送模板', trigger: 'change' }
                ],
                dispatchType: [
                    { required: true, message: '请选择派单类型', trigger: 'change' }
                ],
                dispatchTarget: [
                    {
                        required: true, 
                        validator: (rule, value, callback) => {
                            if(!this.warnInfo.daily.operatorRole || !this.warnInfo.daily.operator) {
                                callback(new Error('请选择派单目标')); 
                            } else {
                                callback();
                            }
                        },
                        trigger: 'change',
                    }
                    
                ],
                dispatchTime: [
                    {
                        required: true, 
                        validator: (rule, value, callback) => {
                            if(!this.warnInfo.daily.dispatchTimeType) {
                                callback(new Error('请选择派单时间')); 
                            } else {
                                if(this.warnInfo.daily.dispatchTimeType === '2' && (!this.warnInfo.daily.dispatchTime || !this.warnInfo.form.dispatchTimeUnit)) {
                                    callback(new Error('请输入派单时间')); 
                                } else {
                                    callback();
                                }
                            }
                        },
                        trigger: 'change',
                    }
                ],
                planTime: [
                    {
                        required: true, 
                        validator: (rule, value, callback) => {
                            if(!this.warnInfo.daily.planTime || !this.warnInfo.daily.planTimeUnit) {
                                callback(new Error('请输入计划完成时间')); 
                            } else {
                                callback();
                            }
                        },
                        trigger: 'change',
                    }
                ],
                level: [
                    { required: true, message: '请选择紧急程度', trigger: 'change' }    
                ]
            },
            weekly: {
                method: [],
                channel: [],
                webTemplate: '',
                wxTemplate: '',
                appTemplate: '',
                messageTemplate: '',
                dispatchType: '',
                operatorRole: '',
                operator: '',
                dispatchTimeType: '',
                dispatchTime: '',
                dispatchTimeUnit: '',
                planTime: '',
                planTimeUnit: '',
                level: '',
            },

            weeklyRules: {
                method: [
                    { required: true, message: '请选择预警措施', trigger: 'change' }
                ],
                channel:[
                    { required: true, message: '请选择通知渠道', trigger: 'change' }
                ],
                webTemplate: [
                    { required: true, message: '请选择web推送模板', trigger: 'change' }
                ],
                wxTemplate: [
                    { required: true, message: '请选择微信推送模板', trigger: 'change' }
                ],
                appTemplate: [
                    { required: true, message: '请选择app推送模板', trigger: 'change' }
                ],
                messageTemplate: [
                    { required: true, message: '请选择短信推送模板', trigger: 'change' }
                ],
                dispatchType: [
                    { required: true, message: '请选择派单类型', trigger: 'change' }
                ],
                dispatchTarget: [
                    {
                        required: true, 
                        validator: (rule, value, callback) => {
                            if(!this.warnInfo.weekly.operatorRole || !this.warnInfo.weekly.operator) {
                                callback(new Error('请选择派单目标')); 
                            } else {
                                callback();
                            }
                        },
                        trigger: 'change',
                    }
                    
                ],
                dispatchTime: [
                    {
                        required: true, 
                        validator: (rule, value, callback) => {
                            if(!this.warnInfo.weekly.dispatchTimeType) {
                                callback(new Error('请选择派单时间')); 
                            } else {
                                if(this.warnInfo.weekly.dispatchTimeType === '2' && (!this.warnInfo.weekly.dispatchTime || !this.warnInfo.form.dispatchTimeUnit)) {
                                    callback(new Error('请输入派单时间')); 
                                } else {
                                    callback();
                                }
                            }
                        },
                        trigger: 'change',
                    }
                ],
                planTime: [
                    {
                        required: true, 
                        validator: (rule, value, callback) => {
                            if(!this.warnInfo.weekly.planTime || !this.warnInfo.weekly.planTimeUnit) {
                                callback(new Error('请输入计划完成时间')); 
                            } else {
                                callback();
                            }
                        },
                        trigger: 'change',
                    }
                ],
                level: [
                    { required: true, message: '请选择紧急程度', trigger: 'change' }    
                ]
            },

            form: {
                method: [],
                channel: [],
                webTemplate: '',
                wxTemplate: '',
                appTemplate: '',
                messageTemplate: '',
                dispatchType: '',
                operatorRole: '',
                operator: '',
                dispatchTimeType: '',
                dispatchTime: '',
                dispatchTimeUnit: '',
                planTime: '',
                planTimeUnit: '',
                level: '',
            },
                
            rules: {
                method: [
                    { required: true, message: '请选择预警措施', trigger: 'change' }
                ],
                channel:[
                    { required: true, message: '请选择通知渠道', trigger: 'change' }
                ],
                webTemplate: [
                    { required: true, message: '请选择web推送模板', trigger: 'change' }
                ],
                wxTemplate: [
                    { required: true, message: '请选择微信推送模板', trigger: 'change' }
                ],
                appTemplate: [
                    { required: true, message: '请选择app推送模板', trigger: 'change' }
                ],
                messageTemplate: [
                    { required: true, message: '请选择短信推送模板', trigger: 'change' }
                ],
                dispatchType: [
                    { required: true, message: '请选择派单类型', trigger: 'change' }
                ],
                dispatchTarget: [
                    {
                        required: true, 
                        validator: (rule, value, callback) => {
                            if(!this.warnInfo.form.operatorRole || !this.warnInfo.form.operator) {
                                callback(new Error('请选择派单目标')); 
                            } else {
                                callback();
                            }
                        },
                        trigger: 'change',
                    }
                    
                ],
                dispatchTime: [
                    {
                        required: true, 
                        validator: (rule, value, callback) => {
                            if(!this.warnInfo.form.dispatchTimeType) {
                                callback(new Error('请选择派单时间')); 
                            } else {
                                if(this.warnInfo.form.dispatchTimeType === '2' && (!this.warnInfo.form.dispatchTime || !this.warnInfo.form.dispatchTimeUnit)) {
                                    callback(new Error('请输入派单时间')); 
                                } else {
                                    callback();
                                }
                            }
                        },
                        trigger: 'change',
                    }
                ],
                planTime: [
                    {
                        required: true, 
                        validator: (rule, value, callback) => {
                            if(!this.warnInfo.form.planTime || !this.warnInfo.form.planTimeUnit) {
                                callback(new Error('请输入计划完成时间')); 
                            } else {
                                callback();
                            }
                        },
                        trigger: 'change',
                    }
                ],
                level: [
                    { required: true, message: '请选择紧急程度', trigger: 'change' }    
                ]
            }
        },

        methodList: [
            { label: '预警通知', value: '1' },
            { label: '预警派单', value: '2' },
        ],

        channelList: [
            { label: 'web', value: '1' },
            { label: '小程序', value: '2' },
            { label: 'app', value: '3' },
            { label: '短信', value: '4' },
        ],

        dispatchTypeList: [
            { label: '手动派单', value: '1' },
            { label: '自动派单', value: '2' },
        ],

        operatorRoleList: [
            { label: '运维人员', value: '1' },
            { label: '运维主管', value: '2' },
            { label: '运维经理', value: '3' },
        ],

        operatorList: [
            { label: '张三', value: '1' },
            { label: '李四', value: '2' },
        ],

        dispatchTimeTypeList: [
            { label: '预警后立即派单', value: '1' },
            { label: '自定义', value: '2' },
        ],

        dispatchTimeUnitList: [
            { label: '天', value: '1' },
            { label: '小时', value: '2' },
        ],

        planTimeUnitList: [
            { label: '天', value: '1' },
            { label: '小时', value: '2' },
        ],

        levelList: [
            { label: '非常紧急', value: '1' },
            { label: '紧急', value: '2' },
            { label: '一般', value: '3' },
        ]


      };
    },

    computed: {
    },
    mounted() {

    },
    methods: {

        // 新增预警规则
        addItem() {
            this.rulerInfo.form.rulerList.push({
                time: '1',
                size: '1',
                percent: '',
                inputPercent: '',
            })
        },
        // 删除预警规则
        deleteItem(index) {
            this.rulerInfo.form.rulerList.splice(index, 1)
        },
        changeCycle() {
            if(this.baseInfo.form.cycle === '1') {
                // 日预警周预警
                this.warningType = '1'
                this.warnInfo.daily = {
                    method: [],
                    channel: [],
                    webTemplate: '',
                    wxTemplate: '',
                    appTemplate: '',
                    messageTemplate: '',
                    dispatchType: '',
                    operatorRole: '',
                    operator: '',
                    dispatchTimeType: '',
                    dispatchTime: '',
                    dispatchTimeUnit: '',
                    planTime: '',
                    planTimeUnit: '',
                    level: '',
                }

                this.warnInfo.weekly = {
                    method: [],
                    channel: [],
                    webTemplate: '',
                    wxTemplate: '',
                    appTemplate: '',
                    messageTemplate: '',
                    dispatchType: '',
                    operatorRole: '',
                    operator: '',
                    dispatchTimeType: '',
                    dispatchTime: '',
                    dispatchTimeUnit: '',
                    planTime: '',
                    planTimeUnit: '',
                    level: '',
                }
            } else {
                // 其他预警
                this.warnInfo.form = {
                    method: [],
                    channel: [],
                    webTemplate: '',
                    wxTemplate: '',
                    appTemplate: '',
                    messageTemplate: '',
                    dispatchType: '',
                    operatorRole: '',
                    operator: '',
                    dispatchTimeType: '',
                    dispatchTime: '',
                    dispatchTimeUnit: '',
                    planTime: '',
                    planTimeUnit: '',
                    level: '',
                }
            }
        }
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .container-full {
    background-color: rgba(244, 246, 249, 1);
    padding-bottom: 16px;
    min-height: calc(100% - 80px);
    overflow-y: auto;
  }


  .info-card {
    margin: 16px;
    border-radius: 5px;
    border: 1px solid #fff;
    background-color: #fff;

    .card-head {
      height: 56px;
      padding: 0 16px;
      display: flex;
      align-items: center;

      .before-icon {
        width: 3px;
        height: 16px;
        background-image: url('~@/assets/station/consno-before.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        margin-right: 8px;
      }

      .card-head-text {
        font-weight: 500;
        font-size: 16px;
        color: #12151a;
      }
    }

    .card-head-split {
        height: 1.5px;
        background-color: #F9F9FB;
    }
    .form-wrap {
      padding: 16px;
      .flex-box {
        display: flex;
        gap: 6px;
        align-items: center;
        margin-bottom: 20px;
      }
    }
  }
  .bottom-wrap {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 86px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background-color: #ffffff;
    padding-right: 32px;
    box-sizing: border-box;
  }


  .ruler-wrap {
    width: 100%;
    background-color: #F9F9FB;
    padding: 16px;
    box-sizing: border-box;
    border-radius: 2px;
    .rule {
        display: flex;
        align-items: center;
        height: 38px;
        margin-bottom: 16px;
        .rule-item-wrap {
            width: 95%;
            display: flex;
            align-items: center;
            height: 38px;
        }
        .delete-btn {
            width: 36px;
            height: 38px;
            background-image: url('~@/assets/charge/delete-icon.png');
            background-size: 100% 100%;
            background-repeat: no-repeat;
        }
    }
    .add-btn {
        width: 95%;
        height: 34px;
        border-radius: 2px;
        border: #DFE1E5 1px solid;
        background-color: #ffffff;
        color: #217AFF;
        font-family: PingFang SC;
        font-weight: 400;
        font-size: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        

    }
    

  }

 
  .bottom-button-wrap {
  height: 86px;
  margin-top: 16px;
  width: 100%;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: 32px;
  box-sizing: border-box;
}
  </style>
  