<template>
    <div class="container container-float " >
        <div class="table-wrap">
            <BuseCrud
                ref="crud"
                :loading="loading"
                :filterOptions="filterOptions"
                :tablePage="tablePage"
                :tableColumn="tableColumn"
                :tableData="tableData"
                :pagerProps="pagerProps"
                :modalConfig="modalConfig"
                class="buse-wrap-station"
                @loadData="loadData"
            >
            <template slot="defaultHeader">
                <div>
                    <div class="card-head">
                        <div class="card-head-text">线损预警记录列表</div>
                    </div>
                    

                    
                </div>
                
            </template>

                    <template slot="operate" slot-scope="{ row }">
                        <div class="menu-box">
                            <el-button
                            class="button-border"
                            @click="handleDispatch(row)"
                            >
                                派单
                            </el-button>
                            <el-button
                                class="button-border"
                                @click="hanleDetail(row)"
                            >
                                详情
                            </el-button>

                        

                        



                    
                        </div>
                    
                    </template>

            </BuseCrud>
        </div>

        <dispatchModal ref="dispatchModal" />
    </div>
    
  </template>
  
  <script>
import { Statistic } from 'element-ui';
import dispatchModal from './components/dispatchModal.vue'

  
    export default {
    components: {
        dispatchModal
    },
    dicts: [],
    data() {
        return {
            loading: false,
            tablePage: { total: 0, currentPage: 1, pageSize: 10 },
            tableColumn: [
                {
                type: 'seq',
                title: '序号',
                width: 60,
                minWidth: 60,
                },
                {
                field: 'warningId',
                title: '预警编号',
                minWidth: 180,
                },
                {
                field: 'warningCycle',
                title: '预警周期',
                minWidth: 180,
                },
                {
                field: 'warningTime',
                title: '预警时间',
                minWidth: 180,
                },
                {
                field: 'statisticCycle',
                title: '统计周期',
                minWidth: 180,
                },
                {
                field: 'warningContent',
                title: '预警内容',
                minWidth: 180,
                },
                {
                field: 'ruleName',
                title: '预警规则名称',
                minWidth: 180,
                },
                {
                field: 'rulerId',
                title: '预警规则编号',
                minWidth: 180,
                },
                {
                field: 'stationId',
                title: '充电站编号',
                minWidth: 180,
                },
                {
                field: 'stationName',
                title: '充电站名称',
                minWidth: 180,
                },
                {
                field: 'city',
                title: '地市',
                minWidth: 180,
                },
                {
                field: 'warningMethod',
                title: '预警处理措施',
                minWidth: 180,
                },
                {
                field: 'warningPeople',
                title: '预警通知人',
                minWidth: 180,
                },

                {
                title: '操作',
                slots: { default: 'operate' },
                width: 300,
                align: 'center',
                fixed: 'right',
                },
            ],
            tableData: [
            {
                warningId: "W20231001",
                warningCycle: "每日",
                warningTime: "2023-10-01 08:00:00",
                statisticCycle: "月度统计",
                warningContent: "充电桩利用率低于阈值",
                ruleName: "利用率预警规则",
                rulerId: "R003",
                stationId: "STATION_1001",
                stationName: "北京朝阳充电站",
                city: "北京市",
                warningMethod: "自动发送邮件通知",
                warningPeople: "张三; 李四"
            },
            {
                warningId: "W20231002",
                warningCycle: "每周",
                warningTime: "2023-10-02 14:30:00",
                statisticCycle: "周度统计",
                warningContent: "设备温度异常",
                ruleName: "温度安全规则",
                rulerId: "R007",
                stationId: "STATION_1002",
                stationName: "上海浦东充电站",
                city: "上海市",
                warningMethod: "短信提醒运维人员",
                warningPeople: "王五"
            }

            ],
            params: {
                warningId: '',
                rulerId: '',
                ruleName: '',
                warningMethod: '',
                warningCycle: '',
                stationId: '',
                stationName: '',
                warningTime: '',
                statisticCycle: '',
            },
        };
    },

    computed: {
        filterOptions() {
        return {
            config: [
                {
                    field: 'warningId',
                    title: '预警编号',
                    element: 'el-input',
                },
                {
                    field: 'ruleName',
                    title: '预警规则名称',
                    element: 'el-input',
                },
                {
                    field: 'rulerId',
                    title: '预警规则编号',
                    element: 'el-input',
                },
                {
                    field: 'warningMethod',
                    title: '预警处理措施',
                    element: 'el-select',
                    props: {
                        placeholder: '请选择',
                        options: []
                    }
                },
                {
                    field: 'warningCycle',
                    title: '预警周期',
                    element: 'el-select',
                    props: {
                        placeholder: '请选择',
                        options: []
                    }
                },
            
            {
                field: 'stationId',
                title: '充电站',
                element: 'el-select',
                props: {
                    placeholder: '请选择',
                    options: []
                }
            },
            {
                field: 'stationName',
                title: '充电站编号',
                element: 'el-input',
            },

            {
                field: 'warningTime',
                title: '预警时间',
                element: 'el-date-picker',
                props: {
                    type: 'daterange',
                    valueFormat: 'yyyy-MM-dd',
                    options: [],
                }
            },
            {
                field: 'statisticCycle',
                title: '统计周期',
                element: 'el-date-picker',
                props: {
                    type: 'daterange',
                    valueFormat: 'yyyy-MM-dd',
                    options: [],
                }
            },
            ],
            params: this.params,
        };
        },
        modalConfig() {
        return {
            addBtn: false,
            viewBtn: false,
            menu: false,
            editBtn: false,
            delBtn: false,
        }
        },
    },
    mounted() {

    },
    methods: {
        handleDispatch() {
            this.$refs.dispatchModal.dialogVisible = true;
        },
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .container-full {
    background-color: rgba(244, 246, 249, 1);
    padding-bottom: 16px;
    min-height: calc(100% - 80px);
    overflow-y: auto;
  }
   
  

.table-wrap {
  ::v-deep .bd3001-table-select-box {
      display: none;
  }
  ::v-deep .bd3001-header  {
      display: block;
  }
  ::v-deep .bd3001-button {
      display: block !important;
  }
  
  .card-head {
      // position: relative; 
      height: 56px;
      padding: 0 16px;
      display: flex;
      align-items: center;
      margin-top: -20px;
      .card-head-text {
      flex:1;
      width: 520px;
          height: 26px;
          background-image: url('~@/assets/images/bg-title.png');
          background-size: 520px 26px;
          background-repeat: no-repeat;
          font-size: 20px;
          font-style: normal;
          font-weight: 500;
          line-height: 16px;
          padding-left: 36px;
          color: #21252e;
      &::before {
          content: '';
          position: absolute;
          top: 0;
          bottom: 0;
          left: -3px; /* 调整这个值来改变边框的宽度 */
          width: 0;
          border-top: 3px solid transparent;
          border-bottom: 3px solid transparent;
          border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
      }   

  }

  .card-head-after {
      width: 100%;
      height: 1px;
      background-color: #DCDEE2;
      margin-bottom: 16px;
  }
  .info-wrap {
      margin-top: 16px;
      display: flex;
      flex-wrap: wrap;
      gap: 16px;

      .info-item {
          background-color: #FAFBFC;
          flex: 1 1 0;
          // min-width: 180px;
          
          border-radius: 5px;
          padding: 8px 24px;
          box-sizing: border-box;
          // margin-right: 16px;
          display: flex;
          .info-icon {
              width: 42px;
              height: 42px;
          }
          .info-right-wrap {
              flex:1;
              margin-left: 8px;
              .info-title {
                  font-weight: 400;
                  font-size: 14px;
                  line-height: 14px;
                  margin-bottom: 8px;
              }
              .info-number {
                  font-size: 20px;
                  font-weight: 500;
                  .info-unit {
                      font-size: 14px;
                      font-weight: 400;
                  }
              }
          }
      }
      .info-item:last-child {
          margin-right: 0;
      }
  }

  .top-button-wrap {
      display:flex;
      margin: 16px 0;
      .set-btn {
        background-color: #FFFFFF;
        color: #292B33;
        border-color: #DFE1E5;
    }
  }
}

.button-border {
  border: 0.01rem solid #217AFF;
  color: #217AFF;
  background-color: #fff;
}

 
  </style>
  