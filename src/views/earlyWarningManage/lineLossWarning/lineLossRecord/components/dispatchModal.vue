<template>
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="630px"
      @close="handleCancel"
    >


        <el-form :model="form" :rules="rules" ref="ruleForm"  label-position="top">
            <el-row :gutter="20">
                <el-col :span="24">
                    <el-form-item
                        label="派单目标"
                        prop="dispatchTarget"
                    >
                       <div style="width: 100%;display: flex; align-items: center;">
                            <el-select 
                                v-model="form.operatorRole"
                                placeholder="请选择运营角色"
                                style="width: 50%; margin-right: 8px;"
                            >
                                <el-option
                                    v-for="item in operatorRoleList"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                            </el-select>
                            <el-select 
                                v-model="form.operator"
                                placeholder="请选择运营人员"
                                style="width: 50%"
                            >
                                <el-option
                                    v-for="item in operatorList"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                            </el-select>
                       </div>
                    </el-form-item>
                </el-col>

                <el-col :span="24">
                    <el-form-item
                        label="计划完成时间"
                        prop="planTime"
                    >
                        <div style="width: 100%;display: flex; align-items: center;">
                            <el-select 
                                style="width: 33%; margin-right: 8px"
                                disabled
                                v-model="rate"
                            >
                                <el-option key="1" label="预警后" value="1" />
                            </el-select>

                            <el-input 
                                v-model="form.planTime"
                                placeholder="请输入"
                                style="width: 33%; margin-right: 8px"
                            ></el-input>

                            <el-select 
                                v-model="form.planTimeUnit"
                                placeholder="请选择"
                                style="width: 33%;"
                            >
                                <el-option
                                    v-for="item in planTimeUnitList"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                />
                            </el-select>
                        </div>
                    </el-form-item>
                </el-col>

                <el-col :span="24">
                    <el-form-item
                        label="紧急程度"
                        prop="planTime"
                    >
                        <el-select 
                            v-model="form.level"
                            placeholder="请选择"
                            style="width:100%"
                        >
                            <el-option
                                v-for="item in levelList"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>
           

        </el-form>

        <div slot="footer" class="dialog-footer">
            <el-button @click="handleCancel">取 消</el-button>
            <el-button type="primary" @click="handleConfirm">确 定</el-button>
        </div>
    </el-dialog>
  </template>
  <script>  

import {
    editPileControl
} from '@/api/pile/index';


import moment from 'moment';

import _ from 'lodash';

  export default {
    props: {
        dialogTitle:{
            type: String,
            default: '调控配置'
        }
    },
    components: {
  
    },
    dicts: [
        'ls_charging_status', // 是否状态
        'ls_charging_adjustable_type', // 可调控类型
        'ls_charging_station_control', // 站点调节方式
    ],
    data() {
        return {
            dialogVisible: false,
            pileId: '',
            rate: '1',
            form: {
                operatorRole: '',
                operator: '',
                planTime: '',
                planTimeUnit:  '',
                level: '',
            },
            rules: {
                dispatchTarget: [
                    {
                        required: true, 
                        validator: (rule, value, callback) => {
                            if(!this.form.operatorRole || !this.form.operator) {
                                callback(new Error('请选择派单目标')); 
                            } else {
                                callback();
                            }
                        },
                        trigger: 'change',
                    }
                    
                ],
                planTime: [
                    {
                        required: true, 
                        validator: (rule, value, callback) => {
                            if(!this.form.planTime || !this.form.planTimeUnit) {
                                callback(new Error('请输入计划完成时间')); 
                            } else {
                                callback();
                            }
                        },
                        trigger: 'change',
                    }
                ],
                level: [
                    { required: true, message: '请选择紧急程度', trigger: 'change' }    
                ]
            },
            formLabelWidth: '120px',

            operatorRoleList: [
                { label: '运维人员', value: '1' },
                { label: '运维主管', value: '2' },
                { label: '运维经理', value: '3' },
            ],

            operatorList: [
                { label: '张三', value: '1' },
                { label: '李四', value: '2' },
            ],

            planTimeUnitList: [
                { label: '天', value: '1' },
                { label: '小时', value: '2' },
            ],

            levelList: [
                { label: '非常紧急', value: '1' },
                { label: '紧急', value: '2' },
                { label: '一般', value: '3' },
            ]


        };
    },
    computed: {},
    mounted() {},
    methods: {

        resetForm() {
            this.pileId = '';

            Object.keys(this.form).forEach((key) => {
                this.form[key] = '';
                
            });
        },

        handleCancel() {
            this.$refs.ruleForm.resetFields();
            this.resetForm();
            console.log(this.form, 'this.form')
            this.dialogVisible = false;
        },



        // 新增按钮防抖        
        handleConfirm: _.debounce(function() {
            this.$refs.ruleForm.validate(async (valid) => {
                if (valid) {
                    console.log(this.form, 'this.form')
                    const {
                        controlType,
                        maxDownCapability,
                        maxUpwardCapability
                    } = this.form;
             
                         const params = {
                            pileId: this.pileId,
                            controlType,
                            maxDownCapability,
                            maxUpwardCapability
                        }

                        console.log('params', params)
                        const [err, res] = await editPileControl(params);
                        if (err) return;
                    
                        this.$message.success('编辑成功');
                        this.dialogVisible = false;
                        this.pileId  = '';
                        this.$emit('loadData');
                    
                }
            });
        }, 300) ,


    },
  };
  </script>
  <style lang="scss" scoped>
::v-deep .el-form-item__content{
    display: flex !important;
}

::v-deep .el-input-number {
    width: 90% !important;
}



.info-wrap {
    display: flex;
    height: 20px;
    margin-bottom: 24px;
    align-items: center;
    .info-title {
        font-weight: 400;
        font-size: 16px;
        line-height: 16px;
        color: #505363;
      }
      .info-detail {
        font-weight: 400;
        font-size: 16px;
        line-height: 16px;
        color: #292B33;
      }
}
  </style>
  