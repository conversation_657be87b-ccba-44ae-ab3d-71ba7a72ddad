<template>
    <div class="container container-float " >
        <div class="table-wrap">
            <BuseCrud
                ref="crud"
                :loading="loading"
                :filterOptions="filterOptions"
                :tablePage="tablePage"
                :tableColumn="tableColumn"
                :tableData="tableData"
                :pagerProps="pagerProps"
                :modalConfig="modalConfig"
                class="buse-wrap-station"
                @loadData="loadData"
            >
            <template slot="defaultHeader">
                <div>
                    <div class="card-head">
                        <div class="card-head-text">线损预警记录列表</div>
                    </div>
                    

                    
                </div>
                
            </template>

                    <template slot="operate" slot-scope="{ row }">
                        <div class="menu-box">
                            <el-button
                            class="button-border"
                            @click="handleOrder(row)"
                            >
                                处理
                            </el-button>
                            <el-button
                                class="button-border"
                                @click="hanleDetail(row)"
                            >
                                详情
                            </el-button>

                        

                        



                    
                        </div>
                    
                    </template>

            </BuseCrud>
        </div>

        <HandleOrderModal ref="handleOrderModal" />
    </div>
    
  </template>
  
  <script>
import { Statistic } from 'element-ui';
import HandleOrderModal from './components/handleOrderModal.vue'

  
    export default {
    components: {
        HandleOrderModal
    },
    dicts: [],
    data() {
        return {
            loading: false,
            tablePage: { total: 0, currentPage: 1, pageSize: 10 },
            tableColumn: [
                {
                type: 'seq',
                title: '序号',
                width: 60,
                minWidth: 60,
                },
                {
                field: 'orderId',
                title: '工单编号',
                minWidth: 180,
                },
                {
                field: 'warningId',
                title: '预警编号',
                minWidth: 180,
                },
                {
                field: 'warningContent',
                title: '预警内容',
                minWidth: 180,
                },
                {
                field: 'stationId',
                title: '充电站编号',
                minWidth: 180,
                },
                {
                field: 'stationName',
                title: '充电站名称',
                minWidth: 180,
                },
                {
                field: 'city',
                title: '地市',
                minWidth: 180,
                },
                {
                field: 'level',
                title: '紧急程度',
                minWidth: 180,
                },
                {
                field: 'dispatchType',
                title: '派单类型',
                minWidth: 180,
                },
                {
                field: 'dispatcher',
                title: '派单人',
                minWidth: 180,
                },
                {
                field: 'dispatchPeople',
                title: '派单目标',
                minWidth: 180,
                },
                {
                field: 'handleStatus',
                title: '处理状态',
                minWidth: 180,
                },
                {
                field: 'handleTime',
                title: '处理时间',
                minWidth: 180,
                },

                {
                title: '操作',
                slots: { default: 'operate' },
                width: 300,
                align: 'center',
                fixed: 'right',
                },
            ],
            tableData: [
            {
                orderId: '***********-001',
                warningId: 'ALERT-20240521-001',
                warningContent: '充电桩温度过高报警',
                stationId: 'S10001',
                stationName: '南山科技园充电站',
                city: '深圳市',
                level: '紧急',
                dispatchType: '自动派单',
                dispatcher: '系统自动派单',
                dispatchPeople: '李四维修组',
                handleStatus: '已处理',
                handleTime: '2024-05-21 10:30:00'
            },
            {
                orderId: '***********-002',
                warningId: 'ALERT-20240521-002',
                warningContent: '直流充电电压异常',
                stationId: 'S10002',
                stationName: '福田市民中心站',
                city: '深圳市',
                level: '高',
                dispatchType: '手动派单',
                dispatcher: '王五',
                dispatchPeople: '张三技术部',
                handleStatus: '处理中',
                handleTime: '2024-05-21 11:15:00'
            },
            ],
            params: {
                orderId: '',
                warningId: '',
                stationId: '',
                stationName: '',
                city: '',
                dispatchType: '',
                level: '',
                dispatchTime: [],
                handleTime: [],
                handleStatus: '',
            },
        };
    },

    computed: {
        filterOptions() {
        return {
            config: [
                {
                    field: 'orderId',
                    title: '工单编号',
                    element: 'el-input',
                },
                {
                    field: 'warningId',
                    title: '预警编号',
                    element: 'el-input',
                },
                {
                    field: 'stationId',
                    title: '充电站',
                    element: 'el-select',
                    props: {
                        placeholder: '请选择',
                        options: []
                    }
                },
                {
                    field: 'stationName',
                    title: '充电站编号',
                    element: 'el-input',
                },
                {
                    field: 'city',
                    title: '地市',
                    element: 'el-select',
                    props: {
                        placeholder: '请选择',
                        options: []
                    }
                },
                {
                    field: 'dispatchType',
                    title: '派单类型',
                    element: 'el-select',
                    props: {
                        placeholder: '请选择',
                        options: []
                    }
                },
                {
                    field: 'level',
                    title: '紧急程度',
                    element: 'el-select',
                    props: {
                        placeholder: '请选择',
                        options: []
                    }
                },
                {
                    field: 'dispatchTime',
                    title: '派单时间',
                    element: 'el-select',
                    props: {
                        placeholder: '请选择',
                        options: []
                    }
                },
                {
                    field: 'handleTime',
                    title: '处理时间',
                    element: 'el-select',
                    props: {
                        placeholder: '请选择',
                        options: []
                    }
                },

                {
                    field: 'handleStatus',
                    title: '处理状态',
                    element: 'el-select',
                    props: {
                        placeholder: '请选择',
                        options: []
                    }
                },
            ],
            params: this.params,
        };
        },
        modalConfig() {
        return {
            addBtn: false,
            viewBtn: false,
            menu: false,
            editBtn: false,
            delBtn: false,
        }
        },
    },
    mounted() {

    },
    methods: {
        handleOrder() {
            this.$refs.handleOrderModal.dialogVisible = true;
        },
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .container-full {
    background-color: rgba(244, 246, 249, 1);
    padding-bottom: 16px;
    min-height: calc(100% - 80px);
    overflow-y: auto;
  }
   
  

.table-wrap {
  ::v-deep .bd3001-table-select-box {
      display: none;
  }
  ::v-deep .bd3001-header  {
      display: block;
  }
  ::v-deep .bd3001-button {
      display: block !important;
  }
  
  .card-head {
      // position: relative; 
      height: 56px;
      padding: 0 16px;
      display: flex;
      align-items: center;
      margin-top: -20px;
      .card-head-text {
      flex:1;
      width: 520px;
          height: 26px;
          background-image: url('~@/assets/images/bg-title.png');
          background-size: 520px 26px;
          background-repeat: no-repeat;
          font-size: 20px;
          font-style: normal;
          font-weight: 500;
          line-height: 16px;
          padding-left: 36px;
          color: #21252e;
      &::before {
          content: '';
          position: absolute;
          top: 0;
          bottom: 0;
          left: -3px; /* 调整这个值来改变边框的宽度 */
          width: 0;
          border-top: 3px solid transparent;
          border-bottom: 3px solid transparent;
          border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
      }   

  }

  .card-head-after {
      width: 100%;
      height: 1px;
      background-color: #DCDEE2;
      margin-bottom: 16px;
  }
  .info-wrap {
      margin-top: 16px;
      display: flex;
      flex-wrap: wrap;
      gap: 16px;

      .info-item {
          background-color: #FAFBFC;
          flex: 1 1 0;
          // min-width: 180px;
          
          border-radius: 5px;
          padding: 8px 24px;
          box-sizing: border-box;
          // margin-right: 16px;
          display: flex;
          .info-icon {
              width: 42px;
              height: 42px;
          }
          .info-right-wrap {
              flex:1;
              margin-left: 8px;
              .info-title {
                  font-weight: 400;
                  font-size: 14px;
                  line-height: 14px;
                  margin-bottom: 8px;
              }
              .info-number {
                  font-size: 20px;
                  font-weight: 500;
                  .info-unit {
                      font-size: 14px;
                      font-weight: 400;
                  }
              }
          }
      }
      .info-item:last-child {
          margin-right: 0;
      }
  }

  .top-button-wrap {
      display:flex;
      margin: 16px 0;
      .set-btn {
        background-color: #FFFFFF;
        color: #292B33;
        border-color: #DFE1E5;
    }
  }
}

.button-border {
  border: 0.01rem solid #217AFF;
  color: #217AFF;
  background-color: #fff;
}

 
  </style>
  