<template>
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="630px"
      @close="handleCancel"
    >


        <el-form :model="form" :rules="rules" ref="ruleForm"  label-position="top">
            <el-row :gutter="20">

                <el-col :span="24">
                    <el-form-item
                        label="线损原因"
                        prop="reason"
                    >
                        <el-select 
                            v-model="form.reason"
                            placeholder="请选择"
                            style="width:100%"
                        >
                            <el-option
                                v-for="item in reasonList"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </el-select>
                    </el-form-item>
                </el-col>

                <el-col :span="24">
                    <el-form-item
                        label="反馈情况"
                        prop="feedback"
                    >
                        <el-input v-model="form.feedback" type="textarea" :rows="3" placeholder="请输入反馈情况">

                        </el-input>
                       
                    </el-form-item>
                </el-col>

                <el-col :span="24">
                    <el-form-item
                        label="处理结果"
                        prop="result"
                    >
                        <el-input v-model="form.result" type="textarea" :rows="3" placeholder="请输入处理结果">

                        </el-input>
                       
                    </el-form-item>
                </el-col>
            </el-row>
           

        </el-form>

        <div slot="footer" class="dialog-footer">
            <el-button @click="handleCancel">取 消</el-button>
            <el-button type="primary" @click="handleConfirm">确 定</el-button>
        </div>
    </el-dialog>
  </template>
  <script>  

import {
    editPileControl
} from '@/api/pile/index';


import moment from 'moment';

import _ from 'lodash';

  export default {
    props: {
        dialogTitle:{
            type: String,
            default: '调控配置'
        }
    },
    components: {
  
    },
    dicts: [
        'ls_charging_status', // 是否状态
        'ls_charging_adjustable_type', // 可调控类型
        'ls_charging_station_control', // 站点调节方式
    ],
    data() {
        return {
            dialogVisible: false,
   
            form: {
                reason: '',
                feedback: '',
                result: '',
            },
            rules: {
                reason: [
                    { required: true, message: '请选择线损原因', trigger: 'change' }    
                ],
                feedback:[
                    { required: true, message: '请输入反馈情况', trigger: 'blur' }
                ],
                result: [
                    { required: true, message: '请输入处理结果', trigger: 'blur' }
                ]
            },
            formLabelWidth: '120px',

            reasonList: [
                { label: '设备故障', value: '1' },  
                { label: '线路故障', value: '2' },
                { label: '其他', value: '3' },
            ],




        };
    },
    computed: {},
    mounted() {},
    methods: {

        resetForm() {
            this.pileId = '';

            Object.keys(this.form).forEach((key) => {
                this.form[key] = '';
                
            });
        },

        handleCancel() {
            this.$refs.ruleForm.resetFields();
            this.resetForm();
            console.log(this.form, 'this.form')
            this.dialogVisible = false;
        },



        // 新增按钮防抖        
        handleConfirm: _.debounce(function() {
            this.$refs.ruleForm.validate(async (valid) => {
                if (valid) {
                    console.log(this.form, 'this.form')
                    const {
                        controlType,
                        maxDownCapability,
                        maxUpwardCapability
                    } = this.form;
             
                         const params = {
                            pileId: this.pileId,
                            controlType,
                            maxDownCapability,
                            maxUpwardCapability
                        }

                        console.log('params', params)
                        const [err, res] = await editPileControl(params);
                        if (err) return;
                    
                        this.$message.success('编辑成功');
                        this.dialogVisible = false;
                        this.pileId  = '';
                        this.$emit('loadData');
                    
                }
            });
        }, 300) ,


    },
  };
  </script>
  <style lang="scss" scoped>
::v-deep .el-form-item__content{
    display: flex !important;
}

::v-deep .el-input-number {
    width: 90% !important;
}



.info-wrap {
    display: flex;
    height: 20px;
    margin-bottom: 24px;
    align-items: center;
    .info-title {
        font-weight: 400;
        font-size: 16px;
        line-height: 16px;
        color: #505363;
      }
      .info-detail {
        font-weight: 400;
        font-size: 16px;
        line-height: 16px;
        color: #292B33;
      }
}
  </style>
  