<template>
    <div class="container container-float " style="padding: 0 0 100px 0;">
        <div class="info-card" >
            <div class="card-head" >
                <div class="before-icon"></div>
                <div class="card-head-text">批量导入</div>
            </div>
            <div class="card-head-after "></div>

            <div class="upload-wrap">
                <el-upload
                    class="upload-demo"
                    drag
                    action="https://jsonplaceholder.typicode.com/posts/"
                    multiple>
                    <div class="upload-icon"></div>
                    <div class="upload-text">点击或将文件拖拽到这里上传</div>
                    <div class="upload-tip">支持扩展名：.xlsx .xls</div>
                </el-upload>
                <div class="upload-info">
                    请 <block style="color: #217AFF;" @click="onClickTemplate">点此下载Excel模板</block>， 并按照模板进行填写后上传，模板数据不超过500条，导入过程请耐心等待
                </div>
            </div>

            <div class="card-head" >
                <div class="before-icon"></div>
                <div class="card-head-text">批量导入结结果</div>
            </div>

            <BuseCrud
                ref="importInfo"
                :tableColumn="importInfoTableColumn"
                :tableData="importInfoData"
                :modalConfig="{ addBtn: false, menu: false }"
            >
                <template slot="operate" slot-scope="{ row }">
                    <div class="operate-btn" @click="onDownloadFile(row)">
                        {{ row.fileName }}                       
                    </div>
                </template>
            </BuseCrud>

            
        </div>

    </div>
    
  </template>
  
  <script>
  
    export default {
    components: {
        
    },
    dicts: [],
    data() {
      return {
            importInfoTableColumn: [
                {
                    field: 'fileName',
                    title: '文件名称',
                    minWidth: 180,
                },
                {
                    field: 'importTime',
                    title: '提交时间',
                    minWidth: 150,
                },
                {
                    title: '操作人',
                    field: 'operator',
                    minWidth: 150,
                },
                {
                    title: '备注',
                    field: 'remark',
                    minWidth: 150,
                },
                {
                    title: '结果文件',
                    slots: { default: 'operate' },
                    width: 220,
                    fixed: 'right',
                },
            ],
            
            importInfoData: [
                {
                    fileName: '导入文件1.xlsx',
                    importTime: '2021-10-01 10:00:00',
                    operator: '张三',
                    remark: '成功数量：4 ，失败数量：0',
                },
                {
                    fileName: '导入文件2.xls',
                    importTime: '2022-10-01 10:00:00',
                    operator: '张三',
                    remark: '成功数量：3 ，失败数量：1',
                }
            ],
      };
    },

    computed: {
    },
    mounted() {

    },
    methods: {
        onClickTemplate() {
            console.log('点击模板');
        },

        onDownloadFile(row) {
            console.log('下载文件', row);
        }
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .container-full {
    background-color: rgba(244, 246, 249, 1);
    padding-bottom: 16px;
    min-height: calc(100% - 80px);
    overflow-y: auto;
  }
   
  .info-card {
    margin: 16px;
    border-radius: 5px;
    border: 1px solid #fff;
    overflow: hidden;
    background-color: #fff;
    // min-height: 300px;
    .card-head {
        height: 56px;
        padding: 0 16px;
        display: flex;
        align-items: center;
        // background: linear-gradient(180deg, #E9F2FF 0%, #FFFFFF 100%);
        .before-icon {
            width: 3px;
            height: 16px;
            background-image: url('~@/assets/station/consno-before.png');
            background-size: 100% 100%;
            background-repeat: no-repeat;
            margin-right: 8px;
        }
        .card-head-text {
            flex:1;
            font-weight: 500;
            font-size: 16px;
            color: #12151A;

        &::before {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            left: -3px; /* 调整这个值来改变边框的宽度 */
            width: 0;
            border-top: 3px solid transparent;
            border-bottom: 3px solid transparent;
            border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
        }

    
        }

        .button-wrap {
        display: flex;
        .invite-btn {
            background-color: #1ab2ff;
            border-color: #1ab2ff;
            }
            ::v-deep .el-button--small {
            font-size: 14px;
            }
        .distribution {
            margin-left: 24px;
            margin-right: 24px;
            display: flex;
            align-items: center;
            }
        }
    }

    .card-head-after {
        width: 100%;
        height: 1px;
        background-color: #E9EBF0;
    }

    .upload-wrap {
        padding: 24px 24px 0 24px;
        ::v-deep .el-upload-dragger {
            width: 732px;
            height: 180px;
            border: 1px dashed #5798FF;
            border-radius: 2px;
            .upload-icon {
                width: 24px;
                height: 24px;
                background-image: url('~@/assets/station/upload-icon.png');
                background-size: 100% 100%;
                background-repeat: no-repeat;
                margin: 46px auto 0 auto;
            }
            .upload-text {
                font-weight: 400;
                font-size: 16px;
                line-height: 16px;
                margin: 16px auto;
                color: #292B33;
            }
            .upload-tip {
                font-weight: 400;
                font-size: 16px;
                line-height: 16px;
                margin: 16px auto;
                color: #818496;
            }
        }
        .upload-info {
            font-weight: 400;
            font-size: 16px;
            line-height: 16px;
            margin: 16px 0 24px 0 ;
            color: #292B33;

        }
    }
    .operate-btn {
        font-weight: 400;
        font-size: 16px;
        line-height: 28px;
        color: #217AFF;
    }

  }

 
  </style>
  