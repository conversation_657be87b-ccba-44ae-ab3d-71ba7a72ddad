<template>
  <div class="container container-region">
    <RegionSelect @nodeClick="nodeClick"></RegionSelect>
    <div class="container-info">
      <div class="top-info-wrap">
        <div class="top-info-icon"></div>
        <div class="top-info-first" v-if="topInfoFirst">{{ topInfoFirst }}</div>
        <div class="top-info-bold">{{ topInfoBold }}</div>
      </div>
      <div class="table-wrap">
        <BuseCrud
          ref="crud"
          :loading="loading"
          :filterOptions="filterOptions"
          :tablePage="tablePage"
          :tableColumn="tableColumn"
          :tableData="tableData"
          :pagerProps="pagerProps"
          :modalConfig="modalConfig"
           class="buse-wrap-station"
          @loadData="loadData"

          :tableOn="{
            'checkbox-change': handleCheckboxChange,
            'checkbox-all': handleCheckboxChange,
          }"
        >
          <template slot="defaultHeader">
            <div>
              <div class="card-head">
                <div class="card-head-text">充电桩列表</div>
              </div>
              <div class="card-head-after"></div>

              <div class="info-wrap">
                <div
                  class="info-item"
                  v-for="item in infoList"
                  :key="item.name"
                >
                  <img :src="item.icon" class="info-icon" />
                  <div class="info-right-wrap">
                    <div class="info-title">{{ item.name }}</div>
                    <div class="info-number">
                      {{ item.value }}
                      <span class="info-unit">{{ item.unit }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div class="top-button-wrap">
                <el-button
                  type="primary"
                  icon="el-icon-plus"
                  @click="() => handleCreatePile()"
                >
                  新增充电桩
                </el-button>

                <el-button
                  type="primary"
                  plain
                  @click="() => handleDeletePile()"
                >
                  已删除充电桩
                </el-button>

                <el-button plain @click="() => handleBatchImport()">
                  批量导入
                </el-button>

                <el-button plain @click="() => handleControl()">
                  线下桩导入
                </el-button>

                <el-button plain @click="() => handleIssue(null)">
                  批量下发
                </el-button>

                <el-button plain @click="() => handleDownloadQR(null)">
                  批量下载二维码
                </el-button>

                <el-button plain @click="() => handleDelete(null)">
                  批量删除
                </el-button>

                <el-button plain @click="() => handleExport()">导出</el-button>
              </div>
            </div>
          </template>

          <template slot="operate" slot-scope="{ row }">
            <!-- <div class="menu-box">
              <el-button 
                  class="button-border"
                  type="primary"
                  plain 
                    @click="hanleDetail(row)">
                详情
              </el-button>

              <el-button
                  class="button-border"
                  type="primary"
                  plain 
                @click="() => handleEdit(row)"
              >
                编辑
              </el-button>

              <el-button 
                  class="button-border"
                  type="primary"
                  plain 
                  @click="hanleRecord(row)">
                枪状态记录
              </el-button>

              <el-dropdown trigger="click">
                <img
                  src="@/assets/images/pile/icon/icon-more.svg"
                  class="icon-more"
                />
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item @click="handleIssue(row)">
                    <div  @click="handleIssue(row)">
                      下发
                     </div>
                  </el-dropdown-item>
                  <el-dropdown-item>
                    <div class="add-btn" @click="handleDownloadQR(row)">
                      下载二维码
                     </div>
                  </el-dropdown-item>
                  
                  <el-dropdown-item>
                      <div class="add-btn" @click="handleControl(row)">
                      调控配置
                     </div>
                   
                  </el-dropdown-item>

                  <el-dropdown-item>
                      <div  @click="handleDelete(row)">
                      删除
                     </div>
                   
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div> -->

            <el-dropdown trigger="click">
                      <el-button
                        class="button-border"
                        type="primary"
                        plain
                      >
                        操作
                      </el-button>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item >
                          <div   @click="hanleDetail(row)">
                            详情
                          </div>
                        </el-dropdown-item>

                        <el-dropdown-item >
                          <div  v-if="row.stationSource === '01'"  @click="() => handleEdit(row)">
                            编辑
                          </div>
                        </el-dropdown-item>

                        <el-dropdown-item  >
                          <div    @click="hanleRecord(row)">
                            枪状态记录
                          </div>
                        </el-dropdown-item>
                        <el-dropdown-item @click="handleIssue(row)">
                          <div  @click="handleIssue(row)">
                            下发
                          </div>
                        </el-dropdown-item>
                        <el-dropdown-item>
                          <div class="add-btn" @click="handleDownloadQR(row)">
                            下载二维码
                          </div>
                        </el-dropdown-item>
                        
                        <el-dropdown-item>
                            <div class="add-btn" @click="handleControl(row)">
                            调控配置
                          </div>
                        
                        </el-dropdown-item>

                        <el-dropdown-item>
                            <div  @click="handleDelete(row)">
                            删除
                          </div>
                        
                        </el-dropdown-item>
                     
                        

                      </el-dropdown-menu>
                    </el-dropdown>
          </template>
        </BuseCrud>
      </div>
    </div>

    <controlModal ref="controlModal"   @loadData="loadData"/>
    <gunStatusModal ref="gunStatusModal" />
    <issueModal ref="issueModal" @loadData="loadData"/>
    <qrcodeModal ref="qrcodeModal" />
  </div>
</template>



<script>
import RegionSelect from '@/components/Business/RegionSelect';
import icon1 from '@/assets/station/total-icon.png';
import icon2 from '@/assets/station/direct-connection-icon.png';
import icon3 from '@/assets/station/aggregation-icon.png';
import icon4 from '@/assets/station/state-grid-icon.png';

import controlModal from './components/controlModal.vue';
import gunStatusModal from './components/gunStatusModal.vue'

import issueModal from './components/issueModal.vue'

import qrcodeModal from './components/qrcodeModal.vue'
import StatusDot from '@/components/Business/StatusDot';


import {
  getStationList,
  getBrandList,
  getModelList,
  getAssetUnit,
  getPileList,
  getPileSummary,
  getPileControlDetail,
  getPileLog,
  batchDeletePile,
} from '@/api/pile/index';

export default {
  components: {
    RegionSelect,
    controlModal,
    gunStatusModal,
    issueModal,
    qrcodeModal,
    StatusDot,
  },
  dicts: [
    'ls_charging_operation_mode', // 运营模式
    'ls_charging_subType', // 设备类型  
    'ls_charging_adjustable_type', // 可控类型
    'ls_charging_status',// 是否
    'ls_charging_station_source', // 桩来源
    'ls_charging_pile_operation_status', // 运营状态
    'ls_charging_station_access_type', // 充电桩接入方式
    'ls_charging_structural_type',
  ],
  data() {
    return {
      loading: false,
      stationLoading: false,
      topInfo: {},
      topInfoFirst: '',
      topInfoBold: '',

      stationList: [], // 所属充电站
      brandList: [], //设备品牌
      modelList: [], //设备型号
      assetUnitList: [], //产权单位

      pileList: [], // 选中的桩列表

      infoList: [],
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },

      tableColumn: [
        {
            type: 'checkbox',
            width: 50,
            fixed: 'left',
        },
        {
          type: 'seq',
          title: '序号',
          width: 60,
        },
        {
          field: 'pileName',
          title: '充电桩名称',
          minWidth: 200, // 最小宽度
        },
        {
          field: 'pileNo',
          title: '充电桩编号',
          minWidth: 150, // 最小宽度
        },
        {
          field: 'stationName',
          title: '所属充电站',
          minWidth: 160, // 最小宽度
          slots: {
                  default: ({ row }) => [
                      <el-tooltip 
                          content={row.stationName} 
                          placement="top" 
                          disabled={!row.stationName || row.stationName.length < 10}
                      >
                          <span class="ellipsis-text">{row.stationName}</span>
                      </el-tooltip>
                  ]
              }
        },
        {
          field: 'operationMode',
          title: '运营模式',
          minWidth: 160, // 最小宽度
          formatter: ({ cellValue }) => {
                  return this.selectDictLabel(
                    this.dict.type.ls_charging_operation_mode,
                    cellValue
                  );
          },
        },
        {
          field: 'stationAccessType',
          title: '接入方式',
          minWidth: 100, // 最小宽度
          formatter: ({ cellValue }) => {
                  return this.selectDictLabel(
                    this.dict.type.ls_charging_station_access_type,
                    cellValue
                  );
                },
        },
        {
          field: 'stationSource',
          title: '桩来源',
          minWidth: 120, // 最小宽度
          formatter: ({ cellValue }) => {
                  return this.selectDictLabel(
                    this.dict.type.ls_charging_station_source,
                    cellValue
                  );
                },
        },
        {
          field: 'brandName',
          title: '设备品牌',
          minWidth: 120, // 最小宽度
        },
        {
          field: 'modelName',
          title: '设备型号',
          minWidth: 200, // 最小宽度
        },
        {
          field: 'subType',
          title: '设备类型',
          minWidth: 120, // 最小宽度
          formatter: ({ cellValue }) => {
                  return this.selectDictLabel(
                    this.dict.type.ls_charging_subType,
                    cellValue
                  );
                },
        },
        {
          field: 'structuralType',
          title: '结构形式',
          minWidth: 120, // 最小宽度

          formatter: ({ cellValue }) => {
                  return this.selectDictLabel(
                    this.dict.type.ls_charging_structural_type,
                    cellValue
                  );
                },
          
        },
        {
          field: 'stackNo',
          title: '充电堆编号',
          minWidth: 120, // 最小宽度
        },
        {
          field: 'controlType',
          title: '可控类型',
          minWidth: 120, // 最小宽度
          formatter: ({ cellValue }) => {
                  return this.selectDictLabel(
                    this.dict.type.ls_charging_adjustable_type,
                    cellValue
                  );
                },
        },
        {
          field: 'ratePower',
          title: '额定功率(kW)',
          minWidth: 130, // 最小宽度
        },
        {
          field: 'gunSum',
          title: '枪数量',
          minWidth: 100, // 最小宽度
        },
        {
          field: 'pileOwnershipUnitName',
          title: '产权单位',
          minWidth: 200, // 最小宽度
          slots: {
                  default: ({ row }) => [
                      <el-tooltip 
                          content={row.pileOwnershipUnitName} 
                          placement="top" 
                          disabled={!row.pileOwnershipUnitName || row.pileOwnershipUnitName.length < 10}
                      >
                          <span class="ellipsis-text">{row.pileOwnershipUnitName}</span>
                      </el-tooltip>
                  ]
              }
        },
        {
          field: 'isPrivatePile',
          title: '是否私桩',
          minWidth: 100, // 最小宽度
          formatter: ({ cellValue }) => {
                  return this.selectDictLabel(
                    this.dict.type.ls_charging_status,
                    cellValue
                  );
                },
        },
        {
          field: 'openFlag',
          title: '是否开放',
          minWidth: 120, // 最小宽度
          formatter: ({ cellValue }) => {
                  return this.selectDictLabel(
                    this.dict.type.ls_charging_status,
                    cellValue
                  );
                },
        },
        {
          field: 'operDate',
          title: '桩投运日期',
          minWidth: 120, // 最小宽度
        },
        {
          field: 'operStatus',
          title: '运营状态',
          minWidth: 100, // 最小宽度
          formatter: ({ cellValue }) => {
                  return this.selectDictLabel(
                    this.dict.type.ls_charging_pile_operation_status,
                    cellValue
                  );
                },
          fixed: 'right',
          slots: {
                  // 自定义render函数
                  default: ({ row }) => {
                    return (
                      <StatusDot
                        value={row.operStatus}
                        dictValue={this.dict.type.ls_charging_pile_operation_status}
                        colors={['build', 'ty', 'stop','tempstop','forverstop','danger','tempstop']}
                      ></StatusDot>
                    );
                  },
                },
        },
        {
          title: '操作',
          slots: { default: 'operate' },
          width: 100,
          align: 'center',
          fixed: 'right',
        },
      ],
      tableData: [],
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      params: {
        stationId: '', // 所属充电站
        pileName: '', // 充电桩名称
        pileId: '', // 充电桩编号
        operationMode: '', // 运营模式
        deviceBrand: '', // 设备品牌
        deviceModel: '', // 设备型号
        deviceType: '', // 设备类型
        propertyUnit: '', // 产权单位
        controlType: '', // 可控类型
        isPrivate: '', // 是否私桩
        pileSource: '02', // 桩来源
        isOpen: '', // 是否开放
        operationStatus: '', // 运营状态
        operationDate: [], // 投运日期
        stackNumber: '', // 充电堆编号
        accessMethod: '', // 接入方式
      },
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'stationId',
            title: '所属充电站',
            element: 'el-select',
            props: {
              options: this.stationList,
              filterable: true,
              remote: true,
              remoteMethod: this.debouncedStationSearch,
              loading: this.stationLoading,
            },
          },
          {
            field: 'pileName',
            title: '充电桩名称',
            element: 'el-input',
            placeholder: '请输入',
          },
          {
            field: 'pileId',
            title: '充电桩编号',
            element: 'el-input',
            placeholder: '请输入',
          },
          {
            field: 'operationMode',
            title: '运营模式',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_charging_operation_mode,
            },
          },
          {
            field: 'deviceBrand',
            title: '设备品牌',
            element: 'el-select',
            props: {
              options: this.brandList,
            },
          },
          {
            field: 'deviceModel',
            title: '设备型号',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.modelList,
            },
          },
          {
            field: 'deviceType',
            title: '设备类型',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_charging_subType,
            },
          },
          {
            field: 'propertyUnit',
            title: '产权单位',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.assetUnitList,
            },
          },
          {
            field: 'controlType',
            title: '可控类型',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_charging_adjustable_type,
            },
          },
          {
            field: 'isPrivate',
            title: '是否私桩',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_charging_status,
            },
          },
          {
            field: 'pileSource',
            title: '桩来源',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_charging_station_source,
            },
          },
          {
            field: 'isOpen',
            title: '是否开放',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_charging_status,
            },
          },
          {
            field: 'operationStatus',
            title: '运营状态',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_charging_pile_operation_status,
            },
            
          },
          {
            field: 'operationDate',
            title: '投运日期',
            element: 'el-date-picker',
            props: {
              type: 'daterange',
              placeholder: '开始日期 - 自定日期',
              startPlaceholder: '开始日期',
              endPlaceholder: '结束日期',
              valueFormat: 'yyyy-MM-dd',
            },
          },
          {
            field: 'stackNumber',
            title: '充电堆编号',
            element: 'el-input',
          },
          {
            field: 'accessMethod',
            title: '接入方式',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_charging_station_access_type,
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  mounted() {
    

    // this.getStationList();
    this.getBrandList();
    this.getModelList();
    this.getAssetUnit();
  },
  methods: {
    async debouncedStationSearch(query) {
      console.log(query, 'query');
      if (query !== '') {
          this.stationLoading = true;
          setTimeout(async() => {
            const [err, res] = await getStationList(
              {
                stationName: query,
              }
            );

            if (err) return;
            this.stationLoading = false;
            this.stationList = res.data.map((item) => ({
              label: item.stationName,
              value: item.stationId,
            }));
          }, 200);
          
        } else {
          this.stationList = []
      }
     
    },
    // 获取所属充电站
    async getStationList() {
      // const [err, res] = await getStationList({})
      //   if (err) return

      //   const { data } = res
      //   const list = []
      //   data.forEach(item => {
      //     list.push({
      //       label: item.stationName,
      //       value: item.stationId,
      //     })
      //   })
      //   this.stationList = list
      this.stationList = []
    },

    // 获取设备品牌
    async getBrandList() {
        const [err, res] = await getBrandList({})
        if (err) return

        const { data } = res
        const list = []
        data.forEach(item => {
          list.push({
            label: item.brandName,
            value: item.brandId,
          })
        })
        this.brandList = list
    },

    // 获取设备型号
    async getModelList() {
        const [err, res] = await getModelList({})
        if (err) return

        const { data } = res
        const list = []
        data.forEach(item => {
          list.push({
            label: item.modelName,
            value: item.modelId,
          })
        })
        this.modelList = list
    },

    // 获取产权单位
    async getAssetUnit() {
        const [err, res] = await getAssetUnit({})
        if (err) return
        this.assetUnitList = res.data
      },
    nodeClick(node) {
      if (node) {
        const { list, areaCode, areaName, ...reset } = node;
        this.params.operatorCode = '';
        this.params.stationId = '';
        this.params.pileId = '';
        this.regionInfo = reset;
        console.log('node', node);
        this.loadData();
        this.topInfo = {
          ...reset,
          areaName,
        };

        this.handleTopInfo();
      }
    },
    handleTopInfo() {
      const { provinceCodeName, cityCodeName, districtCodeName, areaName } =
        this.topInfo;
      if (provinceCodeName === areaName) {
        // 选中了省
        this.topInfoFirst = '';
        this.topInfoBold = areaName;
      } else if (cityCodeName === areaName) {
        // 选中了市
        this.topInfoFirst = `${provinceCodeName} / `;
        this.topInfoBold = areaName;
      } else if (districtCodeName === areaName) {
        // 选中了区
        this.topInfoFirst = `${provinceCodeName} / ${cityCodeName} / `;
        this.topInfoBold = areaName;
      }
    },

    // 选择充电桩
    handleCheckboxChange({ records }) {
      this.pileList = records
    },

    async loadData() {
      const {
          stationId,
          pileName,
          pileId,
          operationMode,
          deviceBrand,
          deviceModel,
          deviceType,
          propertyUnit,
          controlType,
          isPrivate,
          pileSource,
          isOpen,
          operationStatus,
          operationDate,
          stackNumber,
          accessMethod,
        } = this.filterOptions.params

        const  {
          provinceCode,
          cityCode,
          districtCode,
        } = this.regionInfo

        let operationDateStart = ''
        let operationDateEnd = ''
        if (operationDate && operationDate.length > 0) {
          operationDateStart = operationDate[0]
          operationDateEnd = operationDate[1]
        }

        const parmas = {
            stationId,
            pileName,
            pileNo:pileId,
            operationMode,
            pileBrandId: deviceBrand,
            pileModelId: deviceModel,
            subType:deviceType,
            pileOwnershipUnit: propertyUnit,
            controlType: controlType,
            isPrivatePile: isPrivate,
            stationSource: pileSource,
            openFlag: isOpen,
            operStatus: operationStatus,
            operationDateStart,
            operationDateEnd,
            stackNo:stackNumber,
            stationAccessType: accessMethod,

            province: provinceCode,
            city: cityCode,
            county: districtCode,
            pageNum: this.tablePage.currentPage,
            pageSize: this.tablePage.pageSize,
        }
        this.loading = true;

        const [err, res] = await getPileList(parmas)

        this.loading = false;
        if (err) return 
        const { data, total } = res;

        const list = [];

        this.tableData = data;
        this.tablePage.total = total;

        const [err1, res1] = await getPileSummary(parmas);
        if (err) return 

        const { data: data1 } = res1;
      
        this.infoList = [
          {
            name: '总充电桩',
            value: data1.pileTotal,
            unit: '个',
            icon: icon1,
          },
          {
            name: '直连充电桩',
            value: data1.directlypileTotal,
            unit: '个',
            icon: icon2,
          },
          {
            name: '互联互通充电桩',
            value: data1.polypileTotal,
            unit: '个',
            icon: icon3,
          },
          {
            name: '国网充电桩',
            value: data1.stateGridpileTotal,
            unit: '个',
            icon: icon4,
          },
        ];

    },

    // 详情
    hanleDetail(row) {
      const { pileId } = row;
      this.$router.push({
        path: '/v2g-charging/baseInfo/equipmentAndAssets/pile/detail',
        query: {
          pileId
        }
      });
    },
    // 编辑
    handleEdit(row) {
      const { pileId } = row;

      this.$router.push({
        path: '/v2g-charging/baseInfo/equipmentAndAssets/pile/create',
        query: {
          pileId
        }
      });
    },

    // 枪状态记录
    async hanleRecord(row) {
      const {
        pileId,
        pileName,
        stationName,
      } = row

        this.loading = true;
        const [err, res] = await getPileLog({
          pileId
        });
        this.loading = false;
        if (err) return 
        const { data } = res;
       

        this.$refs.gunStatusModal.form = {
          stationName,
          pileName,
          tableData:data
        }
        this.$refs.gunStatusModal.dialogVisible = true;
        
    },

    // 下发
    handleIssue(row) {
      let rows = [row];
      if(!row) {
        if (!this.pileList.length) {
            this.$message.warning('请先选择要下发的充电桩');
            return;
          }
          rows = this.pileList;
      }

      const list = []
      rows.forEach(item => {
        list.push(item.pileId)
      })

      this.$refs.issueModal.pileId = list;
      this.$refs.issueModal.dialogVisible = true;
    },

    // 下载二维码
    handleDownloadQR(row) {
      let rows = [row];
      if(!row) {
        if (!this.pileList.length) {
            this.$message.warning('请先选择要下载二维码的充电桩');
            return;
          }
          rows = this.pileList;
      }
      const list = []
      rows.forEach(item => {
        list.push(item.pileId)
      })

      this.$refs.qrcodeModal.pileId = list;
      this.$refs.qrcodeModal.dialogVisible = true;
    },

    // 调控配置
    async handleControl(row) {
      const {
        pileId
      } = row;
      this.loading = true;
        const [err, res] = await getPileControlDetail({
          pileId
        });
        this.loading = false;
        if (err) return 
        const { data } = res;

        const {
          stationName,
          stationNo,
          ratePower,
          controlType,
          maxDownCapability,
          maxUpwardCapability,
        } = data;

        this.$refs.controlModal.form = {
          stationName,
          stationNo,
          ratePower,
          controlType,
          maxDownCapability,
          maxUpwardCapability,
        }

        this.$refs.controlModal.pileId = pileId;

        this.$refs.controlModal.dialogVisible = true;
    },

    // 新增充电桩
    handleCreatePile() {
      console.log('新增充电桩');
      this.$router.push({
        path: '/v2g-charging/baseInfo/equipmentAndAssets/pile/create',
      });
    },

    // 已删除充电桩
    handleDeletePile() {
      this.$router.push({
        path: '/v2g-charging/baseInfo/equipmentAndAssets/pile/delete',
      });
    },

    // 批量导入
    handleBatchImport() {
      this.$router.push({
        path: '/v2g-charging/baseInfo/equipmentAndAssets/pile/batchImport',
      });
    },

    handlerunStatus(status,list) {
            let text = status
            list.forEach(item => {
                if(item.label === status) {
                    text = item.value
                }
            })
            return text
        },

    // 删除充电桩
    async handleDelete(row) {
      let rows = [row];

      if (!row) {
          if (!this.pileList.length) {
            this.$message.warning('请先选择要删除的充电桩');
            return;
          }
          rows = this.pileList;
        }

        // const pileIds = []

        // rows.forEach(item => {
        //   pileIds.push(item.pileId)
        // })


        const delRows = rows.filter((x) => x.operStatus == '02' || x.operStatus == '04');

        console.log(delRows,'delRows');

        if (delRows.length) {
          this.$message.warning('已选择充电桩运营状态包括运营中或检修中，不支持删除。');
          return;
        }

        const ids = rows.map((x) => x.pileId);

        const pileName = rows.map((x) => x.pileName).join('、');

        this.$confirm(`确认删除充电桩：${pileName}吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {

          const [err,res] = await batchDeletePile({
            pileIds: ids
          })
          

          if (err) return;
          this.$message({
            type: 'success',
            message: '删除成功!'
          });
          this.loadData();
        });
    },
    

     // 导出
     async handleExport() {
      const {
          stationId,
          pileName,
          pileId,
          operationMode,
          deviceBrand,
          deviceModel,
          deviceType,
          propertyUnit,
          controlType,
          isPrivate,
          pileSource,
          isOpen,
          operationStatus,
          operationDate,
          stackNumber,
          accessMethod,
        } = this.filterOptions.params

        const  {
          provinceCode,
          cityCode,
          districtCode,
        } = this.regionInfo

        let operationDateStart = ''
        let operationDateEnd = ''
        if (operationDate && operationDate.length > 0) {
          operationDateStart = operationDate[0]
          operationDateEnd = operationDate[1]
        }

        const params = {
            stationId,
            pileName,
            pileNo:pileId,
            operationMode,
            pileBrandId: deviceBrand,
            pileModelId: deviceModel,
            subType:deviceType,
            pileOwnershipUnit: propertyUnit,
            controlType: controlType,
            isPrivatePile: isPrivate,
            stationSource: pileSource,
            openFlag: isOpen,
            operStatus: operationStatus,
            operationDateStart,
            operationDateEnd,
            stackNo:stackNumber,
            stationAccessType: accessMethod,

            province: provinceCode,
            city: cityCode,
            county: districtCode,
            pageNum: this.tablePage.currentPage,
            pageSize: this.tablePage.pageSize,
        }

        this.download(
          '/vehicle-charging-admin/pile/export',
          {
            ...params,
          },
          `充电桩列表.xlsx`
        );
      }
    
  },
};
</script>

<style lang="scss" scoped>
.table-wrap {
  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }

  .card-head {
    // position: relative;
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    margin-top: -20px;
    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }
  .info-wrap {
    margin-top: 16px;
    display: flex;
    flex-wrap: wrap;
    gap: 16px;

    .info-item {
      background-color: #fafbfc;
      flex: 1 1 0;
      // min-width: 180px;

      border-radius: 5px;
      padding: 8px 24px;
      box-sizing: border-box;
      // margin-right: 16px;
      display: flex;
      .info-icon {
        width: 42px;
        height: 42px;
      }
      .info-right-wrap {
        flex: 1;
        margin-left: 8px;
        .info-title {
          font-weight: 400;
          font-size: 14px;
          line-height: 14px;
          margin-bottom: 8px;
        }
        .info-number {
          font-size: 20px;
          font-weight: 500;
          .info-unit {
            font-size: 14px;
            font-weight: 400;
          }
        }
      }
    }
    .info-item:last-child {
      margin-right: 0;
    }
  }

  .top-button-wrap {
    display: flex;
    margin: 16px 0;
  }

  .icon-more {
    margin-left: 8px;
    width: 3px;
    height: 13px;
  }
}

.top-info-wrap {
  display: flex;
  height: 20px;
  align-items: center;
  margin-bottom: 16px;

  .top-info-icon {
    margin-left: 10px;
    margin-right: 4px;

    width: 20px;
    height: 20px;
    background-image: url('~@/assets/station/location-top.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
  .top-info-first {
    font-weight: 400;
    font-size: 16px;
    color: #505363;
  }
  .top-info-bold {
    margin-left: 4px;
    font-weight: 400;
    font-size: 16px;
    color: #12151a;
  }
}

.button-border {
    border: 0.01rem solid #217AFF;
    color: #217AFF;
    background-color: #fff;
}

.ellipsis-text {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }
</style>
