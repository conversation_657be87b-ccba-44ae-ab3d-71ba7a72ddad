<template>
  <div class="container container-region">
    <RegionSelect @nodeClick="nodeClick"></RegionSelect>
    <div class="container-info">
      <div class="top-info-wrap">
        <div class="top-info-icon"></div>
        <div class="top-info-first" v-if="topInfoFirst">{{ topInfoFirst }}</div>
        <div class="top-info-bold">{{ topInfoBold }}</div>
      </div>
      <div class="table-wrap">
        <BuseCrud
          ref="crud"
          :loading="loading"
          :filterOptions="filterOptions"
          :tablePage="tablePage"
          :tableColumn="tableColumn"
          :tableData="tableData"
          :pagerProps="pagerProps"
          :modalConfig="modalConfig"
          class="buse-wrap-station"
          @loadData="loadData"
        >
          <template slot="defaultHeader">
            <div>
              <div class="card-head">
                <div class="card-head-text">充电站列表</div>

                <div class="top-button-wrap">
                  <el-button plain @click="() => handleStatus('04')">
                    充电桩永久退运
                  </el-button>

                  <el-button plain @click="() => handleStatus('03')">
                    充电桩临时退运
                  </el-button>

                  <el-button
                    type="primary"
                    plain
                    @click="() => handleStatus('02')"
                  >
                    充电桩停运
                  </el-button>

                  <el-button type="primary" @click="() => handleStatus('01')">
                    充电桩投运
                  </el-button>
                </div>
              </div>
              <div class="card-head-after"></div>
            </div>
          </template>

          <template slot="operate" slot-scope="{ row }">
            <!-- <div class="menu-box">
              <el-button
                type="primary"
                plain
                @click="handleAudit(row, 'index')"
                 v-if="row.auditType == '00'"
              >
                审核
              </el-button>

              <el-button
                type="primary"
                plain
                @click="handleAudit(row, 'detail')"
              >
                详情
              </el-button>
            </div> -->

            <el-dropdown trigger="click">
              <el-button class="button-border" type="primary" plain>
                操作
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-if="row.auditType == '00'">
                  <div @click="handleAudit(row, 'index')">审核</div>
                </el-dropdown-item>

                <el-dropdown-item>
                  <div @click="handleAudit(row, 'detail')">详情</div>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </template>
        </BuseCrud>
      </div>
    </div>
  </div>
</template>

<script>
import RegionSelect from '@/components/Business/RegionSelect';

import StatusDot from '@/components/Business/StatusDot';

import {
  getStationList,
  getBrandList,
  getModelList,
  getAssetUnit,
  getPileStatusList,
  getPileSummary,
  getPileControlDetail,
  getPileLog,
} from '@/api/pile/index';

export default {
  components: {
    RegionSelect,
    StatusDot,
  },
  dicts: [
    'ls_charging_operation_mode', // 运营模式
    'ls_charging_subType', // 设备类型
    'ls_charging_adjustable_type', // 可控类型
    'ls_charging_status', // 是否
    'ls_charging_station_source', // 桩来源
    'ls_charging_pile_operation_status', // 运营状态
    'ls_charging_station_access_type', // 充电桩接入方式
    'ls_charging_audit_type', // 审核状态
    'ls_charging_apply_status', // 申请类型
    'ls_charging_structural_type', // 结构形式
  ],

  // dicts: [
  //   'ls_charging_operation_mode', // 运营模式
  //   'ls_charging_subType', // 设备类型
  //   'ls_charging_adjustable_type', // 可控类型
  //   'ls_charging_status',// 是否
  //   'ls_charging_station_source', // 桩来源
  //   'ls_charging_pile_operation_status', // 运营状态
  //   'ls_charging_station_access_type', // 充电桩接入方式
  // ],
  data() {
    return {
      loading: false,
      topInfo: {},
      topInfoFirst: '',
      topInfoBold: '',

      tablePage: { total: 0, currentPage: 1, pageSize: 10 },

      tableColumn: [
        // {
        //     type: 'checkbox',
        //     width: 50,
        //     fixed: 'left',
        // },
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60, // 最小宽度
        },
        {
          field: 'applyNo',
          title: '申请编号',
          minWidth: 220, // 最小宽度
        },
        {
          field: 'stationName',
          title: '所属充电站',
          minWidth: 160, // 最小宽度
          slots: {
            default: ({ row }) => [
              <el-tooltip
                content={row.stationName}
                placement="top"
                disabled={!row.stationName || row.stationName.length < 10}
              >
                <span class="ellipsis-text">{row.stationName}</span>
              </el-tooltip>,
            ],
          },
        },
        {
          field: 'operationMode',
          title: '运营模式',
          minWidth: 160, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_operation_mode,
              cellValue
            );
          },
        },
        {
          field: 'stationAccessType',
          title: '接入方式',
          minWidth: 100, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_station_access_type,
              cellValue
            );
          },
        },
        {
          field: 'stationSource',
          title: '桩来源',
          minWidth: 120, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_station_source,
              cellValue
            );
          },
        },
        {
          field: 'pileNo',
          title: '充电桩编号',
          minWidth: 200, // 最小宽度

          slots: {
            default: ({ row }) => [
              <el-tooltip
                content={row.pileNo}
                placement="top"
                disabled={!row.pileNo || row.pileNo.length < 10}
              >
                <span class="ellipsis-text">{row.pileNo}</span>
              </el-tooltip>,
            ],
          },
        },
        {
          field: 'pileName',
          title: '充电桩名称',
          minWidth: 180, // 最小宽度
        },
        {
          field: 'brandName',
          title: '设备品牌',
          minWidth: 120, // 最小宽度
        },
        {
          field: 'modelName',
          title: '设备型号',
          minWidth: 200, // 最小宽度
        },
        {
          field: 'subType',
          title: '设备类型',
          minWidth: 120, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_subType,
              cellValue
            );
          },
        },
        {
          field: 'structuralType',
          title: '结构形式',
          minWidth: 120, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_structural_type,
              cellValue
            );
          },
        },
        {
          field: 'stackNo',
          title: '充电堆编号',
          minWidth: 120, // 最小宽度
        },
        {
          field: 'controlType',
          title: '可控类型',
          minWidth: 120, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_adjustable_type,
              cellValue
            );
          },
        },
        {
          field: 'ratePower',
          title: '额定功率(kW)',
          minWidth: 130, // 最小宽度
        },
        {
          field: 'gunSum',
          title: '枪数量',
          minWidth: 100, // 最小宽度
        },
        {
          field: 'pileOwnershipUnitName',
          title: '产权单位',
          minWidth: 200, // 最小宽度
          slots: {
            default: ({ row }) => [
              <el-tooltip
                content={row.pileOwnershipUnitName}
                placement="top"
                disabled={
                  !row.pileOwnershipUnitName ||
                  row.pileOwnershipUnitName.length < 10
                }
              >
                <span class="ellipsis-text">{row.pileOwnershipUnitName}</span>
              </el-tooltip>,
            ],
          },
        },
        {
          field: 'isPrivatePile',
          title: '是否私桩',
          minWidth: 100, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_status,
              cellValue
            );
          },
        },
        {
          field: 'openFlag',
          title: '是否开放',
          minWidth: 120, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_status,
              cellValue
            );
          },
        },
        {
          field: 'operDate',
          title: '桩投运日期',
          minWidth: 220, // 最小宽度
        },
        {
          field: 'operStatus',
          title: '运营状态',
          minWidth: 200, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_pile_operation_status,
              cellValue
            );
          },
        },
        {
          title: '申请类型',
          field: 'applyType',
          minWidth: 120, // 最小宽度
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_apply_status,
              cellValue
            );
          },
        },
        {
          title: '申请人',
          field: 'applicant',
          minWidth: 120, // 最小宽度
          align: 'center',
        },
        {
          title: '申请时间',
          field: 'applyTime',
          minWidth: 200, // 最小宽度
          align: 'center',
        },
        {
          title: '审核状态',
          field: 'auditType',
          minWidth: 100, // 最小宽度
          align: 'center',
          fixed: 'right',
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_audit_type,
              cellValue
            );
          },
          slots: {
            // 自定义render函数
            default: ({ row }) => {
              return (
                <StatusDot
                  value={row.auditType}
                  dictValue={this.dict.type.ls_charging_audit_type}
                  colors={['warning', 'success', 'danger']}
                ></StatusDot>
              );
            },
          },
        },
        {
          title: '操作',
          slots: { default: 'operate' },
          width: 80,
          align: 'center',
          fixed: 'right',
        },
      ],
      tableData: [],
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      params: {
        stationId: '', // 所属充电站
        pileName: '', // 充电桩名称
        pileId: '', // 充电桩编号
        operationMode: '', // 运营模式
        deviceBrand: '', // 设备品牌
        deviceModel: '', // 设备型号
        deviceType: '', // 设备类型
        propertyUnit: '', // 产权单位
        controlType: '', // 可控类型
        isPrivate: '', // 是否私桩
        pileSource: '02', // 桩来源
        isOpen: '', // 是否开放
        operationStatus: '', // 运营状态
        operationDate: [], // 投运日期
        stackNumber: '', // 充电堆编号
        accessMethod: '', // 接入方式
        auditStatus: '',
        applyType: '',
        applyTime: [],
      },

      stationLoading: false,
      stationList: [],
      brandList: [],
      modelList: [],
      assetUnitList: [],
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'stationId',
            title: '所属充电站',
            element: 'el-select',
            props: {
              options: this.stationList,
              filterable: true,
              remote: true,
              remoteMethod: this.debouncedStationSearch,
              loading: this.stationLoading,
            },
          },
          {
            field: 'pileName',
            title: '充电桩名称',
            element: 'el-input',
            placeholder: '请输入',
          },
          {
            field: 'pileId',
            title: '充电桩编号',
            element: 'el-input',
            placeholder: '请输入',
          },
          {
            field: 'operationMode',
            title: '运营模式',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_charging_operation_mode,
            },
          },
          {
            field: 'deviceBrand',
            title: '设备品牌',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.brandList,
            },
          },
          {
            field: 'deviceModel',
            title: '设备型号',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.modelList,
            },
          },
          {
            field: 'deviceType',
            title: '设备类型',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.dict.type.ls_charging_subType,
            },
          },
          {
            field: 'propertyUnit',
            title: '产权单位',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.assetUnitList,
            },
          },
          {
            field: 'controlType',
            title: '可控类型',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.dict.type.ls_charging_adjustable_type,
            },
          },
          {
            field: 'isPrivate',
            title: '是否私桩',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.dict.type.ls_charging_status,
            },
          },
          {
            field: 'pileSource',
            title: '桩来源',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.dict.type.ls_charging_station_source,
            },
          },
          {
            field: 'isOpen',
            title: '是否开放',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.dict.type.ls_charging_status,
            },
          },
          {
            field: 'operationStatus',
            title: '运营状态',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.dict.type.ls_charging_pile_operation_status,
            },
          },
          {
            field: 'operationDate',
            title: '投运日期',
            element: 'el-date-picker',
            props: {
              type: 'daterange',
              placeholder: '开始日期 - 自定日期',
              startPlaceholder: '开始日期',
              endPlaceholder: '结束日期',
              valueFormat: 'yyyy-MM-dd',
            },
          },
          {
            field: 'stackNumber',
            title: '充电堆编号',
            element: 'el-input',
          },
          {
            field: 'accessMethod',
            title: '接入方式',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.dict.type.ls_charging_station_access_type,
            },
          },
          {
            field: 'applyType',
            title: '申请类型',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_charging_apply_status,
            },
          },
          {
            field: 'auditStatus',
            title: '审核状态',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_charging_audit_type,
            },
          },
          {
            field: 'applyTime',
            title: '申请时间',
            element: 'el-date-picker',
            props: {
              type: 'datetimerange',
              options: [],
              valueFormat: 'yyyy-MM-dd HH:mm:ss',
              defaultTime: ['00:00:00', '23:59:59'],
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  mounted() {
    this.getBrandList();
    this.getModelList();
    this.getAssetUnit();
  },
  methods: {
    // 获取设备品牌
    async getBrandList() {
      const [err, res] = await getBrandList({});
      if (err) return;

      const { data } = res;
      const list = [];
      data.forEach((item) => {
        list.push({
          label: item.brandName,
          value: item.brandId,
        });
      });
      this.brandList = list;
    },

    // 获取设备型号
    async getModelList() {
      const [err, res] = await getModelList({});
      if (err) return;

      const { data } = res;
      const list = [];
      data.forEach((item) => {
        list.push({
          label: item.modelName,
          value: item.modelId,
        });
      });
      this.modelList = list;
    },

    // 获取产权单位
    async getAssetUnit() {
      const [err, res] = await getAssetUnit({});
      if (err) return;
      this.assetUnitList = res.data;
    },

    async debouncedStationSearch(query) {
      console.log(query, 'query');
      if (query !== '') {
        this.stationLoading = true;
        setTimeout(async () => {
          const [err, res] = await getStationList({
            stationName: query,
          });

          if (err) return;
          this.stationLoading = false;
          this.stationList = res.data.map((item) => ({
            label: item.stationName,
            value: item.stationId,
          }));
        }, 200);
      } else {
        this.stationList = [];
      }
    },
    nodeClick(node) {
      if (node) {
        const { list, areaCode, areaName, ...reset } = node;
        this.params.operatorCode = '';
        this.params.stationId = '';
        this.params.pileId = '';
        this.regionInfo = reset;
        console.log('node', node);
        this.loadData();
        this.topInfo = {
          ...reset,
          areaName,
        };

        this.handleTopInfo();
      }
    },
    handleTopInfo() {
      const { provinceCodeName, cityCodeName, districtCodeName, areaName } =
        this.topInfo;
      if (provinceCodeName === areaName) {
        // 选中了省
        this.topInfoFirst = '';
        this.topInfoBold = areaName;
      } else if (cityCodeName === areaName) {
        // 选中了市
        this.topInfoFirst = `${provinceCodeName} / `;
        this.topInfoBold = areaName;
      } else if (districtCodeName === areaName) {
        // 选中了区
        this.topInfoFirst = `${provinceCodeName} / ${cityCodeName} / `;
        this.topInfoBold = areaName;
      }
    },
    async loadData() {
      const {
        stationId,
        pileName,
        pileId,
        operationMode,
        deviceBrand,
        deviceModel,
        deviceType,
        propertyUnit,
        controlType,
        isPrivate,
        pileSource,
        isOpen,
        operationStatus,
        operationDate,
        stackNumber,
        accessMethod,
        auditStatus,
        applyType,
        applyTime,
      } = this.filterOptions.params;

      const { provinceCode, cityCode, districtCode } = this.regionInfo;

      let operationDateStart = '';
      let operationDateEnd = '';
      if (operationDate && operationDate.length > 0) {
        operationDateStart = operationDate[0];
        operationDateEnd = operationDate[1];
      }

      let applyStartTime = '';
      let applyEndTime = '';
      if (applyTime && applyTime.length > 0) {
        applyStartTime = applyTime[0];
        applyEndTime = applyTime[1];
      }

      const parmas = {
        stationId,
        pileName,
        pileNo: pileId,
        operationMode,
        pileBrandId: deviceBrand,
        pileModelId: deviceModel,
        subType: deviceType,
        pileOwnershipUnit: propertyUnit,
        controlType: controlType,
        isPrivatePile: isPrivate,
        stationSource: pileSource,
        openFlag: isOpen,
        operStatus: operationStatus,
        operationDateStart,
        operationDateEnd,
        stackNo: stackNumber,
        stationAccessType: accessMethod,

        province: provinceCode,
        city: cityCode,
        county: districtCode,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,

        applyType,
        auditStatus,
        applyStartTime,
        applyEndTime,
      };
      this.loading = true;

      const [err, res] = await getPileStatusList(parmas);

      this.loading = false;
      if (err) return;
      const { data, total } = res;

      const list = [];

      this.tableData = data;
      this.tablePage.total = total;
    },

    // handleCheckboxChange({ records }) {
    //   console.log('records', records);
    //   this.stationList = records.map((item) => item.stationId);
    // },

    handleStatus(status) {
      this.$router.push({
        path: '/v2g-charging/baseInfo/equipmentAndAssets/pile/apply',
        query: {
          status,
        },
      });
    },

    // 审核
    handleAudit(row, type) {
      this.$router.push({
        path: '/v2g-charging/baseInfo/equipmentAndAssets/pile/audit',
        query: {
          type,
          applyNo: row.applyNo,
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.table-wrap {
  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }

  .card-head {
    // position: relative;
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    margin-top: -20px;
    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }
  .info-wrap {
    margin-top: 16px;
    display: flex;
    flex-wrap: wrap;
    gap: 16px;

    .info-item {
      background-color: #fafbfc;
      flex: 1 1 0;
      // min-width: 180px;

      border-radius: 5px;
      padding: 8px 24px;
      box-sizing: border-box;
      // margin-right: 16px;
      display: flex;
      .info-icon {
        width: 42px;
        height: 42px;
      }
      .info-right-wrap {
        flex: 1;
        margin-left: 8px;
        .info-title {
          font-weight: 400;
          font-size: 14px;
          line-height: 14px;
          margin-bottom: 8px;
        }
        .info-number {
          font-size: 20px;
          font-weight: 500;
          .info-unit {
            font-size: 14px;
            font-weight: 400;
          }
        }
      }
    }
    .info-item:last-child {
      margin-right: 0;
    }
  }

  .top-button-wrap {
    display: flex;
    margin: 16px 0;
  }
}

.top-info-wrap {
  display: flex;
  height: 20px;
  align-items: center;
  margin-bottom: 16px;

  .top-info-icon {
    margin-left: 10px;
    margin-right: 4px;

    width: 20px;
    height: 20px;
    background-image: url('~@/assets/station/location-top.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
  .top-info-first {
    font-weight: 400;
    font-size: 16px;
    color: #505363;
  }
  .top-info-bold {
    margin-left: 4px;
    font-weight: 400;
    font-size: 16px;
    color: #12151a;
  }
}

::v-deep .vxe-table--render-default .vxe-body--column.col--center {
  text-align: left !important;
}

.ellipsis-text {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}
</style>
