<template>
    <div class="container container-float " style="padding: 0 0 100px 0;">
        <div class="device-head">
            <img src="@/assets/station/pile-detail-top-icon.png" class="device-head-icon">
            
            <div class="device-info-wrap">
                <div class="device-title-wrap">
                    <div class="device-title">{{ pileName }}</div>
                    <div class="device-status">{{ pileStatus }}</div>
                </div>
                <div class="device-info-wrap">
                    <el-row>
                        <el-col :span="8">
                            <span class="label">充电桩编号：</span>
                            <span class="value">{{pileNo}}</span>
                        </el-col>
                        <el-col :span="8">
                            <span class="label">枪数量：</span>
                            <span class="value">{{gunNumber}}</span>
                        </el-col>
                        <el-col :span="8">
                            <span class="label">桩投运日期：</span>
                            <span class="value">{{ pileDate}}</span>
                        </el-col>
                    </el-row>
                </div>
            </div>
        </div>

        <div class="info-card" >
            <div class="card-head" >
                <div class="before-icon"></div>
                <div class="card-head-text">基础信息</div>
            </div>

            <div class="form-wrap">
                <el-row :gutter="20">
                    <el-col :span="8" v-for="(item, key) in baseInfo" :key="key" style="margin-bottom: 24px;">
                        <div style="display: flex;">
                            <div class="info-title">{{labels.baseInfo[key]}}：</div>
                            <div class="info-detail">{{ item }}</div>
                        </div>
                    </el-col>
                </el-row>

                <el-row :gutter="20" style="margin-bottom: 24px;">
                    <el-col :span="24">
                        <div style="display: flex;">
                            <div class="info-title">备注：</div>
                            <div class="info-detail">{{ remark }}</div>
                        </div>
                        
                    </el-col>
                </el-row>
            </div>
        </div>

        <div class="info-card" >
            <div class="card-head" >
                <div class="before-icon"></div>
                <div class="card-head-text">桩型号及协议信息</div>
            </div>

            <div class="form-wrap">
                <el-row :gutter="20">
                    <el-col :span="8" v-for="(item, key) in protocolInfo" :key="key" style="margin-bottom: 24px;">
                        <div style="display: flex;">
                            <div class="info-title">{{labels.protocolInfo[key]}}：</div>
                            <div class="info-detail">{{ item }}</div>
                        </div>
                    </el-col>
                </el-row>
            </div>
        </div>

        <div class="info-card" >
            <div class="card-head" >
                <div class="before-icon"></div>
                <div class="card-head-text">充电枪信息</div>
            </div>

            <div class="table-wrap">
                <BuseCrud
                    ref="gunInfo"
                    :tableColumn="gunInfoTableColumn"
                    :tableData="gunInfoData"
                    :modalConfig="{ addBtn: false, menu: false }"
                >
                    <template slot="pileBrandModel" slot-scope="{ row }">
                        {{ row.brandName }} / {{ row.modelName }}
                    </template>
                </BuseCrud>
            </div>
        </div>


        <div class="info-card" >
            <div class="card-head" >
                <div class="before-icon"></div>
                <div class="card-head-text">商户运营信息</div>
            </div>

            <div class="form-wrap">
                <el-row :gutter="20">
                    <el-col :span="8" v-for="(item, key) in operatorInfo" :key="key" style="margin-bottom: 24px;">
                        <div style="display: flex;">
                            <div class="info-title">{{labels.operatorInfo[key]}}：</div>
                            <div class="info-detail">{{ item }}</div>
                        </div>
                    </el-col>
                </el-row>
            </div>
        </div>

        <div class="info-card">
            <div class="card-head">
                <div class="before-icon"></div>
                <div class="card-head-text">所属充电站信息</div>
            </div>

            <div class="form-wrap">
                <el-row :gutter="20">
                    <el-col :span="8" v-for="(item, key) in stationInfo" :key="key" style="margin-bottom: 24px;">
                        <div style="display: flex;">
                            <div class="info-title">{{labels.stationInfo[key]}}：</div>
                            <div class="info-detail">{{ item }}</div>
                        </div>
                    </el-col>
                </el-row>
            </div>
        </div>


        <div class="info-card" >
            <div class="card-head" style="margin-bottom: 8px;">
                <div class="before-icon"></div>
                <div class="card-head-text">变更信息</div>
            </div>

            <div class="table-wrap">
                <BuseCrud
                    ref="changeInfo"
                    :tableColumn="changeInfoTableColumn"
                    :tableData="changeInfoData"
                    :modalConfig="{ addBtn: false, menu: false }"
                >
                    <template slot="operate" slot-scope="{ row }">
                        <div class="operate-btn">
                            <el-button type="primary" plain @click="() => handleDetail(row)">
                                详情
                            </el-button>
                        </div>
                    </template>
                </BuseCrud>
            </div>
        </div>


        <editModal
             ref="editModal"
             :editInfo="editInfo"
             :beforeData="beforeData"
             :afterData="afterData"
             :brandList="brandList"
             :modelList="modelList"
             :assetUnitList="assetUnitList"
        />
    </div>
    
  </template>
  
  <script>
    import editModal from './components/editModal.vue';

    

    import {
        getPileDetail,
        getGunBrandList,
        getBrandList,
        getModelList,
        getAssetUnit,

    } from '@/api/pile/index';
  
    export default {
    components: {
        editModal,
    },
    dicts: [
        'ls_charging_operation_status', // 运营状态
        'ls_charging_status', // 是否
        'ls_charging_subType',
        'ls_charging_structural_type',
        'ls_charging_gunCouplerRunStatus',
        'ls_charging_station_type',
        'ls_charging_operation_mode',
        'ls_charging_asset_property',
        'ls_charging_area_type',
        'ls_charging_construction',
        'ls_charging_station_operation_type',
        'ls_charging_subType',
    ],
    data() {
      return {
            pileId: '',
            pileName: '',
            pileStatus: '',
            pileNo: '',
            gunNumber: '',
            pileDate: '',

            labels: {
                baseInfo: {
                    stationName: '充电站名称',
                    iotNumber: '物联网编号',
                    isOpen: '是否开放',
                    privatePile: '是否私桩',
                },
                protocolInfo: {
                    pileBrand: '设备品牌',
                    pileModel: '设备型号',
                    pileType: '设备类型',
                    structuralStyle: '结构形式',
                    chargePileId: '充电堆编号',
                    communicationProtocol: '通讯模块协议',
                    communicationAddress: '充电桩通讯地址',
                },
                operatorInfo: {
                    propertyUnit: '桩产权单位',
                    productionDate: '生产日期',
                    usageYears: '使用年限',
                    salesMeterNo: '营销电表号',
                    operationDate: '投运日期'
                },
                stationInfo: {
                    siteType: '站点类型',
                    operationMode: '运营模式',
                    assetNature: '资产性质',
                    region: '所属区域',
                    constructionSite: '建设场所',
                    siteAddress: '站点地址',
                    operationStatus: '运营状态',
                    assetUnit: '资产单位',
                    operationUnit: '运营单位',
                    maintenanceUnit: '运维单位'
                }
            },

            baseInfo: {
                stationName: '',
                iotNumber: '',
                isOpen: '',
                privatePile: '',
            },

            remark: '',

            protocolInfo: {
                pileBrand: '',
                pileModel: '',
                pileType: '',
                structuralStyle: '',
                chargePileId: '',
                communicationProtocol: '',
                communicationAddress: '',
            },

            gunInfoTableColumn: [
                {
                    field: 'gunNo',
                    title: '枪编号',
                    minWidth: 180,
                },
                {
                    field: 'gunName',
                    title: '枪名称',
                    minWidth: 150,
                },
                {
                    title: '品牌/型号',
                    minWidth: 150,
                    slots: { default: 'pileBrandModel' },
                },
                {
                    title: '充电接口类型', // todo 少字段
                    field: 'subType',
                    minWidth: 150,
                     formatter: ({ cellValue }) => {
                            return this.selectDictLabel(
                                this.dict.type.ls_charging_subType,
                                cellValue
                            );
                    },
                },
                {
                    title: '额定功率(kW)',
                    field: 'gunRatedPower',
                    minWidth: 150,
                },
                {
                    title: '均摊功率(kW)',
                    field: 'gunAveragePower',
                    minWidth: 150,
                },
                {
                    title: '运行状态',
                    field: 'runStatus',
                    width: 220,
                    formatter: ({ cellValue }) => {
                        //  todo 没有转译成功
                            return this.selectDictLabel(
                                this.dict.type.ls_charging_gunCouplerRunStatus,
                                cellValue
                            );
                    },

                },
            ],
            
            gunInfoData: [],

            operatorInfo: {
                propertyUnit: '',
                productionDate: '',
                usageYears: '',
                salesMeterNo: '',
                operationDate: ''
            },

            stationInfo: {
                siteType: '',
                operationMode: '',
                assetNature: '',
                region: '',
                constructionSite: '',
                siteAddress: '',
                operationStatus: '',
                assetUnit: '',
                operationUnit: '',
                maintenanceUnit: ''
            },

            changeInfoTableColumn: [
                {
                    field: 'createTime',
                    title: '时间',
                    minWidth: 180,
                },
                {
                    field: 'operationType',
                    title: '操作类型',
                    minWidth: 150,
                    formatter: ({ cellValue }) => {
                        return this.selectDictLabel(
                            this.dict.type.ls_charging_station_operation_type,
                            cellValue
                        );
                    },
                },
                {
                    title: '具体事项',
                    field: 'operationItems',
                    minWidth: 150,
                },
                {
                    title: '操作人',
                    field: 'operator',
                    minWidth: 150,
                },
                {
                    title: '操作部门',
                    field: 'operationDepartment',
                    minWidth: 150,
                },
                {
                    title: '操作',
                    slots: { default: 'operate' },
                    width: 100,
                    fixed: 'right',
                },
            ],
            
            changeInfoData: [
                // {
                //     time: '2021-10-01 10:00:00',
                //     operatorType: '编辑',
                //     content: '修改了充电桩名称',
                //     operator: '张三',
                //     department: '充电桩管理部',
                // },
                // {
                //     time: '2021-10-01 10:00:00',
                //     operatorType: '投运',
                //     content: '修改了充电桩名称',
                //     operator: '李四',
                //     department: '充电桩管理部',
                // }
            ],

            editInfo: {},

            gunBrandList: [],


            beforeData: {},
            afterData: {},

            brandList:  [], // 设备品牌
            modelList: [], // 设备型号
            assetUnitList: [], // 资产单位

      };
    },

    computed: {
    },
    mounted() {
        const pileId = this.$route.query.pileId;

        this.getGunBrandList()

        // this.getBrandList();
        this.getModelList();
        this.getAssetUnit();
        

        if(pileId) {
            this.pileId = pileId
            this.getPileDetail()
        }
    },
    methods: {
        // 获取产权单位
     async getAssetUnit() {
        const [err, res] = await getAssetUnit({})
        if (err) return
        this.assetUnitList = res.data

        console.log('getAssetUnit', this.assetUnitList)
      },
         // 获取设备品牌
    async getBrandList() {
        console.log('getBrandList1', )
        const [err, res] = await getBrandList({})
        if (err) return

        const { data } = res

        console.lig('getBrandList', data)
        const list = []
        data.forEach(item => {
          list.push({
            label: item.brandName,
            value: item.brandId,
          })
        })
        this.brandList = list

        console.log('this.brandList', this.brandList)
    },

     // 获取设备型号
     async getModelList() {
        const [err, res] = await getModelList({})
        if (err) return

        const { data } = res
        const list = []
        data.forEach(item => {
          list.push({
            label: item.modelName,
            value: item.modelId,
          })
        })
        this.modelList = list
    },
        // 获取充电枪品牌列表 获取设备品牌
        async getGunBrandList() {
            const [err, res] = await getGunBrandList({})
            if (err) return

            const { data } = res
            const list = []
            data.forEach(item => {
                list.push({
                    label: item.brandName,
                    value: item.brandId,
                })
            })
            this.gunBrandList = list
            this.brandList = list
        },

        // 获取充电桩详情
        async getPileDetail() {
            const [err, res] = await getPileDetail({
                pileId: this.pileId
            })
            if (err) return
            console.log(res.data,'res.data')

            const  {
                pileName,
                pileNo,
                operStatus,
                gunSum,
                operDate
            } = res.data

            // 顶部数据
            this.pileName = pileName
            this.pileStatus = this.selectDictLabel(
                    this.dict.type.ls_charging_operation_status,
                    operStatus
                  );
            this.pileNo = pileNo
            this.gunNumber = gunSum
            this.pileDate = operDate

            // 基础信息
            const  {
                stationName,
                pileIoeNo,
                openFlag,
                isPrivatePile,
                remark,
            } = res.data

            this.baseInfo = {
                stationName,
                iotNumber: pileIoeNo,
                isOpen:  this.selectDictLabel(
                    this.dict.type.ls_charging_status,
                    openFlag
                  ),
                privatePile: this.selectDictLabel(
                    this.dict.type.ls_charging_status,
                    isPrivatePile
                  ),
            }
            this.remark = remark

            // 桩型号及协议信息
            const {
                brandName,
                modelName,
                subType,
                structuralType,
                stackNo,
                protocolName,	
                pilePostalAddress,
            } = res.data

            this.protocolInfo = {
                pileBrand: brandName,
                pileModel: modelName,
                pileType:  this.selectDictLabel(
                    this.dict.type.ls_charging_subType,
                    subType
                  ),
                structuralStyle: this.selectDictLabel(
                    this.dict.type.ls_charging_structural_type,
                    structuralType
                  ),
                chargePileId: stackNo,
                communicationProtocol: protocolName,
                communicationAddress: pilePostalAddress,
            }

            // 充电枪信息
            const {
                pileGunList
            }   = res.data

            const list = []
            pileGunList.forEach(item => {
                list.push({
                    ...item,
                    runStatusText: this.handlerunStatus(item.runStatus, this.dict.type.ls_charging_gunCouplerRunStatus)
                })
            })

            this.gunInfoData = list

            // 商户运营信息
            const {
                pileOwnershipUnit,
                productionTime,
                serviceLife,
                meterNo,
            } = res.data

            this.operatorInfo = {
                propertyUnit: pileOwnershipUnit,
                productionDate: productionTime,
                usageYears: serviceLife,
                salesMeterNo: meterNo,
                operationDate: operDate
            }

            // 所属充电站信息
            const {
                stationType,
                operationMode,
                assetProperty,
                areaType, 
                construction, 
                stationAddress,
                operationStatus,
                assetUnitName,
                operatingUnitName,
                maintenanceUnitName,
            } = res.data

            this.stationInfo = {
                siteType:  this.selectDictLabel(
                    this.dict.type.ls_charging_station_type,
                    stationType
                ),
                operationMode: this.selectDictLabel(
                    this.dict.type.ls_charging_operation_mode,
                    operationMode
                ),
                assetNature: this.selectDictLabel(
                    this.dict.type.ls_charging_asset_property,
                    assetProperty
                ),
                region: this.selectDictLabel(
                    this.dict.type.ls_charging_area_type,
                    areaType
                  ),
                constructionSite: this.selectDictLabel(
                    this.dict.type.ls_charging_construction,
                    construction
                ),
                siteAddress: stationAddress,
                operationStatus: this.selectDictLabel(
                    this.dict.type.ls_charging_operation_status,
                    operationStatus
                ),
                assetUnit: assetUnitName,
                operationUnit: operatingUnitName,
                maintenanceUnit: maintenanceUnitName,
            }

            // 变更信息
            const {
                pileOperationDtoList
            } = res.data

            this.changeInfoData = pileOperationDtoList

               
        },

        handlerunStatus(status,list) {
            let text = status
            list.forEach(item => {
                if(item.label === status) {
                    text = item.value
                }
            })
            return text
        },

        handleDetail(row) {
            // this.editInfo = {
            //     beforeType: '超充',
            //     beforeIsOpen: '否',
            //     beforeDesignUnit: '场站设计单位',
            //     beforeConcactPerson: '张三',
            //     afterType: '超充',
            //     afterIsOpen: '是',
            //     afterDesignUnit: '场站设计单位',
            //     afterConcactPerson: '李四',
            // }

            const {
                operationType,
                pileAlterBeforeJson,
                pileAlterAfterJson,
            } = row

            if(operationType === '99') {
                let pileAlterBefore = this.filterEmptyObjects(JSON.parse(pileAlterBeforeJson))
                let pileAlterAfter = this.filterEmptyObjects(JSON.parse(pileAlterAfterJson))

                this.beforeData = pileAlterBefore
                this.afterData = pileAlterAfter

                console.log('beforeData', this.beforeData, this.afterData)

                this.$refs.editModal.dialogVisible = true;
            } else {
                const {
                    applyNo
                }  = row

                this.$router.push({
                    path: '/v2g-charging-web/baseInfo/equipmentAndAssets/pile/audit',
                    query: {
                        type: 'detail',
                        applyNo,
                    }
                })
            }

           
        },

        // 过滤空对象
        filterEmptyObjects(obj) {
            return Object.fromEntries(
                Object.entries(obj).filter(([_, value]) => {
                // 判断是否为非空对象
                return !(typeof value === 'object' && 
                        value !== null && 
                        !Array.isArray(value) && 
                        Object.keys(value).length === 0);
                })
            );
        }
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .container-full {
    background-color: rgba(244, 246, 249, 1);
    padding-bottom: 16px;
    min-height: calc(100% - 80px);
    overflow-y: auto;
  }
  .device-head {
    background-color: #fff;
    
    display: flex;
    height: 112px;
    display: flex;
    align-items: center;
    padding: 0 24px;
    box-sizing: border-box;
    .device-head-icon {
        width: 48px;
        height: 48px;
        margin-right: 24px;
    }
    .device-info-wrap {
        flex: 1;
        .device-title-wrap {
            height: 32px;
            display: flex;
            align-items: center;
            .device-title {
                font-weight: 500;
                font-size: 24px;
                color: #12151A;
            }
            .device-status {
                // width: 50px;
                padding: 0 10px;
                box-sizing: border-box;
                height: 24px;
                border-radius: 10px 0 10px 0;
                font-size: 16px;
                font-weight: 400;
                line-height: 24px;
                text-align: center;
                color: #fff;
                background: linear-gradient(321.01deg, #00C864 8.79%, #38F3CA 100.27%);
                margin-left: 12px;
            }
        }
        .device-info-wrap {
            height: 16px;
            margin-top: 16px;
            font-size: 16px;
            font-weight: 400;
            color: #292B33;
        }
    }
    
  
  }
   
  .info-card {
    margin: 16px;
    border-radius: 5px;
    border: 1px solid #fff;
    overflow: hidden;
    background-color: #fff;
    // min-height: 300px;
    .card-head {
        height: 56px;
        padding: 0 16px;
        display: flex;
        align-items: center;
        background: linear-gradient(180deg, #E9F2FF 0%, #FFFFFF 100%);
        .before-icon {
            width: 3px;
            height: 16px;
            background-image: url('~@/assets/station/consno-before.png');
            background-size: 100% 100%;
            background-repeat: no-repeat;
            margin-right: 8px;
        }
        .card-head-text {
            flex:1;
            font-weight: 500;
            font-size: 16px;
            color: #12151A;

        &::before {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            left: -3px; /* 调整这个值来改变边框的宽度 */
            width: 0;
            border-top: 3px solid transparent;
            border-bottom: 3px solid transparent;
            border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
        }

    
        }

    }


    .form-wrap {
      padding: 0 24px 0 24px;
      .info-title {
        font-weight: 400;
        font-size: 16px;
        line-height: 16px;
        color: #505363;
      }
      .info-detail {
        font-weight: 400;
        font-size: 16px;
        line-height: 16px;
        color: #292B33;
      }
      .info-amount {
        height: 28px;
        background-color: #FFF7E6;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 8px;
        font-weight: 400;
        font-size: 20px;
        color: #FE8921;
        margin-top: -6px;
        margin-right: 4px;

      }
      .info-img {
        width: 140px;
        height: 140px;
      }
    }

    .table-wrap {
        padding: 8px 24px 24px 24px;
    }

  }


  ::v-deep  .bd3001-content{
    padding: 0 !important;
  }
 
  </style>
  