<template>
    <el-dialog title="详情" :visible.sync="dialogVisible" width="858px">
        <div class="edit-info-wrap">
            <div class="edit-info-item">
                <div class="edit-info-item-title-before">
                    <div class="edit-detail-title-icon"></div>
                    <div>调整前信息</div>         
                </div>

                <template v-for="module in activeModules">
                    <module-section 
                        :key="'before-'+module.key"
                        :title="module.title"
                        :data="beforeData[module.beforeKey]"
                        :fields="module.fields"
                    />
                </template>

                <div class="module-section" v-if="beforeData.pileGunDetailDtoListBefore">
                    <div class="title-wrap">
                        <div class="before-icon"></div>
                        <div>充电枪信息</div>
                    </div>

                    <BuseCrud
                        style="margin-left: 24px;"
                        ref="gunInfo"
                        :tableColumn="gunInfoTableColumn"
                        :tableData="beforeData.pileGunDetailDtoListBefore"
                        :modalConfig="{ addBtn: false, menu: false }"
                        >
                            <template slot="pileBrandModel" slot-scope="{ row }">
                                {{ row.brandName }} / {{ row.modelName }}
                            </template>
                    </BuseCrud>
                
                </div>
                            

              

            </div>
            <div class="edit-info-item">
                <div class="edit-info-item-title-after">
                    <div class="edit-detail-title-icon"></div>
                    <div>调整后信息</div>
                </div>
                <template v-for="module in activeModules">
                <module-section 
                    :key="'after-'+module.key"
                    :title="module.title"
                    :data="afterData[module.afterKey]"
                    :fields="module.fields"
                    :compare-data="beforeData[module.beforeKey]"
                />
                </template>

                <div class="module-section" v-if="afterData.pileGunDetailDtoListAfter">
                    <div class="title-wrap">
                        <div class="before-icon"></div>
                        <div>充电枪信息</div>
                    </div>

                    <BuseCrud
                        style="margin-left: 24px;"
                        ref="gunInfo"
                        :tableColumn="gunInfoTableColumn"
                        :tableData="afterData.pileGunDetailDtoListAfter"
                        :modalConfig="{ addBtn: false, menu: false }"
                        >
                            <template slot="pileBrandModel" slot-scope="{ row }">
                                {{ row.brandName }} / {{ row.modelName }}
                            </template>
                    </BuseCrud>
                
                </div>

            </div>

        </div>
    </el-dialog>
   
</template>
<script>  
import ModuleSection from './ModuleSection.vue'


  export default {
    props: {
        beforeData: {
            type: Object,
            default: () => {},
        },
        afterData: {
            type: Object,
            default: () => {},
        },
        brandList: {
            type: Array,
            default: () => [],
        },
        modelList: {
            type: Array,
            default: () => [],
        },
        protocolOptions: {
            type: Array,
            default: () => [],
        },
        assetUnitList: {
            type: Array,
            default: () => [],
        }
    },
    dicts: [
        'ls_charging_status',
        'ls_charging_pile_gun_subType',
        'ls_charging_structural_type',
        'ls_charging_subType',
    ],
    components: {
        ModuleSection,
    },
    data() {
        return {
            dialogVisible: false,

            moduleConfig:[
                {
                    key: 'foundation',
                    title: '基础信息',
                    beforeKey: 'pileFoundationDtoBefore',
                    afterKey: 'pileFoundationDtoAfter',
                    fields: [
                        { key: 'stationName', label: '充电站名称' },
                        { key: 'pileName', label: '充电桩名称' },
                        { key: 'pileNo', label: '充电桩编号' },
                        { key: 'pileIoeNo', label: '物联网编号'},
                        {
                            key: 'openFlag', 'label': '是否开放',
                            formatter: openFlag=>this.selectDictLabel(
                                this.dict.type.ls_charging_status,
                                openFlag
                            )
                        },
                        {
                            key: 'isPrivatePile', 'label': '是否私桩',
                            formatter: isPrivatePile=>this.selectDictLabel(
                                this.dict.type.ls_charging_status,
                                isPrivatePile
                            )
                        },
                        { key: 'remark', label: '备注'},
                    ]
                },
                {
                    key: 'pileBrandDetail',
                    title: '桩型号及协议信息',
                    beforeKey: 'pileBrandDetailDtoBefore',
                    afterKey: 'pileBrandDetailDtoAfter',
                    fields: [
                        {   
                            key: 'pileBrandId', label: '设备品牌',
                            formatter: pileBrandId=>this.selectDictLabel(
                                this.brandList,
                                pileBrandId
                            )
                        },
                        {
                            key: 'pileModelId', label: '设备型号',
                            formatter: openFlag=>this.selectDictLabel(
                                this.modelList,
                                openFlag
                            )
                        },
                        {
                            key: 'subType', label: '设备类型',
                            formatter: subType=>this.selectDictLabel(
                                this.dict.type.ls_charging_pile_gun_subType,
                                subType
                            )
                        },
                        {
                            key: 'structuralType', label: '结构形式',
                            formatter: structuralType=>this.selectDictLabel(
                                this.dict.type.ls_charging_structural_type,
                                structuralType
                            )
                        },
                        {
                            key: 'stackNo', label: '充电堆编号',
                        },
                        {
                            key: 'protocolName', label: '通讯模块协议',
                        },
                        {
                            key: 'pilePostalAddress',label:'充电桩通讯地址'
                        }

                    ]
                },
                {
                    key: 'pileOperatorDetail',
                    title: '商户运营信息',
                    beforeKey: 'pileOperatorDetailDtoBefore',
                    afterKey: 'pileOperatorDetailDtoAfter',
                    fields: [
                        {   
                            key: 'pileOwnershipUnit', label: '桩产权单位',
                            formatter: pileOwnershipUnit=>this.selectDictLabel(
                                this.assetUnitList,
                                pileOwnershipUnit
                            ), 
                        },
                        {
                            key: 'productionTime',label:'生产时间'
                        },
                        {
                            key: 'serviceLife',label:'使用年限'
                        },
                        {
                            key: 'meterNo',label:'营销电表号'
                        },
                        {
                            key: 'consNo',label:'营销户号'
                        }
                    ]
                }

            ],

            gunInfoTableColumn: [
                {
                    field: 'gunNo',
                    title: '枪编号',
                    minWidth: 180,
                },
                {
                    field: 'gunName',
                    title: '枪名称',
                    minWidth: 150,
                },
                {
                    title: '品牌/型号',
                    minWidth: 150,
                    slots: { default: 'pileBrandModel' },
                },
                {
                    title: '充电接口类型', // todo 少字段
                    field: 'subType',
                    minWidth: 150,
                    formatter: ({ cellValue }) => {
                            return this.selectDictLabel(
                                this.dict.type.ls_charging_subType,
                                cellValue
                            );
                    },
                },
                {
                    title: '额定功率(kW)',
                    field: 'gunRatedPower',
                    minWidth: 150,
                },
                {
                    title: '均摊功率(kW)',
                    field: 'gunAveragePower',
                    minWidth: 150,
                },
                {
                    title: '运行状态',
                    field: 'runStatusText',
                    width: 220,

                },
            ],
        };
    },
    watch: {
        
    },
    computed: {
        activeModules() {
            
            return this.moduleConfig.map(module => {
                const beforeModuleData = this.beforeData[module.beforeKey] || {};
                const afterModuleData = this.afterData[module.afterKey] || {};

                // 动态过滤字段
                const validFields = module.fields.filter(field => {
                    const hasBefore = Object.keys(beforeModuleData).includes(field.key);
                    const hasAfter = Object.keys(afterModuleData).includes(field.key);
                    return hasBefore || hasAfter;
                });
                return {
                    ...module,
                    fields: validFields
                };

            }).filter(module => module.fields.length > 0); // 过滤空模块
        },
    },
    mounted() {
        
    },
    methods: {
        
    },
  };
  </script>

<style lang="scss" scoped>
.edit-info-wrap {
    display: flex;
    min-height: 520px;
    justify-content: space-between;
    .edit-info-item {
        width: 373px;
        // padding: 0 0 2px 24px;
        // box-sizing: border-box;
        .edit-info-item-title-before {
            display: flex;
            height: 88px;
            width: 100%;
            background: linear-gradient(180deg, #F2F5F7 0%, #FFFFFF 100%);
            padding: 0 0 0 24px;
            box-sizing: border-box;
            align-items: center;
            font-weight: 500;
            font-size: 24px;

            .edit-detail-title-icon {
                width: 32px;
                height: 32px;
                background-image: url('~@/assets/station/edit-detail-title-icon.png');
                background-size: 100% 100%;
                background-repeat: no-repeat;
                margin-right: 12px;
            }
        }
        .edit-info-item-title-after {
            display: flex;
            height: 88px;
            width: 100%;
            background: linear-gradient(180deg, #D9ECFF 0%, #FFFFFF 100%);
            padding: 0 0 0 24px;
            box-sizing: border-box;
            align-items: center;
            font-weight: 500;
            font-size: 24px;
            .edit-detail-title-icon {
                width: 32px;
                height: 32px;
                background-image: url('~@/assets/station/edit-detail-title-icon.png');
                background-size: 100% 100%;
                background-repeat: no-repeat;
                margin-right: 12px;
            }
        }
        .title-wrap {
            height: 18px;
            padding: 0 0 0 24px;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            font-weight: 500;
            font-size: 18px;
            margin-bottom: 24px;
            color: #12151A;
            .before-icon {
                width: 3px;
                height: 16px;
                background-image: url('~@/assets/station/consno-before.png');
                background-size: 100% 100%;
                background-repeat: no-repeat;
                margin-right: 8px;
            }
        }
        .info-wrap {
            height: 16px;
            padding: 0 0 0 24px;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            margin-bottom: 24px;
            .info-title {
                font-weight: 400;
                font-size: 16px;
                line-height: 16px;
                color: #505363;
            }
            .info-detail {
                font-weight: 400;
                font-size: 16px;
                line-height: 16px;
                color: #292B33;
            }
        }
        

    }
}

  </style>
  