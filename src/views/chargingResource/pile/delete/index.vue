<template>
    <div class="container container-region">
      <RegionSelect @nodeClick="nodeClick"></RegionSelect>
      <div class="container-info ">
        <div class="top-info-wrap" >
            <div class="top-info-icon"></div>
            <div class="top-info-first" v-if="topInfoFirst">{{ topInfoFirst }}</div>
            <div class="top-info-bold " >{{ topInfoBold }}</div>
        </div>
        <div class="table-wrap">
            <BuseCrud
                ref="crud"
                :loading="loading"
                :filterOptions="filterOptions"
                :tablePage="tablePage"
                :tableColumn="tableColumn"
                :tableData="tableData"
                :pagerProps="pagerProps"
                :modalConfig="modalConfig"
                :tableOn="{
                    'checkbox-change': handleCheckboxChange,
                    'checkbox-all': handleCheckboxChange,
                }"
                @loadData="loadData"
            >
            <template slot="defaultHeader">
                <div>
                    <div class="card-head">
                        <div class="card-head-text">充电站列表</div>
                    </div>
                    <div class="card-head-after"></div>

                    <div class="top-button-wrap">
   
                        <el-button
                             type="primary"
                            @click="() => handleCancelDelete(null)"
                        >
                            批量取消删除
                        </el-button>
                        

                        <el-button
                            plain
                            @click="() => handleExport()"
                        >
                            导出
                        </el-button>

                        
 
                    
                    </div>
                </div>
                
            </template>

                <template slot="operate" slot-scope="{ row }">
                    <div class="menu-box">
                        <el-button
                            type="primary"
                            plain
                            @click="handleCancelDelete(row)"
                        >
                            取消删除
                        </el-button>



                    
                    </div>
                
                </template>

            </BuseCrud>
        </div>
        

      </div>
    </div>
  </template>
  
  <script>
  import RegionSelect from '@/components/Business/RegionSelect';
  import StatusDot from '@/components/Business/StatusDot';

  import {
    getStationList,
    getBrandList,
    getModelList,
    getAssetUnit,
    getPileList,
    batchCancelDelete,
    getPileSummary,
    getPileControlDetail,
    getPileLog,
  } from '@/api/pile/index';

  export default {
    components: {
      RegionSelect,
    },
    dicts: [
    'ls_charging_operation_mode', // 运营模式
    'ls_charging_subType', // 设备类型  
    'ls_charging_adjustable_type', // 可控类型
    'ls_charging_status',// 是否
    'ls_charging_station_source', // 桩来源
    'ls_charging_operation_status', // 运营状态
    'ls_charging_station_access_type', // 充电桩接入方式
  ],
    data() {
      return {
        loading: false,
        stationList: [],
        topInfo: {},
        topInfoFirst: '',
        topInfoBold: '',

        tablePage: { total: 0, currentPage: 1, pageSize: 10 },
        

        tableColumn:[
            {
                type: 'checkbox',
                width: 50,
                fixed: 'left',
            },
            {
                type: 'seq',
                title: '序号',
                width: 60,
                minWidth: 60, // 最小宽度
            },
            {
                field: 'stationName',
                title: '所属充电站',
                minWidth: 120, // 最小宽度
            },
            {
          field: 'operationMode',
          title: '运营模式',
          minWidth: 100, // 最小宽度
          formatter: ({ cellValue }) => {
                  return this.selectDictLabel(
                    this.dict.type.ls_charging_operation_mode,
                    cellValue
                  );
          },
        },
        {
          field: 'stationAccessType',
          title: '接入方式',
          minWidth: 100, // 最小宽度
          formatter: ({ cellValue }) => {
                  return this.selectDictLabel(
                    this.dict.type.ls_charging_station_access_type,
                    cellValue
                  );
                },
        },
        {
          field: 'stationSource',
          title: '桩来源',
          minWidth: 120, // 最小宽度
          formatter: ({ cellValue }) => {
                  return this.selectDictLabel(
                    this.dict.type.ls_charging_station_source,
                    cellValue
                  );
                },
        },
        {
          field: 'pileNo',
          title: '充电桩编号',
          minWidth: 150, // 最小宽度
        },
        {
          field: 'pileName',
          title: '充电桩名称',
          minWidth: 120, // 最小宽度
        },
        {
          field: 'brandName',
          title: '设备品牌',
          minWidth: 120, // 最小宽度
        },
        {
          field: 'modelName',
          title: '设备型号',
          minWidth: 100, // 最小宽度
        },
        {
          field: 'subType',
          title: '设备类型',
          minWidth: 120, // 最小宽度
          formatter: ({ cellValue }) => {
                  return this.selectDictLabel(
                    this.dict.type.ls_charging_subType,
                    cellValue
                  );
                },
        },
        {
          field: 'controlType',
          title: '可控类型',
          minWidth: 100, // 最小宽度
          formatter: ({ cellValue }) => {
                  return this.selectDictLabel(
                    this.dict.type.ls_charging_adjustable_type,
                    cellValue
                  );
                },
        },
        {
          field: 'ratePower',
          title: '额定功率(kW)',
          minWidth: 130, // 最小宽度
        },
        {
          field: 'gunSum',
          title: '枪数量',
          minWidth: 100, // 最小宽度
        },
        {
          field: 'pileOwnershipUnitName',
          title: '产权单位',
          minWidth: 100, // 最小宽度
        },
        {
          field: 'isPrivatePile',
          title: '是否私桩',
          minWidth: 100, // 最小宽度
          formatter: ({ cellValue }) => {
                  return this.selectDictLabel(
                    this.dict.type.ls_charging_status,
                    cellValue
                  );
                },
        },
        {
          field: 'openFlag',
          title: '是否开放',
          minWidth: 120, // 最小宽度
          formatter: ({ cellValue }) => {
                  return this.selectDictLabel(
                    this.dict.type.ls_charging_status,
                    cellValue
                  );
                },
        },
        {
          field: 'operDate',
          title: '桩投运日期',
          minWidth: 120, // 最小宽度
        },
        {
          field: 'operStatus',
          title: '运营状态',
          minWidth: 100, // 最小宽度
          // formatter: ({ cellValue }) => {
          //         return this.selectDictLabel(
          //           this.dict.type.ls_charging_operation_status,
          //           cellValue
          //         );
          //       },
          align: 'center',
                fixed: 'right',
                slots: {
                  // 自定义render函数
                  default: ({ row }) => {
                    return (
                      <StatusDot
                        value={row.operStatus}
                        dictValue={this.dict.type.ls_charging_operation_status}
                        colors={['default', 'success', 'danger','warning','danger','danger','warning']}
                      ></StatusDot>
                    );
                  },
                },
        },
            {
                title: '操作',
                slots: { default: 'operate' },
                width: 120,
                align: 'center',
                fixed: 'right',
            },
  
        ],
        tableData: [],
        pagerProps: {
          layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
        },
        params: {
            stationId: '',
            pileName: '',
            pileId: '',
            operationMode: '',
            pileBrand:'',
            pileModel: '',
            pileType: '',
            propertyUnit: '',
            controlType: '',
            privatePile: '',
            pileSource: '',
            isOpen: '',
            // operationStatus: '',
            operationDate: [],
            chargePileId: '',
            accessMode: '',
        },

        stationNameList: [],
        brandList: [], //设备品牌
        modelList: [], //设备型号
        assetUnitList: [], //产权单位
      };
    },
    computed: {
      filterOptions() {
        return {
          config: [
            {
              field: 'stationId',
              title: '所属充电站',
              element: 'el-select',
              props: {
                options: this.stationNameList,
                filterable: true,
                remote: true,
                remoteMethod: this.debouncedStationSearch,
                loading: this.stationLoading,
              },
            },
            {
              field: 'pileName',
              title: '充电桩名称',
              element: 'el-input',
              placeholder: '请输入',
            },
            {
              field: 'pileId',
              title: '充电桩编号',
              element: 'el-input',
              placeholder: '请输入',
            },
            {
              field: 'operationMode',
              title: '运营模式',
              element: 'el-select',
              props: {
                options: this.dict.type.ls_charging_operation_mode,
              },
            },
            {
            field: 'deviceBrand',
            title: '设备品牌',
            element: 'el-select',
            props: {
              options: this.brandList,
            },
          },
          {
            field: 'deviceModel',
            title: '设备型号',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.modelList,
            },
          },
          {
            field: 'deviceType',
            title: '设备类型',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_charging_subType,
            },
          },
          {
            field: 'propertyUnit',
            title: '产权单位',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.assetUnitList,
            },
          },
          {
            field: 'controlType',
            title: '可控类型',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_charging_adjustable_type,
            },
          },
          {
            field: 'isPrivate',
            title: '是否私桩',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_charging_status,
            },
          },
          {
            field: 'pileSource',
            title: '桩来源',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_charging_station_source,
            },
          },
          {
            field: 'isOpen',
            title: '是否开放',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_charging_status,
            },
          },
            // {
            //   field: 'operationStatus',
            //   title: '运营状态',
            //   element: 'el-select',
            //   props: {
            //     options: [
            //       {
            //         label: '待投运',
            //         value: '01',
            //       },
            //       {
            //         label: '投运',
            //         value: '02',
            //       }
            //     ],
            //   },
            // },
            {
            field: 'operationDate',
            title: '投运日期',
            element: 'el-date-picker',
            props: {
              type: 'daterange',
              placeholder: '开始日期 - 自定日期',
              startPlaceholder: '开始日期',
              endPlaceholder: '结束日期',
              valueFormat: 'yyyy-MM-dd',
            },
          },
          {
            field: 'stackNumber',
            title: '充电堆编号',
            element: 'el-input',
          },
          {
            field: 'accessMethod',
            title: '接入方式',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_charging_station_access_type,
            },
          },
          ],
          params: this.params,
        };
      },
      modalConfig() {
        return {
            addBtn: false,
            viewBtn: false,
            menu: false,
            editBtn: false,
            delBtn: false,
        }
      },
    },
    mounted() {
      this.getBrandList();
    this.getModelList();
    this.getAssetUnit();
    },
    methods: {
        async debouncedStationSearch(query) {
        console.log(query, 'query');
        if (query !== '') {
            this.stationLoading = true;
            setTimeout(async() => {
              const [err, res] = await getStationList(
                {
                  stationName: query,
                }
              );

              if (err) return;
              this.stationLoading = false;
              this.stationNameList = res.data.map((item) => ({
                label: item.stationName,
                value: item.stationId,
              }));
            }, 200);
            
          } else {
            this.stationNameList = []
        }
      
      },

       // 获取设备品牌
    async getBrandList() {
        const [err, res] = await getBrandList({})
        if (err) return

        const { data } = res
        const list = []
        data.forEach(item => {
          list.push({
            label: item.brandName,
            value: item.brandId,
          })
        })
        this.brandList = list
    },

    // 获取设备型号
    async getModelList() {
        const [err, res] = await getModelList({})
        if (err) return

        const { data } = res
        const list = []
        data.forEach(item => {
          list.push({
            label: item.modelName,
            value: item.modelId,
          })
        })
        this.modelList = list
    },

    // 获取产权单位
    async getAssetUnit() {
        const [err, res] = await getAssetUnit({})
        if (err) return
        this.assetUnitList = res.data
      },
      nodeClick(node) {
        if (node) {
          const { list, areaCode, areaName, ...reset } = node;
          this.params.operatorCode = '';
          this.params.stationId = '';
          this.params.pileId = '';
          this.regionInfo = reset;
          console.log('node', node);
          this.loadData();
          this.topInfo = {
            ...reset,
            areaName,
          }


            this.handleTopInfo()
        }
      },
    handleTopInfo() {
        const { provinceCodeName, cityCodeName, districtCodeName, areaName} = this.topInfo;
        if (provinceCodeName === areaName) {
            // 选中了省
            this.topInfoFirst = '';
            this.topInfoBold = areaName;
        } else if (cityCodeName === areaName) {
            // 选中了市
            this.topInfoFirst = `${provinceCodeName} / `;
            this.topInfoBold = areaName;
        } else if (districtCodeName === areaName) {
            // 选中了区
            this.topInfoFirst = `${provinceCodeName} / ${cityCodeName} / `;
            this.topInfoBold = areaName;
        }
    },
      async loadData() {
        const operStatus = '06'
        

        const {
          stationId,
          pileName,
          pileId,
          operationMode,
          deviceBrand,
          deviceModel,
          deviceType,
          propertyUnit,
          controlType,
          isPrivate,
          pileSource,
          isOpen,
          operationDate,
          stackNumber,
          accessMethod,
        } = this.filterOptions.params

        const  {
          provinceCode,
          cityCode,
          districtCode,
        } = this.regionInfo

        let operationDateStart = ''
        let operationDateEnd = ''
        if (operationDate && operationDate.length > 0) {
          operationDateStart = operationDate[0]
          operationDateEnd = operationDate[1]
        }

        const parmas = {
            stationId,
            pileName,
            pileNo:pileId,
            operationMode,
            pileBrandId: deviceBrand,
            pileModelId: deviceModel,
            subType:deviceType,
            pileOwnershipUnit: propertyUnit,
            controlType: controlType,
            isPrivatePile: isPrivate,
            stationSource: pileSource,
            openFlag: isOpen,
            operStatus,
            operationDateStart,
            operationDateEnd,
            stackNo:stackNumber,
            stationAccessType: accessMethod,

            province: provinceCode,
            city: cityCode,
            county: districtCode,
            pageNum: this.tablePage.currentPage,
            pageSize: this.tablePage.pageSize,
            
        }

        this.loading = true;

        const [err, res] = await getPileList(parmas)

        this.loading = false;
        if (err) return 
        const { data, total } = res;


        this.tableData = data;
        this.tablePage.total = total;
        
      },

        handleCheckboxChange({ records }) {
            console.log('records', records);
            this.stationList = records;
        },
        
        // // 批量取消删除
        // handleCancelDeleteBatch() {
        //     if(!this.stationList.length) {
        //         this.$message.warning('请先选择站点')
        //         return
        //     } else {
        //         // todo 调用接口
        //     }
        // },

        // 取消删除
        handleCancelDelete(row) {
          let rows = [row];
          if (!row) {
            if (!this.stationList.length) {
              this.$message.warning('请先选择要恢复的数据！');
              return;
            }
            rows = this.stationList;
          }

          const ids = rows.map((x) => x.pileId);
          const stationName = rows.map((x) => x.pileName).join('、');
          this.$confirm(`确认恢复充电桩：${stationName}吗？`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(async () => {
            
            const [err, res] =await batchCancelDelete(ids.map((item) => (item)))

            console.log(res, err)
            if (err){ 
              return
            }
            this.$message({
                type: 'success',
                message: '恢复成功!'
            });
            this.loadData();
            
            
          })
        }
        

    },
  };
  </script>
  
  <style lang="scss" scoped>
.table-wrap {
    ::v-deep .bd3001-table-select-box {
        display: none;
    }
    ::v-deep .bd3001-header  {
        display: block;
    }
    ::v-deep .bd3001-button {
        display: block !important;
    }
    
    .card-head {
        // position: relative; 
        height: 56px;
        padding: 0 16px;
        display: flex;
        align-items: center;
        margin-top: -20px;
        .card-head-text {
        flex:1;
        width: 520px;
            height: 26px;
            background-image: url('~@/assets/images/bg-title.png');
            background-size: 520px 26px;
            background-repeat: no-repeat;
            font-size: 20px;
            font-style: normal;
            font-weight: 500;
            line-height: 16px;
            padding-left: 36px;
            color: #21252e;
        &::before {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            left: -3px; /* 调整这个值来改变边框的宽度 */
            width: 0;
            border-top: 3px solid transparent;
            border-bottom: 3px solid transparent;
            border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
        }
        }   

    }

    .card-head-after {
        width: 100%;
        height: 1px;
        background-color: #DCDEE2;
        margin-bottom: 16px;
    }
    .info-wrap {
        margin-top: 16px;
        display: flex;
        flex-wrap: wrap;
        gap: 16px;

        .info-item {
            background-color: #FAFBFC;
            flex: 1 1 0;
            // min-width: 180px;
            
            border-radius: 5px;
            padding: 8px 24px;
            box-sizing: border-box;
            // margin-right: 16px;
            display: flex;
            .info-icon {
                width: 42px;
                height: 42px;
            }
            .info-right-wrap {
                flex:1;
                margin-left: 8px;
                .info-title {
                    font-weight: 400;
                    font-size: 14px;
                    line-height: 14px;
                    margin-bottom: 8px;
                }
                .info-number {
                    font-size: 20px;
                    font-weight: 500;
                    .info-unit {
                        font-size: 14px;
                        font-weight: 400;
                    }
                }
            }
        }
        .info-item:last-child {
            margin-right: 0;
        }
    }

    .top-button-wrap {
        display:flex;
        margin: 16px 0;
    }
}


  
.top-info-wrap {
    display: flex;
    height: 20px;
    align-items: center;
    margin-bottom: 16px;
    
    .top-info-icon {
        margin-left: 10px;
        margin-right: 4px;

        width: 20px;
        height: 20px;
        background-image: url('~@/assets/station/location-top.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }
    .top-info-first {
        font-weight: 400;
        font-size: 16px;
        color: #505363;
    }
    .top-info-bold {
        margin-left: 4px;
        font-weight: 400;
        font-size: 16px;
        color: #12151A;

    }
  }
</style>
  