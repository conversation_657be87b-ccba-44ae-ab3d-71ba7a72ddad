<template>
    <div class="status-bg" :class="[className]">
      {{ value }}
    </div>
  </template>
  
  <script>
  export default {
    name: 'StatusDot',
    props: {
      value: {
        type: String,
        default: '',
      },
      
    },
    data() {
      return {
        className: '',
      };
    },
    watch: {
      value: {
        immediate: true,
        handler(newVal) {
          if (newVal) {
            if( newVal === '离线') {
                this.className = 'status-bg-1';
            } else if (newVal === '故障') {
                this.className = 'status-bg-2';
            } else if (newVal === '结束') {
                this.className = 'status-bg-3';
            } else if (newVal === '充电') {
                this.className = 'status-bg-4';
            } else if (newVal === '已插枪') {
                this.className = 'status-bg-5';
            } else if (newVal === '待机') {
                this.className = 'status-bg-6';
            }
          } 
        },
      },
    },
    computed: {

    },
    mounted() {},
    methods: {
      tabChange(val) {
        this.$emit('input', val);
        this.$emit('tabChange', val);
      },
    },
  };
  </script>
  
  <style rel="stylesheet/scss" lang="scss" scoped>
.status-bg {
    height: 22px;
    border-radius: 2px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
}
.status-bg-1 {
    width: 44px;
    background-color:  #F5F7FA;
    color: #818496;
}
.status-bg-2 {
    width: 44px;
    background-color:  #FFE8E6;
    color: #FC1E31;
}
.status-bg-3 {
    width: 44px;
    background-color: #EBFFF1;
    color: #00C864;
}
.status-bg-4 {
    width: 44px;
    background-color:#EBF3FF;
    color: #217AFF;
}
.status-bg-5 {
    width: 60px;
    background-color:#FFF7E6;
    color: #FF8D24;
}
.status-bg-6 {
    width: 44px;
    background-color:#EBFCFF;
    color: #1AB2FF;
}
  </style>
  