<template>
  <div class="container container-float" style="padding: 0">
    <div class="info-card">
      <div class="card-head">
        <div class="before-icon"></div>
        <div class="card-head-text">基础信息</div>
      </div>
      <div class="form-wrap">
        <el-form
          :model="baseInfo.form"
          :rules="baseInfo.rules"
          ref="baseInfoForm"
          label-position="top"
        >
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="所属充电站" prop="stationId">

                      <el-select
                        v-model="baseInfo.form.stationId"
                        placeholder="请选择站点"
                        style="width: 100%"
                        clearable
                        filterable
                        remote
                        :remote-method="debouncedStationSearch"
                        :loading="stationLoading"
                        @change="stationChange"
                      >
                        <el-option
                          v-for="item in stationList"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        ></el-option>
                      </el-select>

              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="充电桩名称" prop="pileName">
                <el-input v-model="baseInfo.form.pileName"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="充电桩编号" prop="pileNo">
                <el-input v-model="baseInfo.form.pileNo"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="物联网编号" prop="pileIoeNo">
                <el-input v-model="baseInfo.form.pileIoeNo"></el-input>
              </el-form-item>
            </el-col>
            
          </el-row>

          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="是否开放" prop="openFlag">
                <el-select v-model="baseInfo.form.openFlag" placeholder="请选择">
                  <el-option
                    v-for="item in dict.type.ls_charging_status"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="是否私桩" prop="isPrivatePile">
                <el-select
                  v-model="baseInfo.form.isPrivatePile"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in dict.type.ls_charging_status""
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="备注" prop="remark">
                <el-input v-model="baseInfo.form.remark"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <div class="info-card">
      <div class="card-head">
        <div class="before-icon"></div>
        <div class="card-head-text">桩型号及协议信息</div>
      </div>
      <div class="form-wrap">
        <el-form
          :model="protocolInfo.form"
          :rules="protocolInfo.rules"
          ref="protocolInfoForm"
          label-position="top"
        >
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="设备品牌" prop="pileBrandId">
                <el-select
                  v-model="protocolInfo.form.pileBrandId"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in brandList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="设备型号" prop="pileModelId">
                <el-select
                  v-model="protocolInfo.form.pileModelId"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in modelList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="设备类型" prop="subType">
                <el-select
                  v-model="protocolInfo.form.subType"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in dict.type.ls_charging_subType"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="结构形式" prop="structuralType">
                <el-select
                  v-model="protocolInfo.form.structuralType"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in dict.type.ls_charging_structural_type"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20">
            <el-col :span="6" v-if="protocolInfo.form.structuralType === '00'">
              <el-form-item label="充电堆编号" prop="stackNo">
                <el-input v-model="protocolInfo.form.stackNo"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item prop="protocol">
                <template #label>
                  <div class="protocol-label">
                    <span>通讯模块协议</span>
                    <div class="btn-wrap" @click="handleAddProtocol">
                      <img
                        class="icon-add"
                        src="@/assets/images/pile/icon/icon-add.svg"
                      />
                      添加
                    </div>
                  </div>
                </template>
                <el-select
                  v-model="protocolInfo.form.protocol"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in protocolOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="充电桩通讯地址" prop="pilePostalAddress">
                <el-input v-model="protocolInfo.form.pilePostalAddress"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <div class="info-card">
      <div class="card-head">
        <div class="before-icon"></div>
        <div class="card-head-text">充电枪信息</div>
      </div>
      <div class="form-wrap">
        <vxe-table border :data="gunData" ref="gunTable">
          <vxe-column type="seq" title="序号" width="60"></vxe-column>
          <vxe-column field="gunNo" title="枪编号">
            <template #default="{ row }">
              <el-input
                v-model="row.gunNo"
                placeholder="请输入枪编号"
              ></el-input>
            </template>
          </vxe-column>
          <vxe-column field="gunName" title="枪名称">
            <template #default="{ row }">
              <el-input
                v-model="row.gunName"
                placeholder="请输入枪名称"
              ></el-input>
            </template>
          </vxe-column>
          <vxe-column field="brand" title="品牌/型号">
            <template #default="{ row , rowIndex }">
              <el-cascader
                v-model="row.brand"
                :options="brandOptions"
                :props="{
                    label: 'brandName',
                    children: 'modelGunList',
                    value: 'brandId',
                    expandTrigger: 'hover',
                  }"
                @change="handleChange(rowIndex, $event)"></el-cascader>
            </template>
          </vxe-column>
          <vxe-column field="subType" title="充电接口类型">
            <template #default="{ row }">
              <el-select v-model="row.subType" placeholder="请选择" disabled>
                <el-option
                  v-for="item in dict.type.ls_charging_pile_gun_subType"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </template>
          </vxe-column>
          <vxe-column field="gunRatedPower" title="额定功率">
            <template #default="{ row }">
              <!-- <el-input
                v-model="row.gunRatedPower"
                placeholder="请输入额定功率"
              ></el-input> -->
              <el-input-number
                v-model="row.gunRatedPower"
                :precision="2"
                :controls="false"
                :min="0"
                placeholder="请输入额定功率"
              ></el-input-number>
            </template>
          </vxe-column>
          <vxe-column field="gunAveragePower" title="均摊功率">
            <template #default="{ row }">
              <!-- <el-input
                v-model="row.gunAveragePower"
                placeholder="请输入均摊功率"
              ></el-input> -->
              <el-input-number
                v-model="row.gunAveragePower"
                :precision="2"
                :controls="false"
                :min="0"
                 placeholder="请输入均摊功率"
              ></el-input-number>
            </template>
          </vxe-column>
          <vxe-column title="操作" width="150">
            <template #default="{ row, $rowIndex }">
              <el-button
                type="text"
                size="small"
                :disabled="$rowIndex === 0"
                @click="moveUp(row)"
              >
                <i class="el-icon-top"></i>
              </el-button>
              <el-button
                type="text"
                size="small"
                :disabled="$rowIndex === gunData.length - 1"
                @click="moveDown(row)"
              >
                <i class="el-icon-bottom"></i>
              </el-button>
              <el-button type="text" size="small" @click="deleteRow(row)">
                <i class="el-icon-delete"></i>
              </el-button>
            </template>
          </vxe-column>
        </vxe-table>
        <div class="add-btn" @click="addGun">
          <i class="el-icon-plus"></i>
          添加
        </div>
      </div>
    </div>

    <div class="info-card">
      <div class="card-head">
        <div class="before-icon"></div>
        <div class="card-head-text">商户运营信息</div>
      </div>
      <div class="form-wrap">
        <el-form
          :model="operationInfo.form"
          :rules="operationInfo.rules"
          ref="operationInfoForm"
          label-position="top"
        >
          <el-row :gutter="20">
            <el-col :span="6">
              <el-form-item label="桩产权单位" prop="pileOwnershipUnit">
                <el-select
                  v-model="operationInfo.form.pileOwnershipUnit"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in assetUnitList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="生产时间" prop="productionTime">
                <el-date-picker
                  type="datetime"
                  v-model="operationInfo.form.productionTime"
                  value-format="yyyy-MM-dd HH:mm:ss"
                ></el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="使用年限" prop="serviceLife">
                <el-input
                  v-model="operationInfo.form.serviceLife"
                  placeholder="请输入"
                >
                  <template #append>年</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="营销电表号" prop="meterNo">
                <el-input
                  v-model="operationInfo.form.meterNo"
                  placeholder="请输入"
                ></el-input>
              </el-form-item>
            </el-col>

            <el-col :span="6">
              <el-form-item label="营销户号" prop="consNo">
                <!-- <el-input
                  v-model="operationInfo.form.consNo"
                  placeholder="请输入"
                ></el-input> -->
                <el-select
                  v-model="operationInfo.form.consNo"
                  placeholder="请选择"
                >
                  <el-option
                    v-for="item in consList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </div>

    <div class="bottom-btns">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleSave">保存</el-button>
    </div>

    <ProtocolDialog ref="protocolDialog" @submit="handleProtocolSubmit" />
  </div>
</template>

<script>
import ProtocolDialog from './components/ProtocolDialog.vue';

import {
  getStationList,
  getBrandList,
  getModelList,
  getGunBrandModel,
  getAssetUnit,
  getProtocolList,
  createPile,
  getPileDetail,
  updatePile,
  getConsList,
} from '@/api/pile/index';
export default {
  components: {
    ProtocolDialog,
  },
  dicts:[
    'ls_charging_status',
    'ls_charging_subType',
    'ls_charging_structural_type',
    'ls_charging_pile_gun_subType',
  ],
  data() {
    return {
      pageType: 'create',
      stationLoading: false,
      updatePile: '',
      baseInfo: {
        form: {
          stationId: '',
          pileName: '',
          pileNo: '',
          pileIoeNo: '',
          openFlag: '',
          isPrivatePile: '',
          remark: '',
        },
        rules: {
          stationId: [
            { required: true, message: '请输入所属充电站', trigger: 'blur' },
          ],
          pileName: [
            { required: true, message: '请输入充电桩名称', trigger: 'blur' },
          ],
          pileNo: [
            { required: true, message: '请输入充电桩编号', trigger: 'blur' },
          ],
          openFlag: [
            { required: true, message: '请选择是否开放', trigger: 'blur' },
          ],
          isPrivatePile: [
            { required: true, message: '请选择是否私桩', trigger: 'blur' },
          ],
        },
      },
      protocolInfo: {
        form: {
          pileBrandId: '',
          pileModelId: '',
          subType: '',
          structuralType: '',
          stackNo: '',
          protocol: '',
          pilePostalAddress: '',
        },
        rules: {
          pileBrandId: [
            { required: true, message: '请选择设备品牌', trigger: 'blur' },
          ],
          pileModelId: [
            { required: true, message: '请选择设备型号', trigger: 'blur' },
          ],
          structuralType: [
            { required: true, message: '请选择结构形式', trigger: 'blur' },
          ],
          stackNo: [
            { required: true, message: '请输入充电堆编号', trigger: 'blur' },
          ],
          protocol: [
            { required: true, message: '请选择通讯模块协议', trigger: 'blur' },
          ],
          pilePostalAddress: [
            {
              required: true,
              message: '请输入充电桩通讯地址',
              trigger: 'blur',
            },
          ],
        },
      },
      operationInfo: {
        form: {
          pileOwnershipUnit: '',
          productionTime: '',
          serviceLife: '',
          meterNo: '',
          consNo: '',
        },
        rules: {
          pileOwnershipUnit: [
            { required: true, message: '请输入桩产权单位', trigger: 'blur' },
          ],
          consNo: [
            {required: true, message: '请输入营销户号', trigger: 'blur' },
          ]
        },
      },
      gunData: [],
      // 选项数据
      isOpenOptions: [],
      privatePileOptions: [],
     
      modelOptions: [],
      typeOptions: [],
      structureOptions: [],
     
      gunTypeOptions: [],


      stationList: [], // 所属充电站
      brandList:  [], // 设备品牌
      modelList: [], //设备型号
      brandOptions: [], // 充电枪品牌型号

      assetUnitList: [], // 产权单位
      protocolOptions: [], // 通讯模块协议
      consList:[], // 营销户号

    };
  },
  mounted() {
    this.getStationList();
    this.getBrandList();
    this.getModelList();
    this.getGunBrandModel();
    this.getAssetUnit();
    this.getProtocolList();

    const pileId = this.$route.query.pileId
    if(pileId) {
            this.pageType = 'edit'
            this.pileId = pileId
            this.getPileIdDetail(pileId)
        }
  },
  methods: {
     // 获取所属充电站
     async getStationList() {
      // const [err, res] = await getStationList({})
      //   if (err) return

      //   const { data } = res
      //   const list = []
      //   data.forEach(item => {
      //     list.push({
      //       label: item.stationName,
      //       value: item.stationId,
      //     })
      //   })
      //   this.stationList = list
      this.stationList = []
    },

    async debouncedStationSearch(query) {
      console.log(query, 'query');
      if (query !== '') {
          this.stationLoading = true;
          setTimeout(async() => {
            const [err, res] = await getStationList(
              {
                stationName: query,
              }
            );

            if (err) return;
            this.stationLoading = false;
            this.stationList = res.data.map((item) => ({
              label: item.stationName,
              value: item.stationId,
            }));
          }, 200);
          
        } else {
          this.stationList = []
      }
     
    },

    // 选中站点
    stationChange() {
      if(this.baseInfo.form.stationId) {
        // 清除营销户号
        this.operationInfo.form.consNo = ''
        // 获取营销户号列表
        this.getConsList()
      } else {
         // 清除营销户号
        this.operationInfo.form.consNo = ''
        this.consList = []
        this.stationList = []
      }
      
    },

    // 获取营销户号列表
    async getConsList() {
      const [err, res] = await getConsList({
        stationId: this.baseInfo.form.stationId, 
      })
        if (err) return

        const { data } = res
        const list = []
        data.forEach(item => {
          list.push({
            label: item.consName,
            value: item.consNo,
          })
        })
        this.consList = list
        
    },

    // 获取设备品牌
    async getBrandList() {
        const [err, res] = await getBrandList({})
        if (err) return

        const { data } = res
        const list = []
        data.forEach(item => {
          list.push({
            label: item.brandName,
            value: item.brandId,
          })
        })
        this.brandList = list
    },

     // 获取设备型号
     async getModelList() {
        const [err, res] = await getModelList({})
        if (err) return

        const { data } = res
        const list = []
        data.forEach(item => {
          list.push({
            label: item.modelName,
            value: item.modelId,
          })
        })
        this.modelList = list
    },

    // 充电枪品牌型号
    async getGunBrandModel() {
      const [err, res] = await getGunBrandModel({
        deviceType: '02'
      })
        if (err) return

        const { data } = res

        const list = []
        data.forEach(item => {
          const {
            modelGunList
          } = item

          const temModelGunList = []
          modelGunList.forEach(item1 => {
            temModelGunList.push({
              ...item1,
              brandId: item1.modelId,
              brandName: item1.modelName,
            })
          })

          list.push({
            ...item,
            modelGunList: temModelGunList
          })
        })
        
        this.brandOptions = list

      
    },
     // 获取产权单位
     async getAssetUnit() {
        const [err, res] = await getAssetUnit({})
        if (err) return
        this.assetUnitList = res.data
      },

      // 获取通讯模块协议列表
      async getProtocolList() {
        const [err, res] = await getProtocolList({})
        if (err) return

        const { data } = res
        const list = []
        data.forEach(item => {
          list.push({
            label: item.protocolName,
            value: item.protocolCode,
          })
        })
        this.protocolOptions = list
      },

    handleChange(rowIndex,value) {
      console.log(rowIndex,this.gunData)
      const brand = this.gunData[rowIndex].brand
      console.log(brand, this.brandOptions)

      let subType = ''

      this.brandOptions.forEach(item => {
        if(item.brandId === brand[0]) {
          const { modelGunList } = item
          modelGunList.forEach(item1 => {
            if (item1.brandId === brand[1]) {
              subType = item1.subType
            }
          })
        }
      })
      console.log(subType, '111')
      this.gunData[rowIndex].subType = subType
    },

    // 充电桩详情
    async getPileIdDetail(pileId) {
      const [err, res] = await getPileDetail({
        pileId
      })
        if (err) return

        const { 
            stationId,
            stationName,
            pileName,
            pileNo,
            pileIoeNo,
            openFlag,
            isPrivatePile,
            remark,
            pileBrandId,
            pileModelId,
            subType,
            structuralType,
            stackNo,
            protocolCode,
            pilePostalAddress,
            pileOwnershipUnit,
            productionTime,
            serviceLife,
            meterNo,
            consNo,
            pileGunList
          } = res.data 

          this.baseInfo.form = {
            stationId,
            pileName,
            pileNo,
            pileIoeNo,
            openFlag,
            isPrivatePile,
            remark,
          }

          this.getConsList()

          this.stationList = [
            {
              label: stationName,
              value: stationId,
            }
          ]

          this.protocolInfo.form = {
            pileBrandId,
            pileModelId,
            subType,
            structuralType,
            stackNo,
            protocol: protocolCode,
            pilePostalAddress,
          }

          this.operationInfo.form = {
            pileOwnershipUnit,
            productionTime,
            serviceLife,
            meterNo,
            consNo,
          }

          const list = []
          pileGunList.forEach((item) => {
            list.push({
              ...item,
              brand: [
                item.gunBrandId,
                item.gunModelId,
              ]
            })
          })

          this.gunData = list


       
    },
    // 添加协议
    handleAddProtocol() {
      this.$refs.protocolDialog.getComiiProtocolList();
      this.$refs.protocolDialog.show();
    },

    // 添加处理提交方法
    handleProtocolSubmit() {
      this.getProtocolList()
    },

    addGun() {
      this.gunData.push({
        gunId: '',
        gunNo: '',
        gunName: '',
        brand: [],
        subType: '',
        gunRatedPower: '',
        gunAveragePower: '',
      });
    },
    moveUp(row) {
      const index = this.gunData.indexOf(row);
      if (index > 0) {
        this.gunData.splice(index, 1);
        this.gunData.splice(index - 1, 0, row);
      }
    },
    moveDown(row) {
      const index = this.gunData.indexOf(row);
      if (index < this.gunData.length - 1) {
        this.gunData.splice(index, 1);
        this.gunData.splice(index + 1, 0, row);
      }
    },
    deleteRow(row) {
      const index = this.gunData.indexOf(row);
      this.gunData.splice(index, 1);
    },
    // 校验单个表单
    validateForm(formRef) {
            return new Promise((resolve, reject) => {
                this.$refs[formRef].validate((valid) => {
                    if (valid) {
                        resolve(); // 校验通过
                    } else {
                        reject(new Error(`${formRef} 校验失败`)); // 校验失败
                    }
                });
            });
    },
    handleCancel() {
      this.$router.go(-1);
    },
     handleSave() {
      Promise.all([
        this.validateForm('baseInfoForm'),
        this.validateForm('protocolInfoForm'),
        this.validateForm('operationInfoForm'),
      ])
        .then(async() => {
            // 所有表单校验通过
          const {
            stationId,
            pileName,
            pileNo,
            pileIoeNo,
            openFlag,
            isPrivatePile,
            remark,
          } = this.baseInfo.form;

          const {
            pileBrandId,
            pileModelId,
            subType,
            structuralType,
            stackNo,
            protocol,
            pilePostalAddress,
          } = this.protocolInfo.form;


          const protocolCode = protocol;

          let protocolName = '';

          this.protocolOptions.forEach((item) => {
            if (item.value === protocolCode) {
              protocolName = item.label;
            }
          })

          const {
            pileOwnershipUnit,
            productionTime,
            serviceLife,
            meterNo,
            consNo,
          } = this.operationInfo.form;

          if(!this.gunData.length) {
            this.$message.warning('请添加充电枪');
            return;
          }



          const pileGunList = []

          this.gunData.forEach((item) => {
            console.log(item.gunAveragePower, 'gunAveragePower')
            pileGunList.push({
              ...item,
              gunBrandId: item.brand[0],
              gunModelId: item.brand[1],
             
            })
          })

          const params = {
            stationId,
            pileName,
            pileNo,
            pileIoeNo,
            openFlag,
            isPrivatePile,
            remark,
            pileBrandId,
            pileModelId,
            subType,
            structuralType,
            stackNo,
            protocolCode,
            protocolName,
            pilePostalAddress,
            pileOwnershipUnit,
            productionTime,
            serviceLife,
            meterNo,
            consNo,
            pileGunList
          }


          if(this.pageType === 'create') {
            const [err,res] = await createPile(
              params
            )

                    console.log(res,err, 'createStation')
                    if (err){ 
                        return this.$message.error(err.message || '新增充电桩失败');
                    }
                        this.$message({
                            type: 'success',
                            message: '新增成功!'
                        });

                        setTimeout(() => {
                            this.$router.back();
                        }, 2000);
          } else if (this.pageType === 'edit') {
            

            const [err,res] = await updatePile(
              {
                ...params,
                pileId: this.pileId
              }
            )
                    if (err){ 
                        return this.$message.error(err.message || '编辑充电桩失败');
                    }
                        this.$message({
                            type: 'success',
                            message: '编辑成功!'
                        });

                        setTimeout(() => {
                            this.$router.back();
                        }, 2000);
          }
 

        })
        .catch((error) => {
          console.log(this.baseInfo.form)
          console.log(error);
          // 有表单校验失败
          this.$message.error('表单校验失败，请检查输入');
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.el-select {
  width: 100%;
}
.el-date-editor {
  width: 100%;
}
.info-card {
  margin: 16px;
  border-radius: 5px;
  border: 1px solid #fff;
  background-color: #fff;

  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;

    .before-icon {
      width: 3px;
      height: 16px;
      background-image: url('~@/assets/station/consno-before.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin-right: 8px;
    }

    .card-head-text {
      font-weight: 500;
      font-size: 16px;
      color: #12151a;
    }
  }

  .form-wrap {
    padding: 16px;
  }

  ::v-deep .el-form-item__label {
    display: flex;
  }

  .protocol-label {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .btn-wrap {
      display: flex;
      align-items: center;
      color: #409eff;
      cursor: pointer;
      font-size: 14px;
      font-weight: 400;

      .icon-add {
        width: 16px;
        height: 16px;
        margin-right: 8px;
      }
    }
  }
}

.add-btn {
  width: 100%;
  height: 34px;
  border: 1px solid #dfe1e5;
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: #217aff;
  margin-top: 12px;
  cursor: pointer;

  i {
    margin-right: 4px;
  }
}

.bottom-btns {
  margin: 16px;
  text-align: center;
}
</style>
