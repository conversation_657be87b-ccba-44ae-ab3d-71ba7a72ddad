<template>
    <div class="container container-region">
      <RegionSelect @nodeClick="nodeClick"></RegionSelect>
      <div class="container-info">
        <div class="top-info-wrap" >
            <div class="top-info-icon"></div>
            <div class="top-info-first" v-if="topInfoFirst">{{ topInfoFirst }}</div>
            <div class="top-info-bold " >{{ topInfoBold }}</div>
        </div>
        <div class="table-wrap">
            <BuseCrud
                ref="crud"
                :loading="loading"
                :filterOptions="filterOptions"
                :tablePage="tablePage"
                :tableColumn="tableColumn"
                :tableData="tableData"
                :pagerProps="pagerProps"
                :modalConfig="modalConfig"
                :tableOn="{
                    'checkbox-change': handleCheckboxChange,
                    'checkbox-all': handleCheckboxChange,
                }"
                class="buse-wrap-station"
                @loadData="loadData"
            >
            <template slot="defaultHeader">
                <div>
                    <div class="card-head">
                        <div class="card-head-text">充电站列表</div>
                    </div>
                    <div class="card-head-after"></div>

                    <div class="info-wrap">
                        <div class="info-item"  v-for="item in infoList" :key="item.name">
                            <img :src="item.icon" class="info-icon">
                            <div class="info-right-wrap">
                                <div class="info-title">{{ item.name }}</div>
                                <div class="info-number">
                                    {{ item.value }}
                                    <span class="info-unit">{{ item.unit }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="top-button-wrap">
                        <el-button
                            type="primary"
                            @click="() => handleCreate()"
                        >
                            新增充电站
                        </el-button>

                        <el-button
                            type="primary"
                            plain
                            @click="() => handleToDelete()"
                        >
                            已删除充电站
                        </el-button>

                        <el-button
                            plain
                            @click="() => handleExport()"
                        >
                            导出
                        </el-button>

                        <el-button
                            plain
                            @click="() => handleDelete(null)"
                        >
                            批量删除
                        </el-button>
                        
                        <el-button
                            plain
                            @click="() => handleControl()"
                        >
                            批量导入
                        </el-button>

                        <el-button
                            plain
                            @click="() => handleControl()"
                        >
                            线下站导入
                        </el-button>
                    
                    </div>
                </div>
                
            </template>

                <template slot="operate" slot-scope="{ row }">
                    <!-- <div class="menu-box">
                        <el-button
                            class="button-border"
                            type="primary"
                            plain
                            @click="hanleDetail(row)"
                        >
                            详情
                        </el-button>

                        <el-button
                           
                            class="button-border"
                            type="primary"
                            plain
                            @click="hanleEdit(row)"
                        >
                            编辑
                        </el-button>

                        <el-button
                            class="button-border"
                            type="primary"
                            plain
                            v-show="row.adjustableType!=='00' && (row.operationStatus === '01'|| row.operationStatus === '02')"
                            @click="() => handleControl(row)"
                        >
                            调控配置
                        </el-button>

                        <el-button
                          class="delete-btn"
                          type="danger"
                          plain
                          v-show="row.operationStatus === '05' "
                          @click="() => handleDelete(row)"
                          
                        >
                          删除
                        </el-button>

                    
                    </div> -->

                    <el-dropdown trigger="click">
                      <el-button
                        class="button-border"
                        type="primary"
                        plain
                      >
                        操作
                      </el-button>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item >
                          <div   @click="hanleDetail(row)">
                            详情
                          </div>
                        </el-dropdown-item>

                        <el-dropdown-item >
                          <div v-if="row.stationSource === '01'"   @click="hanleEdit(row)">
                            编辑
                          </div>
                        </el-dropdown-item>

                        <el-dropdown-item  v-show="row.adjustableType!=='00' && (row.operationStatus === '01'|| row.operationStatus === '02')">
                          <div   @click="handleControl(row)">
                            调控配置
                          </div>
                        </el-dropdown-item>

                        <el-dropdown-item v-show="row.operationStatus === '05' ">
                          <div   @click="() => handleDelete(row)">
                            删除
                          </div>
                        </el-dropdown-item>

                        

                      </el-dropdown-menu>
                    </el-dropdown>
                
                </template>

            </BuseCrud>

            <ControlConfigurationModal ref="controlConfigurationModal" @loadData="loadData"/>
        </div>
        

      </div>
    </div>
  </template>
  
  <script>
  import RegionSelect from '@/components/Business/RegionSelect';
  import ControlConfigurationModal from './components/controlConfigurationModal';
import icon1 from '@/assets/station/total-icon.png';
import icon2 from '@/assets/station/direct-connection-icon.png';
import icon3 from '@/assets/station/aggregation-icon.png';
import icon4 from '@/assets/station/state-grid-icon.png';

import {
  getStationList,
  getStationStatistic,
  batchDeleteStation,
  deleteStation,
  getAssetUnit,
  getOperationUnit,
  getMaintenanceUnit,
  getControlDetail,
} from '@/api/station/index';

import StatusDot from '@/components/Business/StatusDot';





  export default {
    components: {
      RegionSelect,
      ControlConfigurationModal,
      StatusDot,
    },
    dicts: [
      'ls_charging_station_type', // 站点类型
      'ls_charging_construction', // 建设场所
      'ls_charging_operation_mode', // 运营模式
      'ls_charging_asset_property', // 资产属性
      'ls_charging_station_access_type', // 充电站接入类型
      'ls_charging_operation_status', // 运营状态
      'ls_charging_station_source', // 数据来源
      'ls_charging_status', // 是否状态
      'ls_charging_adjustable_type', // 可控类型
      'ls_charging_parking_charge_type', // 停车场收费类型
      'ls_charging_area_type', // 所属区域
      'ls_charging_contracted_unit', // 签约单位 投资主体 供电机构
      'ls_charging_structural_type',
      'ls_charging_parking_location',
    ],
    data() {
      return {
        loading: false,
        topInfo: {},
        topInfoFirst: '',
        topInfoBold: '',

        infoList: [],
        tablePage: { total: 0, currentPage: 1, pageSize: 10 },

        stationList: [],

        assetUnitList: [], // 资产单位列表
        operationUnitList: [], // 运营单位列表
        maintenanceUnitList: [], // 运维单位列表

        tableColumn:[
            {
                type: 'checkbox',
                width: 50,
                fixed: 'left',
            },
            {
                type: 'seq',
                title: '序号',
                width: 60,
                minWidth: 60, // 最小宽度
            },
            {
                field: 'stationNo',
                title: '充电站编号',
                minWidth: 250, // 最小宽度
            },
            {
                field: 'stationName',
                title: '充电站名称',
                minWidth: 200, // 最小宽度
            },
            {
                field: 'stationType',
                title: '站点类型',
                minWidth: 100, // 最小宽度
                formatter: ({ cellValue }) => {
                  return this.selectDictLabel(
                    this.dict.type.ls_charging_station_type,
                    cellValue
                  );
                },
            },
            {
                field: 'operationMode',
                title: '运营模式',
                minWidth: 150, // 最小宽度
                formatter: ({ cellValue }) => {
                  return this.selectDictLabel(
                    this.dict.type.ls_charging_operation_mode,
                    cellValue
                  );
                },
            },
            {
                field: 'assetProperty',
                title: '资产属性',
                minWidth: 100, // 最小宽度
                formatter: ({ cellValue }) => {
                  return this.selectDictLabel(
                    this.dict.type.ls_charging_asset_property,
                    cellValue
                  );
                },
            },
            {
                field: 'stationAccessType',
                title: '接入方式',
                minWidth: 100, // 最小宽度
                formatter: ({ cellValue }) => {
                  return this.selectDictLabel(
                    this.dict.type.ls_charging_station_access_type,
                    cellValue
                  );
                },
            },
            // {
            //     field: 'pileNum',
            //     title: '充电桩数量',
            //     minWidth: 120, // 最小宽度
            // },
            {
                field: 'isAdjustable',
                title: '是否参与调控',
                minWidth: 120, // 最小宽度
                formatter: ({ cellValue }) => {
                  return this.selectDictLabel(
                    this.dict.type.ls_charging_status,
                    cellValue
                  );
                },
            },
            {
                field: 'adjustableType',
                title: '可控类型',
                minWidth: 100, // 最小宽度
                formatter: ({ cellValue }) => {
                  return this.selectDictLabel(
                    this.dict.type.ls_charging_adjustable_type,
                    cellValue
                  );
                },
            },
            {
                field: 'unifiedConstruction',
                title: '是否统建统服',
                minWidth: 120, // 最小宽度
                formatter: ({ cellValue }) => {
                  return this.selectDictLabel(
                    this.dict.type.ls_charging_status,
                    cellValue
                  );
                },
            },
            {
                field: 'construction',
                title: '建设场所',
                minWidth: 120, // 最小宽度
                formatter: ({ cellValue }) => {
                  return this.selectDictLabel(
                    this.dict.type.ls_charging_construction,
                    cellValue
                  );
                },
            },
            {
                field: 'areaType',
                title: '所属区域',
                minWidth: 120, // 最小宽度
                formatter: ({ cellValue }) => {
                  return this.selectDictLabel(
                    this.dict.type.ls_charging_area_type,
                    cellValue
                  );
                },
            },
            {
                field: 'provinceName',
                title: '省',
                minWidth: 100, // 最小宽度
            },
            {
                field: 'cityName',
                title: '市',
                minWidth: 100, // 最小宽度
            },
            {
                field: 'countyName',
                title: '区',
                minWidth: 100, // 最小宽度
            },
            {
                field: 'street',
                title: '乡镇',
                minWidth: 100, // 最小宽度
            },
            {
                field: 'cun',
                title: '村',
                minWidth: 100, // 最小宽度
            },
            {
                field: 'lon',
                title: '入口经度',
                minWidth: 120, // 最小宽度
            },
            {
                field: 'lat',
                title: '入口纬度',
                minWidth: 120, // 最小宽度
            },
            {
                field: 'stationAddress',
                title: '充电站地址',
                minWidth: 200, // 最小宽度
                // showOverflow: {
                //     tooltip: true,
                //     ellipsis: true,
                //     className: 'address-ellipsis'
                // }
                slots: {
                  default: ({ row }) => [
                      <el-tooltip 
                          content={row.stationAddress} 
                          placement="top" 
                          disabled={!row.stationAddress || row.stationAddress.length < 10}
                      >
                          <span class="ellipsis-text">{row.stationAddress}</span>
                      </el-tooltip>
                  ]
              }
            },
            {
                title: '对外开放',
                field: 'openFlag',
                minWidth: 100, // 最小宽度
                formatter: ({ cellValue }) => {
                  return this.selectDictLabel(
                    this.dict.type.ls_charging_status,
                    cellValue
                  );
                },
            },
            {
                title: '开放时间',
                field: 'runTime',
                minWidth: 180, // 最小宽度
            },
            {
                title: '投资主体',
                field: 'investors',
                minWidth: 220, // 最小宽度
                formatter: ({ cellValue }) => {
                  return this.selectDictLabel(
                    this.dict.type.ls_charging_contracted_unit,
                    cellValue
                  );
                },
            },
            {
                title: '服务电话',
                field: 'serviceTel',
                minWidth: 120, // 最小宽度
            },
            {
                title: '停车场位置',
                field: 'parkingLocation',
                minWidth: 150, // 最小宽度
                formatter: ({ cellValue }) => {
                  return this.selectDictLabel(
                    this.dict.type.ls_charging_parking_location,
                    cellValue
                  );
                }
            },
            {
                title: '停车场收费类型',
                field: 'parkingChargeType',
                minWidth: 150, // 最小宽度
                formatter: ({ cellValue }) => {
                  return this.selectDictLabel(
                    this.dict.type.ls_charging_parking_charge_type,
                    cellValue
                  );
                },
            },
            // {
            //     title: '停车费描述',
            //     field: 'parkingChargeRemark',
            //     minWidth: 150, // 最小宽度
            // },
            {
                title: '服务标签',
                field: 'serviceTagName',
                minWidth: 260, // 最小宽度
            },
            {
                title: '资产单位',
                field: 'assetUnitName',
                minWidth: 200, // 最小宽度
                slots: {
                  default: ({ row }) => [
                      <el-tooltip 
                          content={row.assetUnitName} 
                          placement="top" 
                          disabled={!row.assetUnitName || row.assetUnitName.length < 10}
                      >
                          <span class="ellipsis-text">{row.assetUnitName}</span>
                      </el-tooltip>
                  ]
              }
            },
            {
                title: '运营单位',
                field: 'operatingUnitName',
                minWidth: 220, // 最小宽度
                slots: {
                  default: ({ row }) => [
                      <el-tooltip 
                          content={row.operatingUnitName} 
                          placement="top" 
                          disabled={!row.operatingUnitName || row.operatingUnitName.length < 10}
                      >
                          <span class="ellipsis-text">{row.operatingUnitName}</span>
                      </el-tooltip>
                  ]
              }
            },
            {
                title: '运维单位',
                field: 'maintenanceUnitName',
                minWidth: 220, // 最小宽度
                slots: {
                  default: ({ row }) => [
                      <el-tooltip 
                          content={row.maintenanceUnitName} 
                          placement="top" 
                          disabled={!row.maintenanceUnitName || row.maintenanceUnitName.length < 10}
                      >
                          <span class="ellipsis-text">{row.maintenanceUnitName}</span>
                      </el-tooltip>
                  ]
              }
            },
            {
                title: '签约单位',
                field: 'contractedUnit',
                minWidth: 220, // 最小宽度
                formatter: ({ cellValue }) => {
                  return this.selectDictLabel(
                    this.dict.type.ls_charging_contracted_unit,
                    cellValue
                  );
                },
            },
            {
              field: 'structureName',
              title: '结构形式',
              minWidth: 120, // 最小宽度
              // formatter: ({ cellValue }) => {
              //         return this.selectDictLabel(
              //           this.dict.type.ls_charging_structural_type,
              //           cellValue
              //         );
              //       },
              
            },
            {
                title: '充电堆数量',
                field: 'stacksNum',
                minWidth: 120, // 最小宽度
            },
            {
                title: '直流桩数量',
                field: 'dcPileNum',
                minWidth: 120, // 最小宽度
            },
            {
                title: '交流桩数量',
                field: 'acPileNum',
                minWidth: 120, // 最小宽度
            },
            {
                title: '充电桩总数量', 
                field: 'pileNum',
                minWidth: 150, // 最小宽度
            },
            {
                title: '直流枪数量',
                field: 'dcGunNum',
                minWidth: 120, // 最小宽度
            },
            {
                title: '交流枪数量',
                field: 'acGunNum',
                minWidth: 120, // 最小宽度
            },
            {
                title: '充电枪总数量',
                field: 'pileGunNum',
                minWidth: 150, // 最小宽度
            },
            {
                title: '资产码数量',
                field: 'assetCodeNum',
                minWidth: 120, // 最小宽度
            },
            {
                title: '充电站总功率 (kWh)',
                field: 'stationPower',
                minWidth: 150, // 最小宽度
            },
            {
                title: '数据来源',
                field: 'stationSource',
                minWidth: 120, // 最小宽度
                formatter: ({ cellValue }) => {
                  return this.selectDictLabel(
                    this.dict.type.ls_charging_station_source,
                    cellValue
                  );
                },
            },
            {
                title: '投运日期',
                field: 'openDate',
                minWidth: 220, // 最小宽度
            },
            {
                title: '运营状态',
                field: 'operationStatus',
                minWidth: 100, // 最小宽度
                fixed: 'right',
                // formatter: ({ cellValue }) => {
                //   return this.selectDictLabel(
                //     this.dict.type.ls_charging_operation_status,
                //     cellValue
                //   );
                // },
                slots: {
                  // 自定义render函数
                  default: ({ row }) => {
                    return (
                      <StatusDot
                        value={row.operationStatus}
                        dictValue={this.dict.type.ls_charging_operation_status}
                        colors={['build', 'ty', 'stop','tempstop','forverstop','danger','tempstop']}
                      ></StatusDot>
                    );
                  },
                },
            },
            {
                title: '创建人',
                field: 'createUser',
                minWidth: 120, // 最小宽度
            },
            {
                title: '创建时间',
                field: 'createTime',
                minWidth: 220, // 最小宽度
            },
            {
                title: '修改人',
                field: 'updateUser',
                minWidth: 120, // 最小宽度
            },
            {
                title: '修改时间',
                field: 'updateTime',
                minWidth: 220, // 最小宽度
            },
            {
                title: '操作',
                slots: { default: 'operate' },
                // width: 300,
                width: 80,
                align: 'center',
                fixed: 'right',
            },
        ],
        tableData: [],
        pagerProps: {
          layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
        },
        params: {
            stationId: '',
            stationName: '',
            stationType: '',
            operationMode: '',
            assetAttribute: '',
            assetType: '',
            assetUnit: '',
            operationUnit: '',
            operationMaintainUnit: '',
            operationStatus: '',
            dataSources: '02',
            operationDate: [],
            isRegulation: '',
            controlType: '',
            isTjtf: '',
            constructionSite: '',
        },
      };
    },
    computed: {
      filterOptions() {
        return {
          config: [
            {
              field: 'stationId',
              title: '充电站编号',
              element: 'el-input',
            },
            {
              field: 'stationName',
              title: '充电站名称',
              element: 'el-input',
            },
            {
              field: 'stationType',
              title: '站点类型',
              element: 'el-select',
              props: {
                options: this.dict.type.ls_charging_station_type,
              },
            },
            {
              field: 'operationMode',
              title: '运营模式',
              element: 'el-select',
              props: {
                options: this.dict.type.ls_charging_operation_mode,
              },
            },
            {
              field: 'assetAttribute',
              title: '资产属性',
              element: 'el-select',
              props: {
                options: this.dict.type.ls_charging_asset_property,
              },
            },
            {
              field: 'assetType',
              title: '接入方式',
              element: 'el-select',
              props: {
                options: this.dict.type.ls_charging_station_access_type,
              },
            },
            {
              field: 'assetUnit',
              title: '资产单位',
              element: 'el-select',
              props: {
                options: this.assetUnitList,
              },
            },
            {
              field: 'operationUnit',
              title: '运营单位',
              element: 'el-select',
              props: {
                options: this.operationUnitList,
              },
            },
            {
              field: 'operationMaintainUnit',
              title: '运维单位',
              element: 'el-select',
              props: {
                options: this.maintenanceUnitList,
              },
            },
            {
              field: 'operationStatus',
              title: '运营状态',
              element: 'el-select',
              props: {
                options: this.dict.type.ls_charging_operation_status,
              },
            },
            {
              field: 'dataSources',
              title: '数据来源',
              element: 'el-select',
              props: {
                options: this.dict.type.ls_charging_station_source,
              },
            },
            {
              field: 'operationDate',
              title: '投运日期',
              element: 'el-date-picker',
              props: {
                type: 'daterange',
                options: [],
                valueFormat: 'yyyy-MM-dd',
              },
            },
            {
              field: 'isRegulation',
              title: '是否参与调控',
              element: 'el-select',
              props: {
                options: this.dict.type.ls_charging_status,
              },
            },
            {
              field: 'controlType',
              title: '可控类型',
              element: 'el-select',
              props: {
                options: this.dict.type.ls_charging_adjustable_type,
              },
            },
            {
              field: 'isTjtf',
              title: '是否统建统服',
              element: 'el-select',
              props: {
                options: this.dict.type.ls_charging_status,
              },
            },
            {
              field: 'constructionSite',
              title: '建设场所',
              element: 'el-select',
              props: {
                options: this.dict.type.ls_charging_construction,
              },
            },
          ],
          params: this.params,
        };
      },
      modalConfig() {
        return {
            addBtn: false,
            viewBtn: false,
            menu: false,
            editBtn: false,
            delBtn: false,
        }
      },
    },
    mounted() {
        this.getAssetUnit()
        this.getOperationUnit()
        this.getMaintenanceUnit()

    },
    methods: {
      // 获取资产单位
      async getAssetUnit() {
        const [err, res] = await getAssetUnit({})
        if (err) return
        this.assetUnitList = res.data
        
      },

      // 获取运营单位
      async getOperationUnit() {
        const [err, res] = await getOperationUnit({})
        if (err) return
        this.operationUnitList = res.data
      },

      // 获取运维单位
      async getMaintenanceUnit() {
        const [err, res] = await getMaintenanceUnit({})
        if (err) return
        this.maintenanceUnitList = res.data
      },

      nodeClick(node) {
        if (node) {
          
          const { list, areaCode, areaName, ...reset } = node;
          this.params.operatorCode = '';
          this.params.stationId = '';
          this.params.pileId = '';
          this.regionInfo = reset;
          
          this.topInfo = {
            ...reset,
            areaName,
          }

          console.log('node', node);
          this.loadData();


            this.handleTopInfo()
        }
      },
    handleTopInfo() {
        const { provinceCodeName, cityCodeName, districtCodeName, areaName} = this.topInfo;
        if (provinceCodeName === areaName) {
            // 选中了省
            this.topInfoFirst = '';
            this.topInfoBold = areaName;
        } else if (cityCodeName === areaName) {
            // 选中了市
            this.topInfoFirst = `${provinceCodeName} / `;
            this.topInfoBold = areaName;
        } else if (districtCodeName === areaName) {
            // 选中了区
            this.topInfoFirst = `${provinceCodeName} / ${cityCodeName} / `;
            this.topInfoBold = areaName;
        }
    },
    async loadData() {
        const {
          stationId,
          stationName,
          stationType,
          operationMode,
          assetAttribute,
          assetType,
          assetUnit,
          operationUnit,
          operationMaintainUnit,
          operationStatus,
          dataSources,
          operationDate,
          isRegulation,
          controlType,
          isTjtf,
          constructionSite,
        } = this.filterOptions.params

        const  {
          provinceCode,
          cityCode,
          districtCode,
        } = this.regionInfo


        let openDateStart = '';
        let openDateEnd = '';
        if (operationDate && operationDate.length > 0) {
            openDateStart = operationDate[0];  
            openDateEnd = operationDate[1];
        }

        this.loading = true;
        const [err, res] = await getStationList({
            stationNo: stationId,
            stationName,
            stationType,
            operationMode,
            assetProperty:assetAttribute,
            stationAccessType: assetType,
            assetUnit,
            operatingUnit:operationUnit,
            maintenanceUnit: operationMaintainUnit,
            operationStatus,
            stationSource: dataSources,
            openDateStart,
            openDateEnd,
            isAdjustable:isRegulation,
            adjustableType: controlType,
            unifiedConstruction: isTjtf,
            construction:constructionSite,
            province: provinceCode,
            city: cityCode,
            county: districtCode,
            pageNum: this.tablePage.currentPage,
            pageSize: this.tablePage.pageSize,
        });
        this.loading = false;
        if (err) return 
        const { data, total } = res;

        const list = []
        

        data.forEach(element => {
          const {
            runStartTime = '',
            runEndTime = '',
          } = element;

          let runTime = '-'
          if (runStartTime && runEndTime) {
            runTime = runStartTime  + '-' + runEndTime
          }
          list.push({
            ...element,
            runTime ,
          })
        });
        
        this.tableData = list;
        this.tablePage.total = total;


        const [err1, res1] = await getStationStatistic({
            stationNo: stationId,
            stationName,
            stationType,
            operationMode,
            assetProperty:assetAttribute,
            stationAccessType: assetType,
            assetUnit,
            operatingUnit:operationUnit,
            maintenanceUnit: operationMaintainUnit,
            operationStatus,
            stationSource: dataSources,
            openDateStart,
            openDateEnd,
            isAdjustable:isRegulation,
            adjustableType: controlType,
            unifiedConstruction: isTjtf,
            construction:constructionSite,
            province: provinceCode,
            city: cityCode,
            county: districtCode, 
        });

        if (err1) return
        const { data: data1 } = res1;

        this.infoList = [
            {
                name: '总充电站',
                value: data1.stationTotal,
                unit: '个',
                icon: icon1,
            },
            {
                name: '直连充电站',
                value: data1.directlyStationTotal,
                unit: '个',
                icon: icon2,
            },
            {
                name: '聚合充电站',
                value: data1.polyStationTotal,
                unit: '个',
                icon: icon3,
            },
            {
                name: '国网充电站',
                value: data1.stateGridStationTotal,
                unit: '个',
                icon: icon4,
            },
        ]

      },

      // 选择站点
      handleCheckboxChange({ records }) {
            // this.stationList = records.map((item) => item.stationId);
            this.stationList = records
        },

      // 批量删除
      async handleDeleteBatch() {
          // if(!this.stationList.length) {
          //     this.$message.warning('请先选择站点')
          //     return
          // } else {                
          //       const [err, res] = await batchDeleteStation({
          //           stationIdList: this.stationList
          //       })
          //       if(err) {
          //           this.$message.error(err.message)
          //       } 
          //       this.tableData = []
          //       this.tablePage = {
          //         total: 0,
          //         currentPage: 1,
          //       }
          //       this.loadData()
          // }
      },

      // 删除充电站
      async handleDelete(row) {
        let rows = [row];

        if (!row) {
          if (!this.stationList.length) {
            this.$message.warning('请先选择要删除的站点');
            return;
          }
          rows = this.stationList;
        }

        const delRows = rows.filter(({ operationStatus }) => {
          return (operationStatus === '05' ) // 退运
          return true;
        });

        if (delRows?.length !== rows.length) {
          this.$message.warning('仅支持删除已退运的站点，已选的站点不符合要求，请重新检查。');
          return;
        }

        const ids = delRows.map((x) => x.stationId);
        const stationName = delRows.map((x) => x.stationName).join('、');


        this.$confirm(`确认删除站点：${stationName}吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {

          const [err,res] =
          ids.length > 1
            ? await batchDeleteStation(ids.map((item) => ({ stationId: item })))
            : await deleteStation({
                stationId: ids[0],
              });

          if (err) return;
          this.$message({
            type: 'success',
            message: '删除成功!'
          });
          this.loadData();
        });

        console.log(ids, stationName);

        // const { stationId } = row;
        // const [err, res] = await deleteStation({
        //   stationId,
        // });
        // if (err) {
        //   this.$message.error(err.message);
        // } else {
        //   this.$message.success("删除成功");
        //   this.tableData = [];
        //   this.tablePage = {
        //       total: 0,
        //       currentPage: 1,
        //   }
        //   this.loadData()
        // }
    },      
      // 新增充电站
      handleCreate() {
          this.$router.push({
              path: '/v2g-charging/baseInfo/equipmentAndAssets/station/create',
          });

      },
      // 已删除充电站
      handleToDelete() {
          this.$router.push({
              path: '/v2g-charging/baseInfo/equipmentAndAssets/station/delete',
          });
      },
      

      // 充电站详情
      hanleDetail(row) {
        const {
          stationId
        } = row;
        this.$router.push({
          path: '/v2g-charging/baseInfo/equipmentAndAssets/station/detail',
          query: {
            stationId
          }
        })
      },

      // 充电站编辑
      hanleEdit(row) {
        const {
          stationId
        } = row;
        this.$router.push({
          path: '/v2g-charging/baseInfo/equipmentAndAssets/station/create',
          query: {
            stationId
          }
        })
      },



      // 调控配置
      async handleControl(row) {
        

        const {
          stationId
        } = row;

        this.loading = true;
        const [err, res] = await getControlDetail({
           stationId
        });
        this.loading = false;
        if (err) return 
        const { data } = res;

        const  {
          // stationId,
          isAdjustable,
          adjustableType,
          stationControl,
          stationControlConsDtoList
        } = data

        const list = []
        stationControlConsDtoList.forEach(item => {
          list.push({
            ...item,
            disabled: item.tgNo ? true : false,
            stationControlDtoList: item.stationControlDtoList? item.stationControlDtoList : []
          })
        })


        this.$refs.controlConfigurationModal.form = {
            isAdjustable,
            adjustableType,
            stationControl,
            stationControlConsDtoList:list,
        }

        this.$refs.controlConfigurationModal.stationId = stationId;

        this.$refs.controlConfigurationModal.dialogVisible = true;
      },

      // 导出
      async handleExport() {
        const {
          stationId,
          stationName,
          stationType,
          operationMode,
          assetAttribute,
          assetType,
          assetUnit,
          operationUnit,
          operationMaintainUnit,
          operationStatus,
          dataSources,
          operationDate,
          isRegulation,
          controlType,
          isTjtf,
          constructionSite,
        } = this.filterOptions.params

        const  {
          provinceCode,
          cityCode,
          districtCode,
        } = this.regionInfo


        let openDateStart = '';
        let openDateEnd = '';
        if (operationDate && operationDate.length > 0) {
            openDateStart = operationDate[0];  
            openDateEnd = operationDate[1];
        }

        const params = {
            stationNo: stationId,
            stationName,
            stationType,
            operationMode,
            assetProperty:assetAttribute,
            stationAccessType: assetType,
            assetUnit,
            operatingUnit:operationUnit,
            maintenanceUnit: operationMaintainUnit,
            operationStatus,
            stationSource: dataSources,
            openDateStart,
            openDateEnd,
            isAdjustable:isRegulation,
            adjustableType: controlType,
            unifiedConstruction: isTjtf,
            construction:constructionSite,
            province: provinceCode,
            city: cityCode,
            county: districtCode,
        }

        this.download(
          '/vehicle-charging-admin/station/detail/export',
          {
            ...params,
          },
          `充电站列表.xlsx`
        );
      }

    },
  };
  </script>
  
  <style lang="scss" scoped>
.table-wrap {
    ::v-deep .bd3001-table-select-box {
        display: none;
    }
    ::v-deep .bd3001-header  {
        display: block;
    }
    ::v-deep .bd3001-button {
        display: block !important;
    }
    
    .card-head {
        // position: relative; 
        height: 56px;
        padding: 0 16px;
        display: flex;
        align-items: center;
        margin-top: -20px;
        .card-head-text {
        flex:1;
        width: 520px;
            height: 26px;
            background-image: url('~@/assets/images/bg-title.png');
            background-size: 520px 26px;
            background-repeat: no-repeat;
            font-size: 20px;
            font-style: normal;
            font-weight: 500;
            line-height: 16px;
            padding-left: 36px;
            color: #21252e;
        }   

    }

    .card-head-after {
        width: 100%;
        height: 1px;
        background-color: #DCDEE2;
        margin-bottom: 16px;
    }
    .info-wrap {
        margin-top: 16px;
        display: flex;
        flex-wrap: wrap;
        gap: 16px;

        .info-item {
            background-color: #FAFBFC;
            flex: 1 1 0;
            // min-width: 180px;
            
            border-radius: 5px;
            padding: 8px 24px;
            box-sizing: border-box;
            // margin-right: 16px;
            display: flex;
            .info-icon {
                width: 42px;
                height: 42px;
            }
            .info-right-wrap {
                flex:1;
                margin-left: 8px;
                .info-title {
                    font-weight: 400;
                    font-size: 14px;
                    line-height: 14px;
                    margin-bottom: 8px;
                }
                .info-number {
                    font-size: 20px;
                    font-weight: 500;
                    .info-unit {
                        font-size: 14px;
                        font-weight: 400;
                    }
                }
            }
        }
        .info-item:last-child {
            margin-right: 0;
        }
    }

    .top-button-wrap {
        display:flex;
        margin: 16px 0;
        justify-content: flex-end ;
    }
}


  
.top-info-wrap {
    display: flex;
    height: 20px;
    align-items: center;
    margin-bottom: 16px;
    
    .top-info-icon {
        margin-left: 10px;
        margin-right: 4px;

        width: 20px;
        height: 20px;
        background-image: url('~@/assets/station/location-top.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }
    .top-info-first {
        font-weight: 400;
        font-size: 16px;
        color: #505363;
    }
    .top-info-bold {
        margin-left: 4px;
        font-weight: 400;
        font-size: 16px;
        color: #12151A;

    }
  }

  .ellipsis-text {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }

  .button-border {
    border: 0.01rem solid #217AFF;
    color: #217AFF;
    background-color: #fff;
}
.delete-btn {
  border: 0.01rem solid #FC1E31;
    color: #FC1E31;
    background-color: #fff;
}

::v-deep .vxe-table--render-default .vxe-body--column.col--center{
  text-align: left !important;
}
</style>
  