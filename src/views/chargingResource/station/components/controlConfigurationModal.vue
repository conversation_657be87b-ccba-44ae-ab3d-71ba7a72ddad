<template>
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      @close="handleCancel"
    >
        <el-form :model="form" :rules="rules" ref="ruleForm"  label-position="top">
            <el-row :gutter="20">
                <el-col :span="8">
                    <el-form-item
                        label="是否参与调控："
                        prop="isAdjustable"
                        :label-width="formLabelWidth"
                    >
                        <el-select
                            v-model="form.isAdjustable"
                            placeholder="请选择"
                            style="width: 100%"
                            @change="handleAdjustableChange"
                            >
                            <el-option
                                v-for="item in dict.type.ls_charging_status"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            ></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="8"  v-if="form.isAdjustable === '1'">
                    <el-form-item
                        label="调控类型："
                        prop="adjustableType"
                        :label-width="formLabelWidth"
                    >
                        <el-select
                        v-model="form.adjustableType"
                        placeholder="请选择调控类型"
                        style="width: 100%"
                        >
                        <el-option
                            v-for="item in dict.type.ls_charging_station_adjustable_type"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        ></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
                <el-col :span="8">
                    <el-form-item
                        label="调控方式："
                        prop="stationControl"
                        :label-width="formLabelWidth"
                    >
                        <el-select
                            v-model="form.stationControl"
                            placeholder="请选择调控方式"
                            style="width: 100%"
                        >
                        <el-option
                            v-for="item in  dict.type.ls_charging_station_control"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                        ></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>

            </el-row>
           
            <div v-for="(item,index) in form.stationControlConsDtoList" :key="index">
                <el-row class="consNo-wrap">
                    <div class="before-icon"></div>
                    户号: {{ item.consNo }}
                </el-row>

                <el-row :gutter="20">
                   
                    <el-col :span="8">
                        <el-form-item
                            label="最大上调(填谷)能力(kW)："
                            :prop="'stationControlConsDtoList.' + index + '.maxUpwardPower'"
                            :rules="{
                                required: true, message: '请输入最大上调(填谷)能力', trigger: 'blur'
                            }"
                            label-width="200px"
                        >
                            <el-input v-model="form.stationControlConsDtoList[index].maxUpwardPower" ></el-input>
                        </el-form-item>
                    </el-col>

                    <el-col :span="8">
                        <el-form-item
                            label="最大下调(削峰)能力(kW)："
                            :prop="'stationControlConsDtoList.' + index + '.maxDownwardPower' "
                            :rules="{
                                required: true, message: '请输入最大下调(削峰)能力', trigger: 'blur'
                            }"
                            label-width="200px"
                        >
                            <el-input v-model="form.stationControlConsDtoList[index].maxDownwardPower" ></el-input>
                        </el-form-item>
                    </el-col>

                    <el-col :span="8">
                        <el-form-item
                            label="调节系数(%)："
                            :prop="'stationControlConsDtoList.' + index + '.adjustFactor' "
                            :rules="{
                                required: true, message: '请输入调节系数', trigger: 'blur'
                            }"
                            :label-width="formLabelWidth"
                        >
                            <el-input v-model="form.stationControlConsDtoList[index].adjustFactor" ></el-input>
                        </el-form-item>
                    </el-col>
                 
                    <el-col :span="8">
                        <el-form-item
                            label="所属台区："
                            :prop="'stationControlConsDtoList.' + index + '.tgName'"
                            :label-width="formLabelWidth"
                        >
                            <el-input  placeholder="请输入所属台区"  v-model="form.stationControlConsDtoList[index].tgName" ></el-input>
                        </el-form-item>
                    </el-col>

                    <el-col :span="8">
                        <el-form-item
                            label="所属台区编号："
                            :prop="'stationControlConsDtoList.' + index + '.tgNo'"
                            :label-width="formLabelWidth"
                        >
                            <el-input  placeholder="请输入所属台区编号"   v-model="form.stationControlConsDtoList[index].tgNo" ></el-input>
                        </el-form-item>
                    </el-col>


                </el-row>

                <el-row :gutter="20" style="padding: 0 12px;">
                    <registerItem
                        ref="registerItem"
                        v-model="form.stationControlConsDtoList[index].stationControlDtoList"
                        :value="item.stationControlDtoList"

                    />
                </el-row>

            </div>

        </el-form>

        <div slot="footer" class="dialog-footer">
            <el-button @click="handleCancel">取 消</el-button>
            <el-button type="primary" @click="handleConfirm">确 定</el-button>
        </div>
    </el-dialog>
  </template>
  <script>  

import {
    updateControl
} from '@/api/station/index';

import registerItem from './registerItem.vue'

import moment from 'moment';

import _ from 'lodash';

  export default {
    props: {
        dialogTitle:{
            type: String,
            default: '调控配置'
        }
    },
    components: {
        registerItem
    },
    dicts: [
        'ls_charging_status', // 是否状态
        'ls_charging_station_adjustable_type', // 可调控类型
        'ls_charging_station_control', // 站点调节方式
    ],
    data() {
        return {
            dialogVisible: false,
            stationId: '',
            templateId: '',
            templateCode: '',
            form: {
                isAdjustable: '',
                adjustableType: '',
                stationControl: '',
                // belongTq: '',
                stationControlConsDtoList: [],

            },
            rules: {
                isAdjustable: [
                    { required: true, message: '请选择是否参与调控', trigger: 'blur' },
                ],
                adjustableType: [
                    { required: true, message: '请选择调控类型', trigger: 'blur' },
                ],
                stationControl: [
                    { required: true, message: '请选择调控方式', trigger: 'blur' },
                ],
            },
            formLabelWidth: '120px',

        };
    },
    computed: {},
    mounted() {},
    methods: {

        resetForm() {
            this.templateId = '';

            Object.keys(this.form).forEach((key) => {
                
                if(key === 'acChargePeriodsList') {
                    this.form[key] = [];
                } else {
                    this.form[key] = '';
                }
            });
        },

        handleCancel() {
            this.$refs.ruleForm.resetFields();
            this.resetForm();
            console.log(this.form, 'this.form')
            this.dialogVisible = false;
        },

        handleAdjustableChange() {
            if(this.form.isAdjustable === '1') {
                this.form.adjustableType = '';
                // 判断注册信息列表是否为空 空则造一条数据
                const {stationControlConsDtoList} = this.form;

                stationControlConsDtoList.forEach((item,index) => {
                    const {
                        stationControlDtoList = []
                    } = item;
                    console.log(index, item,stationControlDtoList, 'item')
                    if(!stationControlDtoList.length) {
                       
                        // 获取当前日期 yyyy-MM-dd  
                        const currentDate = new Date();
                        this.form.stationControlConsDtoList[index].stationControlDtoList = [
                            {
                                gridInformation: '03',
                                powerGridTime: currentDate,
                            }
                        ];
                    }
                })

            } else if(  this.form.isAdjustable === '0') { 
                this.form.adjustableType = '00';
            }
        },


        // 新增按钮防抖        
        handleConfirm: _.debounce(function() {
            this.$refs.ruleForm.validate(async (valid) => {
                if (valid) {
                    console.log(this.form, 'this.form')
                    const {
                        isAdjustable,
                        adjustableType,
                        stationControl,
                        stationControlConsDtoList,
                    } = this.form;
             
                         const params = {
                            stationId: this.stationId,
                            isAdjustable,
                            adjustableType: isAdjustable === '0' ?  '00': adjustableType,
                            stationControl,
                            stationControlConsDtoList,
                        }

                        console.log('params', params)
                        const [err, res] = await updateControl(params);
                        if (err) return;
                    
                        this.$message.success('编辑成功');
                        this.dialogVisible = false;
                        this.stationId  = '';
                        this.$emit('loadData');
                    
                }
            });
        }, 300) ,


    },
  };
  </script>
  <style lang="scss" scoped>
::v-deep .el-form-item__content{
    display: flex !important;
}

.consNo-wrap {
    font-size: 16px;
    font-weight: 500;
    // margin-bottom: 10px;
    color: #12151A;
    display: flex;
    height: 48px;
    align-items: center;
    .before-icon {
        width: 3px;
        height: 16px;
        background-image: url('~@/assets/station/consno-before.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        margin-right: 8px;
    }

}

  </style>
  