<template>
    <div class="module-section" v-if="showModule">
        <div class="title-wrap">
            <div class="before-icon"></div>
            <div>{{ title }}</div>
        </div>
      <!-- <div class="section-header">
        <span class="title">{{ title }}</span>
      </div> -->
     
        <div 
          v-for="field in fields" 
          :key="field.key"
          class="info-wrap"
        >
          <div class="before-icon">{{ field.label }}：</div>
          <div >{{ formattedValue(field) }}</div>
        </div>
    </div>
  </template>
  
  <script>
  export default {
    name: 'ModuleSection',
    props: {
      // 模块标题
      title: {
        type: String,
        required: true
      },
      // 当前数据对象
      data: {
        type: Object,
        default: () => ({})
      },
      // 对比数据对象（调整前）
      compareData: {
        type: Object,
        default: () => ({})
      },
      // 字段配置数组
      fields: {
        type: Array,
        required: true,
        validator: value => value.every(f => f.key && f.label)
      }
    },
    computed: {
      // 控制模块显示条件
      showModule() {
        return this.fields.some(f => this.data[f.key] !== undefined)
      }
    },
    methods: {
      // 值格式化处理
      formattedValue(field) {
        const value = this.data[field.key]
        if (typeof field.formatter === 'function') {
          return field.formatter(value) || '-'
        }
        return value !== undefined ? value : '-'
      },
    }
  }
  </script>
  
  <style scoped>
  .module-section {
    margin-bottom: 20px;
    /* background: #FAFAFA; */
    border-radius: 4px;
    overflow: hidden;
  }
  
  .title-wrap {
            height: 18px;
            padding: 0 0 0 24px;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            font-weight: 500;
            font-size: 18px;
            margin-bottom: 24px;
            color: #12151A;
            .before-icon {
                width: 3px;
                height: 16px;
                background-image: url('~@/assets/station/consno-before.png');
                background-size: 100% 100%;
                background-repeat: no-repeat;
                margin-right: 8px;
            }
        }

        .info-wrap {
            height: 16px;
            padding: 0 0 0 24px;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            margin-bottom: 24px;
            .info-title {
                font-weight: 400;
                font-size: 16px;
                line-height: 16px;
                color: #505363;
            }
            .info-detail {
                font-weight: 400;
                font-size: 16px;
                line-height: 16px;
                color: #292B33;
            }
        }
  

  </style>