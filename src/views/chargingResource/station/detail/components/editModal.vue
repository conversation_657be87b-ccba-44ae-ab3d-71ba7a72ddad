<template>
    <el-dialog title="详情" :visible.sync="dialogVisible" width="858px">
        <div class="edit-info-wrap">
            <div class="edit-info-item">
                <div class="edit-info-item-title-before">
                    <div class="edit-detail-title-icon"></div>
                    <div>调整前信息</div>         
                </div>

                <template v-for="module in activeModules">
                    <module-section 
                        :key="'before-'+module.key"
                        :title="module.title"
                        :data="beforeData[module.beforeKey]"
                        :fields="module.fields"
                    />
                </template>
                

                <div class="module-section" v-if="beforeData.stationBuildDtoBefore">
                    <div class="title-wrap">
                        <div class="before-icon"></div>
                        <div>场站建设信息</div>
                    </div>

                    <div v-if="beforeData.stationBuildDtoBefore.stationTransformerDtoList">
                        <div class="info-wrap" >
                        变压器
                        </div>

                        <BuseCrud
                            style="margin-left: 24px;"
                            ref="stationTransformer"
                            :tableColumn="stationTransformerTableColumn"
                            :tableData="beforeData.stationBuildDtoBefore.stationTransformerDtoList"
                            :modalConfig="{ addBtn: false, menu: false }"
                            >
                                
                        </BuseCrud>
                    </div>

                    <div v-if="beforeData.stationBuildDtoBefore.stationMonitorDtoList">
                        <div class="info-wrap" >
                            监控设备
                        </div>

                        <BuseCrud
                            style="margin-left: 24px;"
                            ref="stationMonitor"
                            :tableColumn="stationMonitorTableColumn"
                            :tableData="beforeData.stationBuildDtoBefore.stationMonitorDtoList"
                            :modalConfig="{ addBtn: false, menu: false }"
                            >
                                
                        </BuseCrud>
                    </div>

                    <div v-if="beforeData.stationBuildDtoBefore.stationFirefightDtoList">
                        <div class="info-wrap" >
                            消防设备
                        </div>

                        <BuseCrud
                            style="margin-left: 24px;"
                            ref="stationFirefight"
                            :tableColumn="stationFirefightTableColumn"
                            :tableData="beforeData.stationBuildDtoBefore.stationFirefightDtoList"
                            :modalConfig="{ addBtn: false, menu: false }"
                            >
                                
                        </BuseCrud>
                    </div>

                    <div v-if="beforeData.stationBuildDtoBefore.stationSubsidiaryDtoList">
                        <div class="info-wrap" >
                            场站附属设施
                        </div>

                        <BuseCrud
                            style="margin-left: 24px;"
                            ref="stationSubsidiary"
                            :tableColumn="stationSubsidiaryTableColumn"
                            :tableData="beforeData.stationBuildDtoBefore.stationSubsidiaryDtoList"
                            :modalConfig="{ addBtn: false, menu: false }"
                            >
                                
                        </BuseCrud>
                    </div>


                    <div v-if="beforeData.stationBuildDtoBefore.chargingStacksDtoList">
                        <div class="info-wrap" >
                            充电堆
                        </div>

                        <BuseCrud
                            style="margin-left: 24px;"
                            ref="chargingStacks"
                            :tableColumn="chargingStacksTableColumn"
                            :tableData="beforeData.stationBuildDtoBefore.chargingStacksDtoList"
                            :modalConfig="{ addBtn: false, menu: false }"
                            >
                                
                        </BuseCrud>
                    </div>


                    
                    
                
                </div>
              

            </div>
            <div class="edit-info-item">
                <div class="edit-info-item-title-after">
                    <div class="edit-detail-title-icon"></div>
                    <div>调整后信息</div>
                </div>
                <template v-for="module in activeModules">
                <module-section 
                    :key="'after-'+module.key"
                    :title="module.title"
                    :data="afterData[module.afterKey]"
                    :fields="module.fields"
                    :compare-data="beforeData[module.beforeKey]"
                />
                </template>

                <div class="module-section" v-if="afterData.stationBuildDtoAfter">
                    <div class="title-wrap">
                        <div class="before-icon"></div>
                        <div>场站建设信息</div>
                    </div>

                    <div v-if="afterData.stationBuildDtoAfter.stationTransformerDtoList">
                        <div class="info-wrap" >
                        变压器
                        </div>

                        <BuseCrud
                            style="margin-left: 24px;"
                            ref="stationTransformer1"
                            :tableColumn="stationTransformerTableColumn"
                            :tableData="afterData.stationBuildDtoAfter.stationTransformerDtoList"
                            :modalConfig="{ addBtn: false, menu: false }"
                            >
                                
                        </BuseCrud>
                    </div>

                    <div v-if="afterData.stationBuildDtoAfter.stationMonitorDtoList">
                        <div class="info-wrap" >
                            监控设备
                        </div>

                        <BuseCrud
                            style="margin-left: 24px;"
                            ref="stationMonitor1"
                            :tableColumn="stationMonitorTableColumn"
                            :tableData="afterData.stationBuildDtoAfter.stationMonitorDtoList"
                            :modalConfig="{ addBtn: false, menu: false }"
                            >
                                
                        </BuseCrud>
                    </div>

                    <div v-if="afterData.stationBuildDtoAfter.stationFirefightDtoList">
                        <div class="info-wrap" >
                            消防设备
                        </div>

                        <BuseCrud
                            style="margin-left: 24px;"
                            ref="stationFirefight1"
                            :tableColumn="stationFirefightTableColumn"
                            :tableData="afterData.stationBuildDtoAfter.stationFirefightDtoList"
                            :modalConfig="{ addBtn: false, menu: false }"
                            >
                                
                        </BuseCrud>
                    </div>

                    <div v-if="afterData.stationBuildDtoAfter.stationSubsidiaryDtoList">
                        <div class="info-wrap" >
                            场站附属设施
                        </div>

                        <BuseCrud
                            style="margin-left: 24px;"
                            ref="stationSubsidiary1"
                            :tableColumn="stationSubsidiaryTableColumn"
                            :tableData="afterData.stationBuildDtoAfter.stationSubsidiaryDtoList"
                            :modalConfig="{ addBtn: false, menu: false }"
                            >
                                
                        </BuseCrud>
                    </div>

                    <div v-if="afterData.stationBuildDtoAfter.chargingStacksDtoList">
                        <div class="info-wrap" >
                            充电堆
                        </div>

                        <BuseCrud
                            style="margin-left: 24px;"
                            ref="chargingStacks1"
                            :tableColumn="chargingStacksTableColumn"
                            :tableData="afterData.stationBuildDtoAfter.chargingStacksDtoList"
                            :modalConfig="{ addBtn: false, menu: false }"
                            >
                                
                        </BuseCrud>
                    </div>
                    
                
                </div>

            </div>

        </div>

  
    </el-dialog>
   
</template>
<script>  

import ModuleSection from './ModuleSection.vue'

  export default {
    props: {
        beforeData: {
            type: Object,
            default: () => {},
        },
        afterData: {
            type: Object,
            default: () => {},
        },
        assetUnitList: {
            type: Array,
            default: () => [],
        },
        operationUnitList: {
            type: Array,
            default: () => [],
        },
        maintenanceUnitList: {
            type: Array,
            default: () => [],
        },
        serviceTagList: {
            type: Array,
            default: () => [],
        },

    },
    components: {
        ModuleSection,

    },
    dicts: [
        'ls_charging_station_type',
        'ls_charging_operation_mode',
        'ls_charging_asset_property',
        'ls_charging_construction',
        'ls_charging_area_type',
        'ls_charging_adjustable_type',
        'ls_charging_status',
        'ls_charging_stationChargeType',
        'ls_charging_station_tag',
        'ls_charging_parking_charge_type',
        'ls_charging_parking_location',
        'ls_charging_contracted_unit',
        'ls_charging_billing_mode',
    ],
    data() {
        return {
            dialogVisible: false,
   
            moduleConfig:[
                {
                    key: 'foundation',
                    title: '基础信息',
                    beforeKey: 'stationFoundationDtoBefore',
                    afterKey: 'stationFoundationDtoAfter',
                    fields: [
                        { key: 'stationNo', label: '充电站编码' },
                        { key: 'stationName', label: '充电站名称' },
                        { key: 'stationType', label: '站点类型',
                            formatter: stationType=>this.selectDictLabel(
                                this.dict.type.ls_charging_station_type,
                                    stationType
                            )
                        },
                        {
                            key: 'operationMode', 'label': '运营模式',
                            formatter: operationMode=>this.selectDictLabel(
                                this.dict.type.ls_charging_operation_mode,
                                operationMode
                            )
                        },
                        {
                            key: 'assetProperty', 'label': '资产属性',
                            formatter: assetProperty=>this.selectDictLabel(
                                this.dict.type.ls_charging_asset_property,
                                assetProperty
                            )
                        },
                        {
                            key: 'construction', 'label': '建设场所',
                            formatter: construction=>this.selectDictLabel(
                                this.dict.type.ls_charging_construction,
                                construction
                            )
                        },
                        { key: 'buildDate', label: '建设完成日期',
                            // formatter: buildDate=>this.formatDate(buildDate)
                         },
                        {
                            key: 'areaType', 'label': '所属区域',
                            formatter: areaType=>this.selectDictLabel(
                                this.dict.type.ls_charging_area_type,
                                areaType
                            )
                        },
                        {
                            key: 'adjustableType', 'label': '可控类型',
                            formatter: adjustableType=>this.selectDictLabel(
                                this.dict.type.ls_charging_adjustable_type,
                                adjustableType
                            )
                        },
                        {
                            key: 'unifiedConstruction', 'label': '是否统建统服',
                            formatter: unifiedConstruction=>this.selectDictLabel(
                                this.dict.type.ls_charging_status,
                                unifiedConstruction
                            )
                        },
                        {   key: 'consNos', 'label': '营销户号',},
                        {
                            key: 'maintenanceGroup', 'label': '运维分组',
                            formatter: maintenanceGroup=>this.selectDictLabel(
                                this.operationUnitList,
                                maintenanceGroup
                            )
                        },
                        {   key: 'stationAddress', 'label': '站点地址',},
                        {   key: 'stationIntroduce', 'label': '站点简介',},
                        {   key: 'siteGuide', 'label': '找桩指引',},


                    ]
                },
                {
                    key: 'stationOperationDetail',
                    title: '场站运营信息',
                    beforeKey: 'stationOperationDetailDtoBefore',
                    afterKey: 'stationOperationDetailDtoAfter',
                    fields: [
                        {   
                            key: 'stationChargeType', label: '充电站充电分类',
                            formatter: stationChargeType=>this.selectDictLabel(
                                this.dict.type.ls_charging_stationChargeType,
                                stationChargeType
                            )
                        },
                        {
                            key: 'openFlag', label: '是否开放',
                            formatter: openFlag=>this.selectDictLabel(
                                this.dict.type.ls_charging_status,
                                openFlag
                            )
                        },
                        {
                            key: 'stationRunTime', label: '开放时间',
                        },
                        {
                            key: 'stationTag', label: '充电站标签',
                            formatter: stationTag=>this.selectDictLabel(
                                this.dict.type.ls_charging_station_tag,
                                stationTag
                            )
                        },
                        {
                            key: 'parkingChargeType', label: '停车场收费类型',
                            formatter: parkingChargeType=>this.selectDictLabel(
                                this.dict.type.ls_charging_parking_charge_type,
                                parkingChargeType
                            )
                        },
                        {
                            key: 'parkingLocation', label: '停车场位置',
                            formatter: parkingLocation=>this.selectDictLabel(
                                this.dict.type.ls_charging_parking_location,
                                parkingLocation
                            )
                        },
                        {
                            key: 'serviceTag', label: '服务标签',
                            formatter: serviceTag=>this.handleServiceTag(
                                this.serviceTagList,
                                JSON.parse(serviceTag) 
                            )
                        },
                        {
                            key: 'serviceTel', label: '服务电话',
                        }

                    ]
                },
                {
                    key: 'operationDetail',
                    title: '商户运营信息',
                    beforeKey: 'operationDetailDtoBefore',
                    afterKey: 'operationDetailDtoAfter',
                    fields: [
                        {
                            key: 'operatingUnit', label: '运营单位',
                            formatter: operatingUnit=>this.selectDictLabel(
                                this.operationUnitList,
                                operatingUnit
                            )
                        },
                        {
                            key: 'assetUnit', label: '资产单位',
                            formatter: assetUnit=>this.selectDictLabel(
                                this.assetUnitList,
                                assetUnit
                            )
                        },
                        {
                            key: 'maintenanceUnit', label: '运维单位',
                            formatter: maintenanceUnit=>this.selectDictLabel(
                                this.maintenanceUnitList,
                                maintenanceUnit
                            )
                        },
                        {
                            key: 'maintenance', label: '运维单位联系人',
                        },
                        {
                            key: 'contractedUnit', label: '签约单位',
                            formatter: contractedUnit=>this.selectDictLabel(
                                this.dict.type.ls_charging_contracted_unit,
                                contractedUnit
                            )
                        },
                        {
                            key: 'contractedUnit', label: '投资主体',
                            formatter: investors=>this.selectDictLabel(
                                this.dict.type.ls_charging_contracted_unit,
                                investors
                            )
                        },
                        {
                            key: 'supplyMechanism', label: '供电机构',
                            formatter: supplyMechanism=>this.selectDictLabel(
                                this.dict.type.ls_charging_contracted_unit,
                                supplyMechanism
                            )
                        },
                        {
                            key: 'isElectricityFeePaid', label: '是否承担供电电费',
                            formatter: isElectricityFeePaid=>this.selectDictLabel(
                                this.dict.type.ls_charging_status,
                                isElectricityFeePaid
                            )
                        },
                    ]
                },
                {
                    key: 'stationPeripheral',
                    title: '场站外围信息',
                    beforeKey: 'stationPeripheralDtoBefore',
                    afterKey: 'stationPeripheralDtoAfter',
                    fields: [
                        {
                            key: 'designUnit', label: '设计单位',
                        },
                        {
                            key: 'designUnitContact', label: '设计单位联系人',
                        },
                        {
                            key: 'equipmentManufacturerContact', label: '设备厂家联系人',
                        },
                        {
                            key: 'stationContact', label: '场站联系人',
                        },
                        {
                            key: 'warrantyPeriodTime', label: '站点质保期',
                        },
                        {
                            key: 'keyManager', label: '钥匙管理人',
                        },
                        {
                            key: 'revenueForm', label: '收资表',
                        },
                        {
                            key: 'remarks', label: '备注',
                        },
                    ]
                },
                {
                    key: 'stationMode',
                    title: '场站扣费模式',
                    beforeKey: 'stationModeDtoBefore',
                    afterKey: 'stationModeDtoAfter',
                    fields: [
                        {
                            key: 'billingModeList', label: '扣费模式',
                            formatter: billingModeList=>this.handleServiceTag(
                                this.dict.type.ls_charging_billing_mode,
                                billingModeList
                            )
                        },
                        {
                            key: 'minStartAmt', label: '启动金额阈值',
                        },
                        {
                            key: 'minStopAmt', label: '结束金额阈值',
                        },
                    ]
                },
            ],

            stationTransformerTableColumn: [
                {
                    field: 'transformerScale',
                    title: '变压器规模(KVA)',
                    minWidth: 180,
                },
                {
                    field: 'transformerModel',
                    title: '变压器型号',
                    minWidth: 150,
                },
                {
                    field: 'manufacturer',
                    title: '变压器厂家',
                    minWidth: 150,
                },
                {
                    title: '变压器数量',
                    field: 'transformerQuantity',
                    minWidth: 150,
                },
                {
                    title: '变压器属性',
                    field: 'transformerAttribute',
                    minWidth: 150,
                },
                {
                    title: '出厂日期',
                    field: 'factoryDate',
                    minWidth: 150,
                },
                {
                    title: '维护人',
                    field: 'maintenancePerson',
                    width: 220,
                },
            ],

            stationMonitorTableColumn: [
                {
                    field: 'deviceName',
                    title: '设备名称',
                    minWidth: 180,
                },
                {
                    field: 'deviceLocation',
                    title: '设备位置',
                    minWidth: 150,
                },
                {
                    field: 'deviceModel',
                    title: '设备型号',
                    minWidth: 150,
                },
                {
                    field: 'deviceType',
                    title: '设备类型',
                    minWidth: 150,
                },
                {
                    field: 'iotCardNumber',
                    title: '对应物联卡卡号',
                    minWidth: 150,
                },
                {
                    field: 'storageCapacity',
                    title: '存储容量',
                    minWidth: 150,
                },
                {
                    field: 'deviceStatus',
                    title: '设备状态',
                    minWidth: 150,
                },
                {
                    field: 'manufacturer',
                    title: '生产厂家',
                    minWidth: 150,
                },
                {
                    field: 'productionDate',
                    title: '生产日期',
                    minWidth: 150,
                },
                {
                    field: 'isBroadbandAccess',
                    title: '是否宽带接入',
                    minWidth: 150,
                },
                {
                    field: 'accessMethod',
                    title: '接入方式',
                    minWidth: 150,
                },
                {
                    field: 'accessPlatform',
                    title: '接入平台',
                    minWidth: 150,
                },
                {
                    field: 'maintenancePerson',
                    title: '维护人',
                    minWidth: 150,
                },
            ],

            stationFirefightTableColumn: [
                {
                    field: 'equipmentName',
                    title: '消防器材名称',
                    minWidth: 150,
                },
                {
                    field: 'equipmentModel',
                    title: '消防器材型号',
                    minWidth: 150,
                },
                {
                    field: 'equipmentCapacity',
                    title: '消防器材容量',
                    minWidth: 150,
                },
                {
                    field: 'equipmentQuantity',
                    title: '消防器材数量',
                    minWidth: 150,
                },
                {
                    field: 'manufacturer',
                    title: '生产厂家',
                    minWidth: 150,
                },
                {
                    field: 'productionDate',
                    title: '生产日期',
                    minWidth: 150,
                },
                {
                    field: 'lastInspectionDate',
                    title: '最近检验时间',
                    minWidth: 150,
                },
                {
                    field: 'lifespanYears',
                    title: '寿命周期(年)',
                    minWidth: 150,
                },
                {
                    field: 'maintenancePerson',
                    title: '维护人',
                    minWidth: 150,
                },
            ],

            stationSubsidiaryTableColumn: [
                {
                    field: 'deviceName',
                    title: '设备名称',
                    minWidth: 150,
                },
                {
                    field: 'deviceQuantity',
                    title: '设备数量',
                    minWidth: 150,
                },
                {
                    field: 'deviceUnit',
                    title: '设备单位',
                    minWidth: 150,
                },
                {
                    field: 'deviceModel',
                    title: '设备型号',
                    minWidth: 150,
                },
                {
                    field: 'deviceLocation',
                    title: '设备位置',
                    minWidth: 150,
                },
                {
                    field: 'deviceType',
                    title: '设备类型',
                    minWidth: 150,
                },
                {
                    field: 'iotCardNumber',
                    title: '设备物联卡号',
                    minWidth: 150,
                },
                {
                    field: 'manufacturer',
                    title: '生产厂家',
                    minWidth: 150,
                },
                {
                    field: 'productionDate',
                    title: '生产日期',
                    minWidth: 150,
                },
                {
                    field: 'commissioningDate',
                    title: '投运日期',
                    minWidth: 150,
                },
                {
                    field: 'isNetworkAccessed',
                    title: '是否接入网络',
                    minWidth: 150,
                },
                {
                    field: 'networkAccessMethod',
                    title: '接入网络方式',
                    minWidth: 150,
                },
                {
                    field: 'remarks',
                    title: '备注',
                    minWidth: 150,
                },
                {
                    field: 'maintenancePerson',
                    title: '维护人',
                    minWidth: 150,
                },
            ],

            chargingStacksTableColumn: [
                {
                    field: 'chargerPileId',
                    title: '充电堆编号',
                    minWidth: 150,
                },
                {
                    field: 'chargerPileModel',
                    title: '充电堆型号',
                    minWidth: 150,
                },
                {
                    field: 'chargerPilePower',
                    title: '充电堆功率(kw)',
                    minWidth: 150,
                },
                {
                    field: 'pileGunRatio',
                    title: '充电堆堆桩枪比',
                    minWidth: 150,
                },
                {
                    field: 'manufacturer',
                    title: '生产厂家',
                    minWidth: 150,
                },
                {
                    field: 'productionDate',
                    title: '生产日期',
                    minWidth: 150,
                },
                {
                    field: 'maintenancePerson',
                    title: '维护人',
                    minWidth: 150,
                },
            ],
        };
    },
    watch: {
        
    },
    computed: {
        activeModules() {
            return this.moduleConfig.map(module => {
                const beforeModuleData = this.beforeData[module.beforeKey] || {};
                const afterModuleData = this.afterData[module.afterKey] || {};

                // 动态过滤字段
                const validFields = module.fields.filter(field => {
                    const hasBefore = Object.keys(beforeModuleData).includes(field.key);
                    const hasAfter = Object.keys(afterModuleData).includes(field.key);
                    return hasBefore || hasAfter;
                });

                return {
                    ...module,
                    fields: validFields
                };
            }).filter(module => module.fields.length > 0); // 过滤空模块
        }
    },
    mounted() {
        
    },
    methods: {
        checkDataExists(data, fields) {
            return data && Object.keys(data).length > 0;
        },
        formatDate(dateStr) {
            const date = new Date(dateStr);
            if (isNaN(date.getTime())) {
                return 'Invalid Date';
            }
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');

            return `${year}-${month}-${day}`;
        },

        handleServiceTag(serviceTagList,serviceTag) {
            const matchedLabels = serviceTag.map(tag => {
                const found = serviceTagList.find(item => item.value === tag);
                return found ? found.label : null;
            });

            return matchedLabels.toString();
        }
    },
  };
  </script>

<style lang="scss" scoped>
.edit-info-wrap {
    display: flex;
    min-height: 520px;
    justify-content: space-between;
    .edit-info-item {
        width: 373px;
        // padding: 0 0 2px 24px;
        // box-sizing: border-box;
        .edit-info-item-title-before {
            display: flex;
            height: 88px;
            width: 100%;
            background: linear-gradient(180deg, #F2F5F7 0%, #FFFFFF 100%);
            padding: 0 0 0 24px;
            box-sizing: border-box;
            align-items: center;
            font-weight: 500;
            font-size: 24px;

            .edit-detail-title-icon {
                width: 32px;
                height: 32px;
                background-image: url('~@/assets/station/edit-detail-title-icon.png');
                background-size: 100% 100%;
                background-repeat: no-repeat;
                margin-right: 12px;
            }
        }
        .edit-info-item-title-after {
            display: flex;
            height: 88px;
            width: 100%;
            background: linear-gradient(180deg, #D9ECFF 0%, #FFFFFF 100%);
            padding: 0 0 0 24px;
            box-sizing: border-box;
            align-items: center;
            font-weight: 500;
            font-size: 24px;
            .edit-detail-title-icon {
                width: 32px;
                height: 32px;
                background-image: url('~@/assets/station/edit-detail-title-icon.png');
                background-size: 100% 100%;
                background-repeat: no-repeat;
                margin-right: 12px;
            }
        }
        .title-wrap {
            height: 18px;
            padding: 0 0 0 24px;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            font-weight: 500;
            font-size: 18px;
            margin-bottom: 24px;
            color: #12151A;
            .before-icon {
                width: 3px;
                height: 16px;
                background-image: url('~@/assets/station/consno-before.png');
                background-size: 100% 100%;
                background-repeat: no-repeat;
                margin-right: 8px;
            }
        }
        .info-wrap {
            height: 16px;
            padding: 0 0 0 24px;
            box-sizing: border-box;
            display: flex;
            align-items: center;
            margin-top: 24px;
            margin-bottom: 12px;
            .info-title {
                font-weight: 400;
                font-size: 16px;
                line-height: 16px;
                color: #505363;
            }
            .info-detail {
                font-weight: 400;
                font-size: 16px;
                line-height: 16px;
                color: #292B33;
            }
        }
        

    }
}

  </style>
  