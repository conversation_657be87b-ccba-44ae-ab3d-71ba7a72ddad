<template>
    <div class="container container-float " style="padding: 0;">
        <div class="device-head">
            <CommonHeader :title="stationName" :status="operationStatus">
              <template slot="content">
                <el-row>
                  <el-col :span="8">
                    <span class="label">充电站编号：</span>
                    <span class="value">{{stationNo}}</span>
                  </el-col>
                  <el-col :span="8">
                    <span class="label">站点类型：</span>
                    <span class="value">{{stationType}}</span>
                  </el-col>
                  <el-col :span="8">
                    <span class="label">运营模式：</span>
                    <span class="value">{{stationMode}}</span>
                  </el-col>
                </el-row>
              </template>
            </CommonHeader>
        </div>

        <div class="info-card" >
            <div class="card-head" style="margin-bottom: 8px;">
                <div class="before-icon"></div>
                <div class="card-head-text">基础信息</div>
            </div>

            <div class="form-wrap">
                <el-row :gutter="20">
                    <el-col :span="8" v-for="(item, key) in baseInfo" :key="key" style="margin-bottom: 24px;">
                    <div style="display: flex;">
                        <div class="info-title">{{labels.baseInfo[key]}}：</div>
                        <div class="info-detail">{{ item }}</div>
                    </div>
                    </el-col>
                </el-row>

                <el-row :gutter="20" style="margin-bottom: 24px;">
                    <el-col :span="8">
                        <div class="info-title" style="line-height: 24px;">站点图片：</div>
                        <img v-if="stationUrl.length" v-for="(item, key) in stationUrl" :key="key" style="margin-top: 8px; margin-right: 8px;" class="info-img" :src="item" />
                        <!-- <img
                            v-if="stationUrl"
                            style="margin-top: 8px;"
                            class="info-img"
                            :src="stationUrl"
                        /> -->
                    </el-col>
                    <el-col :span="8">
                        <div class="info-title">站点简介：</div>
                        <div  style="margin-top: 12px;" class="info-detail">{{ stationIntroduce }}</div>
                    </el-col>
                    <el-col :span="8">
                        <div class="info-title">找桩指引：</div>
                        <div  style="margin-top: 12px;" class="info-detail">{{ siteGuide }}</div>
                    </el-col>
                </el-row>
            </div>
        </div>

        <div class="info-card" >
            <div class="card-head" style="margin-bottom: 8px;">
                <div class="before-icon"></div>
                <div class="card-head-text">场站运营信息</div>
            </div>

            <div class="form-wrap">
                <el-row :gutter="20">
                    <el-col :span="8" v-for="(item, key) in stationOperation" :key="key" style="margin-bottom: 24px;">
                    <div style="display: flex;">
                        <div class="info-title">{{labels.operation[key]}}：</div>
                        <div class="info-detail">{{ item }}</div>
                    </div>
                    </el-col>
                </el-row>
            </div>
        </div>

        <div class="info-card" >
            <div class="card-head" style="margin-bottom: 8px;">
                <div class="before-icon"></div>
                <div class="card-head-text">商户运营信息</div>
            </div>

            <div class="form-wrap">
                <el-row :gutter="20">
                    <el-col :span="8" v-for="(item, key) in merchantOperation" :key="key" style="margin-bottom: 24px;">
                    <div style="display: flex;">
                        <div class="info-title">{{labels.merchant[key]}}：</div>
                        <div class="info-detail">{{ item }}</div>
                    </div>
                    </el-col>
                </el-row>
            </div>
        </div>

        <div class="info-card" >
            <div class="card-head" style="margin-bottom: 8px;">
                <div class="before-icon"></div>
                <div class="card-head-text">场站外围信息</div>
            </div>

            <div class="form-wrap">
                <el-row :gutter="20">
                    <el-col :span="8" v-for="(item, key) in peripheralInfo" :key="key" style="margin-bottom: 24px;">
                    <div style="display: flex;">
                        <div class="info-title">{{labels.peripheral[key]}}：</div>
                        <div class="info-detail">{{ item }}</div>
                    </div>
                    </el-col>
                </el-row>

                <el-row :gutter="20" style="margin-bottom: 24px;">
                    <el-col :span="24">
                    <div style="display: flex;">
                        <div class="info-title">收资表：</div>
                        <div class="info-detail">{{ doc.revenueForm }}</div>
                        <el-button @click="downloadFile(doc.url,doc.revenueForm)" style="margin-top: -4px; margin-left: 12px;" type="primary" v-if="doc.url">下载</el-button>
                    </div>
                    </el-col>
                </el-row>
            </div>

        </div>

        <div class="info-card" >
            <div class="form-edit-wrap">
                <StationConstructionTable 
                   :isEdit="false"
                   :data="stationConstructionData"
                    />
            </div>
        </div>  

        <div class="info-card" >
            <div class="card-head" style="margin-bottom: 8px;">
                <div class="before-icon"></div>
                <div class="card-head-text">场站扣费模式</div>
            </div>

            <div class="form-wrap">
                <el-row :gutter="20" style="margin-bottom: 24px;">
                    <el-col :span="8">
                        <div style="display: flex;">
                            <div class="info-title">扣费模式：</div>
                            <div class="info-detail">{{ stationDeductionMode.billingMode }}</div>
                        </div>
                    </el-col>

                    <el-col :span="8">
                        <div style="display: flex;">
                            <div class="info-title">启动金额阈值：</div>
                            <div class="info-amount">{{ stationDeductionMode.minStartAmt }}</div>
                            <div class="info-title">元</div>

                        </div>
                    </el-col>

                    <el-col :span="8">
                        <div style="display: flex;">
                            <div class="info-title">启动金额阈值：</div>
                            <div class="info-amount">{{ stationDeductionMode.minStopAmt }}</div>
                            <div class="info-title">元</div>

                        </div>
                    </el-col>
                </el-row>
            </div>
        </div>

        <div class="info-card" >
            <div class="card-head" style="margin-bottom: 8px;">
                <div class="before-icon"></div>
                <div class="card-head-text">充电桩信息</div>
            </div>

            <div class="form-wrap">
                <el-row :gutter="20" style="margin-bottom: 24px;">
                    <el-col :span="8">
                        <div style="display: flex;">
                            <div class="info-title">充电站总功率：</div>
                            <div class="info-amount">{{ stationInfo.totalPower }}</div>
                            <div class="info-title">kwh</div>
                        </div>
                    </el-col>
                </el-row>

                <BuseCrud
                    ref="stationInfo"
                    :tableColumn="stationInfoTableColumn"
                    :tableData="stationInfo.tableData"
                    :tableProps="stationInfoTableProps"
                    :modalConfig="{ addBtn: false, menu: false }"
                ></BuseCrud>
            </div>  

        </div>

        <div class="info-card" >
            <div class="card-head" style="margin-bottom: 8px;">
                <div class="before-icon"></div>
                <div class="card-head-text">变更信息</div>
            </div>

            <div class="form-wrap">
                <BuseCrud
                    ref="changeInfo"
                    :tableColumn="changeInfoTableColumn"
                    :tableData="changeInfoData"
                    :modalConfig="{ addBtn: false, menu: false }"
                >
                    <template slot="operate" slot-scope="{ row }">
                        <div class="operate-btn">
                            <el-button type="primary" plain @click="() => handleDetail(row)">
                                详情
                            </el-button>
                        </div>
                    </template>
                </BuseCrud>
            </div>
        </div>


        <editModal
             ref="editModal"
             :beforeData="beforeData"
             :afterData="afterData"
             :assetUnitList="assetUnitList"
             :operationUnitList="operationUnitList"
             :maintenanceUnitList="maintenanceUnitList"
             :serviceTagList="serviceTagList"
        />
    </div>
    
  </template>
  
  <script>
  import CommonHeader from '@/components/Business/CommonHeader';
import StationConstructionTable from '../create/components/stationConstructionTable.vue';
import editModal from './components/editModal.vue';

import {
  getAssetUnit,
  getOperationUnit,
  getMaintenanceUnit,
  getServiceTag,
  getStationDetail,
} from '@/api/station/index';
import { acos } from 'mathjs';
  
  export default {
    components: {
        CommonHeader,
        StationConstructionTable,
        editModal
    },
    dicts: [
        'ls_charging_operation_mode',
        'ls_charging_station_type',
        'ls_charging_operation_status',
        'ls_charging_asset_property',
        'ls_charging_construction',
        'ls_charging_area_type',
        'ls_charging_adjustable_type',
        'ls_charging_status',
        'ls_charging_station_source',
        'ls_charging_stationChargeType',
        'ls_charging_station_tag',
        'ls_charging_parking_charge_type',
        'ls_charging_parking_location',
        'ls_charging_contracted_unit',
        'ls_charging_billing_mode',
        'ls_charging_station_operation_type',// 站点详情操作类型
        
        

    ],

    data() {
      return {
            stationName:'',
            stationId:'',
            stationNo: '',
            stationType:'',
            stationMode:'',

            
            stationUrl: [],
            stationIntroduce: '',
            siteGuide: '',

            baseInfo: {
                assetProperty:  '',
                construction: '',
                buildDate: '',
                areaType: '',
                adjustableType: '',
                unifiedConstruction: '',
                consNos: '',
                maintenanceGroup: '',
                accessMethod: '直连',
                stationSource: '',
                lon: '',
                lat: '',
                stationAddress: '',
            },

            stationOperation: {
                stationChargeType: "",
                openFlag: "",
                openTime: "",
                stationTag: "",
                parkingChargeType: "",
                discountTemplate: "充电减免",
                parkingLocation: "",
                serviceTag: "",
                serviceTel: ""
            },
            merchantOperation: {
                operatingUnit: "",
                assetUnit: "",
                maintenanceUnit: "",
                contractedUnit: "",
                investors: "",
                supplyMechanism: "",
                isElectricityFeePaid: ""
            },

            peripheralInfo: {
                designUnit: "",
                designUnitContact: "",
                equipmentManufacturerContact: "",
                stationContact: "",
                warrantyPeriod: "",
                keyManager: "",
                remarks: "",
            },
            doc: { revenueForm: "", url: "" },

            labels: {
                baseInfo: {
                    assetProperty: '资产属性',
                    construction: '建设场所',
                    buildDate: '建设完成日期',
                    areaType: '所属区域',
                    adjustableType: '可控类型',
                    unifiedConstruction: '是否统建统服',
                    consNos: '营销户号',
                    maintenanceGroup:'运维分组',
                    accessMethod: '接入方式',
                    stationSource: '站点来源',
                    lon:'入口经度',
                    lat:'入口纬度',
                    stationAddress:'站点地址',
                },
                operation: {
                    stationChargeType: "充电站充电分类",
                    openFlag: "是否开放",
                    openTime: "开放时间",
                    stationTag: "充电站标签",
                    parkingChargeType: "停车场收费类型",
                    discountTemplate: "充电停车优惠模版",
                    parkingLocation: "停车场位置",
                    serviceTag: "服务标签",
                    serviceTel: "服务电话"
                },
                merchant: {
                    operatingUnit: "运营单位",
                    assetUnit: "资产单位",
                    maintenanceUnit: "运维单位",
                    contractedUnit: "签约单位",
                    investors: "投资主体",
                    supplyMechanism: "供电机构",
                    isElectricityFeePaid: "是否承担供电电费"
                },
                peripheral: {
                    designUnit: "设计单位",
                    designUnitContact: "设计单位联系人",
                    equipmentManufacturerContact: "设备厂家联系人",
                    stationContact: "场站联系人",
                    warrantyPeriod: "站点质保期",
                    keyManager: "钥匙管理人",
                    remarks: "备注",
                }
            },

            stationConstructionData: {
                stationTransformerDtoList: [],
                stationMonitorDtoList:[],
                stationFirefightDtoList: [],
                stationSubsidiaryDtoList: [],
                chargingStacksDtoList: [],
            },

            stationDeductionMode: {
                billingMode: '',
                minStartAmt: '',
                minStopAmt: '',
            },

            stationInfo: {
                totalPower: '',
                tableData: []
            },

            stationInfoTableColumn: [
                {
                    field: 'name',
                    title: '结构形式',
                    minWidth: 150,
                },
                {
                    field: 'dcPile',
                    title: '直流桩数量',
                    minWidth: 150,
                },
                {
                    field: 'acPile',
                    title: '交流桩数量',
                    minWidth: 150,
                },
                {
                    field: 'pile',
                    title: '交直流桩数量',
                    minWidth: 150,
                },
                {
                    field: 'dcGun',
                    title: '直流枪数量',
                    minWidth: 150,
                },
                {
                    field: 'acGun',
                    title: '交流枪数量',
                    minWidth: 150,
                },
                {
                    field: 'gun',
                    title: '交直流枪数量',
                    minWidth: 150,
                },

            ],

            stationInfoTableProps: {
                showFooter: false,
                    // footerMethod: ({ columns, data }) => {
                    // return [
                    //     columns.map((column, index) => {
                    //     if (index === 0) {
                    //         return '合计';
                    //     } else if (column.field == 'realIncome') {
                    //         return this.calculateTotalNum(data, 'realIncome');
                    //     }
                    //     return null;
                    //     }),
                    // ];
                // },
            },

            changeInfoTableColumn: [
                {
                    field: 'createTime',
                    title: '时间',
                    minWidth: 180,
                },
                {
                    field: 'operationType',
                    title: '操作类型',
                    minWidth: 150,
                    formatter: ({ cellValue }) => {
                        return this.selectDictLabel(
                            this.dict.type.ls_charging_station_operation_type,
                            cellValue
                        );
                    },
                    
                },
                {
                    title: '具体事项',
                    field: 'operationItems',
                    minWidth: 150,
                },
                {
                    title: '操作人',
                    field: 'operator',
                    minWidth: 150,
                },
                {
                    title: '操作部门',
                    field: 'operationDepartment',
                    minWidth: 150,
                },
                {
                    title: '操作',
                    slots: { default: 'operate' },
                    width: 100,
                    fixed: 'right',
                },
            ],
            
            changeInfoData: [],

            // editInfo: {},
            beforeData: {},
            afterData: {},


            assetUnitList: [], // 资产单位列表
            operationUnitList: [], // 运营单位列表
            maintenanceUnitList: [], // 运维单位列表

            serviceTagList: [], // 服务标签列表

      };
    },
    computed: {},
    async mounted() {
        await this.getAssetUnit()
        await this.getOperationUnit()
        await this.getMaintenanceUnit()
        await this.getServiceTag()

        const stationId = this.$route.query.stationId

        if(stationId) {
            this.stationId = stationId
            this.getStationDetail()
        }


    },
    methods: {
         // 获取资产单位
        async getAssetUnit() {
            const [err, res] = await getAssetUnit({})
            if (err) return
            this.assetUnitList = res.data
            
        },

        // 获取运营单位
        async getOperationUnit() {
            const [err, res] = await getOperationUnit({})
            if (err) return
            this.operationUnitList = res.data
        },

        // 获取运维单位
        async getMaintenanceUnit() {
            const [err, res] = await getMaintenanceUnit({})
            if (err) return
            this.maintenanceUnitList = res.data
        },

        // 获取服务标签
        async getServiceTag() {
            const [err, res] = await getServiceTag({
                tagType: "2"
            })
            if (err) return

            const {data} = res
            const list = []
            data.forEach(element => {
                list.push({
                    label: element.tagName,
                    value: element.tagCode
                })
            });
            this.serviceTagList = list
        },
        calculateTotalNum(data, field) {
            return data.reduce((acc, cur) => {
                return Number(acc) + Number(cur[field]);
            }, 0);
        },

          // 获取充电站详情 编辑
        async getStationDetail() {
            const [err, res] = await getStationDetail({
                stationId: this.stationId
            })
            if (err) return
            console.log(res.data,'res.data')

            // 顶部数据
            const {
                stationType,
                stationName,
                operationMode,
                operationStatus,
                stationNo,

            } = res.data
            this.stationNo = stationNo
            this.operationStatus = this.selectDictLabel(
                    this.dict.type.ls_charging_operation_status,
                    operationStatus
                );
            this.stationType = this.selectDictLabel(
                    this.dict.type.ls_charging_station_type,
                    stationType
                );
            this.stationName = stationName
            this.stationMode = this.selectDictLabel(
                    this.dict.type.ls_charging_operation_mode,
                    operationMode
            );

            // 基础信息
            const  {
                assetProperty,
                construction,
                buildDate,
                areaType,
                adjustableType,
                unifiedConstruction,
                consNos,
                maintenanceGroup,
                stationSource,
                lon,
                lat,
                stationAddress,
                stationUrl,
                stationIntroduce,
                siteGuide,
            } = res.data

            this.baseInfo.assetProperty = this.selectDictLabel(
                    this.dict.type.ls_charging_asset_property,
                    assetProperty
            )
            this.baseInfo.construction = this.selectDictLabel(
                    this.dict.type.ls_charging_construction,
                    construction
            )
            this.baseInfo.buildDate = buildDate
            this.baseInfo.areaType = this.selectDictLabel(
                    this.dict.type.ls_charging_area_type,
                    areaType
            )
            this.baseInfo.adjustableType = this.selectDictLabel(
                    this.dict.type.ls_charging_adjustable_type,
                    adjustableType
            )
            this.baseInfo.unifiedConstruction = this.selectDictLabel(
                    this.dict.type.ls_charging_status,
                    unifiedConstruction
            )
            this.baseInfo.consNos = consNos
            this.baseInfo.maintenanceGroup = this.selectDictLabel(
                    this.operationUnitList,
                    maintenanceGroup
            )
            this.baseInfo.stationSource = this.selectDictLabel(
                    this.dict.type.ls_charging_station_source,
                    stationSource
            )
            this.baseInfo.lon = lon
            this.baseInfo.lat = lat
            this.baseInfo.stationAddress = stationAddress
            this.stationUrl =  stationUrl?stationUrl.split(',') : ''
            this.stationIntroduce = stationIntroduce
            this.siteGuide = siteGuide


            // 场站运营信息
            const {
                stationChargeType,
                openFlag,
                runStartTime,
                runEndTime,
                stationTag,
                parkingChargeType,
                parkingLocation,
                serviceTag,
                serviceTel,
            } = res.data

            this.stationOperation.stationChargeType = this.selectDictLabel(
                    this.dict.type.ls_charging_stationChargeType,
                    stationChargeType
            )
            this.stationOperation.openFlag = this.selectDictLabel(
                    this.dict.type.ls_charging_status,
                    openFlag
            )
            this.stationOperation.openTime = runStartTime + '-' + runEndTime
            this.stationOperation.stationTag = this.selectDictLabel(
                    this.dict.type.ls_charging_station_tag,
                    stationTag
            )
            this.stationOperation.parkingChargeType = this.selectDictLabel(
                    this.dict.type.ls_charging_parking_charge_type,
                    parkingChargeType
            )
            this.stationOperation.parkingLocation = this.selectDictLabel(
                    this.dict.type.ls_charging_parking_location,
                    parkingLocation
            )

            let serviceTagText = ''
            this.serviceTagList.forEach(item => {
                if (serviceTag.indexOf(item.value) != -1) {
                    serviceTagText += item.label + ' '
                }
            })

            this.stationOperation.serviceTag = serviceTagText
            this.stationOperation.serviceTel = serviceTel


            // 商户运营信息
            const {
                operatingUnit,
                assetUnit,
                maintenanceUnit,
                contractedUnit,
                investors,
                supplyMechanism,
                isElectricityFeePaid,
            } = res.data

            this.merchantOperation.operatingUnit =  this.selectDictLabel(
                    this.operationUnitList,
                    operatingUnit
            )
            this.merchantOperation.assetUnit = this.selectDictLabel(
                    this.assetUnitList,
                    assetUnit
            )
            this.merchantOperation.maintenanceUnit = this.selectDictLabel(
                    this.maintenanceUnitList,
                    maintenanceUnit
            )
            this.merchantOperation.contractedUnit = this.selectDictLabel(
                    this.dict.type.ls_charging_contracted_unit,
                    contractedUnit
            )
            this.merchantOperation.investors = this.selectDictLabel(
                    this.dict.type.ls_charging_contracted_unit,
                    investors
            )
            this.merchantOperation.supplyMechanism = this.selectDictLabel(
                    this.dict.type.ls_charging_contracted_unit,
                    supplyMechanism
            )
            this.merchantOperation.isElectricityFeePaid = this.selectDictLabel(
                    this.dict.type.ls_charging_status,
                    isElectricityFeePaid
            )

            // 场站外围信息
            const {
                designUnit,
                designUnitContact,
                equipmentManufacturerContact,
                stationContact,
                warrantyPeriodStartTime,
                warrantyPeriodEndTime,
                keyManager,
                revenueForm,
                revenueFormName,
                remarks,
            } = res.data

            this.peripheralInfo.designUnit = designUnit
            this.peripheralInfo.designUnitContact = designUnitContact
            this.peripheralInfo.equipmentManufacturerContact = equipmentManufacturerContact
            this.peripheralInfo.stationContact = stationContact

            let warrantyPeriod = '-'
            if(warrantyPeriodStartTime && warrantyPeriodEndTime){
                warrantyPeriod = warrantyPeriodStartTime+'~'+warrantyPeriodEndTime
            }

            this.peripheralInfo.warrantyPeriod = warrantyPeriod
            this.peripheralInfo.keyManager = keyManager
            this.peripheralInfo.remarks = remarks
            this.doc.revenueForm = revenueFormName
            this.doc.url = revenueForm
            // 场站建设信息
            const {
                stationTransformerDtoList,
                stationMonitorDtoList,
                stationFirefightDtoList,
                stationSubsidiaryDtoList,
                chargingStacksDtoList,
            } = res.data
            
            this.stationConstructionData.stationTransformerDtoList = stationTransformerDtoList
            this.stationConstructionData.stationMonitorDtoList = stationMonitorDtoList
            this.stationConstructionData.stationFirefightDtoList = stationFirefightDtoList
            this.stationConstructionData.stationSubsidiaryDtoList = stationSubsidiaryDtoList
            this.stationConstructionData.chargingStacksDtoList = chargingStacksDtoList

             // 场站扣费模式
            const {
                stationFinanceCfgRequest
            } = res.data

            let billingModeText = ''
            this.dict.type.ls_charging_billing_mode.forEach(item => {
                if (stationFinanceCfgRequest.billingMode.indexOf(item.value) !== -1) {
                    billingModeText += item.label + ' '
                }
            })

            this.stationDeductionMode.billingMode = billingModeText
            this.stationDeductionMode.minStartAmt = stationFinanceCfgRequest.minStartAmt
            this.stationDeductionMode.minStopAmt = stationFinanceCfgRequest.minStopAmt
            

            // 充电桩信息
            const {
                stationPower,
                stationPileCountDto
            } = res.data
            this.stationInfo.totalPower = stationPower

            this.stationInfo.tableData = [
                {
                    name: '分体式',
                    dcPile: stationPileCountDto.dcSplitPile,
                    acPile: stationPileCountDto.acSplitPile,
                    pile: stationPileCountDto.splitPile,
                    dcGun: stationPileCountDto.gunDcSplitPile,
                    acGun:  stationPileCountDto.gunAcSplitPile,
                    gun: stationPileCountDto.gunSplitPile
                },
                {
                    name: '一体式',
                    dcPile: stationPileCountDto.dcAllInOnePile,
                    acPile: stationPileCountDto.acAllInOnePile,
                    pile: stationPileCountDto.allInOnePile,
                    dcGun: stationPileCountDto.gunDcAllInOnePile,
                    acGun: stationPileCountDto.gunAcAllInOnePile,
                    gun: stationPileCountDto.gunAllInOnePile
                },
                {
                    name: '合计',
                    dcPile: stationPileCountDto.dcPileTotal,
                    acPile: stationPileCountDto.acPileTotal,
                    pile: stationPileCountDto.allPileTotal,
                    dcGun: stationPileCountDto.dcPileGunTotal,
                    acGun: stationPileCountDto.acPileGunTotal,
                    gun: stationPileCountDto.allPileGunTotal
                }
            ]

            // 变更信息
            const {
                stationOperationList
            } = res.data

            this.changeInfoData = stationOperationList



        },

        handleDetail(row) {
            const {
                stationAlterBeforeJson,
                stationAlterAfterJson,
                operationType,
            } = row

            if(operationType === '99') {
                let stationAlterBefore = this.filterEmptyObjects(JSON.parse(stationAlterBeforeJson))
                let stationAlterAfter = this.filterEmptyObjects(JSON.parse(stationAlterAfterJson))
                

                console.log(stationAlterBefore, stationAlterAfter)

                
                this.beforeData = stationAlterBefore
                this.afterData = stationAlterAfter

                this.$refs.editModal.dialogVisible = true;
            } else {
                const {
                    applyNo
                }  = row

                this.$router.push({
                    path: '/v2g-charging-web/baseInfo/equipmentAndAssets/station/audit',
                    query: {
                        type: 'detail',
                        applyNo,
                    }
                })
            }


           
        },

        // 过滤空对象
        filterEmptyObjects(obj) {
            return Object.fromEntries(
                Object.entries(obj).filter(([_, value]) => {
                // 判断是否为非空对象
                return !(typeof value === 'object' && 
                        value !== null && 
                        !Array.isArray(value) && 
                        Object.keys(value).length === 0);
                })
            );
        },

        // 下载文件
        downloadFile(fileUrl,fileName) {
             // 创建隐藏的可下载链接
            const link = document.createElement('a');
            link.style.display = 'none';
            
            // 设置文件URL
            link.href = fileUrl;
            
            // 设置下载文件名
            link.download = fileName;
            
            // 添加到页面并触发点击
            document.body.appendChild(link);
            link.click();
            
            // 清理DOM
            document.body.removeChild(link);
        }

    },
  };
  </script>
  
  <style lang="scss" scoped>
  .container-full {
    background-color: rgba(244, 246, 249, 1);
    padding-bottom: 16px;
    min-height: calc(100% - 80px);
    overflow-y: auto;
  }

  
  .device-head {
    background-color: #fff;
    padding: 24px 16px;
    display: flex;
  
  }

   
  .info-card {
  margin: 16px;
  border-radius: 5px;
  border: 1px solid #fff;
  overflow: hidden;
  background-color: #fff;
  // min-height: 300px;
  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    background: linear-gradient(180deg, #E9F2FF 0%, #FFFFFF 100%);
    .before-icon {
        width: 3px;
        height: 16px;
        background-image: url('~@/assets/station/consno-before.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        margin-right: 8px;
    }
    .card-head-text {
        flex:1;
        font-weight: 500;
        font-size: 16px;
        color: #12151A;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
    .button-wrap {
      display: flex;
      .invite-btn {
          background-color: #1ab2ff;
          border-color: #1ab2ff;
        }
        ::v-deep .el-button--small {
          font-size: 14px;
        }
      .distribution {
          margin-left: 24px;
          margin-right: 24px;
          display: flex;
          align-items: center;
        }
    }
  }
  
  .people-table-wrap {
    ::v-deep .bd3001-table-select-box {
      display: none;
    }
    ::v-deep .bd3001-button {
      width: 100%;
    }
  }
  .form-wrap {
      padding: 0 16px 16px 16px;
      .info-title {
        font-weight: 400;
        font-size: 16px;
        line-height: 16px;
        color: #505363;
      }
      .info-detail {
        font-weight: 400;
        font-size: 16px;
        line-height: 16px;
        color: #292B33;
      }
      .info-amount {
        height: 28px;
        background-color: #FFF7E6;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 8px;
        font-weight: 400;
        font-size: 20px;
        color: #FE8921;
        margin-top: -6px;
        margin-right: 4px;

      }
      .info-img {
        width: 140px;
        height: 140px;
      }
    }
  }

  .form-edit-wrap {
    padding: 0 0 16px 0;
  }

  ::v-deep  .bd3001-content{
    padding: 0 !important;
  }
  </style>
  