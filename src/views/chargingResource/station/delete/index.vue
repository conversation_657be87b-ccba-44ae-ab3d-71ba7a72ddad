<template>
    <div class="container container-region">
      <RegionSelect @nodeClick="nodeClick"></RegionSelect>
      <div class="container-info ">
        <div class="top-info-wrap" >
            <div class="top-info-icon"></div>
            <div class="top-info-first" v-if="topInfoFirst">{{ topInfoFirst }}</div>
            <div class="top-info-bold " >{{ topInfoBold }}</div>
        </div>
        <div class="table-wrap">
            <BuseCrud
                ref="crud"
                :loading="loading"
                :filterOptions="filterOptions"
                :tablePage="tablePage"
                :tableColumn="tableColumn"
                :tableData="tableData"
                :pagerProps="pagerProps"
                :modalConfig="modalConfig"
                :tableOn="{
                    'checkbox-change': handleCheckboxChange,
                    'checkbox-all': handleCheckboxChange,
                }"
                class="buse-wrap-station"
                @loadData="loadData"
            >
            <template slot="defaultHeader">
                <div>
                    <div class="card-head">
                        <div class="card-head-text">充电站列表</div>
                    </div>
                    <div class="card-head-after"></div>

                    <div class="top-button-wrap">
   
                        <el-button
                             type="primary"
                            @click="() => handleCancelDelete(null)"
                        >
                            批量取消删除
                        </el-button>
                        

                        <el-button
                            plain
                            @click="() => handleExport()"
                        >
                            导出
                        </el-button>

                        
 
                    
                    </div>
                </div>
                
            </template>

                <template slot="operate" slot-scope="{ row }">
                    <div class="menu-box">
                        <el-button
                            type="primary"
                            plain
                            @click="handleCancelDelete(row)"
                        >
                            取消删除
                        </el-button>
                    </div>
                
                </template>

            </BuseCrud>
        </div>
        

      </div>
    </div>
  </template>
  
  <script>
  import RegionSelect from '@/components/Business/RegionSelect';

  import StatusDot from '@/components/Business/StatusDot';

import {
  getStationList,
  getAssetUnit,
  getOperationUnit,
  getMaintenanceUnit,
  batchCancelDelete,
} from '@/api/station/index';

  export default {
    components: {
      RegionSelect,
      StatusDot
    },
    dicts: [
      'ls_charging_station_type', // 站点类型
      'ls_charging_construction', // 建设场所
      'ls_charging_operation_mode', // 运营模式
      'ls_charging_asset_property', // 资产属性
      'ls_charging_station_access_type', // 充电站接入类型
      'ls_charging_operation_status', // 运营状态
      'ls_charging_station_source', // 数据来源
      'ls_charging_status', // 是否状态
      'ls_charging_adjustable_type', // 可控类型
      'ls_charging_parking_charge_type', // 停车场收费类型
      'ls_charging_area_type', // 所属区域
      'ls_charging_contracted_unit', // 签约单位 投资主体 供电机构
      'ls_charging_structural_type',
    ],
    data() {
      return {
        loading: false,
        stationList: [],
        topInfo: {},
        topInfoFirst: '',
        topInfoBold: '',

        tablePage: { total: 0, currentPage: 1, pageSize: 10 },
        

        tableColumn:[
            {
                type: 'checkbox',
                width: 50,
                fixed: 'left',
            },
            {
                type: 'seq',
                title: '序号',
                width: 60,
                minWidth: 60, // 最小宽度
            },
            {
                field: 'stationNo',
                title: '充电站编号',
                minWidth: 250, // 最小宽度
            },
            {
                field: 'stationName',
                title: '充电站名称',
                minWidth: 200, // 最小宽度
            },
            {
                field: 'stationType',
                title: '站点类型',
                minWidth: 100, // 最小宽度
                formatter: ({ cellValue }) => {
                  return this.selectDictLabel(
                    this.dict.type.ls_charging_station_type,
                    cellValue
                  );
                },
            },
            {
                field: 'operationMode',
                title: '运营模式',
                minWidth: 150, // 最小宽度
                formatter: ({ cellValue }) => {
                  return this.selectDictLabel(
                    this.dict.type.ls_charging_operation_mode,
                    cellValue
                  );
                },
            },
            {
                field: 'assetProperty',
                title: '资产属性',
                minWidth: 100, // 最小宽度
                formatter: ({ cellValue }) => {
                  return this.selectDictLabel(
                    this.dict.type.ls_charging_asset_property,
                    cellValue
                  );
                },
            },
            {
                field: 'stationAccessType',
                title: '接入方式',
                minWidth: 100, // 最小宽度
                formatter: ({ cellValue }) => {
                  return this.selectDictLabel(
                    this.dict.type.ls_charging_station_access_type,
                    cellValue
                  );
                },
            },
            // {
            //     field: 'pileNum',
            //     title: '充电桩数量',
            //     minWidth: 120, // 最小宽度
            // },
            {
                field: 'isAdjustable',
                title: '是否参与调控',
                minWidth: 120, // 最小宽度
                formatter: ({ cellValue }) => {
                  return this.selectDictLabel(
                    this.dict.type.ls_charging_status,
                    cellValue
                  );
                },
            },
            {
                field: 'adjustableType',
                title: '可控类型',
                minWidth: 100, // 最小宽度
                formatter: ({ cellValue }) => {
                  return this.selectDictLabel(
                    this.dict.type.ls_charging_adjustable_type,
                    cellValue
                  );
                },
            },
            {
                field: 'unifiedConstruction',
                title: '是否统建统服',
                minWidth: 120, // 最小宽度
                formatter: ({ cellValue }) => {
                  return this.selectDictLabel(
                    this.dict.type.ls_charging_status,
                    cellValue
                  );
                },
            },
            {
                field: 'construction',
                title: '建设场所',
                minWidth: 120, // 最小宽度
                formatter: ({ cellValue }) => {
                  return this.selectDictLabel(
                    this.dict.type.ls_charging_construction,
                    cellValue
                  );
                },
            },
            {
                field: 'areaType',
                title: '所属区域',
                minWidth: 120, // 最小宽度
                formatter: ({ cellValue }) => {
                  return this.selectDictLabel(
                    this.dict.type.ls_charging_area_type,
                    cellValue
                  );
                },
            },
            {
                field: 'stationAddress',
                title: '充电站地址',
                minWidth: 200, // 最小宽度
            },
            {
                title: '资产单位',
                field: 'assetUnitName',
                minWidth: 200, // 最小宽度
                slots: {
                  default: ({ row }) => [
                      <el-tooltip 
                          content={row.assetUnitName} 
                          placement="top" 
                          disabled={!row.assetUnitName || row.assetUnitName.length < 10}
                      >
                          <span class="ellipsis-text">{row.assetUnitName}</span>
                      </el-tooltip>
                  ]
              }
            },
            {
                title: '运营单位',
                field: 'operationUnit',
                minWidth: 200, // 最小宽度
                slots: {
                  default: ({ row }) => [
                      <el-tooltip 
                          content={row.operatingUnitName} 
                          placement="top" 
                          disabled={!row.operatingUnitName || row.operatingUnitName.length < 10}
                      >
                          <span class="ellipsis-text">{row.operatingUnitName}</span>
                      </el-tooltip>
                  ]
              }
            },
            {
                title: '运维单位',
                field: 'operatingUnitName',
                minWidth: 200, // 最小宽度
                slots: {
                  default: ({ row }) => [
                      <el-tooltip 
                          content={row.maintenanceUnitName} 
                          placement="top" 
                          disabled={!row.maintenanceUnitName || row.maintenanceUnitName.length < 10}
                      >
                          <span class="ellipsis-text">{row.maintenanceUnitName}</span>
                      </el-tooltip>
                  ]
              }
            },
            {
                title: '签约单位',
                field: 'contractedUnit',
                minWidth: 220, // 最小宽度
            },
            {
                field: 'structuralType',
                title: '结构形式',
                minWidth: 120, // 最小宽度
                formatter: ({ cellValue }) => {
                        return this.selectDictLabel(
                          this.dict.type.ls_charging_structural_type,
                          cellValue
                        );
                      },
                
              },
            {
                title: '充电堆数量',
                field: 'stacksNum',
                minWidth: 120, // 最小宽度
            },
            {
                title: '直流桩数量',
                field: 'dcPileNum',
                minWidth: 120, // 最小宽度
            },
            {
                title: '交流桩数量',
                field: 'acPileNum',
                minWidth: 120, // 最小宽度
            },
            {
                title: '充电桩总数量',
                field: 'pileNum',
                minWidth: 150, // 最小宽度
            },
            {
                title: '直流枪数量',
                field: 'dcGunNum',
                minWidth: 120, // 最小宽度
            },
            {
                title: '交流枪数量',
                field: 'acGunNum',
                minWidth: 120, // 最小宽度
            },
            {
                title: '充电枪总数量',
                field: 'pileGunNum',
                minWidth: 150, // 最小宽度
            },
            {
                title: '资产码数量',
                field: 'assetCodeNum',
                minWidth: 120, // 最小宽度
            },
            {
                title: '充电站总功率 (kWh)',
                field: 'stationPower',
                minWidth: 150, // 最小宽度
            },
            {
                title: '数据来源',
                field: 'stationSource',
                minWidth: 120, // 最小宽度
                formatter: ({ cellValue }) => {
                  return this.selectDictLabel(
                    this.dict.type.ls_charging_station_source,
                    cellValue
                  );
                },
            },
            {
                title: '投运日期',
                field: 'openDate',
                minWidth: 120, // 最小宽度
            },

            {
                title: '创建人',
                field: 'createUser',
                minWidth: 120, // 最小宽度
            },
            {
                title: '创建时间',
                field: 'createdTime',
                minWidth: 150, // 最小宽度
            },
            {
                title: '修改人',
                field: 'updateUser',
                minWidth: 120, // 最小宽度
            },
            {
                title: '修改时间',
                field: 'updateTime',
                minWidth: 200, // 最小宽度
            },
            {
                title: '运营状态',
                field: 'operationStatus',
                minWidth: 100, // 最小宽度
                formatter: ({ cellValue }) => {
                  return this.selectDictLabel(
                    this.dict.type.ls_charging_operation_status,
                    cellValue
                  );
                },
                align: 'center',
                fixed: 'right',
                slots: {
                  // 自定义render函数
                  default: ({ row }) => {
                    return (
                      <StatusDot
                        value={row.operationStatus}
                        dictValue={this.dict.type.ls_charging_operation_status}
                        colors={['default', 'success', 'danger','warning','danger','danger','warning']}
                      ></StatusDot>
                    );
                  },
                },
            },
            {
                title: '操作',
                slots: { default: 'operate' },
                width: 110,
                align: 'center',
                fixed: 'right',
            },
        ],
        tableData: [],
        pagerProps: {
          layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
        },
        params: {
            stationId: '',
            stationName: '',
            stationType: '',
            operationMode: '',
            assetAttribute: '',
            assetType: '',
            assetUnit: '',
            operationUnit: '',
            operationMaintainUnit: '',
            // operationStatus: '',
            dataSources: '',
            operationDate: [],
            isRegulation: '',
            controlType: '',
            isTjtf: '',
            constructionSite: '',
        },
      };
    },
    computed: {
      filterOptions() {
        return {
          config: [
            {
              field: 'stationId',
              title: '充电站编号',
              element: 'el-input',
            },
            {
              field: 'stationName',
              title: '充电站名称',
              element: 'el-input',
            },
            {
              field: 'stationType',
              title: '站点类型',
              element: 'el-select',
              props: {
                options: [
                  {
                    label: '城市公共',
                    value: '01',
                  },
                  {
                    label: '个人',
                    value: '02',
                  },
                ],
              },
            },
            {
              field: 'operationMode',
              title: '运营模式',
              element: 'el-select',
              props: {
                options: [
                  {
                    label: '自营',
                    value: '01',
                  },
                  {
                    label: '代运营',
                    value: '02',
                  },
                ],
              },
            },
            {
              field: 'assetAttribute',
              title: '资产属性',
              element: 'el-select',
              props: {
                options: [
                  {
                    label: '自营',
                    value: '01',
                  },
                  {
                    label: '代运营',
                    value: '02',
                  },
                ],
              },
            },
            {
              field: 'assetType',
              title: '接入方式',
              element: 'el-select',
              props: {
                options: [
                  {
                    label: '直连',
                    value: '01',
                  },
                  {
                    label: '互联互通',
                    value: '02',
                  },
                ],
              },
            },
            {
              field: 'assetUnit',
              title: '资产单位',
              element: 'el-select',
              props: {
                options: this.assetUnitList,
              },
            },
            {
              field: 'operationUnit',
              title: '运营单位',
              element: 'el-select',
              props: {
                options: this.operationUnitList,
              },
            },
            {
              field: 'operationMaintainUnit',
              title: '运维单位',
              element: 'el-select',
              props: {
                options: this.maintenanceUnitList,
              },
            },
            // {
            //   field: 'operationStatus',
            //   title: '运营状态',
            //   element: 'el-select',
            //   props: {
            //     options: [
            //       {
            //         label: '待运营',
            //         value: '01',
            //       },
            //       {
            //         label: '投运',
            //         value: '02',
            //       }
            //     ],
            //   },
            // },
            {
              field: 'dataSources',
              title: '数据来源',
              element: 'el-select',
              props: {
                options: [
                  {
                    label: '平台',
                    value: '01',
                  },
                  {
                    label: '车联网平台',
                    value: '02',
                  }
                ],
              },
            },
            {
              field: 'operationDate',
              title: '投运日期',
              element: 'el-date-picker',
              props: {
                type: 'daterange',
                options: [],
                valueFormat: 'yyyy-MM-dd',
              },
            },
            {
              field: 'isRegulation',
              title: '是否参与调控',
              element: 'el-select',
              props: {
                options: [
                  {
                    label: '是',
                    value: '01',
                  },
                  {
                    label: '否',
                    value: '02',
                  }
                ],
              },
            },
            {
              field: 'controlType',
              title: '可控类型',
              element: 'el-select',
              props: {
                options: [
                  {
                    label: '有序调控',
                    value: '01',
                  },
                  {
                    label: 'V2G',
                    value: '02',
                  }
                ],
              },
            },
            {
              field: 'isTjtf',
              title: '是否统建统服',
              element: 'el-select',
              props: {
                options: [
                  {
                    label: '是',
                    value: '01',
                  },
                  {
                    label: '否',
                    value: '02',
                  }
                ],
              },
            },
            {
              field: 'constructionSite',
              title: '建设场所',
              element: 'el-select',
              props: {
                options: [
                  {
                    label: '都市绿地',
                    value: '01',
                  },
                  {
                    label: '写字楼',
                    value: '02',
                  }
                ],
              },
            },
          ],
          params: this.params,
        };
      },
      modalConfig() {
        return {
            addBtn: false,
            viewBtn: false,
            menu: false,
            editBtn: false,
            delBtn: false,
        }
      },
    },
    mounted() {
        this.getAssetUnit()
        this.getOperationUnit()
        this.getMaintenanceUnit()
    },
    methods: {

       // 获取资产单位
       async getAssetUnit() {
        const [err, res] = await getAssetUnit({})
        if (err) return
        this.assetUnitList = res.data
        
      },

      // 获取运营单位
      async getOperationUnit() {
        const [err, res] = await getOperationUnit({})
        if (err) return
        this.operationUnitList = res.data
      },

      // 获取运维单位
      async getMaintenanceUnit() {
        const [err, res] = await getMaintenanceUnit({})
        if (err) return
        this.maintenanceUnitList = res.data
      },

      nodeClick(node) {
        if (node) {
          const { list, areaCode, areaName, ...reset } = node;
          this.params.operatorCode = '';
          this.params.stationId = '';
          this.params.pileId = '';
          this.regionInfo = reset;
          console.log('node', node);
          this.loadData();
          this.topInfo = {
            ...reset,
            areaName,
          }


            this.handleTopInfo()
        }
      },
    handleTopInfo() {
        const { provinceCodeName, cityCodeName, districtCodeName, areaName} = this.topInfo;
        if (provinceCodeName === areaName) {
            // 选中了省
            this.topInfoFirst = '';
            this.topInfoBold = areaName;
        } else if (cityCodeName === areaName) {
            // 选中了市
            this.topInfoFirst = `${provinceCodeName} / `;
            this.topInfoBold = areaName;
        } else if (districtCodeName === areaName) {
            // 选中了区
            this.topInfoFirst = `${provinceCodeName} / ${cityCodeName} / `;
            this.topInfoBold = areaName;
        }
    },
      async loadData() {
        const {
          stationId,
          stationName,
          stationType,
          operationMode,
          assetAttribute,
          assetType,
          assetUnit,
          operationUnit,
          operationMaintainUnit,
          dataSources,
          operationDate,
          isRegulation,
          controlType,
          isTjtf,
          constructionSite,
        } = this.filterOptions.params

        const operationStatus = '06'

        const  {
          provinceCode,
          cityCode,
          districtCode,
        } = this.regionInfo


        let openDateStart = '';
        let openDateEnd = '';
        if (operationDate && operationDate.length > 0) {
            openDateStart = operationDate[0];  
            openDateEnd = operationDate[1];
        }

        this.loading = true;
        const [err, res] = await getStationList({
            stationNo: stationId,
            stationName,
            stationType,
            operationMode,
            assetProperty:assetAttribute,
            stationAccessType: assetType,
            assetUnit,
            operatingUnit:operationUnit,
            maintenanceUnit: operationMaintainUnit,
            operationStatus,
            stationSource: dataSources,
            openDateStart,
            openDateEnd,
            isAdjustable:isRegulation,
            adjustableType: controlType,
            unifiedConstruction: isTjtf,
            construction:constructionSite,
            province: provinceCode,
            city: cityCode,
            county: districtCode,
            pageNum: this.tablePage.currentPage,
            pageSize: this.tablePage.pageSize,
        });
        this.loading = false;
        if (err) {
            return this.$message.error(err.message);
        }
        const { data, total } = res;

        const list = []

        data.forEach(element => {
          list.push({
            ...element,
            runTime: element.runStartTime + '-' + element.runEndTime,
          })
        });
        
        this.tableData = list;
        this.tablePage.total = total;
        
      },

        handleCheckboxChange({ records }) {
            this.stationList = records
        },
        
        // 批量取消删除
        handleCancelDeleteBatch() {
            if(!this.stationList.length) {
                this.$message.warning('请先选择站点')
                return
            } else {
                // todo 调用接口
            }
        },

        // 取消删除
        handleCancelDelete(row) {
          let rows = [row];
          if (!row) {
            if (!this.stationList.length) {
              this.$message.warning('请先选择要恢复的数据！');
              return;
            }
            rows = this.stationList;
          }

          const ids = rows.map((x) => x.stationId);
          const stationName = rows.map((x) => x.stationName).join('、');


          this.$confirm(`确认恢复站点：${stationName}吗？`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(async () => {
            
            const [err, res] =await batchCancelDelete(ids.map((item) => (item)))

            console.log(res, err)
            if (err){ 
              return
            }
            this.$message({
                type: 'success',
                message: '恢复成功!'
            });
            this.loadData();
            
            
          })

        }
        

    },
  };
  </script>
  
  <style lang="scss" scoped>
.table-wrap {
    ::v-deep .bd3001-table-select-box {
        display: none;
    }
    ::v-deep .bd3001-header  {
        display: block;
    }
    ::v-deep .bd3001-button {
        display: block !important;
    }
    
    .card-head {
        // position: relative; 
        height: 56px;
        padding: 0 16px;
        display: flex;
        align-items: center;
        margin-top: -20px;
        .card-head-text {
        flex:1;
        width: 520px;
            height: 26px;
            background-image: url('~@/assets/images/bg-title.png');
            background-size: 520px 26px;
            background-repeat: no-repeat;
            font-size: 20px;
            font-style: normal;
            font-weight: 500;
            line-height: 16px;
            padding-left: 36px;
            color: #21252e;
        &::before {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            left: -3px; /* 调整这个值来改变边框的宽度 */
            width: 0;
            border-top: 3px solid transparent;
            border-bottom: 3px solid transparent;
            border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
        }
        }   

    }

    .card-head-after {
        width: 100%;
        height: 1px;
        background-color: #DCDEE2;
        margin-bottom: 16px;
    }
    .info-wrap {
        margin-top: 16px;
        display: flex;
        flex-wrap: wrap;
        gap: 16px;

        .info-item {
            background-color: #FAFBFC;
            flex: 1 1 0;
            // min-width: 180px;
            
            border-radius: 5px;
            padding: 8px 24px;
            box-sizing: border-box;
            // margin-right: 16px;
            display: flex;
            .info-icon {
                width: 42px;
                height: 42px;
            }
            .info-right-wrap {
                flex:1;
                margin-left: 8px;
                .info-title {
                    font-weight: 400;
                    font-size: 14px;
                    line-height: 14px;
                    margin-bottom: 8px;
                }
                .info-number {
                    font-size: 20px;
                    font-weight: 500;
                    .info-unit {
                        font-size: 14px;
                        font-weight: 400;
                    }
                }
            }
        }
        .info-item:last-child {
            margin-right: 0;
        }
    }

    .top-button-wrap {
        display:flex;
        margin: 16px 0;
    }
}


  
.top-info-wrap {
    display: flex;
    height: 20px;
    align-items: center;
    margin-bottom: 16px;
    
    .top-info-icon {
        margin-left: 10px;
        margin-right: 4px;

        width: 20px;
        height: 20px;
        background-image: url('~@/assets/station/location-top.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }
    .top-info-first {
        font-weight: 400;
        font-size: 16px;
        color: #505363;
    }
    .top-info-bold {
        margin-left: 4px;
        font-weight: 400;
        font-size: 16px;
        color: #12151A;

    }
  }
  .ellipsis-text {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }
</style>
  