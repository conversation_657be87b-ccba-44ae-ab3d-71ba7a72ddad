<template>
    <div class="container container-float " style="padding: 0;">
        <div class="info-card">
            <div class="card-head">
                <div class="before-icon"></div>
                <div class="card-head-text">基础信息</div>
            </div>
            <div class="form-wrap">
                <el-form :model="baseInfo.form" :rules="baseInfo.rules" ref="baseInfoForm"  label-position="top">
                        <el-row :gutter="20">
                            <el-col :span="6">
                                <el-form-item
                                    label="充电站编号"
                                    prop="stationNo"
                                    :label-width="formLabelWidth"
                                >
                                    <el-input v-model="baseInfo.form.stationNo" ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item
                                    label="充电站名称"
                                    prop="stationName"
                                    :label-width="formLabelWidth"
                                >
                                    <el-input v-model="baseInfo.form.stationName" ></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item
                                    label="站点类型"
                                    prop="stationType"
                                    :label-width="formLabelWidth"
                                >
                                    <el-select
                                    v-model="baseInfo.form.stationType"
                                    placeholder="请选择站点类型"
                                    style="width: 100%"
                                    >
                                    <el-option
                                        v-for="item in dict.type.ls_charging_station_type"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    ></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>

                            <el-col :span="6">
                                <el-form-item
                                    label="运营模式"
                                    prop="operationMode"
                                    :label-width="formLabelWidth"
                                >
                                    <el-select
                                        v-model="baseInfo.form.operationMode"
                                        placeholder="请选择运营模式"
                                        style="width: 100%"
                                    >
                                    <el-option
                                        v-for="item in dict.type.ls_charging_operation_mode"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    ></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-row :gutter="20">
                            <el-col :span="6">
                                <el-form-item
                                    label="资产属性"
                                    prop="assetProperty"
                                    :label-width="formLabelWidth"
                                >
                                    <el-select
                                        v-model="baseInfo.form.assetProperty"
                                        placeholder="请选择资产属性"
                                        style="width: 100%"
                                    >
                                    <el-option
                                        v-for="item in dict.type.ls_charging_asset_property"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    ></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item
                                    label="建设场所"
                                    prop="construction"
                                    :label-width="formLabelWidth"
                                >
                                    <el-select
                                        v-model="baseInfo.form.construction"
                                        placeholder="请选择建设场所"
                                        style="width: 100%"
                                    >
                                        <el-option
                                            v-for="item in dict.type.ls_charging_construction"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value"
                                        ></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item
                                    label="建设完成日期"
                                    prop="buildDate"
                                    :label-width="formLabelWidth"
                                >
                                <el-date-picker
                                    v-model="baseInfo.form.buildDate" 
                                    value-format="yyyy-MM-dd"
                                >

                                </el-date-picker>
                                </el-form-item>
                            </el-col>

                            <el-col :span="6">
                                <el-form-item
                                    label="所属区域"
                                    prop="areaType"
                                    :label-width="formLabelWidth"
                                >
                                    <el-select
                                        v-model="baseInfo.form.areaType"
                                        placeholder="请选择所属区域"
                                        style="width: 100%"
                                    >
                                    <el-option
                                        v-for="item in dict.type.ls_charging_area_type"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    ></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>

                        <el-row :gutter="20">
                            <el-col :span="6">
                                <el-form-item
                                    label="可控类型"
                                    prop="adjustableType"
                                    :label-width="formLabelWidth"
                                >
                                    <el-select
                                        v-model="baseInfo.form.adjustableType"
                                        placeholder="请选择可控类型"
                                        style="width: 100%"
                                    >
                                    <el-option
                                        v-for="item in dict.type.ls_charging_adjustable_type"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    ></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item
                                    label="是否统建统服"
                                    prop="unifiedConstruction"
                                    :label-width="formLabelWidth"
                                >
                                    <el-select
                                        v-model="baseInfo.form.unifiedConstruction"
                                        placeholder="请选择是否统建统服"
                                        style="width: 100%"
                                    >
                                        <el-option
                                            v-for="item in dict.type.ls_charging_status"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value"
                                        ></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="6">
                                <el-form-item
                                    label="营销户号"
                                    prop="consNos"
                                    :label-width="formLabelWidth"
                                >
                                    <el-input 
                                        v-model="baseInfo.form.consNos"
                                         placeholder="请输入，多个时用逗号隔开"
                                    ></el-input>
                                </el-form-item>
                            </el-col>

                            <el-col :span="6">
                                <el-form-item
                                    label="运维分组"
                                    prop="maintenanceGroup"
                                    :label-width="formLabelWidth"
                                >
                                    <el-select
                                        v-model="baseInfo.form.maintenanceGroup"
                                        placeholder="请选择运维分组"
                                        style="width: 100%"
                                        filterable
                                    >
                                    <el-option
                                        v-for="item in operationUnitList"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    ></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        
                        <el-row :gutter="20">
                            <el-col :span="24">
                                <el-form-item
                                    label="站点地址"
                                    prop="addressMap"
                                    :label-width="formLabelWidth"
                                >
                                    <MapItem ref="mapItem" v-model="baseInfo.form.addressMap" />
                                </el-form-item>
                            </el-col>
                           
                        </el-row>

                        <el-row :gutter="20">
                            <el-col :span="24">
                                <el-form-item
                                    label="站点图片"
                                    prop="stationUrl"
                                    :label-width="formLabelWidth"
                                >
                                <Upload
                                    v-model="baseInfo.form.stationUrl"
                                    :limit="3"
                                    @input="handleUpload"
                                    @success="handleUploadSuccess"
                                />
                                    <!-- <el-upload
                                        action="#"
                                        list-type="picture-card"
                                        :auto-upload="false"
                                        :on-success="handleSuccess"
                                        :on-change="handleChange"
                                        :file-list="baseInfo.form.stationUrl"
                                        :limit="1"
                                        >
                                            <div  
                                                :class="{ 'upload-disabled': baseInfo.form.stationUrl.length >= 1 }"
                                                slot="default">
                                                <i class="el-icon-plus"></i>
                                                上传图片
                                            </div>
                                        
                                            <div slot="file" slot-scope="{file}">
                                            <img
                                                class="el-upload-list__item-thumbnail"
                                                :src="file.url" alt=""
                                            >
                                            <span class="el-upload-list__item-actions">
                                                <span
                                                class="el-upload-list__item-preview"
                                                @click="handlePictureCardPreview(file)"
                                                >
                                                <i class="el-icon-zoom-in"></i>
                                                </span>
                                                <span
                                                    v-if="!disabled"
                                                    class="el-upload-list__item-delete"
                                                    @click="handleDownload(file)"
                                                >
                                                <i class="el-icon-download"></i>
                                                </span>
                                                <span
                                                v-if="!disabled"
                                                class="el-upload-list__item-delete"
                                                @click="handleRemove(file)"
                                                >
                                                <i class="el-icon-delete"></i>
                                                </span>
                                            </span>
                                            </div>
                                    </el-upload> -->

                                </el-form-item>
                            </el-col>
                           
                        </el-row>

                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item
                                    label="站点简介"
                                    prop="stationIntroduce"
                                    :label-width="formLabelWidth"
                                >
                                <el-input
                                    v-model="baseInfo.form.stationIntroduce"
                                    type="textarea"
                                    :rows="3"
                                    placeholder="请输入站点简介"
                                ></el-input>

                                </el-form-item>
                            </el-col>
                           
                            <el-col :span="12">
                                <el-form-item
                                    label="找桩指引"
                                    prop="siteGuide"
                                    :label-width="formLabelWidth"
                                >
                                <el-input
                                    v-model="baseInfo.form.siteGuide"
                                    type="textarea"
                                    :rows="3"
                                    placeholder="请输入找桩指引"
                                ></el-input>

                                </el-form-item>
                            </el-col>
                        </el-row>
                        
                </el-form> 
            </div>
        </div>

        <div class="info-card" >
            <div class="card-head">
                <div class="before-icon"></div>
                <div class="card-head-text">场站运营信息</div>
            </div>

    
            <div class="form-wrap">
                <el-form :model="stationOperatorInfo.form" :rules="stationOperatorInfo.rules" ref="stationOperatorInfoForm"  label-position="top">
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-form-item
                                label="充电站充电分类"
                                prop="stationChargeType"
                                :label-width="formLabelWidth"
                            >
                            <el-select
                                    v-model="stationOperatorInfo.form.stationChargeType"
                                    placeholder="请选择充电站充电分类"
                                    style="width: 100%"
                                    >
                                    <el-option
                                        v-for="item in dict.type.ls_charging_stationChargeType"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    ></el-option>
                                    </el-select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <el-form-item
                                label="是否开放"
                                prop="openFlag"
                                :label-width="formLabelWidth"
                            >
                                <el-select
                                    v-model="stationOperatorInfo.form.openFlag"
                                    placeholder="请选择是否开放"
                                    style="width: 100%"
                                    >
                                    <el-option
                                        v-for="item in dict.type.ls_charging_status"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="6" v-if="stationOperatorInfo.form.openFlag === '1'">
                            <el-form-item
                                label="开放时间"
                                prop="openTime"
                                :label-width="formLabelWidth"
                            >
                                <el-time-picker
                                    v-model="stationOperatorInfo.form.openTime"
                                    is-range
                                    range-separator="至"
                                    start-placeholder="开始时间"
                                    end-placeholder="结束时间"
                                    format="HH:mm"
                                    value-format="HH:mm"
                                    :picker-options="{
                                        selectableRange: '00:00:00 - 24:00:00',
                                        format: 'HH:mm'
                                    }"
                                ></el-time-picker>
                            
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <el-form-item
                                label="充电站标签"
                                prop="stationTag"
                                :label-width="formLabelWidth"
                            >
                                <el-select
                                    v-model="stationOperatorInfo.form.stationTag"
                                    placeholder="请选择充电站标签"
                                    style="width: 100%"
                                    clearable
                                    >
                                    <el-option
                                        v-for="item in dict.type.ls_charging_station_tag"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>


                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-form-item
                                label="停车场收费类型"
                                prop="parkingChargeType"
                                :label-width="formLabelWidth"
                            >
                            <el-select
                                    v-model="stationOperatorInfo.form.parkingChargeType"
                                    placeholder="请选择停车场收费类型"
                                    style="width: 100%"
                                    clearable
                                    >
                                    <el-option
                                        v-for="item in dict.type.ls_charging_parking_charge_type"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    ></el-option>
                                    </el-select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <el-form-item
                                label="停车场位置"
                                prop="parkingLocation"
                                :label-width="formLabelWidth"
                            >
                            <el-select
                                    v-model="stationOperatorInfo.form.parkingLocation"
                                    placeholder="请选择停车场位置"
                                    style="width: 100%"
                                    clearable
                                    >
                                    <el-option
                                        v-for="item in dict.type.ls_charging_parking_location"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    ></el-option>
                                    </el-select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <el-form-item
                                label="服务标签"
                                prop="serviceTag"
                                :label-width="formLabelWidth"
                            >
                            <el-select
                                    v-model="stationOperatorInfo.form.serviceTag"
                                    placeholder="请选择服务标签"
                                    multiple
                                    style="width: 100%"
                                    clearable
                                    >
                                    <el-option
                                        v-for="item in serviceTagList"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    ></el-option>
                                    </el-select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                                <el-form-item
                                    label="服务电话"
                                    prop="serviceTel"
                                    :label-width="formLabelWidth"
                                    clearable
                                >
                                    <el-input v-model="stationOperatorInfo.form.serviceTel" ></el-input>
                                </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
    
        </div>

        <div class="info-card" >
            <div class="card-head">
                <div class="before-icon"></div>
                <div class="card-head-text">商户运营信息</div>
            </div>

            <div class="form-wrap">
                <el-form :model="merchantOperatorInfo.form" :rules="merchantOperatorInfo.rules" ref="merchantOperatorInfoForm"  label-position="top">
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-form-item
                                label="运营单位"
                                prop="operatingUnit"
                                :label-width="formLabelWidth"
                            >
                            <el-select
                                    v-model="merchantOperatorInfo.form.operatingUnit"
                                    placeholder="请选择运营单位"
                                    style="width: 100%"
                                    >
                                    <el-option
                                        v-for="item in operationUnitList"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    ></el-option>
                                    </el-select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <el-form-item
                                label="资产单位"
                                prop="assetUnit"
                                :label-width="formLabelWidth"
                            >
                            <el-select
                                    v-model="merchantOperatorInfo.form.assetUnit"
                                    placeholder="请选择资产单位"
                                    style="width: 100%"
                                    @change="changeAssetUnit"
                                    >
                                    <el-option
                                        v-for="item in assetUnitList"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    ></el-option>
                                    </el-select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <el-form-item
                                label="运维单位"
                                prop="maintenanceUnit"
                                :label-width="formLabelWidth"
                            >
                            <el-select
                                    v-model="merchantOperatorInfo.form.maintenanceUnit"
                                    placeholder="请选择运维单位"
                                    style="width: 100%"
                                    clearable
                                    >
                                    <el-option
                                        v-for="item in maintenanceUnitList"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    ></el-option>
                                    </el-select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <el-form-item
                                label="运维单位联系人"
                                prop="maintenance"
                                :label-width="formLabelWidth"
                                clearable
                            >
                                <el-input v-model="merchantOperatorInfo.form.maintenance" placeholder="请输入运维单位联系人"></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>


                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-form-item
                                label="签约单位"
                                prop="contractedUnit"
                                :label-width="formLabelWidth"
                            >
                            <el-select
                                    v-model="merchantOperatorInfo.form.contractedUnit"
                                    placeholder="请选择签约单位"
                                    style="width: 100%"
                                    clearable
                                    >
                                    <el-option
                                        v-for="item in  dict.type.ls_charging_contracted_unit"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    ></el-option>
                                    </el-select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <el-form-item
                                label="投资主体"
                                prop="investors"
                                :label-width="formLabelWidth"
                            >
                            <el-select
                                    v-model="merchantOperatorInfo.form.investors"
                                    placeholder="请选择投资主体"
                                    style="width: 100%"
                                    clearable
                                    >
                                    <el-option
                                        v-for="item in dict.type.ls_charging_contracted_unit"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    ></el-option>
                                    </el-select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <el-form-item
                                label="供电机构"
                                prop="supplyMechanism"
                                :label-width="formLabelWidth"
                            >
                            <el-select
                                    v-model="merchantOperatorInfo.form.supplyMechanism"
                                    placeholder="请选择供电机构"
                                    style="width: 100%"
                                    clearable
                                    >
                                    <el-option
                                        v-for="item in dict.type.ls_charging_contracted_unit"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    ></el-option>
                                    </el-select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <el-form-item
                                label="是否承担供电电费"
                                prop="isElectricityFeePaid"
                                :label-width="formLabelWidth"
                            >
                                <el-select
                                    v-model="merchantOperatorInfo.form.isElectricityFeePaid"
                                    placeholder="请选择是否承担供电电费"
                                    style="width: 100%"
                                    clearable
                                    >
                                    <el-option
                                        v-for="item in dict.type.ls_charging_status"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
        </div>

        <div class="info-card" >
            <div class="card-head">
                <div class="before-icon"></div>
                <div class="card-head-text">场站外围信息</div>
            </div>

            <div class="form-wrap">
                <el-form :model="stationPeripheralInfo.form" :rules="stationPeripheralInfo.rules" ref="stationPeripheralInfoForm"  label-position="top">
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-form-item
                                label="设计单位"
                                prop="designUnit"
                                :label-width="formLabelWidth"
                            >
                                <el-input 
                                    v-model="stationPeripheralInfo.form.designUnit" 
                                    placeholder="请输入设计单位"
                                ></el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <el-form-item
                                label="设计单位联系人"
                                prop="designUnitContact"
                                :label-width="formLabelWidth"
                            >
                                <el-input 
                                    v-model="stationPeripheralInfo.form.designUnitContact" 
                                    placeholder="请输入设计单位联系人"
                                ></el-input>
                            </el-form-item>
                        </el-col>

                        <!-- <el-col :span="6">
                            <el-form-item
                                label="设备厂家"
                                prop="equipmentManufacturer"
                                :label-width="formLabelWidth"
                            >
                                <el-input 
                                    v-model="stationPeripheralInfo.form.equipmentManufacturer" 
                                    placeholder="请输入设备厂家"
                                ></el-input>
                            </el-form-item>
                        </el-col> -->

                        <el-col :span="6">
                            <el-form-item
                                label="设备厂家联系人"
                                prop="equipmentManufacturerContact"
                                :label-width="formLabelWidth"
                            >
                                <el-input 
                                    v-model="stationPeripheralInfo.form.equipmentManufacturerContact" 
                                    placeholder="请输入设备厂家联系人"
                                ></el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-form-item
                                label="场站联系人"
                                prop="stationContact"
                                :label-width="formLabelWidth"
                            >
                                <el-input 
                                    v-model="stationPeripheralInfo.form.stationContact" 
                                    placeholder="请输入场站联系人"
                                ></el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <el-form-item
                                    label="站点质保期"
                                    prop="warrantyPeriod"
                                    :label-width="formLabelWidth"
                            >
                                <el-date-picker
                                    type="daterange"
                                    v-model="stationPeripheralInfo.form.warrantyPeriod" 
                                    value-format="yyyy-MM-dd"
                                >
                                </el-date-picker>
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <el-form-item
                                label="钥匙管理人"
                                prop="keyManager"
                                :label-width="formLabelWidth"
                            >
                                <el-input 
                                    v-model="stationPeripheralInfo.form.keyManager" 
                                    placeholder="请输入钥匙管理人"
                                ></el-input>
                            </el-form-item>
                        </el-col>

                       
                    </el-row>

                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item
                                label="收资表"
                                prop="revenueForm"
                                :label-width="formLabelWidth"
                            >
                                <Upload
                                    v-model="stationPeripheralInfo.form.revenueForm"
                                    :limit="1"
                                    :maxSizeMB="10"
                                    :accept="'image/jpeg,image/png,image/jpg,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document'"
                                    @input="handleUpload"
                                    @success="handleUploadSuccess"
                                />
                                <!-- <el-input 
                                    v-model="stationPeripheralInfo.form.revenueForm" 
                                    placeholder="请输入收资表"
                                ></el-input> -->
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-row :gutter="20">
                        <el-col :span="24">
                            <el-form-item
                                label="备注"
                                prop="remarks"
                                :label-width="formLabelWidth"
                            >
                                <el-input
                                    v-model="stationPeripheralInfo.form.remarks"
                                    type="textarea"
                                    :rows="3"
                                    placeholder="请输入备注"
                                ></el-input>
                            </el-form-item>
                        </el-col>  
                    </el-row>
                </el-form>
            </div>
        </div>


        <div class="info-card" >
            

            <div class="form-wrap">
                <StationConstructionTable 
                    :data="stationConstructionInfo"
                    @transformerDataChange="transformerDataChange"
                    @monitorDataChange="monitorDataChange"
                    @fireFightDataChange="fireFightDataChange"
                    @ancillaryDataChange="ancillaryDataChange"
                    @chargePileDataChange="chargePileDataChange"
                    />
            </div>
        </div>  


        <div class="info-card" v-if="isContainChargeAgent" >
            <div class="card-head">
                <div class="before-icon"></div>
                <div class="card-head-text">场站扣费模式</div>
            </div>

            <div class="form-wrap">
                <el-form :model="stationDeductionModeInfo.form" :rules="stationDeductionModeInfo.rules" ref="stationDeductionModeInfoForm"  label-position="top">
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-form-item
                                label="扣费模式"
                                prop="deductionMode"
                                :label-width="formLabelWidth"
                            >
                                <el-select
                                    v-model="stationDeductionModeInfo.form.deductionMode"
                                    placeholder="请选择扣费模式"
                                    multiple
                                    style="width: 100%"
                                >
                                    <el-option
                                        v-for="item in dict.type.ls_charging_billing_mode"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <el-form-item
                                label="启动金额阈值"
                                prop="startAmount"
                                :label-width="formLabelWidth"
                            >
                                <el-input-number
                                    v-model="stationDeductionModeInfo.form.startAmount"
                                    :min="0"
                                    :precision="2" 
                                    :step="0.01"
                                    controls-position="right"
                                >
                                </el-input-number>
                                元
                            </el-form-item>
                        </el-col>

                        <el-col :span="6">
                            <el-form-item
                                label="结束金额阈值"
                                prop="endAmount"
                                :label-width="formLabelWidth"
                            >
                                <el-input-number
                                    v-model="stationDeductionModeInfo.form.endAmount"
                                    :min="0"
                                    :precision="2" 
                                    :step="0.01"
                                    controls-position="right"
                                >
                                </el-input-number>
                                元
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            
        </div>

        <div class="bottom-button-wrap">
            <div>
                <el-button @click="handleCancel">取消</el-button>
                <el-button @click="handleSave" type="primary">保存</el-button>

            </div>

        </div>

        

        <el-dialog :visible.sync="previewDialogVisible" :modal="false" width="60%">
            <img width="100%" :src="dialogImageUrl" alt="">
        </el-dialog>
    </div>
    
  </template>
  
  <script>

import MapItem from './components/mapItem.vue'
import StationConstructionTable from './components/stationConstructionTable.vue'
import  Upload from '@/components/Upload/index'


import {
  getAssetUnit,
  getOperationUnit,
  getMaintenanceUnit,
  getServiceTag,
  createStation,
  updateStation,
  getStationDetail,
} from '@/api/station/index';
  
  export default {
    components: {
        MapItem,
        StationConstructionTable,
        Upload
    },
    dicts: [
        'ls_charging_station_type', // 站点类型
        'ls_charging_operation_mode', // 运营模式
        'ls_charging_asset_property', // 资产属性
        'ls_charging_construction', // 建设场所
        'ls_charging_status', // 是否状态
        'ls_charging_adjustable_type', // 可控类型
        'ls_charging_area_type', // 所属区域
        'ls_charging_stationChargeType', //充电站充电分类
        'ls_charging_parking_charge_type', // 停车场收费类型
        'ls_charging_station_tag', // 站点标签
        'ls_charging_parking_location', // 停车场位置
        'ls_charging_contracted_unit', // 签约单位 投资主体 供电机构
        'ls_charging_billing_mode', // 扣费模式
        
    ],
    data() {
     
  

      return {
        pageType: 'create',
        stationId: '',
        baseInfo: {
            form: {
                stationNo: '',
                stationName: '',
                stationType: '',
                operationMode: '',
                assetProperty: '',
                construction: '',
                buildDate: '',
                areaType: '',
                adjustableType: '',
                unifiedConstruction:'',
                consNos:'',
                maintenanceGroup: '',
                addressMap: {},
                stationUrl:[],
                stationIntroduce: '',
                siteGuide: '',
            },
            rules: {
                stationNo: [
                   { required: true, message: '请输入充电站编号', trigger: 'blur'}
                ],
                stationName: [
                    {required: true, message: '请输入充电站名称', trigger: 'blur'}
                ],
                stationType: [
                    {required: true, message: '请选择站点类型', trigger: 'blur'}
                ],
                operationMode: [
                    {required: true, message: '请选择运营模式', trigger: 'blur'}
                ],
                assetProperty: [
                    {required: true, message: '请选择资产属性', trigger: 'blur'}
                ],
                construction: [
                   { required: true, message: '建设站点', trigger: 'blur'}
                ],
                areaType: [
                    { required: true, message: '请选择所属区域', trigger: 'blur'}
                ],
                adjustableType: [
                    { required: true, message: '请选择可控类型', trigger: 'blur'}
                ],
                unifiedConstruction: [
                    { required: true, message: '请选择是否统建统服', trigger: 'blur'}
                ],
                consNos: [
                    { required: true, message: '请输入营销户号', trigger: 'blur'}
                ],
                maintenanceGroup: [
                    { required: true, message: '请选择运维分组', trigger: 'blur'}
                ],
                addressMap: [
                    { required: true, message: '请填写完整', trigger: 'blur' },
                    {
                        validator: this.validateAddressMap, // 自定义校验方法
                        trigger: 'blur',
                    },
                ],
            }
        },
        dialogImageUrl: '',
        previewDialogVisible: false,
        formLabelWidth: '120px',

        stationOperatorInfo: {
            form: {
                stationChargeType: '',
                openFlag: '',
                openTime: ['', ''], 
                stationTag:'',
                parkingChargeType: '',
                parkingLocation: '',
                serviceTag: [],
                serviceTel: '',
            },
            rules: {
                stationChargeType: [
                    { required: true, message: '请选择充电站充电分类', trigger: 'blur'}
                ],
                openFlag: [
                    { required: true, message: '请选择是否开放', trigger: 'blur'}
                ],
                openTime: [
                    { required: true, message: '请选择完整的开放时间', trigger: 'blur'},
                    {
                        validator: this.validateOpenTime,
                        trigger: 'change',
                    },
                ],
            }
        },

        serviceTagList: [],


        merchantOperatorInfo: {
            form: {
                operatingUnit: '',
                assetUnit: '',
                maintenanceUnit: '',
                maintenance: '',
                contractedUnit: '',
                investors: '',
                supplyMechanism: '',
                isElectricityFeePaid: '',
            },
            rules: {
                operatingUnit: [
                    { required: true, message: '请选择运营单位', trigger: 'blur'}
                ],
                assetUnit: [
                    { required: true, message: '请选择资产单位', trigger: 'blur'}
                ],
                // maintenanceUnit: [
                //     { required: true, message: '请选择运维单位', trigger: 'blur'}
                // ]
            }
        },

        assetUnitList: [], // 资产单位列表
        operationUnitList: [], // 运营单位列表
        maintenanceUnitList: [], // 运维单位列表
        

        operatorUnitList: [
            { label: '国网湖南电动汽车有限公司', value: '01' },
        ],
        
        stationPeripheralInfo: {
            form: {
                designUnit: '',
                designUnitContact: '',
                // equipmentManufacturer: '',
                equipmentManufacturerContact: '',
                stationContact: '',
                warrantyPeriod: ['',''],
                keyManager: '',
                revenueForm: [],
                remarks: '',
            },
            rules: {
                
            }
        },

        stationConstructionInfo: {
            stationTransformerDtoList: [],
            stationMonitorDtoList:[],
            stationFirefightDtoList: [],
            stationSubsidiaryDtoList: [],
            chargingStacksDtoList: [],
        },

        stationDeductionModeInfo: {
            form: {
                deductionMode:  [],
                startAmount: '',
                endAmount: '',
            },
            rules: {
                deductionMode: [
                    { required: true, message: '请选择扣费模式', trigger: 'blur'}
                ],
                startAmount: [
                    { required: true, message: '请输入启动金额阈值', trigger: 'blur'}
                ],
                endAmount: [
                    { required: true, message: '请输入结束金额阈值', trigger: 'blur'}
                ]
            }
        },

        isContainChargeAgent: true,
        
      };
    },
    computed: {},
    mounted() {
        this.getAssetUnit()
        this.getOperationUnit()
        this.getMaintenanceUnit()
        this.getServiceTag()

        const stationId = this.$route.query.stationId

        if(stationId) {
            this.pageType = 'edit'
            this.stationId = stationId
            this.getStationDetail(stationId)
        }
    },
    methods: {
        // 资产单位切换，控制场站扣费模式是否展示
        changeAssetUnit(){
            const {
                assetUnit
            } = this.merchantOperatorInfo.form

            this.stationDeductionModeInfo.form = {
                deductionMode: [],
                startAmount: '',
                endAmount: ''
            }

            let  isContainChargeAgent = false

            this.assetUnitList.forEach(item => {
                if(item.value === assetUnit) {
                    isContainChargeAgent = item.isContainChargeAgent
                }
            })

            this.isContainChargeAgent = isContainChargeAgent

        },
    handleUpload(file) {
      console.log('上传中:', file)
    },
    handleUploadSuccess(file) {
      console.log('上传成功:', file)
    },
        // 获取资产单位
      async getAssetUnit() {
        const [err, res] = await getAssetUnit({})
        if (err) return
        this.assetUnitList = res.data
        
      },

      // 获取运营单位
      async getOperationUnit() {
        const [err, res] = await getOperationUnit({})
        if (err) return
        this.operationUnitList = res.data
      },

      // 获取运维单位
      async getMaintenanceUnit() {
        const [err, res] = await getMaintenanceUnit({})
        if (err) return
        this.maintenanceUnitList = res.data
      },

      // 获取服务标签
      async getServiceTag() {
        const [err, res] = await getServiceTag({
            tagType: "2"
        })
        if (err) return

        const {data} = res
        const list = []
        data.forEach(element => {
            list.push({
                label: element.tagName,
                value: element.tagCode
            })
        });
        this.serviceTagList = list
      },

      // 获取充电站详情 编辑
      async getStationDetail(stationId) {
        const [err, res] = await getStationDetail({
            stationId
        })
        if (err) return
        console.log(res.data,'res.data')

         // 基础信息
        const {
            stationNo,
            stationName,
            stationType,
            operationMode,
            assetProperty,
            construction,
            buildDate,
            areaType,
            adjustableType,
            unifiedConstruction,
            consNos,
            maintenanceGroup,
            province,
            city,
            county,
            stationAddress,
            lon,
            lat,
            stationUrl,
            stationIntroduce,
            siteGuide,
        } = res.data

        this.baseInfo.form.stationNo = stationNo
        this.baseInfo.form.stationName = stationName
        this.baseInfo.form.stationType = stationType
        this.baseInfo.form.operationMode = operationMode
        this.baseInfo.form.assetProperty = assetProperty
        this.baseInfo.form.construction = construction
        this.baseInfo.form.buildDate = buildDate
        this.baseInfo.form.areaType = areaType
        this.baseInfo.form.adjustableType = adjustableType
        this.baseInfo.form.unifiedConstruction = unifiedConstruction
        this.baseInfo.form.consNos = consNos
        this.baseInfo.form.maintenanceGroup = maintenanceGroup
        this.baseInfo.form.addressMap = {
            province: province,
            city: city,
            county: county,
            stationAddress: stationAddress,
            lon: lon,
            lat: lat
        }

        const stationUrlList = stationUrl ? stationUrl.split(',') : []
        const tempStationUrlList = []
        stationUrlList.forEach(item => {
            tempStationUrlList.push({
                url: item
            })
        })

        this.baseInfo.form.stationUrl = tempStationUrlList
        this.baseInfo.form.stationIntroduce = stationIntroduce
        this.baseInfo.form.siteGuide = siteGuide


        // 场站运营信息
        const {
            stationChargeType,
            openFlag,
            runStartTime,
            runEndTime,
            stationTag,
            parkingChargeType,
            parkingLocation,
            serviceTag,
            serviceTel,
        } = res.data

        let openTime = []
        if(runStartTime && runEndTime){
            openTime = [runStartTime,runEndTime]
        }

        this.stationOperatorInfo.form.stationChargeType = stationChargeType
        this.stationOperatorInfo.form.openFlag = openFlag
        this.stationOperatorInfo.form.openTime = openTime
        this.stationOperatorInfo.form.stationTag = stationTag
        this.stationOperatorInfo.form.parkingChargeType = parkingChargeType
        this.stationOperatorInfo.form.parkingLocation = parkingLocation
        this.stationOperatorInfo.form.serviceTag = JSON.parse(serviceTag) 
        this.stationOperatorInfo.form.serviceTel = serviceTel


        // 商户运营信息
        const {
            // stationBuildList,
            operatingUnit,
            assetUnit,
            maintenanceUnit,
            maintenance,
            contractedUnit,
            investors,
            supplyMechanism,
            isElectricityFeePaid,
        }  = res.data


        // let assetUnit = ''
        // let operatingUnit = ''
        // let maintenanceUnit = ''
        // stationBuildList.forEach(item => {
        //     if (item.stationUnitType === '01') {
        //         assetUnit = item.operatorId
        //     } else if (item.stationUnitType === '02') {
        //         operatingUnit = item.operatorId
        //     } else if (item.stationUnitType === '03') {
        //         maintenanceUnit = item.operatorId
        //     }
        // })

        this.merchantOperatorInfo.form.assetUnit = assetUnit
        this.merchantOperatorInfo.form.operatingUnit = operatingUnit
        this.merchantOperatorInfo.form.maintenanceUnit = maintenanceUnit
        this.merchantOperatorInfo.form.maintenance = maintenance
        this.merchantOperatorInfo.form.contractedUnit = contractedUnit
        this.merchantOperatorInfo.form.investors = investors
        this.merchantOperatorInfo.form.supplyMechanism = supplyMechanism
        this.merchantOperatorInfo.form.isElectricityFeePaid = isElectricityFeePaid



        // 场站外围信息
        const  {
            designUnit,
            designUnitContact,
            equipmentManufacturerContact,
            stationContact,
            warrantyPeriodStartTime,
            warrantyPeriodEndTime,
            keyManager,
            revenueForm,
            revenueFormName,
            remarks,
        } = res.data

        let warrantyPeriod = []
        if(warrantyPeriodStartTime && warrantyPeriodEndTime){
            warrantyPeriod = [warrantyPeriodStartTime,warrantyPeriodEndTime]
        }


        let revenueFormList = []
        if(revenueForm&& revenueFormName ){
            revenueFormList = [
                {
                    url:revenueForm,
                    name:revenueFormName
                }
            ]
        }


        this.stationPeripheralInfo.form.designUnit = designUnit
        this.stationPeripheralInfo.form.designUnitContact = designUnitContact
        this.stationPeripheralInfo.form.equipmentManufacturerContact = equipmentManufacturerContact
        this.stationPeripheralInfo.form.stationContact = stationContact
        this.stationPeripheralInfo.form.warrantyPeriod = warrantyPeriod
        this.stationPeripheralInfo.form.keyManager = keyManager
        this.stationPeripheralInfo.form.revenueForm = revenueFormList
        this.stationPeripheralInfo.form.remarks = remarks

        // 场站建设信息
        const {
            stationTransformerDtoList,
            stationMonitorDtoList,
            stationFirefightDtoList,
            stationSubsidiaryDtoList,
            chargingStacksDtoList,
        } = res.data

        this.stationConstructionInfo.stationTransformerDtoList = stationTransformerDtoList
        this.stationConstructionInfo.stationMonitorDtoList = stationMonitorDtoList
        this.stationConstructionInfo.stationFirefightDtoList = stationFirefightDtoList
        this.stationConstructionInfo.stationSubsidiaryDtoList = stationSubsidiaryDtoList
        this.stationConstructionInfo.chargingStacksDtoList = chargingStacksDtoList

        // 场站扣费模式
        const {
            stationFinanceCfgRequest
        } = res.data

        this.stationDeductionModeInfo.form.deductionMode = stationFinanceCfgRequest.billingMode
        this.stationDeductionModeInfo.form.startAmount = stationFinanceCfgRequest.minStartAmt
        this.stationDeductionModeInfo.form.endAmount = stationFinanceCfgRequest.minStopAmt
      },

        // 自定义站点地址校验函数
        validateAddressMap(rule, value, callback){
            console.log(rule,value,'111')
              if (value?.province === '900000') {
                callback();
                return;
              }
              if (
                !value?.stationAddress ||
                !value?.province ||
                !value?.city ||
                !value?.county ||
                !value?.lon ||
                !value?.lat
              ) {
                callback(Error('请填写完整'));
              }
              callback();
        },

        // 自定义开发时间校验
        validateOpenTime(rule, value, callback){
            if (!value || !value[0] || !value[1]) {
                callback(new Error('请选择完整的开放时间'));
                return;
            }

            const [startTime, endTime] = value;
            const start = new Date(`1970-01-01T${startTime}`);
            const end = new Date(`1970-01-01T${endTime}`);

            if (end <= start) {
                callback(new Error('结束时间必须晚于开始时间'));
            } else {
                callback();
            }
        },

        // 取消
        handleCancel() {
            this.$router.back();
        },
        async handleSave() {
            // 这里可以执行提交逻辑
            Promise.all([
                this.validateForm('baseInfoForm'),
                this.validateForm('stationOperatorInfoForm'),
                this.validateForm('merchantOperatorInfoForm'),
                this.isContainChargeAgent ? this.validateForm('stationDeductionModeInfoForm'):Promise.resolve(),
            ])
            .then(async () => {
                // 所有表单校验通过
                // this.$message.success('所有表单校验通过，提交成功');
                const {
                    stationNo,
                    stationName,
                    stationType,
                    operationMode,
                    assetProperty,
                    construction,
                    buildDate,
                    areaType,
                    adjustableType,
                    unifiedConstruction,
                    consNos,
                    maintenanceGroup,
                    addressMap,
                    stationUrl,
                    stationIntroduce,
                    siteGuide,
                } = this.baseInfo.form

                const {
                    stationChargeType,
                    openFlag,
                    openTime, 
                    stationTag,
                    parkingChargeType,
                    parkingLocation,
                    serviceTag,
                    serviceTel,
                } = this.stationOperatorInfo.form

                let runStartTime = '';
                let runEndTime = '';
                if (openTime && openTime.length > 0) {
                    runStartTime = openTime[0];  
                    runEndTime = openTime[1];
                }

                const  {
                    operatingUnit,
                    assetUnit,
                    maintenanceUnit,
                    maintenance,
                    contractedUnit,
                    investors,
                    supplyMechanism,
                    isElectricityFeePaid,
                } = this.merchantOperatorInfo.form
            
                
                const stationBuildList = []
                if (assetUnit) {
                    stationBuildList.push(
                        {
                            stationUnitType: '01',
                            operatorId: assetUnit,
                        },
                    )
                }

                if(operatingUnit) {
                    stationBuildList.push(
                        {
                            stationUnitType: '02',
                            operatorId: operatingUnit,
                        },
                    )
                }

                if(maintenanceUnit) {
                    stationBuildList.push(
                        {
                            stationUnitType: '03',
                            operatorId: maintenanceUnit,
                        },
                    )
                }


                const {
                    designUnit,
                    designUnitContact,
                    equipmentManufacturerContact,
                    stationContact,
                    warrantyPeriod, // todo 站点质保期范围
                    keyManager,
                    revenueForm,
                    remarks,
                } = this.stationPeripheralInfo.form

                console.log(
                    this.stationPeripheralInfo.form, 'stationPeripheralInfo'
                )

                let warrantyPeriodStartTime = '';
                let warrantyPeriodEndTime = '';
                if (warrantyPeriod && warrantyPeriod.length > 0) {
                    warrantyPeriodStartTime = warrantyPeriod[0];  
                    warrantyPeriodEndTime = warrantyPeriod[1];
                }

                const {
                    stationTransformerDtoList,
                    stationMonitorDtoList,
                    stationFirefightDtoList,
                    stationSubsidiaryDtoList,
                    chargingStacksDtoList,
                } = this.stationConstructionInfo


                const {
                    deductionMode,
                    startAmount,
                    endAmount,
                } = this.stationDeductionModeInfo.form

                const stationUrlList = []
                stationUrl.forEach(item => {
                    stationUrlList.push(item.url)
                })


                // 收资表
                const revenueFormUrl = []
                 const revenueFormName = []
                revenueForm.forEach(item => {
                    revenueFormUrl.push(item.url)
                    revenueFormName.push(item.name)
                })



                const parmas = {
                    // 基础信息
                    stationNo,
                    stationName,
                    stationType,
                    operationMode,
                    assetProperty,
                    construction,
                    buildDate,
                    areaType,
                    adjustableType,
                    unifiedConstruction,
                    consNos,
                    maintenanceGroup,
                    province: addressMap.province,
                    city: addressMap.city,
                    county: addressMap.county,
                    stationAddress: addressMap.stationAddress,
                    lon: addressMap.lon,
                    lat: addressMap.lat,
                    stationUrl: stationUrlList.join(','),
                    stationIntroduce,
                    siteGuide,

                    // 场站运营信息
                    stationChargeTypeList: [stationChargeType],
                    openFlag,
                    runStartTime,
                    runEndTime,
                    stationTag,
                    parkingChargeType,
                    parkingLocation,
                    serviceTag: JSON.stringify(serviceTag),
                    serviceTel,

                    // 商户运营信息
                    operatingUnit,
                    assetUnit,
                    maintenanceUnit,
                    // stationBuildList: [
                    //     {
                    //         stationUnitType: '01',
                    //         operatorId: assetUnit,
                    //     },
                    //     {
                    //         stationUnitType: '02',
                    //         operatorId: operatingUnit,
                    //     },
                    //     {
                    //         stationUnitType: '03',
                    //         operatorId: maintenanceUnit,
                    //     }
                    // ],
                    stationBuildList,
                    maintenance,
                    contractedUnit,
                    investors,
                    supplyMechanism,
                    isElectricityFeePaid,

                    // 场站外围信息
                    designUnit,
                    designUnitContact,
                    equipmentManufacturerContact,
                    stationContact,
                    // warrantyPeriod: '',
                    warrantyPeriodStartTime,
                    warrantyPeriodEndTime,
                    keyManager,
                    revenueForm: revenueFormUrl.join(','),
                    revenueFormName: revenueFormName.join(','),
                    remarks,

                    // 场站建设信息
                    stationTransformerDtoList,
                    stationMonitorDtoList,
                    stationFirefightDtoList,
                    stationSubsidiaryDtoList,
                    chargingStacksDtoList,

                    // 场站扣费模式
                    stationFinanceCfgRequest: {
                        billingMode:deductionMode,
                        minStartAmt:startAmount,
                        minStopAmt:endAmount,
                    }
                }

                console.log(parmas, 'parmas')

                if(this.pageType === 'create') {
                    const [err,res] = await createStation(
                        parmas
                    )

                    console.log(res,err, 'createStation')
                    if (err){ 
                        return this.$message.error(err.message || '新增站点失败');
                    }
                        this.$message({
                            type: 'success',
                            message: '新增成功!'
                        });

                        setTimeout(() => {
                            this.$router.back();
                        }, 2000);

                } else if(this.pageType === 'edit') {
                    const [err,res] = await updateStation(
                        {
                            ...parmas,
                            stationFinanceCfgRequest: {
                                billingMode:deductionMode,
                                minStartAmt:startAmount,
                                minStopAmt:endAmount,
                                stationId: this.stationId
                            },
                            stationId: this.stationId
                        }
                    )

                    if (err){ 
                        return this.$message.error(err.message || '编辑站点失败');
                    }
                        this.$message({
                            type: 'success',
                            message: '编辑成功!'
                        });

                        setTimeout(() => {
                            this.$router.back();
                        }, 2000);
                }

                
                


            })
            .catch((error) => {
                // 有表单校验失败
                this.$message.error('表单校验失败，请检查输入');
            });
        },

        // 校验单个表单
        validateForm(formRef) {
            return new Promise((resolve, reject) => {
                this.$refs[formRef].validate((valid) => {
                    if (valid) {
                        resolve(); // 校验通过
                    } else {
                        reject(new Error(`${formRef} 校验失败`)); // 校验失败
                    }
                });
            });
        },
        handleRemove(file) {
            console.log(file);
            this.baseInfo.form.stationUrl = this.baseInfo.form.stationUrl.filter((item) => item.uid !== file.uid);
        },
        handlePictureCardPreview(file) {
            this.dialogImageUrl = file.url;
            this.previewDialogVisible = true;
        },

        handleSuccess(response, file, fileList) {
            this.baseInfo.form.stationUrl = fileList;
        },
        // 文件状态改变时的回调
        handleChange(file, fileList) {
            this.baseInfo.form.stationUrl = fileList;
        },
        handleDownload(file) {
            console.log(file);
        },

        // 变压器数据变化
        transformerDataChange(list) {
            this.stationConstructionInfo.stationTransformerDtoList = list;
        }, 
        // 监控设备数据变化
        monitorDataChange(list) {
            this.stationConstructionInfo.stationMonitorDtoList = list;
        },
        // 消防设备数据变化
        fireFightDataChange(list) {
            this.stationConstructionInfo.stationFirefightDtoList = list;
        },
        // 场站附属设施数据变化
        ancillaryDataChange(list) {
            this.stationConstructionInfo.stationSubsidiaryDtoList = list;
        },
        // 充电堆数据变化
        chargePileDataChange(list) {
            this.stationConstructionInfo.chargingStacksDtoList = list;
        }

    },
  };
  </script>
  
  <style lang="scss" scoped>
  .container-full {
    background-color: rgba(244, 246, 249, 1);
    padding-bottom: 16px;
    min-height: calc(100% - 80px);
    overflow-y: auto;
  }

  
  .info-card {
  margin: 16px;
  border-radius: 5px;
  border: 1px solid #fff;
  overflow: hidden;
  background-color: #fff;
  // min-height: 300px;
  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    .before-icon {
        width: 3px;
        height: 16px;
        background-image: url('~@/assets/station/consno-before.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        margin-right: 8px;
    }
    .card-head-text {
        flex:1;
        font-weight: 500;
        font-size: 16px;
        color: #12151A;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
    .button-wrap {
      display: flex;
      .invite-btn {
          background-color: #1ab2ff;
          border-color: #1ab2ff;
        }
        ::v-deep .el-button--small {
          font-size: 14px;
        }
      .distribution {
          margin-left: 24px;
          margin-right: 24px;
          display: flex;
          align-items: center;
        }
    }
  }
  
  .people-table-wrap {
    ::v-deep .bd3001-table-select-box {
      display: none;
    }
    ::v-deep .bd3001-button {
      width: 100%;
    }
  }
  .form-wrap {
      padding: 0 16px 16px 16px;
      .custom-header {
          background: -webkit-gradient(linear, left top, left bottom, from(rgba(0, 149, 255, 0.5)), to(rgba(87, 152, 255, 0))), #f5faff;
          background: linear-gradient(180deg, rgba(0, 149, 255, 0.5) 0%, rgba(87, 152, 255, 0) 100%), #f5faff;
          background-repeat: no-repeat;
      }
    }
  }

  ::v-deep .el-input {
    width: 100%;
  }
  ::v-deep .el-range-editor.el-input__inner {
    width: 100%;
  }

  ::v-deep .el-input-number--mini {
    width: 80%;
  }

.upload-disabled {
  pointer-events: none; /* 禁用点击事件 */
  opacity: 0.5; /* 降低透明度表示禁用状态 */
}

.bottom-button-wrap {
    height: 86px;
    margin-top: 16px;
    width: 100%;
    background-color: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-right: 32px;
    box-sizing: border-box;
}

  </style>
  