<template>
    <div class="container container-float " style="padding: 0 0 100px 0;">
        <div class="table-wrap">
            <BuseCrud
                ref="crud"
                :loading="loading"
                :filterOptions="filterOptions"
                :tablePage="tablePage"
                :tableColumn="tableColumn"
                :tableData="tableData"
                :pagerProps="pagerProps"
                :modalConfig="modalConfig"
                @loadData="loadData"
            >
            <template slot="defaultHeader">
                <div>
                    <div class="card-head">
                        <div class="card-head-text">计费下发列表</div>

                        <div class="top-button-wrap">
                            
                            <el-button
                                type="primary"
                                @click="() => handleAdd()"
                            >
                                计费下发
                            </el-button>

                        </div>
                    </div>
                    

                    
                </div>
                
            </template>
                <template slot="distributionStatus" slot-scope="{ row }">
                    <div v-if="Number(row.successCount) + Number(row.failCount) === 0">
                        /
                    </div>

                    <div v-else  class="progress-wrap">
                        <div class="progress-gray">
                            <div class="progress-green"    :style="`width:${Number(row.successCount) / Number(row.pileCount) * 100}%`"></div>
                        </div>
                        <div style="display: flex;">
                            <div style="color: #00C8A7; margin-left: 5px;">{{ row.successCount }}</div>
                            /
                            {{ Number(row.pileCount) }}
                        </div>
                       
                    </div>
                </template>

                <template slot="operate" slot-scope="{ row }">
                    <!-- <div class="menu-box">
                        <el-button
                            class="button-border"
                            @click="handleDetail(row)"
                        >
                            详情
                        </el-button>

                        <el-button
                            class="button-border"
                            @click="handleIssuance(row)"
                        >
                           重新下发
                        </el-button>

                        <el-button
                            type="primary"
                            plain
                            @click="handleAudit(row)"
                        >
                            审核
                        </el-button>

                        <el-button
                            type="primary"
                            plain
                            @click="handleDelete(row)"
                        >
                            删除
                        </el-button>
                    
                    </div> -->

                    <el-dropdown trigger="click">
                      <el-button
                        class="button-border"
                        type="primary"
                        plain
                      >
                        操作
                      </el-button>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item >
                          <div    @click="handleDetail(row)">
                            详情
                          </div>
                        </el-dropdown-item>

                        <el-dropdown-item >
                          <div    @click="handleIssuance(row)">
                            重新下发
                          </div>
                        </el-dropdown-item>



                        <el-dropdown-item >
                          <div    @click="handleDelete(row)">
                            删除
                          </div>
                        </el-dropdown-item>

                        

                      </el-dropdown-menu>
                    </el-dropdown>
                
                </template>

            </BuseCrud>
        </div>
    </div>
    
  </template>
  
  <script>
  import {
    getBillBindList,
    reBillIssuance,
    deleteBillIssuance,
  } from '@/api/billingIssuance/index'

import StatusDot from '@/components/Business/StatusDot';
import StatusInfo from '@/components/Business/StatusInfo';


  
    export default {
    components: {
        StatusDot,
        StatusInfo,
    },
    dicts: [
        'ls_charging_audit_type',
        'ls_charging_billing_issue_status',
        'ls_charging_billing_bind_type',
        'ls_charging_contracted_unit',
    ],
    data() {
      return {
        loading: false,
        tablePage: { total: 0, currentPage: 1, pageSize: 10 },
        tableColumn:[
            {
                type: 'seq',
                title: '序号',
                width: 60,
                minWidth: 60,
            },
            {
                field: 'chcNo',
                title: '计费编号',
                minWidth: 120, 
            },
            {
                field: 'chcName',
                title: '计费名称',
                minWidth: 120,
            },
            {
                field: 'bindType',
                title: '下发类型',
                minWidth: 120,
                formatter: ({ cellValue }) => {
                        return this.selectDictLabel(
                            this.dict.type.ls_charging_billing_bind_type,
                            cellValue
                        );
                    },
            },
            // {
            //     field: 'chargeType',
            //     title: '收费类型',
            //     minWidth: 120,
            // },
            {
                field: 'stationCount',
                title: '下发站数量',
                minWidth: 120,
            },
            {
                field: 'pileCount',
                title: '下发桩数量',
                minWidth: 120,
            },
            {
                field: 'distributionStatus',
                title: '下发情况(成功/全部)',
                minWidth: 200,
                slots: { default: 'distributionStatus' },
            },
            {
                field: 'validStartTime',
                title: '生效时间',
                minWidth: 120,
            },
            {
                field: 'issueStatusStatistic',
                title: '下发状态',
                minWidth: 120,
                // formatter: ({ cellValue }) => {
                //         return this.selectDictLabel(
                //             this.dict.type.ls_charging_billing_issue_status,
                //             cellValue
                //         ) || '-';
                //     },
                slots: {
                    // 自定义render函数
                    default: ({ row }) => {
                        return (
                            <StatusInfo
                                value={row.issueStatusStatistic}
                                dictValue={this.dict.type.ls_charging_billing_issue_status}
                                colors={['medium', 'low','success','medium','high','low' ]}
                            ></StatusInfo>
                            );
                        },
                    },
                
            },
            {
                field: 'createUser',
                title: '申请人',
                minWidth: 120,
            },
            {
                field: 'createUnit',
                title: '申请单位',
                minWidth: 120,
                formatter: ({ cellValue }) => {
                        return this.selectDictLabel(
                            this.dict.type.ls_charging_contracted_unit,
                            cellValue
                        );
                    },
            },
            {
                field: 'createTime',
                title: '申请时间',
                minWidth: 120,
            },
            {
                field: 'approvalStatusc',
                title: '审核状态',
                minWidth: 110,
                fixed: 'right',
                slots: {
                    // 自定义render函数
                    default: ({ row }) => {
                        return (
                        <StatusDot
                            value={row.approvalStatus}
                            dictValue={this.dict.type.ls_charging_audit_type}
                            colors={['warning', 'success', 'danger']}
                        ></StatusDot>
                        );
                    },
                },
            },
            {
                title: '操作',
                slots: { default: 'operate' },
                width: 80,
                align: 'center',
                fixed: 'right',
            },
        ],
        tableData: [],
        pagerProps: {
            layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
        },
        params: {
            chcNo: '',
            chcName: '',
            bindType:  '',
            issueStatus: '',
            approvalStatus: '',
            applyDate: [],
        },
      };
    },

    computed: {
        filterOptions() {
            return {
                config: [
                    {
                        field: 'chcNo',
                        title: '计费编号',
                        element: 'el-input',
                        props: { placeholder: '请输入' }  // 图片中对应输入框提示文字
                    },
                    {
                        field: 'chcName',
                        title: '计费名称',
                        element: 'el-input',
                        props: { placeholder: '请输入' }
                    },
                    {
                        field: 'bindType',
                        title: '下发类型',
                        element: 'el-select',
                        props: {
                            placeholder: '请选择',
                            options:  this.dict.type.ls_charging_billing_bind_type,
                        }
                    },
                    {
                        field: 'issueStatus',
                        title: '下发状态',
                        element: 'el-select',
                        props: {
                            placeholder: '请选择',
                            options:  this.dict.type.ls_charging_billing_issue_status,
                        }
                    },
                    {
                        field: 'approvalStatus',
                        title: '审核状态',
                        element: 'el-select',
                        props: {
                            placeholder: '请选择',
                            options: this.dict.type.ls_charging_audit_type,
                        }
                    },
                    {
                        field: 'applyDate',
                        title: '申请时间',
                        element: 'el-date-picker',
                        props: {
                            type: 'daterange',
                            options: [],
                            valueFormat: 'yyyy-MM-dd',
                        }
                    },


                ],
                params: this.params,
            };
        },

        modalConfig() {
            return {
                addBtn: false,
                viewBtn: false,
                menu: false,
                editBtn: false,
                delBtn: false,
            }
        },
    },
    mounted() {
        this.loadData();

    },
    methods: {
        // 下发计费
        handleAdd() {
            this.$router.push({
                path: '/v2g-charging-web/operatorManage/chargingManage/billingIssuance/create',
            })
        },

         // 审核
         handleAudit(row) {
            const { id } = row;
            this.$router.push({
                path: '/v2g-charging-web/operatorManage/chargingManage/billingIssuance/audit',
                query: {
                    id,
                },
            })
        },

        // 详情
        handleDetail(row) {
            const { billMissionId } = row;
            this.$router.push({
                path: '/v2g-charging-web/operatorManage/chargingManage/billingIssuance/detail',
                query: {
                    billMissionId,
                },
            })
        },

        // 重新下发
        async handleIssuance(row) {
            const { billMissionId } = row;

            this.$confirm('确定重新下发吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            })
                .then(async () => {
                    const [err, res] = await reBillIssuance({
                        billMissionId
                    })
                
                    if(err) {
                        return
                    }

                    this.$message({
                        type: 'success',
                        message: '下发成功!'
                    });
                })
                .catch(() => {});
    
           
        },

        async loadData() {
                const  {
                    chcNo,
                    chcName,
                    bindType,
                    issueStatus,
                    approvalStatus,
                    applyDate,
                } = this.params;

                let createStartTime = ''
                let createEndTime = ''
                if (applyDate && applyDate.length > 0) {
                    createStartTime = applyDate[0]
                    createEndTime = applyDate[1]
                }


                const params = {
                    chcNo,
                    chcName,
                    bindType,
                    issueStatus,
                    approvalStatus,
                    createStartTime,
                    createEndTime,
                    pageNum: this.tablePage.currentPage,
                    pageSize: this.tablePage.pageSize,
                }


                this.loading = true;

                const [err, res] = await getBillBindList(params)

                this.loading = false;
                
                if (err) return 
                const { data, total } = res;
                this.tableData = data;
                this.tablePage.total = total;
        },

        // 删除计费
        handleDelete(row) {
            const { billMissionId } = row
            
            this.$confirm('确定删除吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(async () => {
                const [err, res] = await deleteBillIssuance({
                    billMissionId
                })
                if (err) return

                this.$message.success('删除成功')
                this.loadData()
            })
            .catch(() => {});
        }
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .container-full {
    background-color: rgba(244, 246, 249, 1);
    padding-bottom: 16px;
    min-height: calc(100% - 80px);
    overflow-y: auto;
  }


  .table-wrap {
    ::v-deep .bd3001-table-select-box {
        display: none;
    }
    ::v-deep .bd3001-header  {
        display: block;
    }
    ::v-deep .bd3001-button {
        display: block !important;
    }
    
    .card-head {
        // position: relative; 
        height: 56px;
        padding: 0 16px;
        display: flex;
        align-items: center;
        margin-top: -20px;
        .card-head-text {
        flex:1;
        width: 520px;
            height: 26px;
            background-image: url('~@/assets/images/bg-title.png');
            background-size: 520px 26px;
            background-repeat: no-repeat;
            font-size: 20px;
            font-style: normal;
            font-weight: 500;
            line-height: 16px;
            padding-left: 36px;
            color: #21252e;
        &::before {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            left: -3px; /* 调整这个值来改变边框的宽度 */
            width: 0;
            border-top: 3px solid transparent;
            border-bottom: 3px solid transparent;
            border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
        }
        }   

    }

    .card-head-after {
        width: 100%;
        height: 1px;
        background-color: #DCDEE2;
        margin-bottom: 16px;
    }
    .info-wrap {
        margin-top: 16px;
        display: flex;
        flex-wrap: wrap;
        gap: 16px;

        .info-item {
            background-color: #FAFBFC;
            flex: 1 1 0;
            // min-width: 180px;
            
            border-radius: 5px;
            padding: 8px 24px;
            box-sizing: border-box;
            // margin-right: 16px;
            display: flex;
            .info-icon {
                width: 42px;
                height: 42px;
            }
            .info-right-wrap {
                flex:1;
                margin-left: 8px;
                .info-title {
                    font-weight: 400;
                    font-size: 14px;
                    line-height: 14px;
                    margin-bottom: 8px;
                }
                .info-number {
                    font-size: 20px;
                    font-weight: 500;
                    .info-unit {
                        font-size: 14px;
                        font-weight: 400;
                    }
                }
            }
        }
        .info-item:last-child {
            margin-right: 0;
        }
    }

    .top-button-wrap {
        display:flex;
        margin: 16px 0;
    }
}


.button-border {
    border: 0.01rem solid #217AFF;
    color: #217AFF;
    background-color: #fff;
}

.progress-wrap {
    display: flex;
    align-items: center;

    .progress-gray {
        width: 122px;
        height: 4px;
        border-radius: 2px;
        background-color: #F0F2F7;
        position: relative;
        .progress-green {
            height: 4px;
            border-radius: 2px;
            background-color: #00C8A7;
            position: absolute;
            left: 0;
            top: 0;
        }
    }
}
 
  </style>
  