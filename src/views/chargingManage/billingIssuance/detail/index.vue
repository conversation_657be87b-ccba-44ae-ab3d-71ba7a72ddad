<template>
  <div class="container container-float" style="padding: 0 0 100px 0">
    <div class="device-head">
      <img
        src="@/assets/charge/price-period-title-icon.png"
        class="device-head-icon"
      />

      <div class="device-info-wrap">
        <div class="device-title-wrap">
          <div class="device-title">计费名称：{{ billModelName }}</div>
          <!-- <div class="device-status">{{ status }}</div> -->
        </div>
        <div class="device-info-wrap">
          <el-row>
            <el-col :span="8">
              <span class="label">下发类型：</span>
              <span class="value">{{ issuanceType }}</span>
            </el-col>
            <el-col :span="8">
              <span class="label">生效时间：</span>
              <span class="value">{{ effectiveTime }}</span>
            </el-col>
          </el-row>
        </div>
      </div>

      <!-- <el-button
                type="primary"
                 @click="drawer = true"
            >
                审核轨迹
            </el-button> -->
    </div>

    <div class="info-card">
      <div class="card-head" style="margin-bottom: 8px">
        <div class="before-icon"></div>
        <div class="card-head-text">计费基础信息</div>
      </div>

      <div class="form-wrap">
        <el-row :gutter="20" style="margin-bottom: 24px">
          <el-col :span="8">
            <div style="display: flex">
              <div class="info-title">申请人：</div>
              <div class="info-detail">{{ baseInfo.applyer }}</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div style="display: flex">
              <div class="info-title">申请单位：</div>
              <div class="info-detail">{{ baseInfo.applyUnit }}</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div style="display: flex">
              <div class="info-title">申请时间：</div>
              <div class="info-detail">{{ baseInfo.applyTime }}</div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <div class="info-card">
      <div class="card-head" style="margin-bottom: 8px">
        <div class="before-icon"></div>
        <div class="card-head-text">下发桩列表</div>
      </div>

      <BuseCrud
        ref="crud"
        :loading="loading"
        :filterOptions="filterOptions"
        :tablePage="tablePage"
        :tableColumn="tableColumn"
        :tableData="tableData"
        :pagerProps="pagerProps"
        :modalConfig="modalConfig"
        @loadData="loadData"
        :tableOn="{
          'checkbox-change': handleCheckboxChange,
          'checkbox-all': handleCheckboxChange,
        }"
      >
        <template slot="defaultHeader">
          <div>
            <div class="table-head">
              <div class="table-head-button-wrap">
                <div class="choose-info-wrap">
                  共计
                  <div class="choose-number">{{ totalNumber }}</div>
                  条
                </div>

                <div class="ing-info-wrap">
                  下发中
                  <div class="ing-number">{{ ingNumber }}</div>
                  条
                </div>

                <div class="success-info-wrap">
                  下发中
                  <div class="success-number">{{ successNumber }}</div>
                  条
                </div>

                <div class="fail-info-wrap">
                  下发中
                  <div class="fail-number">{{ failNumber }}</div>
                  条
                </div>
              </div>

              <div class="top-button-wrap">
                <el-button type="primary" @click="() => handleBatchIssuance()">
                  重新下发
                </el-button>
              </div>
            </div>
          </div>
        </template>

        <template slot="operate" slot-scope="{ row }">
          <div class="menu-box">
            <el-button class="button-border" @click="hanleDetail(row)">
              计费对比
            </el-button>

            <el-button
              v-if="row.issueStatus === '04'"
              class="button-border"
              @click="handleIssuance(row)"
            >
              重新下发
            </el-button>
          </div>
        </template>
      </BuseCrud>
    </div>

    <compareModal
      ref="compareModal"
      :beforeData="beforeData"
      :beforeTimeList="beforeTimeList"
      :beforeChooseTime="beforeChooseTime"
      :beforeTableData="beforeTableData"
      :afterData="afterData"
      :afterTimeList="afterTimeList"
      :afterChooseTime="afterChooseTime"
      :afterTableData="afterTableData"
    />
  </div>
</template>

<script>
import compareModal from '../../components/compareModal.vue';
import StatusInfo from '@/components/Business/StatusInfo';

import {
  getBillIssuanceDetail,
  getBillIssuancePileList,
  getBillIssuancePileCount,
  reBillIssuance,
} from '@/api/billingIssuance/index';

import { getBillModelDetail } from '@/api/billingModel/index';

export default {
  components: {
    compareModal,
    StatusInfo,
  },
  dicts: [
    'ls_charging_billing_bind_type',
    'ls_charging_contracted_unit',
    'ls_charging_billing_issue_status',
    'ls_charging_issue_pile_status',
    'ls_charging_billing_issue_status',
    'ls_charging_billing_charge_mode',
    'ls_charging_billing_charge_type',
  ],
  data() {
    return {
      billMissionId: '',
      billModelName: '',
      status: '审核中',
      issuanceType: '充电站',
      effectiveTime: '2022-01-01 00:00:00',
      baseInfo: {
        applyer: '张三',
        applyUnit: '国网湖南电动汽车服务有限公司',
        applyTime: '2022-01-01 12:00:00',
      },

      tablePage: { total: 0, currentPage: 1, pageSize: 10 },

      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },

      loading: false,
      tableData: [],
      tableColumn: [
        {
          type: 'checkbox',
          width: 50,
          fixed: 'left',
        },
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
        },
        {
          field: 'stationName',
          title: '所属充电站',
          minWidth: 120,
        },
        {
          field: 'pileNo',
          title: '充电桩编号',
          minWidth: 120,
        },
        {
          field: 'pileName',
          title: '充电桩名称',
          minWidth: 120,
        },
        {
          field: 'updateTime',
          title: '最后下发时间',
          minWidth: 180,
        },
        {
          field: 'successTime',
          title: '下发成功时间',
          minWidth: 180,
        },
        {
          field: 'issueTimes',
          title: '下发次数',
          minWidth: 100,
        },
        {
          field: 'issueStatus',
          title: '下发状态',
          minWidth: 120,
          // formatter: ({ cellValue }) => {
          //     return this.selectDictLabel(
          //         this.dict.type.ls_charging_billing_issue_status,
          //         cellValue
          //     );
          // },
          slots: {
            // 自定义render函数
            default: ({ row }) => {
              return (
                <StatusInfo
                  value={row.issueStatus}
                  dictValue={this.dict.type.ls_charging_billing_issue_status}
                  colors={['medium', 'low', 'success', 'medium', 'high', 'low']}
                ></StatusInfo>
              );
            },
          },
        },
        {
          field: 'remark',
          title: '失败原因',
          minWidth: 120,
        },
        // {
        //     field: 'validStatus',
        //     title: '生效状态',
        //     minWidth: 120,
        //     fixed: 'right',
        //     formatter: ({ cellValue }) => {
        //         return this.selectDictLabel(
        //             this.dict.type.ls_charging_issue_pile_status,
        //             cellValue
        //         );
        //     },
        // },

        {
          title: '计费对比',
          slots: { default: 'operate' },
          width: 300,
          align: 'center',
          fixed: 'right',
        },
      ],
      totalNumber: '0',
      ingNumber: '0',
      successNumber: '0',
      failNumber: '0',

      pileList: [],
      params: {
        pileNo: '',
        pileName: '',
        stationName: '',
        issueStatus: '',
        issuanceDate: [],
        validStatus: '',
      },

      beforeData: {},
      beforeTimeList: [],
      beforeChooseTime: '',
      beforeTableData: [],
      afterData: {},
      afterTimeList: [],
      afterChooseTime: '',
      afterTableData: [],
    };
  },

  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'pileNo',
            title: '充电桩编号',
            element: 'el-input',
            props: { placeholder: '请输入' }, // 图片中对应输入框提示文字
          },
          {
            field: 'pileName',
            title: '充电桩名称',
            element: 'el-input',
            props: { placeholder: '请输入' },
          },
          {
            field: 'stationName',
            title: '充电站名称',
            element: 'el-input',
            props: { placeholder: '请输入' },
          },
          {
            field: 'issueStatus',
            title: '下发状态',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.dict.type.ls_charging_billing_issue_status,
            },
          },
          {
            field: 'issuanceDate',
            title: '下发时间',
            element: 'el-date-picker',
            props: {
              type: 'datetimerange',
              options: [],
              valucFormat: 'yyyy-MM-dd HH:mm:ss',
              defaultTime: ['00:00:00', '23:59:59'],
            },
          },
          // {
          //     field: 'validStatus',
          //     title: '生效状态',
          //     element: 'el-select',
          //     props: {
          //         placeholder: '请选择',
          //         options: this.dict.type.ls_charging_issue_pile_status,
          //     }
          // },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  mounted() {
    this.billMissionId = this.$route.query.billMissionId;
    this.getBillIssuanceDetail();

    this.loadData();
  },
  methods: {
    // 获取详情-基本数据
    async getBillIssuanceDetail() {
      const [err, res] = await getBillIssuanceDetail({
        billMissionId: this.billMissionId,
      });
      if (err) return;

      const {
        chcName,
        bindType,
        validStartTime,
        createUser,
        createUnit,
        createTime,
      } = res.data;
      (this.billModelName = chcName),
        (this.issuanceType = this.selectDictLabel(
          this.dict.type.ls_charging_billing_bind_type,
          bindType
        ));
      this.effectiveTime = validStartTime;
      this.baseInfo = {
        applyer: createUser,
        applyUnit: this.selectDictLabel(
          this.dict.type.ls_charging_contracted_unit,
          createUnit
        ),
        applyTime: createTime,
      };
    },

    async loadData() {
      const {
        pileNo,
        pileName,
        stationName,
        issueStatus,
        issuanceDate,
        validStatus,
      } = this.params;

      let updateTimeLeft = '';
      let updateTimeRight = '';
      if (issuanceDate && issuanceDate.length > 0) {
        updateTimeLeft = issuanceDate[0];
        updateTimeRight = issuanceDate[1];
      }

      const params = {
        billMissionId: this.billMissionId,
        stationName,
        pileNo,
        pileName,
        issueStatus,
        validStatus,
        updateTimeRight,
        updateTimeLeft,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };

      this.loading = true;

      const [err, res] = await getBillIssuancePileList(params);

      this.loading = false;

      if (err) return;
      const { data, total } = res;
      this.tableData = data;
      this.tablePage.total = total;

      const [err1, res1] = await getBillIssuancePileCount(params);

      if (err1) return;

      const { data: data1 } = res1;

      this.totalNumber = data1.total || '0';
      this.ingNumber = data1.issuingNumber || '0';
      this.successNumber = data1.successNumber || '0';
      this.failNumber = data1.failNumber || '0';
    },

    // 选择充电桩
    handleCheckboxChange({ records }) {
      // this.stationList = records.map((item) => item.stationId);
      this.pileList = records;
    },

    // 计费对比
    async hanleDetail(row) {
      const { planChcNo, chcNo } = row;

      // 当前计费模型
      if (!chcNo) {
        this.beforeData = {};
      } else {
        const [err, res] = await getBillModelDetail({
          chcNo: chcNo,
        });
        if (err) return;

        const {
          chcName,
          chcNo: chcNo1,
          suitCityName,
          createUnit,

          chargeMode, // 计费模式
          itemNo,
          chargePrice,
          servicePrice,

          topChargePrice,
          peakChargePrice,
          flatChargePrice,
          valleyChargePrice,
          topServicePrice,
          peakServicePrice,
          flatServicePrice,
          valleyServicePrice,

          periodDetail,
        } = res.data;

        if (chargeMode === '0202') {
          const { periodName, periodDetailList } = periodDetail;

          this.beforeData = {
            billNo: chcNo1,
            billModelName: chcName,
            applyArea: suitCityName,
            unit: this.selectDictLabel(
              this.dict.type.ls_charging_contracted_unit,
              createUnit
            ),
            billModel: chargeMode,
            billModelDesc: this.selectDictLabel(
              this.dict.type.ls_charging_billing_charge_mode,
              chargeMode
            ),
            feeType: this.selectDictLabel(
              this.dict.type.ls_charging_billing_charge_type,
              itemNo
            ),
            elePrice: chargePrice,
            servicePrice: servicePrice,
            totalPrice: Number(chargePrice) + Number(servicePrice),
            superPeak: topChargePrice,
            peak: peakChargePrice,
            normal: flatChargePrice,
            valley: valleyChargePrice,
            topServicePrice: topServicePrice || '0',
            peakServicePrice: peakServicePrice || '0',
            flatServicePrice: flatServicePrice || '0',
            valleyServicePrice: valleyServicePrice || '0',
            timePeriod: periodName,
          };

          const list = Array.from(
            periodDetailList
              .reduce((map, item) => {
                const month = item.validMonth;
                if (!map.has(month)) {
                  map.set(month, {
                    month,
                    priceList: [],
                    active: false,
                  });
                }
                map.get(month).priceList.push({
                  priceType: item.timeFlag,
                  startTime: item.beginTime,
                  endTime: item.endTime,
                });
                return map;
              }, new Map())
              .values()
          ).map((group, index) =>
            index === 0 ? { ...group, active: true } : group
          );

          this.beforeTimeList = list;

          this.beforeChooseTime = this.beforeTimeList[0].month;
          this.beforeTableData = this.beforeTimeList[0].priceList;
        } else if (chargeMode === '0201') {
          this.beforeData = {
            billNo: chcNo1,
            billModelName: chcName,
            applyArea: suitCityName,
            unit: this.selectDictLabel(
              this.dict.type.ls_charging_contracted_unit,
              createUnit
            ),
            billModel: chargeMode,
            billModelDesc: this.selectDictLabel(
              this.dict.type.ls_charging_billing_charge_mode,
              chargeMode
            ),
            feeType: this.selectDictLabel(
              this.dict.type.ls_charging_billing_charge_type,
              itemNo
            ),
            elePrice: chargePrice,
            servicePrice: servicePrice,
            totalPrice: Number(chargePrice) + Number(servicePrice),
          };
        }
      }

      // 下发计费模型
      if (!planChcNo) {
        this.afterData = {};
      } else {
        const [err1, res1] = await getBillModelDetail({
          chcNo: planChcNo,
        });
        if (err1) return;

        const {
          chcName: chcName2,
          chcNo: chcNo2,
          suitCityName: suitCityName2,
          createUnit: createUnit2,

          chargeMode: chargeMode2, // 计费模式
          itemNo: itemNo2,
          chargePrice: chargePrice2,
          servicePrice: servicePrice2,

          topChargePrice: topChargePrice2,
          peakChargePrice: peakChargePrice2,
          flatChargePrice: flatChargePrice2,
          valleyChargePrice: valleyChargePrice2,
          topServicePrice: topServicePrice2,
          peakServicePrice: peakServicePrice2,
          flatServicePrice: flatServicePrice2,
          valleyServicePrice: valleyServicePrice2,

          periodDetail: periodDetail2,
        } = res1.data;

        if (chargeMode2 === '0202') {
          const {
            periodName: periodName2,
            periodDetailList: periodDetailList2,
          } = periodDetail2;

          this.afterData = {
            billNo: chcNo2,
            billModelName: chcName2,
            applyArea: suitCityName2,
            unit: this.selectDictLabel(
              this.dict.type.ls_charging_contracted_unit,
              createUnit2
            ),
            billModel: chargeMode2,
            billModelDesc: this.selectDictLabel(
              this.dict.type.ls_charging_billing_charge_mode,
              chargeMode2
            ),
            feeType: this.selectDictLabel(
              this.dict.type.ls_charging_billing_charge_type,
              itemNo2
            ),
            elePrice: chargePrice2,
            servicePrice: servicePrice2,
            totalPrice: Number(chargePrice2) + Number(servicePrice2),
            superPeak: topChargePrice2,
            peak: peakChargePrice2,
            normal: flatChargePrice2,
            valley: valleyChargePrice2,
            topServicePrice: topServicePrice2 || '0',
            peakServicePrice: peakServicePrice2 || '0',
            flatServicePrice: flatServicePrice2 || '0',
            valleyServicePrice: valleyServicePrice2 || '0',
            timePeriod: periodName2,
          };

          const list = Array.from(
            periodDetailList2
              .reduce((map, item) => {
                const month = item.validMonth;
                if (!map.has(month)) {
                  map.set(month, {
                    month,
                    priceList: [],
                    active: false,
                  });
                }
                map.get(month).priceList.push({
                  priceType: item.timeFlag,
                  startTime: item.beginTime,
                  endTime: item.endTime,
                });
                return map;
              }, new Map())
              .values()
          ).map((group, index) =>
            index === 0 ? { ...group, active: true } : group
          );

          this.afterTimeList = list;

          this.afterChooseTime = this.afterTimeList[0].month;
          this.afterTableData = this.afterTimeList[0].priceList;
        } else if (chargeMode2 === '0201') {
          this.afterData = {
            billNo: chcNo2,
            billModelName: chcName2,
            applyArea: suitCityName2,
            unit: this.selectDictLabel(
              this.dict.type.ls_charging_contracted_unit,
              createUnit2
            ),
            billModel: chargeMode2,
            billModelDesc: this.selectDictLabel(
              this.dict.type.ls_charging_billing_charge_mode,
              chargeMode2
            ),
            feeType: this.selectDictLabel(
              this.dict.type.ls_charging_billing_charge_type,
              itemNo2
            ),
            elePrice: chargePrice2,
            servicePrice: servicePrice2,
            totalPrice: Number(chargePrice2) + Number(servicePrice2),
          };
        }
      }

      setTimeout(() => {
        this.$refs.compareModal.handleBeforeData();
        this.$refs.compareModal.handleAfterData();

        this.$refs.compareModal.dialogVisible = true;
      }, 50);
    },

    // 重新下发
    async handleIssuance(row) {
      const { recordId } = row;

      this.$confirm('确定重新下发吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          const [err, res] = await reBillIssuance({
            recordIds: [recordId],
          });

          if (err) {
            return;
          }

          this.$message({
            type: 'success',
            message: '下发成功!',
          });
        })
        .catch(() => {});
    },

    // 批量重新下发
    async handleBatchIssuance() {
      console.log(111);
      if (!this.pileList.length) {
        this.$message({
          type: 'warning',
          message: '请先选择充电桩!',
        });
      } else {
        const recordIds = this.pileList.map((item) => item.recordId);
        const [err, res] = await reBillIssuance({
          recordIds,
        });

        if (err) {
          return;
        }

        this.$message({
          type: 'success',
          message: '下发成功!',
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.container-full {
  background-color: rgba(244, 246, 249, 1);
  padding-bottom: 16px;
  min-height: calc(100% - 80px);
  overflow-y: auto;
}

.device-head {
  background-color: #fff;

  display: flex;
  height: 112px;
  display: flex;
  align-items: center;
  padding: 0 24px;
  box-sizing: border-box;
  .device-head-icon {
    width: 48px;
    height: 48px;
    margin-right: 24px;
  }
  .device-info-wrap {
    flex: 1;
    .device-title-wrap {
      height: 32px;
      display: flex;
      align-items: center;
      .device-title {
        font-weight: 500;
        font-size: 24px;
        color: #12151a;
      }
      .device-status {
        // width: 50px;
        padding: 0 10px;
        height: 24px;
        border-radius: 10px 0 10px 0;
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;
        text-align: center;
        color: #fff;
        background: linear-gradient(321.01deg, #ffb624 8.79%, #ff8d24 100.27%);
        margin-left: 12px;
      }
    }
    .device-info-wrap {
      height: 16px;
      margin-top: 16px;
      font-size: 16px;
      font-weight: 400;
      color: #292b33;
    }
  }
  .device-status-wrap {
    display: flex;
    align-items: center;
    .device-status-item-wrap {
      width: 150px;
      .device-status-item-title {
        font-weight: 400;
        font-size: 14px;
        line-height: 14px;
        color: #505363;
        margin: 0 auto 12px auto;
        text-align: center;
      }
      .device-status {
        width: 86px;
        height: 34px;
        border-radius: 4px;
        display: flex;
        margin: 0 auto;
        align-items: center;
        justify-content: center;
        background-color: #ebf3ff;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 18px;
        color: #217aff;
      }
      .device-status-success {
        width: 86px;
        height: 34px;
        border-radius: 4px;
        display: flex;
        margin: 0 auto;
        align-items: center;
        justify-content: center;
        background-color: #ebfff1;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 18px;
        color: #00c864;
      }
    }
    .device-status-split {
      width: 1px;
      height: 36px;
      background-color: #e9ebf0;
    }
  }
}

.info-card {
  margin: 16px;
  border-radius: 5px;
  border: 1px solid #fff;
  overflow: hidden;
  background-color: #fff;
  // min-height: 300px;
  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    background: linear-gradient(180deg, #e9f2ff 0%, #ffffff 100%);
    .before-icon {
      width: 3px;
      height: 16px;
      background-image: url('~@/assets/station/consno-before.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin-right: 8px;
    }
    .card-head-text {
      flex: 1;
      font-weight: 500;
      font-size: 16px;
      color: #12151a;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }

    .button-wrap {
      display: flex;
      .invite-btn {
        background-color: #1ab2ff;
        border-color: #1ab2ff;
      }
      ::v-deep .el-button--small {
        font-size: 14px;
      }
      .distribution {
        margin-left: 24px;
        margin-right: 24px;
        display: flex;
        align-items: center;
      }
    }
  }

  .people-table-wrap {
    ::v-deep .bd3001-table-select-box {
      display: none;
    }
    ::v-deep .bd3001-button {
      width: 100%;
    }
  }
  .form-wrap {
    padding: 0 16px 0 16px;
    .info-title {
      font-weight: 400;
      font-size: 16px;
      line-height: 16px;
      color: #505363;
    }
    .info-detail {
      font-weight: 400;
      font-size: 16px;
      line-height: 16px;
      color: #292b33;
      display: flex;
      .info-price {
        font-weight: 400;
        color: #ff8d24;
        margin-right: 5px;
      }
    }
    .info-amount {
      height: 28px;
      background-color: #fff7e6;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 8px;
      font-weight: 400;
      font-size: 20px;
      color: #fe8921;
      margin-top: -6px;
      margin-right: 4px;
    }
    .info-img {
      width: 140px;
      height: 140px;
    }
  }
}

::v-deep .bd3001-content {
  padding: 0 24px 12px 24px;
  box-shadow: none !important;
}

::v-deep .bd3001-auto-filters-container {
  box-shadow: none !important;
  padding-bottom: 0 !important;
}

::v-deep .bd3001-table-select-box {
  display: none !important;
}
::v-deep .bd3001-header {
  display: block !important;
}
::v-deep .bd3001-button {
  display: block !important;
}
.table-head {
  height: 56px;
  padding: 0;
  display: flex;
  align-items: center;
  margin-top: -20px;
  align-items: center;
  .table-head-button-wrap {
    flex: 1;
    display: flex;
    .choose-info-wrap {
      border-radius: 2px;
      height: 34px;
      padding: 0 10px;
      display: flex;
      align-items: center;
      background-color: #ebf3ff;
      font-family: PingFang SC;
      font-weight: 400;
      font-size: 14px;
      margin: 8px 16px 16px 0;
      color: #217aff;
      .choose-number {
        font-size: 16px;
        font-weight: 500;
        margin: 0 4px;
      }
    }
    .ing-info-wrap {
      border-radius: 2px;
      height: 34px;
      padding: 0 10px;
      display: flex;
      align-items: center;
      background-color: #ebfcff;
      font-family: PingFang SC;
      font-weight: 400;
      font-size: 14px;
      margin: 8px 16px 16px 0;
      color: #1ab2ff;
      .ing-number {
        font-size: 16px;
        font-weight: 500;
        margin: 0 4px;
      }
    }

    .success-info-wrap {
      border-radius: 2px;
      height: 34px;
      padding: 0 10px;
      display: flex;
      align-items: center;
      background-color: #ebfff1;
      font-family: PingFang SC;
      font-weight: 400;
      font-size: 14px;
      margin: 8px 16px 16px 0;
      color: #00c864;
      .success-number {
        font-size: 16px;
        font-weight: 500;
        margin: 0 4px;
      }
    }

    .fail-info-wrap {
      border-radius: 2px;
      height: 34px;
      padding: 0 10px;
      display: flex;
      align-items: center;
      background-color: #ffe8e6;
      font-family: PingFang SC;
      font-weight: 400;
      font-size: 14px;
      margin: 8px 16px 16px 0;
      color: #fc1e31;
      .fail-number {
        font-size: 16px;
        font-weight: 500;
        margin: 0 4px;
      }
    }
  }
}

.button-border {
  border: 0.01rem solid #217aff;
  color: #217aff;
  background-color: #fff;
}
</style>
