<template>
    <div class="container container-float " style="padding: 0 0 100px 0;">
        <div class="device-head">
            <img src="@/assets/charge/price-period-title-icon.png" class="device-head-icon">
            
            <div class="device-info-wrap">
                <div class="device-title-wrap">
                    <div class="device-title">计费名称：{{ billModelName }}</div>
                    <div class="device-status">{{ status }}</div>
                </div>
                <div class="device-info-wrap">
                    <el-row>
                        <el-col :span="8">
                            <span class="label">下发类型：</span>
                            <span class="value">{{ issuanceType }}</span>
                        </el-col>
                        <el-col :span="8">
                            <span class="label">生效时间：</span>
                            <span class="value">{{ effectiveTime }}</span>
                        </el-col>

                    </el-row>
                </div>
            </div>


            <el-button
                type="primary"
                 @click="drawer = true"
            >
                审核轨迹
            </el-button>
        </div>

        <div class="info-card" >
            <div class="card-head" style="margin-bottom: 8px;">
                <div class="before-icon"></div>
                <div class="card-head-text">计费基础信息</div>
            </div>

            <div class="form-wrap">
                <el-row :gutter="20" style="margin-bottom: 24px;">
                    <el-col :span="8">
                        <div style="display: flex;">
                            <div class="info-title">申请人：</div>
                            <div class="info-detail">{{ baseInfo.applyer }}</div>
                        </div>
                    </el-col>
                    <el-col :span="8">
                        <div style="display: flex;">
                            <div class="info-title">申请单位：</div>
                            <div class="info-detail">{{ baseInfo.applyUnit }}</div>
                        </div>
                    </el-col>
                    <el-col :span="8">
                        <div style="display: flex;">
                            <div class="info-title">申请时间：</div>
                            <div class="info-detail">{{ baseInfo.applyTime }}</div>
                        </div>
                    </el-col>
                </el-row>
            </div>
        </div>

        <div class="info-card"  >
              <div class="card-head" style="margin-bottom: 8px;">
                  <div class="before-icon"></div>
                  <div class="card-head-text">下发桩列表</div>
              </div>

              <BuseCrud
                        ref="crud"
                        :loading="table.loading"
                        :tablePage="table.page"
                        :tableColumn="tableColumn"
                        :tableData="table.data"
                        :pagerProps="pagerProps"
                        :modalConfig="modalConfig"
                        @loadData="getTablePage"
                    >
                    <template slot="defaultHeader">
                        <div>
                                <div class="choose-info-wrap">
                                    已选择
                                    <div class="choose-number">{{ stationNumber }} </div>
                                    个充电站，
                                    <div class="choose-number">{{ pileNumber }}</div>

                                    个充电桩
                                </div>
                        </div>
                        
                    </template>

                        <template slot="operate" slot-scope="{ row }">
                            <div class="menu-box">
                                <el-button
                                    type="primary"
                                    plain
                                    @click="hanleDetail(row)"
                                >
                                    查看
                                </el-button>

                                

                            
                            </div>
                        
                        </template>

                </BuseCrud>
        </div>


        <div class="info-card" v-if="type!=='detail'">
            <div class="card-head" style="margin-bottom: 8px;">
                <div class="before-icon"></div>
                <div class="card-head-text">审核信息</div>
            </div>

            <div class="form-wrap">
                <el-form :model="form" :rules="rules" ref="form"  label-position="top">
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item
                                label="审核结果"
                                prop="result"
                                :label-width="formLabelWidth"
                            >
                                <el-select
                                    v-model="form.result"
                                    placeholder="请选择审核结果"
                                    style="width: 100%"
                                    >
                                    <el-option
                                        v-for="item in resultList"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                    ></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row :gutter="20">
                        <el-col :span="24">
                            <el-form-item
                                label="审核意见"
                                prop="remark"
                                :label-width="formLabelWidth"
                            >
                                <el-input
                                    v-model="form.remark"
                                    type="textarea"
                                    :rows="3"
                                    placeholder="请输入审核意见"
                                ></el-input>
                            </el-form-item>
                        </el-col>  
                    </el-row>
                </el-form>
            </div>
        </div>

        <div class="bottom-wrap">
            <el-button
                @click="() => handleCancel()"
            >
                取消
            </el-button>
            <el-button
                type="primary"
                @click="() => handleConfirm()"
            >
                提交
            </el-button>
        </div>

        <compareModal 
            ref="compareModal"
            :beforeData="beforeData"
            :afterData="afterData"
        />

    </div>
    
  </template>
  
  <script>

  import  compareModal  from '../../components/compareModal.vue'
  
    export default {
    components: {
        compareModal
    },
    dicts: [],
    data() {
        return {
            billModelName: '计费模型1',
            status: '审核中',
            issuanceType: '充电站',
            effectiveTime: '2022-01-01 00:00:00',

            baseInfo: {
                applyer: '张三',
                applyUnit: '国网湖南电动汽车服务有限公司',
                applyTime: '2022-01-01 12:00:00',
            },

            table: {
                loading: false,
                page: {
                    total: 0,
                    currentPage: 1,
                    pageSize: 10,
                },
                dataTotal: [],
                data: [],
            },

            pagerProps: {
                layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
            },

            tableColumn: [
                {
                    type: 'seq',
                    title: '序号',
                    width: 60,
                    minWidth: 60,
                },
                {
                    field: 'pileNo',
                    title: '充电桩编号',
                    minWidth: 120,
                },
                {
                    field: 'pileName',
                    title: '充电桩名称',
                    minWidth: 120,
                },
                {
                    field: 'stationName',
                    title: '所属充电站',
                    minWidth: 120,
                },
                {
                    title: '计费对比',
                    slots: { default: 'operate' },
                    width: 300,
                    align: 'center',
                    fixed: 'right',
                },
            ],
            stationNumber: '0',
            pileNumber: '0',

            beforeData: {},
            afterData: {},


            form: {
                result: '',
                remark: '',
            },
            rules: {
                result: [
                   { required: true, message: '请选择审核结果', trigger: 'blur'}
                ],
            },
            formLabelWidth: '120px',

            resultList: [
                { label: '审核通过', value: '01' },
                { label: '审核不通过', value: '02' },
            ],

        };
    },

    computed: {
        modalConfig() {
            return {
                addBtn: false,
                viewBtn: false,
                menu: false,
                editBtn: false,
                delBtn: false,
            }
        },
    },
    mounted() {
        this.table.dataTotal = [
            {
                pileNo: '001',
                pileName: '充电桩1',
                stationName: '充电站1',
            },
            {
                pileNo: '001',
                pileName: '充电桩1',
                stationName: '充电站1',
            },
            {
                pileNo: '001',
                pileName: '充电桩1',
                stationName: '充电站1',
            },
            {
                pileNo: '001',
                pileName: '充电桩1',
                stationName: '充电站1',
            },
            {
                pileNo: '001',
                pileName: '充电桩1',
                stationName: '充电站1',
            },
            {
                pileNo: '001',
                pileName: '充电桩1',
                stationName: '充电站1',
            },
            {
                pileNo: '001',
                pileName: '充电桩1',
                stationName: '充电站1',
            },
            {
                pileNo: '001',
                pileName: '充电桩1',
                stationName: '充电站1',
            },
            {
                pileNo: '001',
                pileName: '充电桩1',
                stationName: '充电站1',
            },
            {
                pileNo: '001',
                pileName: '充电桩1',
                stationName: '充电站1',
            },
            {
                pileNo: '001',
                pileName: '充电桩1',
                stationName: '充电站1',
            },
            {
                pileNo: '001',
                pileName: '充电桩1',
                stationName: '充电站1',
            },
            {
                pileNo: '001',
                pileName: '充电桩1',
                stationName: '充电站1',
            },
        ]

        this.table.page.currentPage = 1;
        this.table.page.total = this.table.dataTotal.length;
        this.getTablePage();
    },
    methods: {
        getTablePage() {
            this.table.data = this.table.dataTotal.slice(
                (this.table.page.currentPage - 1) * this.table.page.pageSize,
                this.table.page.currentPage * this.table.page.pageSize
            );
        },

        // 查看对比
        hanleDetail() {
            this.beforeData = {
                billNo: '21321313',
                billModelName: '计费模型1',
                applyArea: '湖南省长沙市岳麓区',
                unit:'国网湖南电动汽车服务有限公司',
                billModel: "不分时",
                chargeType: "电费",
                marketPriceType: "一般工业不满1kv",
                marketPriceName: "长沙2024一般工商业不满1kv",
                isSameWithMarket: "否",
                effectiveTime: "2024/11/23 11:20:02",
                electricityPrice: "0.5546"
            },
            this.afterData = {
                billNo: '21321315',
                billModelName: '计费模型2',
                applyArea: '湖南省长沙市岳麓区111',
                unit:'国网湖南电动汽车服务有限公司',
                billModel: "分时",
                chargeType: "电费",
                marketPriceType: "一般工业不满1kv",
                marketPriceName: "长沙2024一般工商业不满1kv",
                isSameWithMarket: "否",
                effectiveTime: "2024/11/23 11:20:02",
                peakPrice: "0.5546",      // 尖电价
                highPrice: "0.5546",      // 峰电价
                flatPrice: "0.5546",      // 平电价
                valleyPrice: "0.5546",    // 谷电价
                electricityPeriod: "2024湖南电价时段"
            }
            console.log(this.$refs.compareModal, '111')

            this.$refs.compareModal.dialogVisible = true;
        },
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .container-full {
    background-color: rgba(244, 246, 249, 1);
    padding-bottom: 16px;
    min-height: calc(100% - 80px);
    overflow-y: auto;
  }
   
     
  .device-head {
    background-color: #fff;
    
    display: flex;
    height: 112px;
    display: flex;
    align-items: center;
    padding: 0 24px;
    box-sizing: border-box;
    .device-head-icon {
        width: 48px;
        height: 48px;
        margin-right: 24px;
    }
    .device-info-wrap {
        flex: 1;
        .device-title-wrap {
            height: 32px;
            display: flex;
            align-items: center;
            .device-title {
                font-weight: 500;
                font-size: 24px;
                color: #12151A;
            }
            .device-status {
                // width: 50px;
                padding: 0 10px;
                height: 24px;
                border-radius: 10px 0 10px 0;
                font-size: 16px;
                font-weight: 400;
                line-height: 24px;
                text-align: center;
                color: #fff;
                background: linear-gradient(321.01deg, #FFB624 8.79%, #FF8D24 100.27%);
                margin-left: 12px;
            }
        }
        .device-info-wrap {
            height: 16px;
            margin-top: 16px;
            font-size: 16px;
            font-weight: 400;
            color: #292B33;
        }
    }
    .device-status-wrap {
        display: flex;
        align-items: center;
        .device-status-item-wrap {
            width: 150px;
            .device-status-item-title {
                font-weight: 400;
                font-size: 14px;
                line-height: 14px;
                color: #505363;
                margin: 0 auto 12px auto;
                text-align: center;
            }
            .device-status {
                width: 86px;
                height: 34px;
                border-radius: 4px;
                display: flex;
                margin: 0 auto;
                align-items: center;
                justify-content: center;
                background-color: #EBF3FF;
                font-family: PingFang SC;
                font-weight: 500;
                font-size: 18px;
                color: #217AFF;
            }
            .device-status-success {
                width: 86px;
                height: 34px;
                border-radius: 4px;
                display: flex;
                margin: 0 auto;
                align-items: center;
                justify-content: center;
                background-color: #EBFFF1;
                font-family: PingFang SC;
                font-weight: 500;
                font-size: 18px;
                color: #00C864;

            }
        }
        .device-status-split{
            width: 1px;
            height: 36px;
            background-color: #E9EBF0;
        }
    }
    
  
  }

  .info-card {
  margin: 16px;
  border-radius: 5px;
  border: 1px solid #fff;
  overflow: hidden;
  background-color: #fff;
  // min-height: 300px;
  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    background: linear-gradient(180deg, #E9F2FF 0%, #FFFFFF 100%);
    .before-icon {
        width: 3px;
        height: 16px;
        background-image: url('~@/assets/station/consno-before.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        margin-right: 8px;
    }
    .card-head-text {
        flex:1;
        font-weight: 500;
        font-size: 16px;
        color: #12151A;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }

   
    }

    .button-wrap {
      display: flex;
      .invite-btn {
          background-color: #1ab2ff;
          border-color: #1ab2ff;
        }
        ::v-deep .el-button--small {
          font-size: 14px;
        }
      .distribution {
          margin-left: 24px;
          margin-right: 24px;
          display: flex;
          align-items: center;
        }
    }
  }
  
  .people-table-wrap {
    ::v-deep .bd3001-table-select-box {
      display: none;
    }
    ::v-deep .bd3001-button {
      width: 100%;
    }
  }
  .form-wrap {
      padding: 0 16px 0 16px;
      .info-title {
        font-weight: 400;
        font-size: 16px;
        line-height: 16px;
        color: #505363;
      }
      .info-detail {
        font-weight: 400;
        font-size: 16px;
        line-height: 16px;
        color: #292B33;
        display: flex;
        .info-price {
            font-weight: 400;
            color: #FF8D24;
            margin-right: 5px;
        }
      }
      .info-amount {
        height: 28px;
        background-color: #FFF7E6;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 8px;
        font-weight: 400;
        font-size: 20px;
        color: #FE8921;
        margin-top: -6px;
        margin-right: 4px;

      }
      .info-img {
        width: 140px;
        height: 140px;
      }
    }
  }


  ::v-deep .bd3001-content {
    padding: 0 24px 12px 24px;
  }

  .choose-info-wrap {
            border-radius: 2px;
            height: 34px;
            padding: 0 10px;
            display: flex;
            align-items: center;
            background-color: #EBF3FF;
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 14px;
            margin: 8px 0 16px 0;
            color: #217AFF;
            .choose-number {
                font-size: 16px;
                font-weight: 500;
                margin: 0 4px;
            }
        }

        .container {
      position: relative;
      padding-bottom: 100px;
      box-sizing: border-box;
      .bottom-wrap {
          position: fixed;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 86px;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          background-color: #FFFFFF;
          padding-right: 32px;
          box-sizing: border-box;
      }
  }

 
  </style>
  