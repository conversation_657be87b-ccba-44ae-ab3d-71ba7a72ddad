<template>
    <div class="container container-float " style="padding: 0 0 100px 0;">
        <div class="info-card" >
              <div class="card-head">
                  <div class="before-icon"></div>
                  <div class="card-head-text">基础信息</div>
              </div>

              <div class="card-head-split"></div>

              <div class="form-wrap">
                <el-form :model="baseInfo.form" :rules="baseInfo.rules" ref="baseInfoForm"  label-position="top">
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <!-- todo 切换至充电桩选择-->
                            <el-form-item
                                label="下发类型"
                                prop="issuanceType"
                                :label-width="formLabelWidth"
                                >
                                    <el-select
                                        v-model="baseInfo.form.issuanceType"
                                        placeholder="请选择下发类型"
                                        style="width: 100%"
                                        @change="changeIssuanceType"
                                    >
                                        <el-option
                                        v-for="item in dict.type.ls_charging_billing_bind_type"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                        ></el-option>
                                    </el-select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="8">
                            <el-form-item
                                label="计费模型"
                                prop="billingModel"
                                :label-width="formLabelWidth"
                                >
                                    <el-select
                                        v-model="baseInfo.form.billingModel"
                                        placeholder="请选择计费模型"
                                        style="width: 100%"
                                    >
                                        <el-option
                                        v-for="item in billingModelList"
                                        :key="item.value"
                                        :label="item.label"
                                        :value="item.value"
                                        ></el-option>
                                    </el-select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="8" v-if="baseInfo.form.billingModel">
                            <div class="model-detail" @click="handleDetail">查看计费明细</div>
                        </el-col>
                    </el-row>
                   

                </el-form>
              </div>
        </div>


        <div class="info-card"  v-if="baseInfo.form.issuanceType === '1'">
              <div class="card-head">
                  <div class="before-icon"></div>
                  <div class="card-head-text">充电站信息</div>
              </div>

              <div class="card-head-split"></div>

                <BuseCrud
                        ref="crud"
                        :loading="table.loading"
                        :tablePage="table.page"
                        :tableColumn="tableColumn"
                        :tableData="table.data"
                        :pagerProps="pagerProps"
                        :modalConfig="modalConfig"
                        @loadData="getTablePage"
                    >
                    <template slot="defaultHeader">
                        <div>

                            <div class="top-button-wrap">
                                <el-button
                                    type="primary"
                                    @click="() => handleChooseStation()"
                                >
                                    充电站选择
                                </el-button>

                                <el-button
                                    type="primary"
                                    plain
                                    @click="() => handleChooseStation()"
                                >
                                    充电站导入
                                </el-button>

                                <el-button
                                    type="primary"
                                    plain
                                    @click="() => handleChooseStation()"
                                >
                                    下载模版
                                </el-button>

                                <div class="choose-info-wrap">
                                    已选择
                                    <div class="choose-number">{{ stationNumber }} </div>
                                    个充电站，
                                    <div class="choose-number">{{ pileNumber }}</div>

                                    个充电桩
                                </div>
                            
                            </div>
                        </div>
                        
                    </template>

                        <template slot="operate" slot-scope="{ row }">
                            <div class="menu-box">
                                <el-button
                                    type="primary"
                                    plain
                                    @click="hanleDetail(row)"
                                >
                                    详情
                                </el-button>

                                <el-button
                                    type="danger"
                                    plain
                                    @click="() => handleDelete(row)"
                                >
                                    删除
                                </el-button>

                            
                            </div>
                        
                        </template>

                </BuseCrud>

                <div class="form-wrap">
                <el-form :model="applyInfo.form" :rules="applyInfo.rules" ref="applyInfoForm"  label-position="top">
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item
                                label="生效时间"
                                prop="effectiveTimeType"
                                :label-width="formLabelWidth"
                                >
                                    <div style="display: flex; align-items: center;">
                                        <el-radio-group
                                            v-model="applyInfo.form.effectiveTimeType"
                                            style="margin-right: 10px;"
                                            @change="handleEffectiveTimeTypeChange"
                                        >
                                            <el-radio
                                                v-for="dict in effectiveTimeTypeList"
                                                :key="dict.value"
                                                :label="dict.value"
                                            >
                                                {{ dict.label }}
                                            </el-radio>
                                        </el-radio-group>
                                        <el-date-picker
                                            v-if="applyInfo.form.effectiveTimeType === '2'"
                                            type="date"
                                            v-model="applyInfo.form.effectiveTime" 
                                            value-format="yyyy-MM-dd"
                                        >
                                        </el-date-picker>
                                    </div>
                                    
                            </el-form-item>
                        </el-col> 
                        
                       
                    </el-row>
                    
                    <el-row :gutter="20">
                         <el-col :span="8">
                            <el-form-item
                            label="申请单位"
                            prop="createUnit"
                            :label-width="formLabelWidth"
                            >
                            <el-select
                                v-model="applyInfo.form.createUnit"
                                placeholder="请选择申请单位"
                                style="width: 100%"
                                >
                                <el-option
                                    v-for="item in  dict.type.ls_charging_contracted_unit"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                ></el-option>
                            </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <!-- <el-row :gutter="20">
                        <el-col :span="24">
                            <el-form-item
                                label="申请说明"
                                prop="remark"
                                :label-width="formLabelWidth"
                            >
                                <el-input
                                    v-model="applyInfo.form.remark"
                                    type="textarea"
                                    :rows="3"
                                    placeholder="请输入申请说明"
                                ></el-input>
                            </el-form-item>
                        </el-col>  
                    </el-row> -->
                   

                </el-form>
              </div>
        </div>

        <div class="info-card"  v-else>
              <div class="card-head">
                  <div class="before-icon"></div>
                  <div class="card-head-text">充电桩信息</div>
              </div>

              <div class="card-head-split"></div>

                <BuseCrud
                        ref="crud"
                        :loading="table.loading"
                        :tablePage="table.page"
                        :tableColumn="tablePileColumn"
                        :tableData="table.data"
                        :pagerProps="pagerProps"
                        :modalConfig="modalConfig"
                        @loadData="getTablePage"
                    >
                    <template slot="defaultHeader">
                        <div>


                            <div class="top-button-wrap">
                                <el-button
                                    type="primary"
                                    @click="() => handleChoosePile()"
                                >
                                    充电桩选择
                                </el-button>

                                <el-button
                                    type="primary"
                                    plain
                                    @click="() => handleChoosePile()"
                                >
                                    充电桩导入
                                </el-button>

                                <el-button
                                    type="primary"
                                    plain
                                    @click="() => handleChoosePile()"
                                >
                                    下载模版
                                </el-button>

                                <div class="choose-info-wrap">
                                    已选择
                                  
                                    <div class="choose-number">{{ pileNumber }}</div>

                                    个充电桩
                                </div>
                            
                            </div>
                        </div>
                        
                    </template>

                        <template slot="operate" slot-scope="{ row }">
                            <div class="menu-box">
                                <el-button
                                    type="primary"
                                    plain
                                    @click="hanlePileDetail(row)"
                                >
                                    详情
                                </el-button>

                                <el-button
                                    type="danger"
                                    plain
                                    @click="() => handlePileDelete(row)"
                                >
                                    删除
                                </el-button>

                            
                            </div>
                        
                        </template>

                </BuseCrud>

                <div class="form-wrap">
                <el-form :model="applyInfo.form" :rules="applyInfo.rules" ref="applyInfoForm"  label-position="top">
                    <el-row :gutter="20">
                        <el-col :span="12">
                            <el-form-item
                                label="生效时间"
                                prop="effectiveTimeType"
                                :label-width="formLabelWidth"
                                >
                                    <div style="display: flex; align-items: center;">
                                        <el-radio-group
                                            v-model="applyInfo.form.effectiveTimeType"
                                            style="margin-right: 10px;"
                                            @change="handleEffectiveTimeTypeChange"
                                        >
                                            <el-radio
                                                v-for="dict in effectiveTimeTypeList"
                                                :key="dict.value"
                                                :label="dict.value"
                                            >
                                                {{ dict.label }}
                                            </el-radio>
                                        </el-radio-group>
                                        <el-date-picker
                                            v-if="applyInfo.form.effectiveTimeType === '2'"
                                            type="date"
                                            v-model="applyInfo.form.effectiveTime" 
                                            value-format="yyyy-MM-dd"
                                        >
                                        </el-date-picker>
                                    </div>
                                    
                            </el-form-item>
                        </el-col> 
                        
                       
                    </el-row>
                    
                    <el-row :gutter="20">
                         <el-col :span="8">
                            <el-form-item
                            label="申请单位"
                            prop="createUnit"
                            :label-width="formLabelWidth"
                            >
                            <el-select
                                v-model="applyInfo.form.createUnit"
                                placeholder="请选择申请单位"
                                style="width: 100%"
                                >
                                <el-option
                                    v-for="item in  dict.type.ls_charging_contracted_unit"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                ></el-option>
                            </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    
                    <!-- 
                    <el-row :gutter="20">
                        <el-col :span="24">
                            <el-form-item
                                label="申请说明"
                                prop="remark"
                                :label-width="formLabelWidth"
                            >
                                <el-input
                                    v-model="applyInfo.form.remark"
                                    type="textarea"
                                    :rows="3"
                                    placeholder="请输入申请说明"
                                ></el-input>
                            </el-form-item>
                        </el-col>  
                    </el-row> -->
                   

                </el-form>
              </div>
        </div>

        <div class="bottom-wrap">
            <div>
                <el-button @click="handleCancel">取消</el-button>
                <!-- <el-button @click="handleSave('save')" type="primary">保存</el-button> -->
                <el-button @click="handleSave('audit')" type="primary">提交审核</el-button>

            </div>

        </div>

        <ChooseStationModal ref="chooseStationModal" @confirm="handleChooseStationConfirm" />

        <ChoosePileModal ref="choosePileModal" @confirm="handleChoosePileConfirm" />

    </div>
    
  </template>
  
  <script>

import ChooseStationModal from './components/chooseStationModal';

import ChoosePileModal from './components/choosePileModal';

import {
    getBillModelSelectList,
    createBillIssuance,
    billTemplateIssuance,
} from '@/api/billingIssuance/index'
  
    export default {
    components: {
        ChooseStationModal,
        ChoosePileModal
    },
    dicts: [
        'ls_charging_billing_bind_type',
        'ls_charging_contracted_unit',
        'ls_charging_operation_mode',
        'ls_charging_subType',
    ],
    data() {
      return {
            formLabelWidth: '120px',

            baseInfo: {
                form: {
                    issuanceType: '1',
                    billingModel: '',
                },
                rules: {
                    issuanceType: [
                        { required: true, message: '请选择下发类型', trigger: 'blur' },
                    ],
                    billingModel: [
                        { required: true, message: '请选择计费模型', trigger: 'blur' },
                    ]
                },
            },
            billingModelList: [],

            table: {
                loading: false,
                page: {
                    total: 0,
                    currentPage: 1,
                    pageSize: 10,
                },
                dataTotal: [],
                data: [],
            },

            pagerProps: {
                layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
            },

            tableColumn: [
                {
                    type: 'seq',
                    title: '序号',
                    width: 60,
                    minWidth: 60,
                },
                {
                    field: 'stationNo',
                    title: '充电站编号',
                    minWidth: 120,
                },
                {
                    field: 'stationName',
                    title: '充电站名称',
                    minWidth: 150,
                },
                {
                    field: 'stationType',
                    title: '站点类型',
                    minWidth: 150,
                },
                {
                    field: 'operationMode',
                    title: '运营模式',
                    minWidth: 100, // 最小宽度
                },
                {
                    field: 'assetProperty',
                    title: '资产属性',
                    minWidth: 100, // 最小宽度
                },
                {
                    field: 'stationAccessType',
                    title: '接入方式',
                    minWidth: 100, // 最小宽度
                },
                {
                    field: 'pileNum',
                    title: '充电桩数量',
                    minWidth: 120, // 最小宽度
                },
                {
                    field: 'construction',
                    title: '建设场所',
                    minWidth: 120, // 最小宽度
                },
                {
                    field: 'areaType',
                    title: '行政区域',
                    minWidth: 120, // 最小宽度
                },
                {
                    title: '操作',
                    slots: { default: 'operate' },
                    width: 300,
                    align: 'center',
                    fixed: 'right',
                },
            ],

            tablePileColumn:[
                {
                    type: 'seq',
                    title: '序号',
                    width: 60,
                    minWidth: 60, // 最小宽度
                },
                {
                    field: 'stationName',
                    title: '所属充电站',
                    minWidth: 150, // 最小宽度
                },
                {
                    field: 'operationMode',
                    title: '运营模式',
                    minWidth: 120, // 最小宽度
                    formatter: ({ cellValue }) => {
                        return this.selectDictLabel(
                          this.dict.type.ls_charging_operation_mode,
                          cellValue
                        );
                    },
                },
                {
                    field: 'pileNo',
                    title: '充电桩编号',
                    minWidth: 150, // 最小宽度
                },
                {
                    field: 'pileName',
                    title: '充电桩名称',
                    minWidth: 150, // 最小宽度
                },
                {
                    field: 'brandName',
                    title: '设备品牌',
                    minWidth: 100, // 最小宽度
                },
                {
                    title: '设备型号',
                    field: 'modelName',
                    minWidth: 100, // 最小宽度
                },
                {
                  field: 'subType',
                  title: '设备类型',
                  minWidth: 120, // 最小宽度
                  formatter: ({ cellValue }) => {
                          return this.selectDictLabel(
                            this.dict.type.ls_charging_subType,
                            cellValue
                          );
                        },
                },

                {
                    title: '额定功率(kW)',
                    field: 'ratePower',
                    minWidth: 150, // 最小宽度
                },
                {
                    title: '枪数量',
                    field: 'gunSum',
                    minWidth: 100, // 最小宽度
                },

                {
                    title: '操作',
                    slots: { default: 'operate' },
                    width: 200,
                    align: 'center',
                    fixed: 'right',
                },
            ],

            stationNumber: '0',
            pileNumber: '0',


            applyInfo: {
                form: {
                    effectiveTimeType: '1',
                    effectiveTime: '',
                    createUnit:'',
                    remark: '',
                },
                rules: {
                    effectiveTimeType: [
                        { required: true, message: '请选择生效时间', trigger: 'blur' },
                    ],
                    createUnit: [
                        { required: true, message: '请输入申请单位', trigger: 'blur' },
                    ]
                }
            },

            effectiveTimeTypeList: [
                {
                    label: '审核通过后立即生效',
                    value: '1',
                },
                {
                    label: '定时生效',
                    value: '2',
                }
            ]
            
      };
    },

    computed: {
        modalConfig() {
            return {
                addBtn: false,
                viewBtn: false,
                menu: false,
                editBtn: false,
                delBtn: false,
            }
        },
    },
    mounted() {
        this.table.dataTotal = [];

        this.table.page.currentPage = 1;
        this.table.page.total = this.table.dataTotal.length;
        this.getTablePage();

        // 获取计费模型列表
        this.getBillModelSelectList() 
    },
    methods: {
        // 获取计费模型列表
        async getBillModelSelectList() {
            const [err, res] = await getBillModelSelectList({})
            if (err) return

            const { data } = res

            const list  = []
            data.forEach(item => {
                list.push({
                    label: item.chcName,
                    value: item.chcNo
                })
            })

            this.billingModelList = list
        },

        // 下发类型切换
        changeIssuanceType() {
 
            this.baseInfo.form.billingModel = ''
            this.table = {
                loading: false,
                page: {
                    total: 0,
                    currentPage: 1,
                    pageSize: 10,
                },
                dataTotal: [],
                data: [],
            },
            this.stationNumber= '0',
            this.pileNumber= '0',

            this.applyInfo.form = {
                effectiveTimeType: '1',
                effectiveTime: '',
                createUnit:'',
            }
        },

        getTablePage() {
            this.table.data = this.table.dataTotal.slice(
                (this.table.page.currentPage - 1) * this.table.page.pageSize,
                this.table.page.currentPage * this.table.page.pageSize
            );
        },
        // 计费明细
        handleDetail() {
            
            this.$router.push({
                path: '/v2g-charging-web/operatorManage/chargingManage/billingModel/audit',
                query: {
                    chcNo: this.baseInfo.form.billingModel,
                    type: 'detail',
                },
            })
        },

        // 选择充电站
        handleChooseStation() {

            this.$refs.chooseStationModal.loadData();

            this.$refs.chooseStationModal.dialogVisible = true;
        },

        // 选择充电桩
        handleChoosePile() {
            this.$refs.choosePileModal.loadData();

            this.$refs.choosePileModal.dialogVisible = true;
        },

        // 充电站详情
        hanleDetail(row) {
            const {
                stationId
            } = row;
            this.$router.push({
                path: '/v2g-charging-web/baseInfo/equipmentAndAssets/station/detail',
                query: {
                    stationId
                }
            })
        },

        // 充电桩详情
        hanlePileDetail(row) {
            const { pileId } = row;
            this.$router.push({
                path: '/v2g-charging-web/baseInfo/equipmentAndAssets/pile/detail',
                query: {
                pileId
                }
            });
        },

        // 删除充电站
        handleDelete(row) {
            this.$confirm('确定删除该充电站吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            })
                .then(() => {
                    const index = this.table.dataTotal.findIndex(item => item.stationId === row.stationId);
                    this.table.dataTotal.splice(index, 1);

                    this.table.page.currentPage = 1;
                    this.table.page.total = this.table.dataTotal.length;

                    this.stationNumber =  this.table.dataTotal.length;
                    let number = 0;
                    this.table.dataTotal.forEach(item => {
                        number += Number(item.pileNum);
                    })

                    // this.pileNumber = 0;
                    this.pileNumber = number;

                    this.getTablePage();
                })
                .catch(() => {});
        },

        // 删除充电桩
        handlePileDelete(row) {
            this.$confirm('确定删除该充电桩吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            })
                .then(() => {
                    const index = this.table.dataTotal.findIndex(item => item.pileId === row.pileId);
                    this.table.dataTotal.splice(index, 1);

                    this.table.page.currentPage = 1;
                    this.table.page.total = this.table.dataTotal.length;

                    this.pileNumber = this.table.page.total;

                    this.getTablePage();
                })
                .catch(() => {});
        },

        // 选择充电站确认
        handleChooseStationConfirm(stationList) {
            const list = this.table.dataTotal.concat(stationList);

            const uniqueArray = Array.from(new Map(list.map(item => [item.stationId, item])).values());

            this.table.dataTotal = uniqueArray;

             
            this.stationNumber =  this.table.dataTotal.length;
            let number = 0;
            this.table.dataTotal.forEach(item => {
                number += Number(item.pileNum);
            })

            this.pileNumber = 0;
            this.pileNumber = number;

            this.table.page.currentPage = 1;
            this.table.page.total = this.table.dataTotal.length;
            this.getTablePage();
        },

        // 充电桩选择确认
        handleChoosePileConfirm(stationList) {
            const list = this.table.dataTotal.concat(stationList);

            const uniqueArray = Array.from(new Map(list.map(item => [item.pileId, item])).values());

            this.table.dataTotal = uniqueArray;

            this.pileNumber =  this.table.dataTotal.length;

            this.table.page.currentPage = 1;
            this.table.page.total = this.table.dataTotal.length;
            this.getTablePage();
        },

        // 生效时间切换
        handleEffectiveTimeTypeChange() {
           
        },

        // 保存/提交审核
        handleSave: _.debounce(async function(type){
            Promise.all([
                this.validateForm('baseInfoForm'),
                this.validateForm('applyInfoForm'),
            ])
            .then(async () => {
                const {
                    issuanceType,
                    billingModel,
                } = this.baseInfo.form

                const {
                    effectiveTimeType,
                    effectiveTime,
                    createUnit
                } = this.applyInfo.form

                if (effectiveTimeType === '2' && !effectiveTime) {
                    this.$message.error('请选择生效时间')
                    return
                }

                let effectiveTimeParam = ''
                if (effectiveTimeType === '1') {
                   // 获取当前日期对象
                    const date = new Date();

                    // 分别获取年、月、日
                    const year = date.getFullYear();       // 年份（四位数）
                    const month = date.getMonth() + 1;     // 月份（0-11，需要+1）
                    const day = date.getDate();            // 日期（1-31）

                    // 格式化成 YYYY-MM-DD 的字符串（自动补零）
                    effectiveTimeParam = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
                } else if (effectiveTimeType === '2') {
                    effectiveTimeParam = effectiveTime
                }


                const stationNos = []
                this.table.dataTotal.forEach(item => {
                    stationNos.push(item.stationNo)
                })

                const pileIds = []
                this.table.dataTotal.forEach(item => {
                    pileIds.push(item.pileId)
                })

                const params = {
                    bindType: issuanceType,
                    chcNo: billingModel,
                    chcName: this.selectDictLabel(
                        this.billingModelList,
                        billingModel
                    ),
                    stationNos: issuanceType === '1' ? stationNos : [],
                    pileIds: issuanceType === '2' ? pileIds : [],
                    validStartTime: effectiveTimeParam,
                    createUnit,
                    isSubmitAudit: type === 'audit' ? true : false,
                }

                const [err,res] = await createBillIssuance(
                    params
                )

                if(err) {
                        return this.$message.error(err.message || '计费下发失败');
                    }
                    
                    const { data } = res;

                    if (data === null) {
                        // 不需要调用下发接口
                        this.$message({
                            type: 'success',
                            message: '下发成功!'
                        });

                        setTimeout(() => {
                            this.$router.back();
                        }, 500);
                    } else {
                        // 调用下发接口
                        const {
                            pileIds,
                            chcNo,
                            missionId,
                        } = data;

                        const [err1,res1] = await billTemplateIssuance({
                            pileIds,
                            chcNo,
                            missionId,
                        })
                        if(err1) return

                        this.$message({
                            type: 'success',
                            message: '下发成功!'
                        });

                        setTimeout(() => {
                            this.$router.back();
                        }, 500);


                    }


                   
                
            })
            .catch((error) => {
                // 有表单校验失败
                this.$message.error('表单校验失败，请检查输入');
            });
        },300),

          // 校验单个表单
          validateForm(formRef) {
            return new Promise((resolve, reject) => {
                this.$refs[formRef].validate((valid) => {
                        if (valid) {
                            resolve(); // 校验通过
                        } else {
                            reject(new Error(`${formRef} 校验失败`)); // 校验失败
                        }
                    });
               
            });
        },

        handleCancel() {
            this.$router.back();
        }

    },
  };
  </script>
  
  <style lang="scss" scoped>
  .container-full {
    background-color: rgba(244, 246, 249, 1);
    padding-bottom: 16px;
    min-height: calc(100% - 80px);
    overflow-y: auto;
  }


  .info-card {
  margin: 16px;
  border-radius: 5px;
  border: 1px solid #fff;
  overflow: hidden;
  background-color: #fff;
  // min-height: 300px;
  .card-head {
    height: 48px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    .before-icon {
        width: 3px;
        height: 16px;
        background-image: url('~@/assets/station/consno-before.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        margin-right: 8px;
    }
    .card-head-text {
        flex:1;
        font-weight: 500;
        font-size: 16px;
        color: #12151A;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
    .button-wrap {
      display: flex;
      .invite-btn {
          background-color: #1ab2ff;
          border-color: #1ab2ff;
        }
        ::v-deep .el-button--small {
          font-size: 14px;
        }
      .distribution {
          margin-left: 24px;
          margin-right: 24px;
          display: flex;
          align-items: center;
        }
    }
  }
  .card-head-split {
    width: 100%;
    height: 1px;
    background-color: #E9EBF0;
    margin-bottom: 24px;
  }
  
  .people-table-wrap {
    ::v-deep .bd3001-table-select-box {
      display: none;
    }
    ::v-deep .bd3001-button {
      width: 100%;
    }
  }
  .form-wrap {
      padding: 0 24px 24px 24px;
      .custom-header {
          background: -webkit-gradient(linear, left top, left bottom, from(rgba(0, 149, 255, 0.5)), to(rgba(87, 152, 255, 0))), #f5faff;
          background: linear-gradient(180deg, rgba(0, 149, 255, 0.5) 0%, rgba(87, 152, 255, 0) 100%), #f5faff;
          background-repeat: no-repeat;
      }
      .model-detail {
        font-weight: 400;
        font-size: 16px;
        line-height: 16px;
        color: #217AFF;
        margin-top: 45px;

      }
    }

    .set-price-wrap {
        margin: 0 24px 24px 24px;
        box-sizing: border-box;
        // width: 100%;
        min-height: 132px;
        border-radius: 4px;
        border: 1px solid #E9EBF0;
        .set-price-title {
            height: 48px;
            background: linear-gradient(180deg, #E9F2FF 0%, #FFFFFF 100%);
            width: 100%;
            font-weight: 400;
            font-size: 16px;
            line-height: 16px;
            color: #292B33;
            display: flex;
            align-items: center;
            padding-left: 16px;
            box-sizing: border-box;
        }
        .set-price-info {
            margin: 0 16px 0 16px;
        }
    }
  }

  ::v-deep .bd3001-content {
    padding: 0 24px 12px 24px;
    box-shadow: none !important;
  }
  ::v-deep .bd3001-table-select-box {
        display: none;
    }
  ::v-deep .bd3001-header  {
        display: block !important;
    }
    ::v-deep .bd3001-button {
        display: block !important;
    }
    ::v-deep .vxe-table--header-wrapper{
        border-bottom: 0px !important;
    }

    ::v-deep .el-input {
    // width: 100%;
  }
  ::v-deep .el-range-editor.el-input__inner {
    width: 100%;
  }

  ::v-deep .el-input-number--mini {
    width: 80%;
  }


    .top-button-wrap {
        // margin-left: 24px;
        display: flex;
        align-items: center;
        height: 34px;
        align-items: center;
        margin-bottom: 14px;
        .choose-info-wrap {
            border-radius: 2px;
            height: 34px;
            padding: 0 10px;
            display: flex;
            align-items: center;
            background-color: #EBF3FF;
            font-family: PingFang SC;
            font-weight: 400;
            font-size: 14px;
            margin-left: 16px;
            color: #217AFF;
            .choose-number {
                font-size: 16px;
                font-weight: 500;
                margin: 0 4px;
            }
        }
        
    }


.bottom-button-wrap {
    height: 86px;
    margin-top: 16px;
    width: 100%;
    background-color: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-right: 32px;
    box-sizing: border-box;
}

.container {
      position: relative;
      padding-bottom: 100px;
      box-sizing: border-box;
      .bottom-wrap {
        z-index: 100;
          position: fixed;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 86px;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          background-color: #FFFFFF;
          padding-right: 32px;
          box-sizing: border-box;
      }
  }
 
  </style>
  