<template>
    <div class="container container-float " style="padding: 0 0 100px 0;">
        <div class="info-card" >
              <div class="card-head">
                  <div class="before-icon"></div>
                  <div class="card-head-text">基础信息</div>
              </div>

              <div class="card-head-split"></div>



              <div class="form-wrap">
                <el-form :model="baseInfo.form" :rules="baseInfo.rules" ref="baseInfoForm"  label-position="top">
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item
                            label="电价时段编号"
                            prop="timePeriodId"
                            :label-width="formLabelWidth"
                            >
                            <el-input 
                                v-model="baseInfo.form.timePeriodId"
                                placeholder="自动生成"
                                :disabled="true"
                            ></el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="8">
                            <el-form-item
                                label="电价时段名称"
                                prop="periodName"
                                :label-width="formLabelWidth"
                                >
                                <el-input 
                                    v-model="baseInfo.form.periodName"
                                    placeholder="请输入电价时段名称"
                                ></el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="8">
                            <el-form-item
                            label="适用城市"
                            prop="suitCityCode"
                            :label-width="formLabelWidth"
                            >
                                <el-select
                                    v-model="baseInfo.form.suitCityCode"
                                    placeholder="请选择适用城市"
                                    style="width: 100%"
                                >
                                    <el-option
                                    v-for="item in areaList"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                    ></el-option>
                                </el-select>
                        </el-form-item>
                        </el-col>
                    </el-row>
                   
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item
                            label="创建单位"
                            prop="createUnit"
                            :label-width="formLabelWidth"
                            >
                            <el-select
                                v-model="baseInfo.form.createUnit"
                                placeholder="请选择创建单位"
                                style="width: 100%"
                                >
                                <el-option
                                    v-for="item in  dict.type.ls_charging_contracted_unit"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                ></el-option>
                            </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>

                </el-form>
              </div>
        </div>

        <div class="info-card" >
                <div class="card-head">
                    <div class="before-icon"></div>
                    <div class="card-head-text">时段信息</div>
                </div>

                <div class="card-head-split"></div>

                <div class="month-picker">
                    <!-- 月份标签 -->
                    <div 
                        v-for="(item, index) in periodDetailList"
                        :key="index"
                        :class="item.active?'month-tag-choose':'month-tag' "
                        @click="onClickMonthTag(index)"
                    >
                        {{ item.month }}
                        <span class="remove" @click.stop="removeMonth(index)" v-if="periodDetailList.length > 1">×</span>
                    </div>

                    <!-- 添加按钮 -->
                    <div class="add-btn" @click="addNextMonth"></div>
                </div>

                <div 
                    v-for="(item, index) in periodDetailList"
                    :key="index"
                >
                    <div
                        class="period-warp"
                        v-if="item.active"
                    >
                        <el-form :model="item" :rules="periodInfoRules" :ref="`periodForm${index}`" label-position="top">
                            <el-row :gutter="20">
                                <el-col :span="6">
                                    <el-form-item
                                        label="年月"
                                        prop="month"
                                        :label-width="formLabelWidth"
                                    >
                                    <el-date-picker
                                        v-model="item.month" 
                                        value-format="yyyy-MM"
                                        type="month"
                                        :clearable="false"
                                    >

                                    </el-date-picker>
                                    </el-form-item>
                                </el-col>
                            </el-row>

                            

                            <el-row :gutter="20"  
                                v-for="(priceItem, priceIndex) in item.priceList"
                                :key="index"
                            >
                                <el-col :span="7">
                                    <el-form-item
                                        label="电价区间"
                                        prop="priceType"
                                        :label-width="formLabelWidth"
                                    >
                                        <el-select
                                            v-model="priceItem.priceType"
                                            placeholder="请选择电价区间"
                                            style="width: 100%"
                                        >
                                            <el-option
                                                v-for="item in priceTypeList"
                                                :key="item.value"
                                                :label="item.label"
                                                :value="item.value"
                                            ></el-option>
                                        </el-select>
                                    </el-form-item>
                                </el-col>

                                <el-col :span="8">
                                    <el-form-item
                                        label="开始时间"
                                        prop="startTime"
                                        :label-width="formLabelWidth"
                                    >
                                    <el-time-select
                                        placeholder="开始时间"
                                        v-model="priceItem.startTime"
                                        :picker-options="{
                                            start: '00:00',
                                            step: '00:30',
                                            end: '24:00'
                                        }">
                                    </el-time-select>

                                    </el-form-item>
                                </el-col>

                                <el-col :span="8">
                                    <el-form-item
                                        label="结束时间"
                                        prop="endTime"
                                        :label-width="formLabelWidth"
                                    >
                                    <el-time-select
                                        placeholder="结束时间"
                                        v-model="priceItem.endTime"
                                        :picker-options="{
                                            start: '00:00',
                                            step: '00:30',
                                            end: '24:00',
                                            minTime: priceItem.startTime,
                                        }">
                                    </el-time-select>
                                    </el-form-item>
                                </el-col>

                                <el-col :span="1">
                                    <div class="price-delete " v-if="item.priceList.length > 1" @click="onClickDeletePrice(index,priceIndex)"></div>
                                </el-col>

                            </el-row>

                            <el-row >
                                <el-col :span="23">
                                    <div class="price-add" @click="onClickAddPrice(index)">+ 添加</div>
                                </el-col>
                            </el-row>
                        </el-form>
                    </div>
                </div>



        </div>

        <div class="bottom-wrap">
            <div>
                <el-button @click="handleCancel">取消</el-button>
                <el-button @click="handleSave('save')" type="primary" v-if="approvalStatus !=='02'">保存</el-button>
                <el-button @click="handleSave('audit')" type="primary" >提交</el-button>

            </div>

        </div>
    </div>
    
  </template>
  
  <script>

import {
    getAreaList,
    addElectricPricePeriod,
    getElectricPricePeriodDetail,
    updateElectricPricePeriod,
  } from "@/api/electricPricePeriod/index";
  
    export default {
    components: {
        
    },
    dicts: [
        'ls_charging_contracted_unit',
    ],
    data() {
        return {
            type: 'create', 
            formLabelWidth: '120px',
            areaList:[],

            chargePeriodId: '', // 时段id
            approvalStatus: '', // 审批状态

            baseInfo: {
                form: {
                    timePeriodId: '',
                    periodName: '',
                    suitCityCode: '',
                    createUnit: '',
                },
                rules: {
                    periodName: [
                        { required: true, message: '请输入电价时段名称', trigger: 'blur' },
                    ],
                    suitCityCode: [
                        { required: true, message: '请选择适用城市', trigger: 'blur' },
                    ],
                    createUnit: [
                        { required: true, message: '请选择创建单位', trigger: 'blur' },
                    ]
                },
            },

            periodDetailList: [
                {
                    month: this.getCurrentMonth(),
                    priceList: [
                        { priceType: '1', startTime: '', endTime: '' },
                    ],
                    active: true,
                }
            ],

            periodInfoRules: {
                month: [
                    { required: true, message: '请选择月份', trigger: 'blur' },
                ],
                priceType: [
                    { required: true, message: '请选择电价区间', trigger: 'blur' },
                ],
                // startTime: [
                //     { required: true, message: '请选择开始时间', trigger: 'change' },
                // ],
                // endTime: [
                //     { required: true, message: '请选择结束时间', trigger: 'change' },
                // ],
            },

            priceTypeList: [
                { label: '尖', value: '1' },
                { label: '峰', value: '2' },
                { label: '平', value: '3' },
                { label: '谷', value: '4' },
            ]
        };
    },

    computed: {
    },
    mounted() {
        const chargePeriodId = this.$route.query.chargePeriodId;
        if(chargePeriodId) {
            this.chargePeriodId = chargePeriodId;
            this.getElectricPricePeriodDetail();
        }
        this.getAreaList();
    },
    methods: {
        // 获取计费详情
        async getElectricPricePeriodDetail() {
            const [err, res] = await getElectricPricePeriodDetail({
                operateId: this.chargePeriodId
            })
            if (err) return

            const {
                periodNo,
                periodName,
                suitCityCode,
                createUnit,
                periodDetailList,
                approvalStatus,
            } = res.data;

            this.baseInfo.form = {
                timePeriodId:periodNo,
                periodName: periodName,
                suitCityCode: suitCityCode,
                createUnit: createUnit,
            }

            this.approvalStatus = approvalStatus

            console.log(periodDetailList,'periodDetailList');

            const list = Array.from(
                periodDetailList.reduce((map, item) => {
                    const month = item.validMonth;
                    if (!map.has(month)) {
                    map.set(month, {
                        month,
                        priceList: [],
                        active: false
                    });
                    }
                    map.get(month).priceList.push({
                    priceType: item.timeFlag,
                    startTime: item.beginTime,
                    endTime: item.endTime
                    });
                    return map;
                }, new Map()).values()
            ).map((group, index) => index === 0 ? { ...group, active: true } : group);
            
           this.periodDetailList = list;
          
        },
         // 获取适用地市
         async getAreaList() {
            const [err, res] = await getAreaList({
                areaLevel: '03',
                huNanOnly: true
            })
            if (err) return
            const {
                data
            } = res
            const list = []
            data.forEach(item => {
                list.push({
                    label: item.areaName,
                    value: item.areaCode,
                })
            })
            this.areaList = list
            
        },

        // 获取当前月的YYYY-MM格式
        getCurrentMonth() {
            const date = new Date()
            return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}`
        },
         // 添加下个月
        addNextMonth() {
            const lastMonth = this.periodDetailList[this.periodDetailList.length - 1].month
            const [year, month] = lastMonth.split('-').map(Number)
            
            // 计算下个月
            let nextYear = year
            let nextMonth = month + 1
            if (nextMonth > 12) {
                nextYear += 1
                nextMonth = 1
            }

            const nextMonthStr = `${nextYear}-${nextMonth.toString().padStart(2, '0')}`

            this.periodDetailList.push(
                {
                    month: nextMonthStr,
                    priceList: [
                        {
                             priceType: '1', 
                             startTime: '', 
                             endTime: ''
                        }
                    ],
                    active: false,
                }
            )
        },
        
        // 选中月份
        onClickMonthTag(index) {

            const list = []
            this.periodDetailList.forEach((item, i) => {
                if (i === index) {
                    list.push({
                        ...item,
                        active: true,
                    })
                } else {
                    list.push({
                        ...item,
                        active: false,
                    })
                }
            })
            this.periodDetailList = list
        },

        // 删除月份
        removeMonth(index) {           
            if (this.periodDetailList[index].active) {
                this.periodDetailList.splice(index, 1)
                this.periodDetailList[0].active = true
            } else {
                this.periodDetailList.splice(index, 1)
            }
        },


        // 添加电价区间
        onClickAddPrice(index) {
            this.periodDetailList[index].priceList.push({
                priceType: '',
                startTime: '',
                endTime: '',
            })
        },

        // 删除电价区间
        onClickDeletePrice(index,priceIndex) {
            this.periodDetailList[index].priceList.splice(priceIndex, 1)
        },
        // 取消
        handleCancel() {
            this.$router.back()
        },

        // 保存/提交
        handleSave: _.debounce(async function(type){
            Promise.all([
                this.validateForm('baseInfoForm'),
                // this.validateForm('periodForm'),
                // ...this.periodDetailList.map((item, index) => this.validateForm(`periodForm${index}`)),
            ])
            .then(async () => {
                
                // 手动校验时段信息
                for (const [monthIndex, monthItem] of this.periodDetailList.entries()) {
                    const { month, priceList } = monthItem;

                    // 校验每个电价区间
                    for (const [priceIndex, priceItem] of priceList.entries()) {
                    const { priceType, startTime, endTime } = priceItem;

                    // 手动校验必填项
                    if (!priceType) {
                        return this.$message.warning(`${month} 第${priceIndex + 1}个电价区间类型未选择`);
                    }
                    if (!startTime) {
                        return this.$message.warning(`${month} 第${priceIndex + 1}个电价区间开始时间未填写`);
                    }
                    if (!endTime) {
                        return this.$message.warning(`${month} 第${priceIndex + 1}个电价区间结束时间未填写`);
                    }

                    // 校验时间有效性
                    const start = this.timeToMinutes(startTime);
                    const end = this.timeToMinutes(endTime);
                    if (start >= end) {
                        return this.$message.warning(`${month} 第${priceIndex + 1}个电价区间结束时间必须晚于开始时间`);
                    }
                    }

                    // 校验时间段重叠
                    if (this.hasTimeOverlap(priceList)) {
                        return this.$message.warning(`${month} 中存在时间段重叠，请调整`);
                    }

                   // 新增校验：24小时完整覆盖
                    const sortedTimes = priceList
                        .map(item => ({
                            start: this.timeToMinutes(item.startTime),
                            end: this.timeToMinutes(item.endTime),
                        }))
                        .sort((a, b) => a.start - b.start);

                    // if (sortedTimes.length === 0) {
                    //     return this.$message.warning(`${month} 必须设置时间段`);
                    // }

                    // // 检查开始时间是否为00:00
                    // if (sortedTimes[0].start !== 0) {
                    // return this.$message.warning(`${month} 第一个时间段必须从00:00开始`);
                    // }

                    // // 检查结束时间是否为24:00 
                    // if (sortedTimes[sortedTimes.length - 1].end !== 1440) {
                    // return this.$message.warning(`${month} 最后一个时间段必须到24:00结束`);
                    // }

                    // // 检查时间段连续性
                    // let prevEnd = 0;
                    // for (const time of sortedTimes) {
                    // if (time.start !== prevEnd) {
                    //     return this.$message.warning(`${month} 时间段存在间隙（${prevEnd}→${time.start}）`);
                    // }
                    // prevEnd = time.end;
                    // }

                    // 检查总时长
                    const total = sortedTimes.reduce((sum, time) => sum + (time.end - time.start), 0);
                    if (total !== 1440) {
                        return this.$message.warning(`${month} 配置的时段需要满24小时`);
                    }


                }


                const {
                    periodName,
                    suitCityCode,
                    createUnit,
                } = this.baseInfo.form

                const list = []

                this.periodDetailList.forEach((item, index) => {
                    const {
                        month,
                        priceList,
                    } = item

                    priceList.forEach((priceItem) => {
                        const  {
                            startTime,
                            endTime,
                            priceType,
                        } = priceItem
                        list.push(
                            {
                                validMonth: month,
                                beginTime: startTime,
                                endTime,
                                timeFlag: priceType,
                            }
                        )
                    })
                })

                const params = {
                    periodName,
                    suitCityCode,
                    createUnit,
                    isSubmitAudit: type === 'audit' ? true : false,
                    periodDetailList: list
                }

                if ( !this.chargePeriodId) {
                    // 新增 
                    const [err,res] = await addElectricPricePeriod(
                        params
                    )

                    if (err){ 
                        return this.$message.error(err.message || '新增电价时段失败');
                    }

                    this.$message({
                        type: 'success',
                        message: '新增成功!'
                    });

                    setTimeout(() => {
                        this.$router.back();
                    }, 500);
                } else {
                    // 修改
                    const [err,res] = await updateElectricPricePeriod(
                        {
                            ...params,
                            chargePeriodId : this.chargePeriodId
                        }
                    )

                    if (err){ 
                        return this.$message.error(err.message || '修改电价时段失败');
                    }

                    this.$message({
                        type: 'success',
                        message: '修改成功!'
                    });

                    setTimeout(() => {
                        this.$router.back();
                    }, 500);
                }
            })
            .catch((err) => {
                console.log(err)
                // 有表单校验失败
                this.$message.error('表单校验失败，请检查输入');
            })
           


        }, 300),

         // 校验单个表单
        validateForm(formRef) {
            return new Promise((resolve, reject) => {
                this.$refs[formRef].validate((valid) => {
                    if (valid) {
                        resolve(); // 校验通过
                    } else {
                        reject(new Error(`${formRef} 校验失败`)); // 校验失败
                    }
                });
            });
        },

        timeToMinutes(timeStr) {
            if (!timeStr) return 0;
            const [hours, minutes] = timeStr.split(':').map(Number);
            return hours * 60 + minutes;
        },
        hasTimeOverlap(intervals) {
            // 转换为分钟并排序
            const sorted = intervals.map(item => ({
                start: this.timeToMinutes(item.startTime),
                end: this.timeToMinutes(item.endTime),
            })).sort((a, b) => a.start - b.start);

            // 检查是否有重叠
            for (let i = 1; i < sorted.length; i++) {
                const prev = sorted[i - 1];
                const current = sorted[i];
                if (prev.end > current.start) {
                    return true;
                }
            }
            return false;
        },

    },


  };
  </script>
  
  <style lang="scss" scoped>
  .container-full {
    background-color: rgba(244, 246, 249, 1);
    padding-bottom: 16px;
    min-height: calc(100% - 80px);
    overflow-y: auto;
  }

  .info-card {
  margin: 16px;
  border-radius: 5px;
  border: 1px solid #fff;
  overflow: hidden;
  background-color: #fff;
  // min-height: 300px;
  .card-head {
    height: 48px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    .before-icon {
        width: 3px;
        height: 16px;
        background-image: url('~@/assets/station/consno-before.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        margin-right: 8px;
    }
    .card-head-text {
        flex:1;
        font-weight: 500;
        font-size: 16px;
        color: #12151A;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
    .button-wrap {
      display: flex;
      .invite-btn {
          background-color: #1ab2ff;
          border-color: #1ab2ff;
        }
        ::v-deep .el-button--small {
          font-size: 14px;
        }
      .distribution {
          margin-left: 24px;
          margin-right: 24px;
          display: flex;
          align-items: center;
        }
    }
  }
  .card-head-split {
    width: 100%;
    height: 1px;
    background-color: #E9EBF0;
    margin-bottom: 24px;
  }
  
  .people-table-wrap {
    ::v-deep .bd3001-table-select-box {
      display: none;
    }
    ::v-deep .bd3001-button {
      width: 100%;
    }
  }
  .form-wrap {
      padding: 0 16px 16px 16px;
      .custom-header {
          background: -webkit-gradient(linear, left top, left bottom, from(rgba(0, 149, 255, 0.5)), to(rgba(87, 152, 255, 0))), #f5faff;
          background: linear-gradient(180deg, rgba(0, 149, 255, 0.5) 0%, rgba(87, 152, 255, 0) 100%), #f5faff;
          background-repeat: no-repeat;
      }
    }
  }

  .period-warp {
    padding: 16px 24px 24px 24px;
  }

  .container {
      position: relative;
      padding-bottom: 100px;
      box-sizing: border-box;
      .bottom-wrap {
          position: fixed;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 86px;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          background-color: #FFFFFF;
          padding-right: 32px;
          box-sizing: border-box;
      }
  }

.month-picker {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin: 0 32px;
    .month-tag-choose {
        height: 32px;
        display: inline-flex;
        align-items: center;
        padding: 8px 10px;
        border-radius: 5px;
        border: 1px solid #217AFF;
        background-color: #217AFF;
        color: #FFFFFF;
            .remove {
            margin-left: 8px;
            cursor: pointer;
            color: #FFFFFF;
            &:hover {
                color: #f56c6c;
            }
        }
    }
    .month-tag {
        height: 32px;
        display: inline-flex;
        align-items: center;
        padding: 8px 10px;
        border-radius: 5px;
        border: 1px solid #A6C6FF;
        background-color: #EBF3FF;
        color: #292B33;
            .remove {
            margin-left: 8px;
            cursor: pointer;
            color: #505363;
            &:hover {
                color: #f56c6c;
            }
        }
    }

    .add-btn {
        width: 32px;
        height: 32px;
        background-image: url('~@/assets/charge/add-btn.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;

    }
 
}

::v-deep .el-input {
    width: 100% !important;
}

.price-add {
    width: 100%;
    height: 34px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #DFE1E5;
    border-radius: 2px;
    font-family: PingFang SC;
    font-weight: 400;
    font-size: 16px;
    color: #217AFF;
}
.price-delete {
    width:16px;
    height: 16px;
    background-image: url('~@/assets/station/station-build-delete.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    margin-top: 44px;
}





  </style>
  