<template>
  <div class="container container-float" style="padding: 0 0 100px 0">
    <div class="info-card">
      <div class="card-head" style="margin-bottom: 8px">
        <div class="before-icon"></div>
        <div class="card-head-text">历史下发记录</div>
      </div>
      <div class="form-wrap">
        <el-row :gutter="20" style="margin-bottom: 24px">
          <el-col :span="8">
            <div style="display: flex">
              <div class="info-title">充电桩编号：</div>
              <div class="info-detail">{{ baseInfo.pileNo }}</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div style="display: flex">
              <div class="info-title">充电桩名称：</div>
              <div class="info-detail">{{ baseInfo.pileName }}</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div style="display: flex">
              <div class="info-title">所属充电站：</div>
              <div class="info-detail">{{ baseInfo.stationName }}</div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <div class="info-card">
      <BuseCrud
        ref="crud"
        :loading="table.loading"
        :tablePage="table.page"
        :tableColumn="tableColumn"
        :tableData="table.data"
        :pagerProps="pagerProps"
        :modalConfig="modalConfig"
        @loadData="getTablePage"
      >
        <template slot="operate" slot-scope="{ row }">
          <div class="menu-box">
            <el-button type="primary" plain @click="hanleDetail(row)">
              查看
            </el-button>
          </div>
        </template>
      </BuseCrud>
    </div>

    <div class="bottom-wrap">
      <el-button @click="() => handleCancel()">返回</el-button>
    </div>
  </div>
</template>

<script>
import { getPileHistoryList } from '@/api/chargingManage/stationBilling.js';

export default {
  dicts: [
    'ls_charging_charge_mode', // 计费模式
    'ls_charging_contracted_unit', // 单位
  ],
  data() {
    return {
      baseInfo: {},
      table: {
        loading: false,
        page: {
          total: 0,
          currentPage: 1,
          pageSize: 10,
        },
        dataTotal: [],
        data: [],
      },
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      tableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
        },
        {
          field: 'chcNo',
          title: '计费编号',
          minWidth: 120,
        },
        {
          field: 'chcName',
          title: '计费名称',
          minWidth: 160,
        },
        {
          field: 'chargeMode',
          title: '计费模式',
          minWidth: 120,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_charge_mode,
              cellValue
            );
          },
        },
        {
          field: 'suitCityName',
          title: '适用地市',
          minWidth: 120,
        },
        {
          field: 'createUnit',
          title: '单位',
          minWidth: 120,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_contracted_unit,
              cellValue
            );
          },
        },
        {
          field: 'createTime',
          title: '下发时间',
          minWidth: 160,
        },
        {
          field: 'successTime',
          title: '下发成功时间',
          minWidth: 160,
        },
        {
          field: 'createUser',
          title: '操作人',
          minWidth: 120,
        },
        {
          title: '操作',
          slots: { default: 'operate' },
          width: 200,
          align: 'center',
          fixed: 'right',
        },
      ],
      pileId: '',
    };
  },
  computed: {
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  async mounted() {
    // console.log('===', this.$route.query);
    if (this.$route.query.pileId) {
      this.pileId = this.$route.query.pileId;
    }
    if (this.$route.query.topData) {
      this.baseInfo = this.$route.query.topData;
    }
    await this.loadData();
    await this.getTablePage();
  },
  methods: {
    async loadData() {
      this.loading = true;
      let params = {
        pileId: this.pileId,
      };
      const [err, res] = await getPileHistoryList(params);
      this.loading = false;
      if (err) {
        return;
      }
      const { data, total } = res;
      // console.log('res', res);
      this.table.dataTotal = data;
      this.table.page.total = total;
    },
    getTablePage() {
      this.table.data = this.table.dataTotal.slice(
        (this.table.page.currentPage - 1) * this.table.page.pageSize,
        this.table.page.currentPage * this.table.page.pageSize
      );
    },
    // 查看详情
    hanleDetail(val) {
      // console.log(val);
      this.$router.push({
        path: '/v2g-charging/operatorManage/chargingManage/stationBilling/audit',
        query: {
          chcNo: val.chcNo,
          type: 'detail',
        },
      });
    },
    // 取消
    handleCancel() {
      this.$router.back();
    },
  },
};
</script>

<style lang="scss" scoped>
.container-full {
  background-color: rgba(244, 246, 249, 1);
  padding-bottom: 16px;
  min-height: calc(100% - 80px);
  overflow-y: auto;
}

.info-card {
  margin: 16px;
  border-radius: 5px;
  border: 1px solid #fff;
  overflow: hidden;
  background-color: #fff;
  // min-height: 300px;
  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    background: linear-gradient(180deg, #e9f2ff 0%, #ffffff 100%);
    .before-icon {
      width: 3px;
      height: 16px;
      background-image: url('~@/assets/station/consno-before.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin-right: 8px;
    }
    .card-head-text {
      flex: 1;
      font-weight: 500;
      font-size: 16px;
      color: #12151a;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }

    .button-wrap {
      display: flex;
      .invite-btn {
        background-color: #1ab2ff;
        border-color: #1ab2ff;
      }
      ::v-deep .el-button--small {
        font-size: 14px;
      }
      .distribution {
        margin-left: 24px;
        margin-right: 24px;
        display: flex;
        align-items: center;
      }
    }
  }

  .people-table-wrap {
    ::v-deep .bd3001-table-select-box {
      display: none;
    }
    ::v-deep .bd3001-button {
      width: 100%;
    }
  }
  .form-wrap {
    padding: 0 16px 0 16px;
    .info-title {
      font-weight: 400;
      font-size: 16px;
      line-height: 16px;
      color: #505363;
    }
    .info-detail {
      font-weight: 400;
      font-size: 16px;
      line-height: 16px;
      color: #292b33;
      display: flex;
      .info-price {
        font-weight: 400;
        color: #ff8d24;
        margin-right: 5px;
      }
    }
    .info-amount {
      height: 28px;
      background-color: #fff7e6;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 8px;
      font-weight: 400;
      font-size: 20px;
      color: #fe8921;
      margin-top: -6px;
      margin-right: 4px;
    }
    .info-img {
      width: 140px;
      height: 140px;
    }
  }
}

::v-deep .bd3001-content {
  padding: 0 24px 12px 24px;
}

.choose-info-wrap {
  border-radius: 2px;
  height: 34px;
  padding: 0 10px;
  display: flex;
  align-items: center;
  background-color: #ebf3ff;
  font-family: PingFang SC;
  font-weight: 400;
  font-size: 14px;
  margin: 8px 0 16px 0;
  color: #217aff;
  .choose-number {
    font-size: 16px;
    font-weight: 500;
    margin: 0 4px;
  }
}

.container {
  position: relative;
  padding-bottom: 100px;
  box-sizing: border-box;
  .bottom-wrap {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 86px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    background-color: #ffffff;
    padding-right: 32px;
    box-sizing: border-box;
    z-index: 5;
  }
}
</style>
