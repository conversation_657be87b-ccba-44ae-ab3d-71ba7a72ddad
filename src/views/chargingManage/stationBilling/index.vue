<template>
  <div class="container container-float" style="padding: 0 0 100px 0">
    <div class="table-wrap">
      <BuseCrud
        ref="crud"
        :loading="loading"
        :filterOptions="filterOptions"
        :tablePage="tablePage"
        :tableColumn="tableColumn"
        :tableData="tableData"
        :pagerProps="pagerProps"
        :modalConfig="modalConfig"
        @loadData="loadData"
      >
        <template slot="defaultHeader">
          <div>
            <div class="card-head">
              <div class="card-head-text">站桩计费列表</div>
              <!-- <div class="top-button-wrap">
                <el-button type="primary" @click="() => handleAdd()">
                  计费下发
                </el-button>
              </div> -->
            </div>
          </div>
        </template>
        <template slot="chcName" slot-scope="{ row }">
          <a @click="handleDetail(row)" style="color: #088dff">
            {{ row.chcName }}
          </a>
        </template>
        <template slot="operate" slot-scope="{ row }">
          <div class="menu-box">
            <el-button
              type="primary"
              plain
              class="add-btn"
              @click="historyRecord(row)"
            >
              历史下发记录
            </el-button>
          </div>
        </template>
      </BuseCrud>
    </div>
  </div>
</template>

<script>
import { getStationList } from '@/api/stationSOC/index.js';
import { getPileList } from '@/api/chargingManage/stationBilling.js';

export default {
  components: {},
  dicts: [
    'ls_charging_operation_status', // 桩运营状态
  ],
  data() {
    return {
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
        },
        {
          field: 'pileNo',
          title: '充电桩编号',
          minWidth: 120,
        },
        {
          field: 'pileName',
          title: '充电桩名称',
          minWidth: 160,
        },
        {
          field: 'stationName',
          title: '所属充电站',
          minWidth: 200,
        },
        {
          // field: 'chcName',
          slots: { default: 'chcName' },
          title: '计费模型',
          minWidth: 120,
        },
        {
          field: 'areaNameSummary',
          title: '省市区',
          minWidth: 160,
        },
        {
          field: 'operStatus',
          title: '桩状态',
          minWidth: 120,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charging_operation_status,
              cellValue
            );
          },
        },
        {
          title: '操作',
          slots: { default: 'operate' },
          width: 150,
          align: 'center',
          fixed: 'right',
        },
      ],
      tableData: [],
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      params: {
        pileNo: '',
        pileName: '',
        stationId: '',
        operStatus: '',
      },
      stationList: [],
      stationLoading: false,
    };
  },

  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'pileNo',
            title: '充电桩编号',
            element: 'el-input',
            props: { placeholder: '请输入' },
          },
          {
            field: 'pileName',
            title: '充电桩名称',
            element: 'el-input',
            props: { placeholder: '请输入' },
          },
          {
            field: 'stationId',
            title: '所属充电站',
            element: 'el-select',
            props: {
              options: this.stationList,
              filterable: true,
              remote: true,
              remoteMethod: this.debouncedStationSearch,
              loading: this.stationLoading,
            },
          },
          {
            field: 'operStatus',
            title: '桩状态',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.dict.type.ls_charging_operation_status,
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  mounted() {
    this.loadData();
  },
  methods: {
    // 获取电站
    async debouncedStationSearch(query) {
      // console.log(query, 'query');
      if (query !== '') {
        this.stationLoading = true;
        setTimeout(async () => {
          const [err, res] = await getStationList({
            stationName: query,
          });

          if (err) return;
          this.stationLoading = false;
          this.stationList = res.data.map((item) => ({
            label: item.stationName,
            value: item.stationId,
          }));
        }, 200);
      } else {
        this.stationList = [];
      }
    },
    // 加载数据
    async loadData() {
      this.loading = true;
      let params = {
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
        pileNo: this.params.pileNo,
        pileName: this.params.pileName,
        stationId: this.params.stationId,
        operStatus: this.params.operStatus,
      };
      const [err, res] = await getPileList(params);
      this.loading = false;
      if (err) {
        return;
      }
      const { data, total } = res;
      console.log('data', data);
      this.tableData = data;
      this.tablePage.total = total;
    },
    // 计费模型详情跳转
    handleDetail(row) {
      this.$router.push({
        path: '/v2g-charging-web/operatorManage/chargingManage/stationBilling/audit',
        query: {
          chcNo: row.chcNo,
          type: 'detail',
        },
      });
    },
    // 历史下发记录
    historyRecord(val) {
      console.log(val);
      this.$router.push({
        path: '/v2g-charging-web/operatorManage/chargingManage/stationBilling/history',
        query: {
          pileId: val.pileId,
          topData: {
            pileNo: val.pileNo,
            pileName: val.pileName,
            stationName: val.stationName,
          },
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.container-full {
  background-color: rgba(244, 246, 249, 1);
  padding-bottom: 16px;
  min-height: calc(100% - 80px);
  overflow-y: auto;
}

.table-wrap {
  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }

  .card-head {
    // position: relative;
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    margin-top: -20px;
    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }
  .info-wrap {
    margin-top: 16px;
    display: flex;
    flex-wrap: wrap;
    gap: 16px;

    .info-item {
      background-color: #fafbfc;
      flex: 1 1 0;
      // min-width: 180px;

      border-radius: 5px;
      padding: 8px 24px;
      box-sizing: border-box;
      // margin-right: 16px;
      display: flex;
      .info-icon {
        width: 42px;
        height: 42px;
      }
      .info-right-wrap {
        flex: 1;
        margin-left: 8px;
        .info-title {
          font-weight: 400;
          font-size: 14px;
          line-height: 14px;
          margin-bottom: 8px;
        }
        .info-number {
          font-size: 20px;
          font-weight: 500;
          .info-unit {
            font-size: 14px;
            font-weight: 400;
          }
        }
      }
    }
    .info-item:last-child {
      margin-right: 0;
    }
  }

  .top-button-wrap {
    display: flex;
    margin: 16px 0;
  }
}
</style>
