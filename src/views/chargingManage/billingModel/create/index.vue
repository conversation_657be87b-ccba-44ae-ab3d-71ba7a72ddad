<template>
    <div class="container container-float " style="padding: 0 0 100px 0;">
        <div class="info-card" >
              <div class="card-head">
                  <div class="before-icon"></div>
                  <div class="card-head-text">基础信息</div>
              </div>

              <div class="card-head-split"></div>

              <div class="form-wrap">
                <el-form :model="baseInfo.form" :rules="baseInfo.rules" ref="baseInfoForm"  label-position="top">
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item
                            label="计费编号"
                            prop="billingConfId"
                            :label-width="formLabelWidth"
                            >
                            <el-input 
                                v-model="chcNo"
                                placeholder="自动生成"
                                :disabled="true"
                            ></el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="8">
                            <el-form-item
                                label="计费名称"
                                prop="chcName"
                                :label-width="formLabelWidth"
                                >
                                <el-input 
                                    v-model="baseInfo.form.chcName"
                                    placeholder="请输入计费名称"
                                ></el-input>
                            </el-form-item>
                        </el-col>

                        <el-col :span="8">
                            <el-form-item
                            label="适用城市"
                            prop="suitCityCode"
                            :label-width="formLabelWidth"
                            >
                                <el-select
                                    v-model="baseInfo.form.suitCityCode"
                                    placeholder="请选择适用城市"
                                    style="width: 100%"
                                    @change="cityChange"
                                >
                                    <el-option
                                    v-for="item in areaList"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                    ></el-option>
                                </el-select>
                        </el-form-item>
                        </el-col>
                    </el-row>
                   
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item
                            label="申请单位"
                            prop="createUnit"
                            :label-width="formLabelWidth"
                            >
                            <el-select
                                v-model="baseInfo.form.createUnit"
                                placeholder="请选择申请单位"
                                style="width: 100%"
                                >
                                <el-option
                                    v-for="item in  dict.type.ls_charging_contracted_unit"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                ></el-option>
                            </el-select>
                            </el-form-item>
                        </el-col>
                    </el-row>

                </el-form>
              </div>
        </div>

        <div class="info-card" >
              <div class="card-head">
                  <div class="before-icon"></div>
                  <div class="card-head-text">计费信息</div>
              </div>

              <div class="card-head-split"></div>

              <div class="form-wrap">
                <el-form :model="billingInfo.form" :rules="billingInfo.rules" ref="billingInfoForm"  label-position="top">
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item
                                label="计费模式"
                                prop="chargeMode"
                                :label-width="formLabelWidth"
                                >
                                    <el-select
                                        v-model="billingInfo.form.chargeMode"
                                        placeholder="请选择计费模式"
                                        style="width: 100%"
                                    >
                                        <el-option
                                            v-for="item in dict.type.ls_charging_billing_charge_mode"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value"
                                        ></el-option>
                                    </el-select>
                            </el-form-item>
                        </el-col>

                        <!-- <el-col :span="8">
                            <el-form-item
                                label="收费类型"
                                prop="itemNo"
                                :label-width="formLabelWidth"
                                >
                                    <el-select
                                        v-model="billingInfo.form.itemNo"
                                        placeholder="请选择收费类型"
                                        style="width: 100%"
                                    >
                                        <el-option
                                            v-for="item in dict.type.ls_charging_billing_charge_type"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value"
                                        ></el-option>
                                    </el-select>
                            </el-form-item>
                        </el-col> -->

                        <!-- <el-col :span="8">
                            <el-form-item
                                label="营销电价类型"
                                prop="priceType"
                                :label-width="formLabelWidth"
                                >
                                    <el-select
                                        v-model="billingInfo.form.priceType"
                                        placeholder="请选择营销电价类型"
                                        style="width: 100%"
                                    >
                                        <el-option
                                            v-for="item in priceTypeList"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value"
                                        ></el-option>
                                    </el-select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="8">
                            <el-form-item
                                label="营销电价名称"
                                prop="priceName"
                                :label-width="formLabelWidth"
                                >
                                    <el-select
                                        v-model="billingInfo.form.priceName"
                                        placeholder="请选择营销电价名称"
                                        style="width: 100%"
                                    >
                                        <el-option
                                            v-for="item in priceNameList"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value"
                                        ></el-option>
                                    </el-select>
                            </el-form-item>
                        </el-col>

                        <el-col :span="8">
                            <el-form-item
                                label="是否与营销电价一致"
                                prop="isSame"
                                :label-width="formLabelWidth"
                                >
                                    <el-select
                                        v-model="billingInfo.form.isSame"
                                        placeholder="请选择是否与营销电价一致"
                                        style="width: 100%"
                                    >
                                        <el-option
                                            v-for="item in isSameList"
                                            :key="item.value"
                                            :label="item.label"
                                            :value="item.value"
                                        ></el-option>
                                    </el-select>
                            </el-form-item>
                        </el-col> -->

                        <el-col :span="8" v-if="billingInfo.form.chargeMode === '0201'">
                            <el-form-item
                                label="电价(元/kwh)："
                                prop="chargePrice"
                                :label-width="formLabelWidth"
                                >
                                <el-input-number
                                    style="width: 100%;"
                                    v-model="billingInfo.form.chargePrice"
                                    :min="0"
                                    :precision="2"
                                    :step="0.01"
                                    :controls="false"
                                    placeholder="请输入电价"
                                ></el-input-number>
                            </el-form-item>
                        </el-col>

                        <el-col :span="8" v-if="billingInfo.form.chargeMode === '0201'">
                            <el-form-item
                                label="服务费(元/kwh)："
                                prop="servicePrice"
                                :label-width="formLabelWidth"
                                >
                                <el-input-number
                                    style="width: 100%;"
                                    v-model="billingInfo.form.servicePrice"
                                    :min="0"
                                    :precision="2"
                                    :step="0.01"
                                    :controls="false"
                                    placeholder="请输入服务费"
                                ></el-input-number>
                            </el-form-item>
                        </el-col>

                        <el-col :span="8" v-if="billingInfo.form.chargeMode === '0201'">
                            <el-form-item
                                label="总价(元/kwh)："
                                prop="totalPrice"
                                :label-width="formLabelWidth"
                                >
                                <el-input-number
                                    style="width: 100%;"
                                    v-model="totalPrice"
                                    disabled
                                    :precision="2"
                                    :step="0.01"
                                    :controls="false"
                                    
                                ></el-input-number>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
              </div>



                <div class="set-price-wrap" v-if="billingInfo.form.chargeMode === '0202'" >
                        <div class="set-price-title">
                            设置时段电价
                        </div>
                        <div class="set-price-info">
                            <el-form :model="priceInfo.form" :rules="priceInfo.rules" ref="priceInfoForm"  label-position="top">
                                <el-row :gutter="20">
                                    <el-col :span="6">
                                        <el-form-item
                                            label="尖-电价："
                                            prop="topChargePrice"
                                            :label-width="formLabelWidth"
                                            >
                                            <el-input-number
                                                style="width: 100%;"
                                                v-model="priceInfo.form.topChargePrice"
                                                :min="0"
                                                :precision="2"
                                                :step="0.01"
                                                :controls="false"
                                                placeholder="请输入尖-电价"
                                            ></el-input-number>
                                        </el-form-item>
                                    </el-col>

                                    <el-col :span="6">
                                        <el-form-item
                                            label="峰-电价："
                                            prop="peakChargePrice"
                                            :label-width="formLabelWidth"
                                            >
                                            <el-input-number
                                                style="width: 100%;"
                                                v-model="priceInfo.form.peakChargePrice"
                                                :min="0"
                                                :precision="2"
                                                :step="0.01"
                                                :controls="false"
                                                placeholder="请输入峰-电价"
                                            ></el-input-number>
                                        </el-form-item>
                                    </el-col>

                                    <el-col :span="6">
                                        <el-form-item
                                            label="平-电价："
                                            prop="flatChargePrice"
                                            :label-width="formLabelWidth"
                                            >
                                            <el-input-number
                                                style="width: 100%;"
                                                v-model="priceInfo.form.flatChargePrice"
                                                :min="0"
                                                :precision="2"
                                                :step="0.01"
                                                :controls="false"
                                                placeholder="请输入平-电价"
                                            ></el-input-number>
                                        </el-form-item>
                                    </el-col>

                                    <el-col :span="6">
                                        <el-form-item
                                            label="谷-电价："
                                            prop="valleyChargePrice"
                                            :label-width="formLabelWidth"
                                            >
                                            <el-input-number
                                                style="width: 100%;"
                                                v-model="priceInfo.form.valleyChargePrice"
                                                :min="0"
                                                :precision="2"
                                                :step="0.01"
                                                :controls="false"
                                                placeholder="请输入谷-电价"
                                            ></el-input-number>
                                        </el-form-item>
                                    </el-col>

                                    <el-col :span="6">
                                        <el-form-item
                                            label="尖-服务费："
                                            prop="topServicePrice"
                                            :label-width="formLabelWidth"
                                            >
                                            <el-input-number
                                                style="width: 100%;"
                                                v-model="priceInfo.form.topServicePrice"
                                                :min="0"
                                                :precision="2"
                                                :step="0.01"
                                                :controls="false"
                                                placeholder="请输入尖-服务费"
                                            ></el-input-number>
                                        </el-form-item>
                                    </el-col>

                                    <el-col :span="6">
                                        <el-form-item
                                            label="峰-服务费："
                                            prop="peakServicePrice"
                                            :label-width="formLabelWidth"
                                            >
                                            <el-input-number
                                                style="width: 100%;"
                                                v-model="priceInfo.form.peakServicePrice"
                                                :min="0"
                                                :precision="2"
                                                :step="0.01"
                                                :controls="false"
                                                placeholder="请输入峰-服务费"
                                            ></el-input-number>
                                        </el-form-item>
                                    </el-col>

                                    <el-col :span="6">
                                        <el-form-item
                                            label="平-服务费："
                                            prop="flatServicePrice"
                                            :label-width="formLabelWidth"
                                            >
                                            <el-input-number
                                                style="width: 100%;"
                                                v-model="priceInfo.form.flatServicePrice"
                                                :min="0"
                                                :precision="2"
                                                :step="0.01"
                                                :controls="false"
                                                placeholder="请输入平-服务费"
                                            ></el-input-number>
                                        </el-form-item>
                                    </el-col>

                                    <el-col :span="6">
                                        <el-form-item
                                            label="谷-服务费："
                                            prop="valleyServicePrice"
                                            :label-width="formLabelWidth"
                                            >
                                            <el-input-number
                                                style="width: 100%;"
                                                v-model="priceInfo.form.valleyServicePrice"
                                                :min="0"
                                                :precision="2"
                                                :step="0.01"
                                                :controls="false"
                                                placeholder="请输入谷-服务费"
                                            ></el-input-number>
                                        </el-form-item>
                                    </el-col>
                                </el-row>

                                
                                
                            </el-form>
                        </div>
                        
                </div>

                 <div class="set-price-wrap" v-if="billingInfo.form.chargeMode === '0202'">
                    <div class="set-price-title">
                        设置时段
                    </div>

                    <div class="set-price-info">
                            <el-form :model="priceInfo.form" :rules="priceInfo.rules" ref="priceInfoForm"  label-position="top">
                                <el-row :gutter="20">
                                    <el-col :span="6">
                                        <el-form-item
                                            label="电价时段："
                                            prop="periodNo"
                                            :label-width="formLabelWidth"
                                            >
                                            <el-select
                                                v-model="priceInfo.form.periodNo"
                                                placeholder="请选择电价时段"
                                                style="width: 100%"
                                                @change="handleTimePeriodChange"
                                            >
                                                <el-option
                                                    v-for="item in timePeriodList"
                                                    :key="item.value"
                                                    :label="item.label"
                                                    :value="item.value"
                                                ></el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-col>

                                </el-row>
                                
                            </el-form>

                            <el-radio-group v-if="priceInfo.form.periodNo" v-model="chooseTime"  @change="changeTime" style="margin-bottom: 16px;">
                                <el-radio-button v-for="(item, index) in timeList" :key="index" :label="item.month">{{ item.month }}</el-radio-button>
                            </el-radio-group>


                            <BuseCrud
                                v-if="priceInfo.form.periodNo"
                                style="margin-bottom: 16px;"
                                ref="periodInfo"
                                :tableColumn="tableColumn"
                                :tableData="tableData"
                                :modalConfig="{ addBtn: false, menu: false }"
                            >
                            </BuseCrud>
                    </div>
                </div>
        </div>



        <div class="bottom-wrap">
            <div>
                <el-button @click="handleCancel">取消</el-button>
                <el-button @click="handleSave('save')" type="primary" >保存</el-button>
                <el-button @click="handleSave('audit')" type="primary" >提交审核</el-button>

            </div>

        </div>
    </div>
    
  </template>
  
  <script>
  import {
    getAreaList,
    getElectricPricePeriodDetail,
  } from "@/api/electricPricePeriod/index";

  import { 
    getElectricPricePeriodList,
    addBillModel,
    getBillModelDetail,
    updateBillModel,
 } from "@/api/billingModel/index"; 
  
    export default {
    components: {
        
    },
    dicts: [
        'ls_charging_contracted_unit',
        'ls_charging_billing_charge_mode',
        'ls_charging_billing_charge_type',
    ],
    data() {
        return {
            type: 'create', 
            formLabelWidth: '120px',

            baseInfo: {
                form: {
                    billingConfId: '',
                    chcName: '',
                    suitCityCode: '',
                    createUnit: '',
                },
                rules: {
                    chcName: [
                        { required: true, message: '请输入计费名称', trigger: 'blur' },
                    ],
                    suitCityCode: [
                        { required: true, message: '请选择适用城市', trigger: 'blur' },
                    ],
                    createUnit: [
                        { required: true, message: '请选择申请单位', trigger: 'blur' },
                    ]
                },
            },

            areaList:[],


            billingInfo: {
                form: {
                    chargeMode: '0201',
                    // itemNo: '',
                    // priceType: '',
                    // priceName: '',
                    // isSame: '',
                    chargePrice: undefined,
                    servicePrice: undefined,
                },
                totalPrice: '', // 总价
                rules: {
                    chargeMode: [
                        { required: true, message: '请选择计费模式', trigger: 'blur' },
                    ],
                    // itemNo: [
                    //     { required: true, message: '请选择收费类型', trigger: 'blur' },
                    // ],
                    // priceType: [
                    //     { required: true, message: '请选择营销电价类型', trigger: 'blur' },
                    // ],
                    // priceName: [
                    //     { required: true, message: '请选择营销电价名称', trigger: 'blur'},
                    // ],
                    // isSame: [
                    //     {  required: true, message: '请选择是否与营销电价一致', trigger: 'blur' },
                    // ],
                    chargePrice: [
                        { required: true, message: '请输入电价', trigger: 'blur'},
                    ],
                    servicePrice: [
                        { required: true, message: '请输入服务费', trigger: 'blur' },
                    ],
                }
            },


            priceTypeList: [
                { label: '一般工商业不满1kv', value: '01' },
                { label: '工业用电', value: '02'},
                { label: '居民用电', value: '03' },
            ],
            priceNameList:[
                { label: '峰', value: '01' },
                { label: '平', value: '02'},
            ],
            isSameList: [
                { label: '是', value: '01' },
                { label: '否', value: '02'},
            ],

            priceInfo: {
                form: {
                    topChargePrice: undefined,
                    peakChargePrice: undefined,
                    flatChargePrice: undefined,
                    valleyChargePrice: undefined,
                    topServicePrice: undefined,
                    peakServicePrice: undefined,
                    flatServicePrice: undefined,
                    valleyServicePrice: undefined,
                    periodNo: '',
                },
                rules: {
                    topChargePrice: [
                        { required: true, message: '请输入尖-电价', trigger: 'blur' },
                    ],
                    peakChargePrice: [
                        { required: true, message: '请输入峰-电价', trigger: 'blur' },
                    ],
                    flatChargePrice: [
                        { required: true, message: '请输入平-电价', trigger: 'blur' },
                    ],
                    valleyChargePrice: [
                        {  required: true, message: '请输入谷-电价', trigger: 'blur' },
                    ],
                    topServicePrice: [
                        { required: true, message: '请输入尖-服务费', trigger: 'blur' },
                    ],
                    peakServicePrice: [
                        { required: true, message: '请输入峰-服务费', trigger: 'blur' },
                    ],
                    flatServicePrice: [
                        { required: true, message: '请输入平-服务费', trigger: 'blur' },
                    ],
                    valleyServicePrice: [
                        { required: true, message: '请输入谷-服务费', trigger: 'blur' },
                    ],
                    periodNo: [
                        { required: true, message: '请选择电价时段', trigger: 'change' },
                    ]
                }
            },

            timePeriodList: [],
            initTimePeriodList: [],


            timeList: [],
            chooseTime: '',

            tableColumn: [
                {
                    field: 'priceType',
                    title: '类型',
                    minWidth: 120, 
                    formatter: ({ cellValue }) => {
                        return this.selectDictLabel(
                            this.priceTypeList,
                            cellValue
                        );
                    },
                },
                {
                    field: 'startTime',
                    title: '开始时间',
                    minWidth: 120, 
                },
                {
                    field: 'endTime',
                    title: '结束时间',
                    minWidth: 120, 
                },
            ],


            tableData: [],

            priceTypeList: [
                { label: '尖', value: '1' },
                { label: '峰', value: '2' },
                { label: '平', value: '3' },
                { label: '谷', value: '4' },
            ],

            chcNo: '',

        };
    },

    computed: {

    },
    watch: {
            // 深度监听电价和服务费变化
        'billingInfo.form.chargePrice': {
            handler(newVal) {
                this.calculateTotalPrice()
            },
            immediate: true
        },
        'billingInfo.form.servicePrice': {
            handler(newVal) {
                this.calculateTotalPrice()
            },
            immediate: true
        }

    },
    mounted() {
        const chcNo = this.$route.query.chcNo;
        if (chcNo) {
            this.chcNo = chcNo;
            this.getBillModelDetail();
        }
        this.getAreaList();
    },
    methods: {
        calculateTotalPrice() {
            // 确保数值有效性
            const charge = Number(this.billingInfo.form.chargePrice) || 0
            const service = Number(this.billingInfo.form.servicePrice) || 0
            
            // 精确计算并保留两位小数
            this.totalPrice = parseFloat(
                (charge + service).toFixed(2)
            )
        },
        async handleTimePeriodChange() {
            let operateId = ''
            this.initTimePeriodList.forEach(item=>{
                if(item.periodNo ===  this.priceInfo.form.periodNo ) {
                    operateId = item.chargePeriodId
                }
            })


            const [err, res] = await getElectricPricePeriodDetail({
                operateId
            })

            const {
                periodDetailList,
            } = res.data;

            const list = Array.from(
                periodDetailList.reduce((map, item) => {
                    const month = item.validMonth;
                    if (!map.has(month)) {
                    map.set(month, {
                        month,
                        priceList: [],
                        active: false
                    });
                    }
                    map.get(month).priceList.push({
                        priceType: item.timeFlag,
                        startTime: item.beginTime,
                        endTime: item.endTime
                    });
                    return map;
                }, new Map()).values()
            ).map((group, index) => index === 0 ? { ...group, active: true } : group);
            
           this.timeList = list;

            this.chooseTime = this.timeList[0].month;
            this.tableData  = this.timeList[0].priceList;
            
        },
        // 获取详情
        async getBillModelDetail() {
            const [err, res] = await getBillModelDetail({
                chcNo: this.chcNo
            })
            if (err) return

            const {
                billingConfId,
                chcName,
                suitCityCode,
                createUnit,
                chargeMode,
                // itemNo,
                chargePrice,
                servicePrice,

                topChargePrice,
                peakChargePrice,
                flatChargePrice,
                valleyChargePrice,
                topServicePrice,
                peakServicePrice,
                flatServicePrice,
                valleyServicePrice,
                periodDetail,
            } = res.data;

           


            this.baseInfo.form = {
                billingConfId,
                chcName,
                suitCityCode,
                createUnit,
            }

            this.billingInfo.form = {
                chargeMode,
                // itemNo,
                chargePrice,
                servicePrice,
            }

            

           

            if(chargeMode === '0202'){
                const [err, res] = await getElectricPricePeriodList({
                    suitCityCode: this.baseInfo.form.suitCityCode,
                })
                if (err) return

                const {
                    data
                } = res
                const list = []
                data.forEach(item => {
                    list.push({
                        label: item.periodName,
                        value: item.periodNo,
                    })
                })

                this.timePeriodList = list

                this.initTimePeriodList = data

                const {
                    periodNo
                } = periodDetail

                this.priceInfo.form = {
                    topChargePrice,
                    peakChargePrice,
                    flatChargePrice,
                    valleyChargePrice,
                    topServicePrice,
                    peakServicePrice,
                    flatServicePrice,
                    valleyServicePrice,
                    periodNo,
                }

                this.handleTimePeriodChange()
            }
        },

          // 获取适用地市
          async getAreaList() {
            const [err, res] = await getAreaList({
                areaLevel: '03',
                huNanOnly: true
            })
            if (err) return
            const {
                data
            } = res
            const list = []
            data.forEach(item => {
                list.push({
                    label: item.areaName,
                    value: item.areaCode,
                })
            })
            this.areaList = list

            if(!this.chcNo){
                // 新增，默认选中长沙
                this.baseInfo.form.suitCityCode = '430100'
            }
            
        },
        

        // 城市更新
        async cityChange() {
            console.log(this.baseInfo.form.suitCityCode,)
            this.priceInfo.form.periodNo = ''
            this.timePeriodList = []
            this.initTimePeriodList = []

            this.timeList = []

            this.chooseTime = ''
            this.tableData  = []

            // 获取电价时段列表

            const [err, res] = await getElectricPricePeriodList({
                suitCityCode: this.baseInfo.form.suitCityCode,
            })
            if (err) return

            const {
                data
            } = res
            const list = []
            data.forEach(item => {
                list.push({
                    label: item.periodName,
                    value: item.periodNo,
                })
            })

            this.timePeriodList = list

            this.initTimePeriodList = data

            
        },

        changeTime() {
            this.tableData = this.timeList.find(item => item.month === this.chooseTime).priceList;
        },

        handleCancel() {
            this.$router.back();
        },

        handleSave: _.debounce(async function(type){
            // 这里可以执行提交逻辑
            Promise.all([
                this.validateForm('baseInfoForm'),
                this.validateForm('billingInfoForm'),
                this.validateForm('priceInfoForm'),
            ])

            .then(async () => {
                // 所有表单校验通过
                const {
                    billingConfId,
                    chcName,
                    suitCityCode,
                    createUnit,
                } = this.baseInfo.form

                const {
                    chargeMode,
                    // itemNo,
                    chargePrice,
                    servicePrice,
                } = this.billingInfo.form

                const {
                    topChargePrice,
                    peakChargePrice,
                    flatChargePrice,
                    valleyChargePrice,
                    topServicePrice,
                    peakServicePrice,
                    flatServicePrice,
                    valleyServicePrice,
                    periodNo,
                } = this.priceInfo.form

                let params = {}

                if(chargeMode === '0201') {
                    params = {
                        isSubmitAudit: type === 'audit' ? true : false,
                        billingConfId,
                        chcName,
                        suitCityCode,
                        createUnit,
                        chargeMode,
                        // itemNo,
                        chargePrice,
                        servicePrice,
                    }
                } else {
                    params = {
                        isSubmitAudit: type === 'audit' ? true : false,
                        billingConfId,
                        chcName,
                        suitCityCode,
                        createUnit,
                        chargeMode,
                        // itemNo,
                        topChargePrice,
                        peakChargePrice,
                        flatChargePrice,
                        valleyChargePrice,
                        topServicePrice,
                        peakServicePrice,
                        flatServicePrice,
                        valleyServicePrice,
                        periodNo,
                    }
                }

                if(!this.chcNo) {
                    // 新增
                    const [err,res] = await addBillModel(
                        params
                    )

                    if(err) {
                        return this.$message.error(err.message || '新增计费模版失败');
                    }

                    this.$message({
                        type: 'success',
                        message: '新增成功!'
                    });

                    setTimeout(() => {
                        this.$router.back();
                    }, 500);
                } else {
                    // 修改
                    const [err,res] = await updateBillModel(
                        params
                    )

                    if(err) {
                        return this.$message.error(err.message || '修改计费模版失败');
                    }

                    this.$message({
                        type: 'success',
                        message: '修改成功!'
                    });

                    setTimeout(() => {
                        this.$router.back();
                    }, 500);
                }

            })
            .catch((error) => {
                // 有表单校验失败
                console.log(error,'111'); 
                this.$message.error('表单校验失败，请检查输入');
            });
        },300),

          // 校验单个表单
          validateForm(formRef) {
            console.log(formRef,'formRef',this.billingInfo);
            const chargeMode = this.billingInfo.form.chargeMode
            return new Promise((resolve, reject) => {
                if( chargeMode === '0201' && formRef === 'priceInfoForm') {
                    console.log('chargeMode111');
                    resolve(); // 校验通过
                } else {
                    console.log('chargeMode222');
                    this.$refs[formRef].validate((valid) => {
                        if (valid) {
                            resolve(); // 校验通过
                        } else {
                            reject(new Error(`${formRef} 校验失败`)); // 校验失败
                        }
                    });
                }
               
            });
        },
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .container-full {
    background-color: rgba(244, 246, 249, 1);
    padding-bottom: 16px;
    min-height: calc(100% - 80px);
    overflow-y: auto;
  }

  .info-card {
  margin: 16px;
  border-radius: 5px;
  border: 1px solid #fff;
  overflow: hidden;
  background-color: #fff;
  // min-height: 300px;
  .card-head {
    height: 48px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    .before-icon {
        width: 3px;
        height: 16px;
        background-image: url('~@/assets/station/consno-before.png');
        background-size: 100% 100%;
        background-repeat: no-repeat;
        margin-right: 8px;
    }
    .card-head-text {
        flex:1;
        font-weight: 500;
        font-size: 16px;
        color: #12151A;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
    .button-wrap {
      display: flex;
      .invite-btn {
          background-color: #1ab2ff;
          border-color: #1ab2ff;
        }
        ::v-deep .el-button--small {
          font-size: 14px;
        }
      .distribution {
          margin-left: 24px;
          margin-right: 24px;
          display: flex;
          align-items: center;
        }
    }
  }
  .card-head-split {
    width: 100%;
    height: 1px;
    background-color: #E9EBF0;
    margin-bottom: 24px;
  }
  
  .people-table-wrap {
    ::v-deep .bd3001-table-select-box {
      display: none;
    }
    ::v-deep .bd3001-button {
      width: 100%;
    }
  }
  .form-wrap {
      padding: 0 24px 24px 24px;
      .custom-header {
          background: -webkit-gradient(linear, left top, left bottom, from(rgba(0, 149, 255, 0.5)), to(rgba(87, 152, 255, 0))), #f5faff;
          background: linear-gradient(180deg, rgba(0, 149, 255, 0.5) 0%, rgba(87, 152, 255, 0) 100%), #f5faff;
          background-repeat: no-repeat;
      }
    }

    .set-price-wrap {
        margin: 0 24px 24px 24px;
        box-sizing: border-box;
        // width: 100%;
        min-height: 132px;
        border-radius: 4px;
        border: 1px solid #E9EBF0;
        .set-price-title {
            height: 48px;
            background: linear-gradient(180deg, #E9F2FF 0%, #FFFFFF 100%);
            width: 100%;
            font-weight: 400;
            font-size: 16px;
            line-height: 16px;
            color: #292B33;
            display: flex;
            align-items: center;
            padding-left: 16px;
            box-sizing: border-box;
        }
        .set-price-info {
            margin: 0 16px 0 16px;
        }
    }
  }



  .container {
      position: relative;
      padding-bottom: 100px;
      box-sizing: border-box;
      .bottom-wrap {
          position: fixed;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 86px;
          display: flex;
          align-items: center;
          justify-content: flex-end;
          background-color: #FFFFFF;
          padding-right: 32px;
          box-sizing: border-box;
      }
  }


  ::v-deep .bd3001-content {
    padding: 0;
  }



  </style>
  