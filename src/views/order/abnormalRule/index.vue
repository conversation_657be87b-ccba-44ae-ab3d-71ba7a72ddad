<template>
  <div class="container container-float" style="padding: 0 0 100px 0">
    <el-tabs v-model="activeName" @tab-click="handleClickTab">
      <!-- 异常规则管理标签栏内容 -->
      <el-tab-pane label="异常规则管理" name="manage">
        <div class="table-wrap">
          <BuseCrud
            ref="crud"
            :loading="manageLoading"
            :filterOptions="manageFilterOptions"
            :tablePage="manageTablePage"
            :tableColumn="manageTableColumn"
            :tableData="manageTableData"
            :pagerProps="pagerProps"
            :modalConfig="modalConfig"
            @loadData="loadManageData"
          >
            <template slot="defaultHeader">
              <div>
                <div class="card-head">
                  <div class="card-head-text">异常规则管理列表</div>

                  <div class="top-button-wrap">
                    <el-button type="primary" @click="() => handleRuleAdd()">
                      新增异常规则
                    </el-button>
                  </div>
                </div>
              </div>
            </template>

            <template slot="operate" slot-scope="{ row }">
              <!-- <div class="menu-box">
                <el-button  class="button-border" type="primary" plain @click="handleRuleEdit(row)">
                  编辑
                </el-button>

                <el-button  class="button-border" type="primary" plain @click="toggleRuleDel(row)">
                  {{ row.status === '01' ? '停用' : '启用' }}
                </el-button>

                <el-button class="delete-btn" type="danger" plain @click="handleRuleDel(row)">
                  删除
                </el-button>
              </div> -->
              <el-dropdown trigger="click">
                      <el-button
                        class="button-border"
                        type="primary"
                        plain
                      >
                          操作
                      </el-button>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item >
                          <div    @click="handleRuleEdit(row)">
                           编辑
                          </div>
                        </el-dropdown-item>

                        <el-dropdown-item >
                          <div   @click="toggleRuleDel(row)">
                            {{ row.status === '01' ? '停用' : '启用' }}
                          </div>
                        </el-dropdown-item>

                        <el-dropdown-item >
                          <div   @click="handleRuleDel(row)">
                            删除
                          </div>
                        </el-dropdown-item>

                        

                      </el-dropdown-menu>
                    </el-dropdown>

            </template>
          </BuseCrud>
        </div>
      </el-tab-pane>
      <!-- 异常规则审核标签栏内容 -->
      <el-tab-pane label="异常规则审核" name="audit">
        <div class="table-wrap">
          <BuseCrud
            ref="crud"
            :loading="auditLoading"
            :filterOptions="auditFilterOptions"
            :tablePage="auditTablePage"
            :tableColumn="auditTableColumn"
            :tableData="auditTableData"
            :pagerProps="pagerProps"
            :modalConfig="modalConfig"
            @loadData="loadAuditData"
          >
            <template slot="defaultHeader">
              <div>
                <div class="card-head">
                  <div class="card-head-text">异常规则审核列表</div>
                </div>
              </div>
            </template>

            <template slot="operate" slot-scope="{ row }">
              <!-- <div class="menu-box">
                <el-button
                 class="button-border"
                  type="primary"
                  v-if="row.reviewStatus === '01'"
                  plain
                  @click="handleAudit(row)"
                >
                  审核
                </el-button>

                <el-button  class="button-border" type="primary" plain @click="handleDetail(row)">
                  详情
                </el-button>
              </div> -->
              <el-dropdown trigger="click">
                      <el-button
                        class="button-border"
                        type="primary"
                        plain
                      >
                        操作
                      </el-button>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item   v-if="row.reviewStatus === '01'" >
                          <div   @click="handleAudit(row)">
                            审核
                          </div>
                        </el-dropdown-item>

                        <el-dropdown-item >
                          <div   @click="handleDetail(row)">
                            详情
                          </div>
                        </el-dropdown-item>

                        

                      </el-dropdown-menu>
                    </el-dropdown>

            </template>
          </BuseCrud>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import {
  getExruleList,
  getRuleReviewList,
  exruleDelete,
  exruleEnable,
  exruleDisable,
} from '@/api/order/index';
import StatusDot from '@/components/Business/StatusDot';

import StatusInfo from '@/components/Business/StatusInfo';

import moment from 'moment';
export default {
  components: {
    StatusDot,
    StatusInfo
  },
  dicts: [
    'ls_order_except_level', // 异常级别
    'ls_order_except_type', // 异常类型
    'ls_order_except_measures', // 处理措施
    'ls_order_except_status', // 异常状态
    'ls_except_rule_item', // 异常规则
    'ls_except_order_review_status', // 审核状态
  ],
  data() {
    return {
      activeName: 'manage',
      manageLoading: false,
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      params: {
        exceptId: '',
        name: '',
        level: '',
        type: '',
        measures: '',
        creatorTime: [],
        status: '',
      },
      manageTablePage: { total: 0, currentPage: 1, pageSize: 10 },
      manageTableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
          fixed: 'left',
        },
        {
          field: 'exceptId',
          title: '异常编号',
          minWidth: 190,
        },
        {
          field: 'level',
          title: '异常等级',
          minWidth: 100,
        //   formatter: ({ cellValue }) => {
        //     return this.selectDictLabel(
        //       this.dict.type.ls_order_except_level,
        //       cellValue
        //     );
        //   },

          slots: {
            // 自定义render函数
          default: ({ row }) => {
                return (
                    <StatusInfo
                        value={row.level}
                        dictValue={this.dict.type.ls_order_except_level}
                        colors={['low', 'medium', 'high']}
                      ></StatusInfo>
                    );
                  },
            },
        },
        {
          field: 'name',
          title: '异常名称',
          minWidth: 150,
        },
        {
          field: 'type',
          title: '异常类型',
          minWidth: 120,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_order_except_type,
              cellValue
            );
          },
        },
        {
          field: 'description',
          title: '异常描述',
          minWidth: 180,
        },
        {
          field: 'threshold',
          title: '阈值',
          minWidth: 100,
        },
        {
          field: 'unit',
          title: '单位',
          minWidth: 80,
        },
        {
          field: 'measures',
          title: '处理措施',
          minWidth: 150,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_order_except_measures,
              cellValue
            );
          },
        },
        {
          field: 'createUser',
          title: '创建人',
          minWidth: 120,
        },
        {
          field: 'createTime',
          title: '创建时间',
          minWidth: 200,
        },
        {
          field: 'status',
          title: '启用状态',
          minWidth: 100,
          fixed: 'right',
        //   formatter: ({ cellValue }) => {
        //     return this.selectDictLabel(
        //       this.dict.type.ls_order_except_status,
        //       cellValue
        //     );
        //   },

          slots: {
                  // 自定义render函数
                  default: ({ row }) => {
                    return (
                      <StatusDot
                        value={row.status}
                        dictValue={this.dict.type.ls_order_except_status}
                        colors={['success', 'danger']}
                      ></StatusDot>
                    );
                  },
                },
        },
        {
          title: '操作',
          slots: { default: 'operate' },
          width: 80,
          align: 'center',
          fixed: 'right',
        },
      ],
      manageTableData: [],

      auditLoading: false,
      auditParams: {
        exceptId: '',
        name: '',
        level: '',
        type: '',
        creatorTime: [],
        createUser: '',
        status: '',
      },
      auditTablePage: { total: 0, currentPage: 1, pageSize: 10 },
      auditTableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60,
          fixed: 'left',
        },
        {
          field: 'exceptId',
          title: '异常编号',
          minWidth: 190,
        },
        {
          field: 'level',
          title: '异常等级',
          minWidth: 100,
        //   formatter: ({ cellValue }) => {
        //     return this.selectDictLabel(
        //       this.dict.type.ls_order_except_level,
        //       cellValue
        //     );
        //   },
        slots: {
            // 自定义render函数
          default: ({ row }) => {
                return (
                    <StatusInfo
                        value={row.level}
                        dictValue={this.dict.type.ls_order_except_level}
                        colors={['low', 'medium', 'high']}
                      ></StatusInfo>
                    );
                  },
            },
        },
        {
          field: 'name',
          title: '异常名称',
          minWidth: 150,
        },
        {
          field: 'type',
          title: '异常类型',
          minWidth: 120,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_order_except_type,
              cellValue
            );
          },
        },
        {
          field: 'description',
          title: '异常描述',
          minWidth: 180,
        },
        {
          field: 'threshold',
          title: '阈值',
          minWidth: 100,
        },
        {
          field: 'unit',
          title: '单位',
          minWidth: 80,
        },
        {
          field: 'measures',
          title: '处理措施',
          minWidth: 150,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_order_except_measures,
              cellValue
            );
          },
        },
        {
          field: 'createUser',
          title: '创建人',
          minWidth: 120,
        },
        {
          field: 'createTime',
          title: '创建时间',
          minWidth: 200,
        },
        {
          field: 'reviewStatus',
          title: '审核状态',
          minWidth: 100,
          fixed: 'right',
        //   formatter: ({ cellValue }) => {
        //     return this.selectDictLabel(
        //       this.dict.type.ls_except_order_review_status,
        //       cellValue
        //     );
        //   },
        slots: {
                  // 自定义render函数
                  default: ({ row }) => {
                    return (
                      <StatusDot
                        value={row.reviewStatus}
                        dictValue={this.dict.type.ls_except_order_review_status}
                        colors={['warning', 'success']}
                      ></StatusDot>
                    );
                  },
                },
        },
        // {
        //   field: 'updateUser',
        //   title: '审核人',
        //   minWidth: 120,
        // },
        // {
        //   field: 'updateTime',
        //   title: '审核时间',
        //   minWidth: 200,
        // },
        {
          title: '操作',
          slots: { default: 'operate' },
          width: 80,
          align: 'center',
          fixed: 'right',
        },
      ],
      auditTableData: [],
    };
  },

  computed: {
    manageFilterOptions() {
      return {
        config: [
          {
            field: 'exceptId',
            title: '异常编号',
            element: 'el-input',
          },
          {
            field: 'name',
            title: '异常名称',
            element: 'el-input',
          },
          {
            field: 'level',
            title: '异常等级',
            element: 'el-select',
            props: {
              placeholder: '请选择异常等级',
              options: this.dict.type.ls_order_except_level,
            },
          },
          {
            field: 'type',
            title: '异常类型',
            element: 'el-select',
            props: {
              placeholder: '请选择异常类型',
              options: this.dict.type.ls_order_except_type,
            },
          },
          {
            field: 'measures',
            title: '处理措施',
            element: 'el-select',
            props: {
              placeholder: '请选择处理措施',
              options: this.dict.type.ls_order_except_measures,
            },
          },
          {
            field: 'creatorTime',
            title: '创建时间',
            element: 'el-date-picker',
            props: {
              type: 'daterange',
              options: [],
              valueFormat: 'yyyy-MM-dd',
            },
          },
          {
            field: 'status',
            title: '状态',
            element: 'el-select',
            props: {
              placeholder: '请选择启用状态',
              options: this.dict.type.ls_order_except_status,
            },
          },
        ],
        params: this.params,
      };
    },
    auditFilterOptions() {
      return {
        config: [
          {
            field: 'exceptId',
            title: '异常编号',
            element: 'el-input',
          },
          {
            field: 'name',
            title: '异常名称',
            element: 'el-input',
          },
          {
            field: 'level',
            title: '异常等级',
            element: 'el-select',
            props: {
              placeholder: '请选择异常等级',
              options: this.dict.type.ls_order_except_level,
            },
          },
          {
            field: 'type',
            title: '异常类型',
            element: 'el-select',
            props: {
              placeholder: '请选择异常类型',
              options: this.dict.type.ls_order_except_type,
            },
          },
          {
            field: 'creatorTime',
            title: '创建时间',
            element: 'el-date-picker',
            props: {
              type: 'daterange',
              options: [],
              valueFormat: 'yyyy-MM-dd',
            },
          },
          {
            field: 'createUser',
            title: '创建人',
            element: 'el-input',
          },
          {
            field: 'reviewStatus',
            title: '审核状态',
            element: 'el-select',
            props: {
              placeholder: '请选择审核状态',
              options: this.dict.type.ls_except_order_review_status,
            },
          },
          // {
          //     field: 'auditTime',
          //     title: '审核时间',
          //     element: 'el-date-picker',
          //     props: {
          //         type: 'daterange',
          //         options: [],
          //     }
          // },
        ],
        params: this.auditParams,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  mounted() {
    this.loadManageData();

    console.log(this.dict.type.ls_order_except_status,'this.dict.type.ls_order_except_status,');
  },
  methods: {
    async handleClickTab({ index }) {
      if (index === '0') {
        this.manageTablePage.total = 0;
        this.manageTablePage.currentPage = 1;
        this.loadManageData();
      } else {
        this.auditTablePage.total = 0;
        this.auditTablePage.currentPage = 1;
        this.loadAuditData();
      }
    },

    // 获取管理列表数据
    async loadManageData() {
      const { exceptId, name, level, type, measures, creatorTime, status } =
        this.manageFilterOptions.params;

      let createTimeStart = '';
      let createTimeEnd = '';
      if (creatorTime && creatorTime.length > 0) {
        createTimeStart = moment(creatorTime[0]).format('YYYY-MM-DD');
        createTimeEnd = moment(creatorTime[1]).format('YYYY-MM-DD');
      }

      this.manageLoading = true;
      const [err, res] = await getExruleList({
        exceptId: exceptId,
        name: name,
        type: type,
        level: level,
        measures: measures,
        status: status,
        createTimeStart,
        createTimeEnd,
        pageNum: this.manageTablePage.currentPage,
        pageSize: this.manageTablePage.pageSize,
      });

      this.manageLoading = false;

      if (err) return;
      const { data, total } = res;

      this.manageTableData = data;
      this.manageTablePage.total = total;
    },

    // 新增异常规则
    handleRuleAdd() {
      this.$router.push({
        path: '/v2g-charging-web/operatorManage/orderManage/abnormalRule/create',
      });
    },

    // 编辑异常规则
    handleRuleEdit(row) {
      const { exceptId } = row;
      this.$router.push({
        path: '/v2g-charging-web/operatorManage/orderManage/abnormalRule/create',
        query: {
          exceptId,
        },
      });
    },

    // 启用、停用异常规则
    toggleRuleDel(row) {
      this.$confirm(
        `'确定${row.status === '01' ? '停用' : '启用'}异常规则：${
          row?.name || ''
        }吗？'`,
        '提示',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(async () => {
        if (row.status === '02') {
          const [err, res] = await exruleEnable({
            exceptId: row?.exceptId || '',
          });

          if (err) return;
          this.$message({
            type: 'success',
            message: '启用成功!',
          });
        } else {
          const [err, res] = await exruleDisable({
            exceptId: row?.exceptId || '',
          });

          if (err) return;
          this.$message({
            type: 'success',
            message: '停用成功!',
          });
        }
        this.manageTablePage.total = 0;
        this.manageTablePage.currentPage = 1;
        this.loadManageData();
      });
    },
    // 删除异常规则
    handleRuleDel(row) {
      this.$confirm(`'确定删除异常规则：${row?.name || ''}吗？'`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        const [err, res] = await exruleDelete({
          exceptId: row?.exceptId || '',
        });

        if (err) return;
        this.$message({
          type: 'success',
          message: '删除成功!',
        });
        this.manageTablePage.total = 0;
        this.manageTablePage.currentPage = 1;
        this.loadManageData();
      });
    },

    // 获取异常规则审核列表
    async loadAuditData() {
      const {
        exceptId,
        name,
        level,
        type,
        creatorTime,
        createUser,
        reviewStatus,
      } = this.auditFilterOptions.params;

      let createTimeStart = '';
      let createTimeEnd = '';
      if (creatorTime && creatorTime.length > 0) {
        createTimeStart = creatorTime[0];
        createTimeEnd = creatorTime[1];
      }

      this.auditLoading = true;
      const [err, res] = await getRuleReviewList({
        exceptId: exceptId,
        name: name,
        type: type,
        level: level,
        reviewStatus: reviewStatus,
        createUser: createUser,
        createTimeStart,
        createTimeEnd,
        pageNum: this.auditTablePage.currentPage,
        pageSize: this.auditTablePage.pageSize,
      });

      this.auditLoading = false;

      if (err) return;
      const { data, total } = res;

      this.auditTableData = data;
      this.auditTableData.total = total;
    },

    // 异常规则审核
    handleAudit(row) {
      const { exceptId } = row;
      this.$router.push({
        path: '/v2g-charging-web/operatorManage/orderManage/abnormalRule/audit',
        query: {
          exceptId,
        },
      });
    },

    // 异常规则详情
    handleDetail(row) {
      const { exceptId } = row;
      this.$router.push({
        path: '/v2g-charging-web/operatorManage/orderManage/abnormalRule/detail',
        query: {
          exceptId,
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-tabs {
  margin-top: 16px;
  background-color: #f5f6f9;
  .el-tabs__header {
    padding-left: 0;
    display: flex;
    justify-content: center;
    text-align: center;
    margin-bottom: 1px;
    .el-tabs__item {
      padding: 0;
      width: 164px;
      font-size: 18px;
      font-weight: 400;
      background-color: #fff;
    }
    .el-tabs__item.is-active {
      background-color: #1677fe;
      color: #fff;
    }
    .el-tabs__nav-scroll {
      border-radius: 25px;
      border: solid 1px #dfe1e5;
    }
    .el-tabs__active-bar {
      display: none;
    }
    .el-tabs__nav-wrap::after {
      width: 0;
    }
  }
}

.table-wrap {
  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }
  margin: 16px;

  .card-head {
    // position: relative;
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    margin-top: -20px;
    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }
  .info-wrap {
    margin-top: 16px;
    display: flex;
    flex-wrap: wrap;
    gap: 16px;

    .info-item {
      background-color: #fafbfc;
      flex: 1 1 0;
      // min-width: 180px;

      border-radius: 5px;
      padding: 8px 24px;
      box-sizing: border-box;
      // margin-right: 16px;
      display: flex;
      .info-icon {
        width: 42px;
        height: 42px;
      }
      .info-right-wrap {
        flex: 1;
        margin-left: 8px;
        .info-title {
          font-weight: 400;
          font-size: 14px;
          line-height: 14px;
          margin-bottom: 8px;
        }
        .info-number {
          font-size: 20px;
          font-weight: 500;
          .info-unit {
            font-size: 14px;
            font-weight: 400;
          }
        }
      }
    }
    .info-item:last-child {
      margin-right: 0;
    }
  }

  .top-button-wrap {
    display: flex;
    margin: 16px 0;
  }
}

.button-border {
    border: 0.01rem solid #217AFF;
    color: #217AFF;
    background-color: #fff;
}
.delete-btn {
  border: 0.01rem solid #FC1E31;
    color: #FC1E31;
    background-color: #fff;
}
</style>
