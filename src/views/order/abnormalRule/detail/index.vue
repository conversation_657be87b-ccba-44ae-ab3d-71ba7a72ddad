<template>
  <div class="container container-float" style="padding: 0 0 100px 0">
    <div class="device-head">
      <img
        src="@/assets/order/abnormal-detail-icon.png"
        class="device-head-icon"
      />

      <div class="device-info-wrap">
        <div class="device-title-wrap">
          <div class="device-title">{{ name }}</div>
          <div class="device-status">{{ levelFilter(level) }}</div>
        </div>
        <div class="device-info-wrap">
          <el-row>
            <el-col :span="8">
              <span class="label">异常编号：</span>
              <span class="value">{{ exceptId }}</span>
            </el-col>
            <el-col :span="8">
              <span class="label">异常类型：</span>
              <span class="value">{{ typeFilter(type) }}</span>
            </el-col>
          </el-row>
        </div>
      </div>

      <div class="device-status-wrap">
        <div class="device-status-item-wrap">
          <div class="device-status-item-title">审核状态</div>
          <div
            :class="{
              'device-status-success': true,
              'device-status': reviewStatus === '03',
            }"
          >
            {{ reviewStatusFilter(reviewStatus) }}
          </div>
        </div>
      </div>

      <el-button type="primary" @click="drawer = true">审核轨迹</el-button>
    </div>

    <div class="info-card">
      <div class="card-head" style="margin-bottom: 8px">
        <div class="before-icon"></div>
        <div class="card-head-text">基础信息</div>
      </div>

      <div class="form-wrap">
        <el-row :gutter="20">
          <el-col
            :span="8"
            v-for="(item, key) in baseInfoOperation"
            :key="key"
            style="margin-bottom: 24px"
          >
            <div style="display: flex">
              <div class="info-title">{{ label[key] }}：</div>
              <div class="info-detail">{{ item }}</div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </div>
</template>

<script>
import { getRuleDetail } from '@/api/order/index';

export default {
  components: {},
  dicts: [
    'ls_order_except_level', // 异常级别
    'ls_order_except_type', // 异常类型
    'ls_order_except_measures', // 处理措施
    'ls_order_except_status', // 异常状态
    'ls_except_rule_item', // 异常规则
    'ls_except_order_review_status', // 审核状态
  ],
  data() {
    return {
      name: '',
      level: '',
      exceptId: '',
      type: '',
      reviewStatus: '',
      baseInfoOperation: {
        description: '',
        validationRule: '', // 新增校验规则
        measures: '', // 新增处理方式
        createUser: '', // 新增创建人
        createTime: '', // 新增创建时间
        remark: '', // 新增备注字段
      },
      label: {
        description: '异常描述', // 原字段标签
        validationRule: '校验规则', // 新增校验规则标签
        measures: '处理措施', // 新增处理措施标签
        createUser: '创建人', // 新增创建人标签
        createTime: '创建时间', // 新增时间标签
        remark: '备注', // 新增备注标签
      },
      sizeList: [
        { label: '大于', value: '>' },
        { label: '小于', value: '<' },
        { label: '等于', value: '=' },
        { label: '大于等于', value: '>=' },
        { label: '小于等于', value: '<=' },
      ],
    };
  },

  computed: {},
  mounted() {
    const exceptId = this.$route.query.exceptId;
    this.getDetail(exceptId);
  },
  methods: {
    levelFilter(cellValue) {
      return this.selectDictLabel(
        this.dict.type.ls_order_except_level,
        cellValue
      );
    },
    typeFilter(cellValue) {
      return this.selectDictLabel(
        this.dict.type.ls_order_except_type,
        cellValue
      );
    },
    reviewStatusFilter(cellValue) {
      return this.selectDictLabel(
        this.dict.type.ls_except_order_review_status,
        cellValue
      );
    },
    measuresFilter(cellValue) {
      return this.selectDictLabel(
        this.dict.type.ls_order_except_measures,
        cellValue
      );
    },
    checkItemFilter(cellValue) {
      return this.selectDictLabel(
        this.dict.type.ls_except_rule_item,
        cellValue
      );
    },
    checkMethodFilter(cellValue) {
      return this.selectDictLabel(this.sizeList, cellValue);
    },
    // 获取详情
    async getDetail(exceptId) {
      const [err, res] = await getRuleDetail({
        exceptId,
      });

      if (err) return;

      const {
        name,
        level,
        type,
        reviewStatus,
        description,
        checkItem,
        checkMethod,
        threshold,
        unit,
        measures,
        createUser,
        createTime,
        remark,
      } = res.data;
      this.name = name;
      this.level = level;
      this.exceptId = exceptId;
      this.type = type;
      this.reviewStatus = reviewStatus;

      this.baseInfoOperation = {
        description: description,
        validationRule:
          this.checkItemFilter(checkItem) +
          this.checkMethodFilter(checkMethod) +
          threshold +
          unit,
        measures: this.measuresFilter(measures),
        createUser: createUser,
        createTime: createTime,
        remark: remark,
      };
    },
  },
};
</script>

<style lang="scss" scoped>
.container-full {
  background-color: rgba(244, 246, 249, 1);
  padding-bottom: 16px;
  min-height: calc(100% - 80px);
  overflow-y: auto;
}

.device-head {
  background-color: #fff;

  display: flex;
  height: 112px;
  display: flex;
  align-items: center;
  padding: 0 24px;
  box-sizing: border-box;
  .device-head-icon {
    width: 48px;
    height: 48px;
    margin-right: 24px;
  }
  .device-info-wrap {
    flex: 1;
    .device-title-wrap {
      height: 32px;
      display: flex;
      align-items: center;
      .device-title {
        font-weight: 500;
        font-size: 24px;
        color: #12151a;
      }
      .device-status {
        // width: 50px;
        padding: 0 10px;
        height: 24px;
        border-radius: 10px 0 10px 0;
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;
        text-align: center;
        color: #fff;
        background: linear-gradient(321.01deg, #ffb624 8.79%, #ff8d24 100.27%);
        margin-left: 12px;
      }
    }
    .device-info-wrap {
      height: 16px;
      margin-top: 16px;
      font-size: 16px;
      font-weight: 400;
      color: #292b33;
    }
  }
  .device-status-wrap {
    display: flex;
    align-items: center;
    .device-status-item-wrap {
      width: 150px;
      .device-status-item-title {
        font-weight: 400;
        font-size: 14px;
        line-height: 14px;
        color: #505363;
        margin: 0 auto 12px auto;
        text-align: center;
      }
      .device-status {
        width: 86px;
        height: 34px;
        border-radius: 4px;
        display: flex;
        margin: 0 auto;
        align-items: center;
        justify-content: center;
        background-color: #ebf3ff;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 18px;
        color: #217aff;
      }
      .device-status-success {
        width: 86px;
        height: 34px;
        border-radius: 4px;
        display: flex;
        margin: 0 auto;
        align-items: center;
        justify-content: center;
        background-color: #fff7e6;
        font-family: PingFang SC;
        font-weight: 500;
        font-size: 18px;
        color: #ff8d24;
      }
    }
    .device-status-split {
      width: 1px;
      height: 36px;
      background-color: #e9ebf0;
    }
  }
}

.info-card {
  margin: 16px;
  border-radius: 5px;
  border: 1px solid #fff;
  overflow: hidden;
  background-color: #fff;
  // min-height: 300px;
  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    background: linear-gradient(180deg, #e9f2ff 0%, #ffffff 100%);
    .before-icon {
      width: 3px;
      height: 16px;
      background-image: url('~@/assets/station/consno-before.png');
      background-size: 100% 100%;
      background-repeat: no-repeat;
      margin-right: 8px;
    }
    .card-head-text {
      flex: 1;
      font-weight: 500;
      font-size: 16px;
      color: #12151a;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
    .button-wrap {
      display: flex;
      .invite-btn {
        background-color: #1ab2ff;
        border-color: #1ab2ff;
      }
      ::v-deep .el-button--small {
        font-size: 14px;
      }
      .distribution {
        margin-left: 24px;
        margin-right: 24px;
        display: flex;
        align-items: center;
      }
    }
  }

  .people-table-wrap {
    ::v-deep .bd3001-table-select-box {
      display: none;
    }
    ::v-deep .bd3001-button {
      width: 100%;
    }
  }
  .form-wrap {
    padding: 0 16px 16px 16px;
    .info-title {
      font-weight: 400;
      font-size: 16px;
      line-height: 16px;
      color: #505363;
    }
    .info-detail {
      font-weight: 400;
      font-size: 16px;
      line-height: 16px;
      color: #292b33;
    }
    .info-amount {
      height: 28px;
      background-color: #fff7e6;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0 8px;
      font-weight: 400;
      font-size: 20px;
      color: #fe8921;
      margin-top: -6px;
      margin-right: 4px;
    }
    .info-img {
      width: 140px;
      height: 140px;
    }
  }
}
</style>
