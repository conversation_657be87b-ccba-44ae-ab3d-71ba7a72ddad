<template>
    <el-dialog title="标记异常订单" :visible.sync="dialogVisible" width="630px">
        <el-form :model="form"  ref="cancelForm"  label-position="top">
            <el-form-item
                label="取消订单原因"
                prop="abnormalDesc"
                :label-width="formLabelWidth"
            >
                <el-input
                    v-model="form.reason"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入原因"
                    >
                </el-input>
            </el-form-item>
        </el-form> 

        <div class="bottom-button-wrap">
            <div>
                <el-button @click="handleCancel">取消</el-button>
                <el-button @click="handleSave" type="primary">确定</el-button>

            </div>

        </div>
    </el-dialog>
   
</template>
<script>  

import{
    cancelOrder
} from '@/api/order/index'

  export default {
    props: {
        editInfo: {
            type: Object,
            default: () => {},
        },
    },
    components: {
        
    },
    data() {
        return {
            dialogVisible: false,
            orderNo: '',
            form: {
                reason: '',
            },

            formLabelWidth: '120px',
        };
    },
    watch: {
        
    },
    computed: {
        
    },
    mounted() {
        
    },
    methods: {
        async handleSave() {
           const params = {
                cancelReason: this.form.reason,
                orderNo: this.orderNo,
           }

           const [err, res] = await cancelOrder(params);

           if (err) return 
            this.$message.success('取消订单成功');
           this.handleCancel();
            this.$emit('loadData');
        },
        handleCancel() {
            this.dialogVisible = false;
        },


    },
  };
  </script>

<style lang="scss" scoped>
.bottom-button-wrap {
    height: 56px;
    // margin-top: 46px;
    width: 100%;
    background-color: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    // padding-right: 32px;
    box-sizing: border-box;
}

  </style>
  