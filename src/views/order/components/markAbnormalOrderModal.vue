<template>
    <el-dialog title="标记异常订单" :visible.sync="dialogVisible" width="630px">
        <el-form :model="form" :rules="rules" ref="abnormalForm"  label-position="top">
            <el-form-item
                label="标记异常订单"
                prop="isAbnormal"
                :label-width="formLabelWidth"
            >
                <el-radio-group v-model="form.isAbnormal">
                    <el-radio :label="1">是</el-radio>
                    <el-radio :label="2">否</el-radio>
                </el-radio-group>
            </el-form-item>

            <el-form-item
                label="异常等级"
                prop="abnormalLevel"
                :label-width="formLabelWidth"
            >
                <el-select
                    v-model="form.abnormalLevel"
                    placeholder="请选择异常等级"
                    style="width: 100%"
                    @change="changeAbnormalLevel"
                    >
                    <el-option
                        v-for="item in dict.type.ls_order_except_level"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                        ></el-option>
                    </el-select>
            </el-form-item>

            <el-form-item
                label="异常类型"
                prop="abnormalLevel"
                :label-width="formLabelWidth"
            >
                <el-select
                    v-model="form.abnormalType"
                    placeholder="请选择异常类型"
                    style="width: 100%"
                    @change="changeAbnormalLevel"
                    >
                    <el-option
                        v-for="item in dict.type.ls_order_except_type"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                        ></el-option>
                    </el-select>
            </el-form-item>

            <el-form-item
                label="异常名称"
                prop="abnormalName"
                :label-width="formLabelWidth"
            >
                <el-select
                    v-model="form.abnormalName"
                    placeholder="请选择异常名称"
                    style="width: 100%"
                    filterable
                     @change="abnormalNameChange"
                    >
                    <el-option
                        v-for="item in abnormalNameList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                        ></el-option>
                    </el-select>
            </el-form-item>

            <el-form-item
                label="异常描述"
                prop="abnormalDesc"
                :label-width="formLabelWidth"
            >
                <el-input
                    v-model="form.abnormalDesc"
                    type="textarea"
                    :rows="3"
                    :disabled="true"
                    placeholder="自动带出"
                    >
                </el-input>
            </el-form-item>
        </el-form> 

        <div class="bottom-button-wrap">
            <div>
                <el-button @click="handleCancel">取消</el-button>
                <el-button @click="handleSave" type="primary">确定</el-button>

            </div>

        </div>
    </el-dialog>
   
</template>
<script>  

import{
    getAbnormalRuleList,
    markAbnormal,
} from '@/api/order/index'

  export default {
    props: {
        editInfo: {
            type: Object,
            default: () => {},
        },
    },
    dicts: [
        'ls_order_except_level',
        'ls_order_except_type',
    ],
    components: {
        
    },
    data() {
        return {
            dialogVisible: false,
            orderNo: '',
            form: {
                isAbnormal: '',
                abnormalLevel: '',
                abnormalType: '',
                abnormalName: '',
                abnormalDesc: '',
            },
            rules: {
                isAbnormal: [
                    { required: true, message: '请选择是否异常', trigger: 'change' },
                ],
                abnormalName: [
                    { required: true, message: '请选择异常名称', trigger: 'blur' },
                ]

            },
            formLabelWidth: '120px',
            abnormalLevelList: [
                { label: '低', value: 1 },
                { label: '中', value: 2 },
                { label: '高', value: 3 },
            ],
            abnormalTypeList: [
                { label: '设备异常', value: 1 },
                { label: '订单异常', value: 2 },
                { label: '其他异常', value: 3 },
            ],
            abnormalNameList: [
                { label: '超充', value: 1 , desc: '超出充电额度'},
                { label: '逾期未支付', value: 2 , desc: '超出支付限制日期'},
                { label: '充电异常', value: 3 , desc: '充电过程中出现异常'},
                { label: '设备故障', value: 4 , desc: '充电设备出现故障'},
            ]
        };
    },
    watch: {
        
    },
    computed: {
        
    },
    mounted() {
        this.getAbnormalRuleList();
    },
    methods: {
        handleSave() {
            this.$refs.abnormalForm.validate(async (valid) => {
                if (valid) {
                   const {
                        isAbnormal,
                        abnormalType,
                        abnormalLevel,
                        abnormalName,
                        
                   } = this.form;
                   const params = {
                        orderNos: [this.orderNo],
                        exceptionFlag: isAbnormal,
                        level: abnormalLevel,
                        type: abnormalType,
                        exceptId: abnormalName
                   }


                   const [err, res] = await markAbnormal(params);
                   if (err) return 
                   this.$message.success('标记成功');
                   this.handleCancel();

                   this.$emit('loadData');

                }

            });
        },
        handleCancel() {
            this.dialogVisible = false;
        },
        abnormalNameChange() {
            this.form.abnormalDesc = this.abnormalNameList.find(item => item.value === this.form.abnormalName).desc;
        },

        // 异常等级变更
        changeAbnormalLevel() {
            this.form.abnormalName = '';
            this.form.abnormalDesc = '';
            this.abnormalNameList = [];
            this.getAbnormalRuleList()
        },

        // 获取异常规则列表
        async getAbnormalRuleList() {
            const params = {
                level: this.form.abnormalLevel,
                type: this.form.abnormalType,
            }
            const [err, res] = await getAbnormalRuleList(params);

            if (err) return 

            const {
                data
            } = res;
            const list = []
            data.forEach(item => {
                list.push({
                    value: item.exceptId,
                    label: item.name,
                    desc: item.description
                })
            })

            this.abnormalNameList = list;
        }

    },
  };
  </script>

<style lang="scss" scoped>
.bottom-button-wrap {
    height: 56px;
    margin-top: 46px;
    width: 100%;
    background-color: #FFFFFF;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    // padding-right: 32px;
    box-sizing: border-box;
}

  </style>
  