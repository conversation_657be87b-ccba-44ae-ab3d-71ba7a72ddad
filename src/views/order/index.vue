<template>
  <div class="container container-float">
    <div class="table-wrap">
      <BuseCrud
        ref="crud"
        :loading="loading"
        :filterOptions="filterOptions"
        :tablePage="tablePage"
        :tableColumn="tableColumn"
        :tableData="tableData"
        :pagerProps="pagerProps"
        :modalConfig="modalConfig"
        :tableOn="{
          'checkbox-change': handleCheckboxChange,
          'checkbox-all': handleCheckboxChange,
        }"
        class="buse-wrap-station"
        @loadData="loadData"
      >
        <template slot="defaultHeader">
          <div>
            <div class="card-head">
              <div class="card-head-text">订单管理列表</div>

              <div class="top-button-wrap">
                <el-button type="primary" @click="() => handleExport()">
                  批量导出
                </el-button>
              </div>
            </div>
          </div>
        </template>

        <template slot="operate" slot-scope="{ row }">
          <!-- <div class="menu-box">
                        <el-button
                             class="button-border"
                             plain
                             type="primary"
                            @click="hanleDetail(row)"
                        >
                            详情
                        </el-button>

                        <el-button
                         class="button-border"
                            type="primary"
                            plain
                            @click="handleMarkAbnormal(row)"
                        >
                            标记异常
                        </el-button>

                        <el-button
                         class="button-border"
                            type="primary"
                            plain
                            @click="cancelOrder(row)"
                        >
                            取消订单
                        </el-button>



                    
                    </div> -->

          <el-dropdown trigger="click">
            <el-button class="button-border" type="primary" plain>
              操作
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item>
                <div @click="hanleDetail(row)">详情</div>
              </el-dropdown-item>

              <el-dropdown-item>
                <div @click="handleMarkAbnormal(row)">标记异常</div>
              </el-dropdown-item>

              <el-dropdown-item>
                <div @click="cancelOrder(row)">取消订单</div>
              </el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </BuseCrud>
    </div>

    <markAbnormalOrderModal ref="markAbnormalOrderModal" @loadData="loadData" />
    <cancelOrderModal ref="cancelOrderModal" @loadData="loadData" />
  </div>
</template>
<script>
import { getOrderList } from '@/api/order/index';

import StatusDot from '@/components/Business/StatusDot';

import markAbnormalOrderModal from './components/markAbnormalOrderModal.vue';
import cancelOrderModal from './components/cancelOrderModal.vue';
export default {
  components: {
    markAbnormalOrderModal,
    cancelOrderModal,
    StatusDot,
  },
  dicts: [
    'ls_order_apply_mode', // 下单渠道
    'ls_order_method', // 下单方式
    'ls_charge_status', // 充电状态
    'ls_charge_order_status', // 订单状态
    'ls_pay_mode', // 支付方式
    'ls_charge_user_type', // 用户类型
    'ls_order_source',
    'ls_pile_source',
    'ls_pile_in_type',
  ],
  data() {
    return {
      loading: false,
      orderNo: '',
      orderNumberList: [],

      tablePage: { total: 0, currentPage: 1, pageSize: 10 },

      tableColumn: [
        // {
        //     type: 'checkbox',
        //     width: 50,
        //     fixed: 'left',
        // },
        {
          type: 'seq',
          title: '序号',
          width: 60,
          minWidth: 60, // 最小宽度
        },
        {
          field: 'orderNo',
          title: '订单编号',
          minWidth: 220, // 最小宽度
        },
        {
          field: 'partOrderNo',
          title: '第三方订单编号',
          minWidth: 200, // 最小宽度
          slots: {
            default: ({ row }) => [
              <el-tooltip
                content={row.partOrderNo}
                placement="top"
                disabled={!row.partOrderNo || row.partOrderNo.length < 15}
              >
                <span class="ellipsis-text">{row.partOrderNo}</span>
              </el-tooltip>,
            ],
          },
        },
        {
          field: 'applyModeName',
          title: '下单渠道',
          minWidth: 110,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_order_apply_mode,
              cellValue
            );
          },
        },
        {
          field: 'orderMethod',
          title: '下单方式',
          minWidth: 110,
        },
        {
          field: 'orderSource',
          title: '订单来源',
          minWidth: 110,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_order_source,
              cellValue
            );
          },
        },
        {
          field: 'applyTime',
          title: '下单时间',
          minWidth: 200,
        },
        {
          field: 'stationName',
          title: '充电场站',
          minWidth: 140,
        },
        {
          field: 'stationNo',
          title: '充电站编号',
          minWidth: 140,
        },
        {
          field: 'pileInType',
          title: '桩接入方式',
          minWidth: 110,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_pile_in_type,
              cellValue
            );
          },
        },
        {
          field: 'pileSource',
          title: '桩来源',
          minWidth: 110,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_pile_source,
              cellValue
            );
          },
        },

        {
          field: 'chargeStatus',
          title: '充电状态',
          minWidth: 110,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charge_status,
              cellValue
            );
          },
        },

        {
          field: 'invoiceStatus',
          title: '开票状态',
          minWidth: 110,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(
              this.dict.type.ls_charge_status,
              cellValue
            );
          },
        },
        {
          field: 'liquidationStatus',
          title: '清分状态',
          minWidth: 110,
        },
        {
          field: 'userAccountName',
          title: '下单用户',
          minWidth: 120,
        },
        {
          field: 'mobile',
          title: '用户手机号',
          minWidth: 130,
        },
        {
          field: 'userId',
          title: '用户ID',
          minWidth: 140,
          showOverflow: true,
        },
        {
          field: 'credentialNo',
          title: '用户证件号',
          minWidth: 180,
        },
        {
          field: 'userType',
          title: '用户类型',
          minWidth: 110,
        },
        {
          field: 'enterpriseId',
          title: '所属企业',
          minWidth: 160,
        },
        {
          field: 'enterpriseNo',
          title: '所属企业编号',
          minWidth: 160,
        },
        {
          field: 'bgnTime',
          title: '充电开始时间',
          minWidth: 180,
        },
        {
          field: 'endTime',
          title: '充电结束时间',
          minWidth: 180,
        },
        {
          field: 'chargeTimes',
          title: '充电时长（分钟）',
          minWidth: 150,
        },
        {
          field: 'settleTime',
          title: '结算时间',
          minWidth: 180,
        },
        {
          field: 'payTime',
          title: '支付时间',
          minWidth: 180,
        },
        {
          field: 'payMode',
          title: '支付方式',
          minWidth: 130,
          formatter: ({ cellValue }) => {
            return this.selectDictLabel(this.dict.type.ls_pay_mode, cellValue);
          },
        },
        {
          field: 'payChannel',
          title: '支付渠道',
          minWidth: 120,
        },
        {
          field: 'postpaidFlag',
          title: '是否后付费',
          minWidth: 110,
        },
        {
          field: 'cardNo',
          title: '卡号',
          minWidth: 160,
        },
        {
          field: 'freezeAmt',
          title: '冻结金额（元）',
          minWidth: 140,
        },
        {
          field: 'planChargeAmt',
          title: '预充值金额（元）',
          minWidth: 160,
        },
        {
          field: 'prepayRefundAmt',
          title: '预充退款金额（元）',
          minWidth: 200,
        },

        {
          field: 'vin',
          title: 'VIN码',
          minWidth: 180,
        },
        {
          field: 'licencePlateNumber',
          title: '车牌号',
          minWidth: 120,
        },
        {
          field: 'chargePq',
          title: '充电电量（kwh）',
          minWidth: 150,
        },
        {
          field: 'elecAmt',
          title: '充电电费（元）',
          minWidth: 150,
        },
        {
          field: 'cuspElecAmt',
          title: '尖电费（元）',
          minWidth: 120,
        },
        {
          field: 'peakElecAmt',
          title: '峰电费（元）',
          minWidth: 120,
        },

        {
          field: 'flatElecAmt',
          title: '平电费（元）',
          minWidth: 120,
        },
        {
          field: 'valleyElecAmt',
          title: '谷电费（元）',
          minWidth: 120,
        },

        // 服务费相关列
        {
          field: 'serviceAmt',
          title: '充电服务费（元）',
          minWidth: 180,
        },
        {
          field: 'cuspServiceAmt',
          title: '尖服务费（元）',
          minWidth: 180,
        },
        {
          field: 'peakServiceAmt',
          title: '峰服务费（元）',
          minWidth: 180,
        },
        {
          field: 'flatServiceAmt',
          title: '平服务费（元）',
          minWidth: 180,
        },
        {
          field: 'valleyServiceAmt',
          title: '谷服务费（元）',
          minWidth: 180,
        },
        {
          field: 'chargeAmt',
          title: '充电总金额（元）',
          minWidth: 180,
        },
        {
          field: 'realPrice',
          title: '实扣金额（元）',
          minWidth: 180,
        },
        {
          field: 'electricityPrice',
          title: '实扣电费（元）',
          minWidth: 180,
        },
        {
          field: 'servicePrice',
          title: '实扣服务费（元）',
          minWidth: 150,
        },
        {
          field: 'refundAmount',
          title: '订单退款金额（元）',
          minWidth: 180,
        },
        {
          field: 'discountedPrice',
          title: '优惠金额（元）',
          minWidth: 180,
        },
        {
          field: 'electricityPriceDiscounted',
          title: '优惠电费（元）',
          minWidth: 180,
        },
        {
          field: 'servicePriceDiscounted',
          title: '优惠服务费（元）',
          minWidth: 180,
        },

        {
          field: 'couponCode',
          title: '优惠券编号',
          minWidth: 180,
        },
        {
          field: 'couponPriceDiscounted',
          title: '优惠券优惠金额（元）',
          minWidth: 200,
        },
        {
          field: 'activityId',
          title: '立减活动编号',
          minWidth: 180,
        },
        {
          field: 'activityDiscounted',
          title: '立减优惠金额（元）',
          minWidth: 200,
        },

        {
          field: 'pileName',
          title: '充电桩名称',
          minWidth: 180,
        },
        {
          field: 'pileNo',
          title: '充电桩编号',
          minWidth: 180,
        },
        {
          field: 'gunName',
          title: '充电枪名称',
          minWidth: 180,
        },
        {
          field: 'elecChcNo',
          title: '电费计费模型ID',
          minWidth: 200,
        },
        {
          field: 'serviceChcNo',
          title: '服务费计费模型ID',
          minWidth: 200,
        },
        {
          field: 'assetUnitName',
          title: '资产单位',
          minWidth: 120,
        },
        {
          field: 'operatingUnitName',
          title: '运营单位',
          minWidth: 120,
        },
        {
          field: 'maintenanceUnitName',
          title: '运维单位',
          minWidth: 120,
        },
        {
          field: 'liquidationUnitName',
          title: '清分单位',
          minWidth: 120,
        },

        {
          field: 'cuspElec',
          title: '尖电量(kwh)',
          minWidth: 140,
        },
        {
          field: 'peakElec',
          title: '峰电量(kwh)',
          minWidth: 140,
        },
        {
          field: 'flatElec',
          title: '平电量(kwh)',
          minWidth: 140,
        },
        {
          field: 'valleyElec',
          title: '谷电量(kwh)',
          minWidth: 140,
        },
        {
          field: 'tmr',
          title: '抄表电量(kwh)',
          minWidth: 150,
        },
        {
          field: 'startSoc',
          title: '充电前SOC%',
          minWidth: 140,
        },
        {
          field: 'endSoc',
          title: '充电后SOC%',
          minWidth: 140,
        },
        {
          field: 'subTypeName',
          title: '电流方式',
          minWidth: 120,
        },
        {
          field: 'invoiceId',
          title: '发票序列编号',
          minWidth: 180,
        },
        {
          field: 'liquidationId',
          title: '明细清分ID',
          minWidth: 200,
        },
        {
          field: 'liquidationDate',
          title: '清分时间',
          minWidth: 180,
        },
        {
          field: 'stopReason',
          title: '结束充电原因',
          minWidth: 180,
        },
        {
          field: 'cancelReason',
          title: '取消订单原因',
          minWidth: 180,
        },
        {
          field: 'cancelUser',
          title: '取消人',
          minWidth: 150,
        },
        {
          field: 'cancelTime',
          title: '取消时间',
          minWidth: 180,
        },
        {
          field: 'orderStatus',
          title: '订单状态',
          minWidth: 100,
          // formatter: ({ cellValue }) => {
          //   return this.selectDictLabel(
          //     this.dict.type.ls_charge_order_status,
          //     cellValue
          //   );
          // },
          fixed: 'right',

          slots: {
            // 自定义render函数
            default: ({ row }) => {
              return (
                <StatusDot
                  value={row.orderStatus}
                  dictValue={this.dict.type.ls_charge_order_status}
                  colors={[
                    'warning',
                    'warning',
                    'charge',
                    'success',
                    'forverstop',
                    'stop',
                  ]}
                ></StatusDot>
              );
            },
          },
        },
        {
          title: '操作',
          slots: { default: 'operate' },
          width: 80,
          align: 'center',
          fixed: 'right',
        },
      ],
      tableData: [],
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      params: {
        stationName: '',
        stationNo: '',
        pileName: '',
        pileNo: '',
        orderNo: '',
        partOrderNo: '',
        applyMode: '',
        orderMethod: '',
        userAccountName: '',
        mobile: '',
        orderTime: [],
        chargeStartTimeRange: [],
        chargeEndTimeRange: [],
        settlementTime: [],
        chargeStatus: '',
        orderStatus: '',
        payMode: '',
        userType: '',
        assetUnitName: '',
        operatingUnitName: '',
        orderSource: '02',
        pileSource: '',
        pileInType: '',
      },
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'stationName',
            title: '充电站名称',
            element: 'el-input',
          },
          {
            field: 'stationNo',
            title: '充电站编号',
            element: 'el-input',
          },
          {
            field: 'pileName',
            title: '充电桩名称',
            element: 'el-input',
          },
          {
            field: 'pileNo',
            title: '充电桩编号',
            element: 'el-input',
          },
          {
            field: 'orderNo',
            title: '订单编号',
            element: 'el-input',
          },
          {
            field: 'partOrderNo',
            title: '第三方订单编号',
            element: 'el-input',
            props: {
              placeholder: '请输入',
            },
          },
          {
            field: 'applyMode',
            title: '下单渠道',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.dict.type.ls_order_apply_mode,
            },
          },
          {
            field: 'orderMethod',
            title: '下单方式',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.dict.type.ls_order_method,
            },
          },
          {
            field: 'userAccountName',
            title: '下单用户',
            element: 'el-input',
          },
          {
            field: 'mobile',
            title: '用户手机号',
            element: 'el-input',
            props: {
              maxlength: 11,
            },
          },
          {
            field: 'orderTime',
            title: '下单时间',
            element: 'el-date-picker',
            props: {
              type: 'daterange',
              options: [],
              valueFormat: 'yyyy-MM-dd',
            },
          },
          {
            field: 'chargeStartTimeRange',
            title: '充电开始时间',
            element: 'el-date-picker',
            props: {
              type: 'daterange',
              options: [],
              valueFormat: 'yyyy-MM-dd',
            },
          },
          {
            field: 'chargeEndTimeRange',
            title: '充电结束时间',
            element: 'el-date-picker',
            props: {
              type: 'daterange',
              options: [],
              valueFormat: 'yyyy-MM-dd',
            },
          },
          {
            field: 'settlementTime',
            title: '结算时间',
            element: 'el-date-picker',
            props: {
              type: 'datetimerange',
              'start-placeholder': '开始日期',
              'end-placeholder': '结束日期',
              valueFormat: 'yyyy-MM-dd HH:mm:ss',
              defaultTime: ['00:00:00', '23:59:59'],
            },
          },
          {
            field: 'chargeStatus',
            title: '充电状态',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_charge_status,
            },
          },
          {
            field: 'orderStatus',
            title: '订单状态',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_charge_order_status,
            },
          },
          {
            field: 'payMode',
            title: '支付方式',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_pay_mode,
            },
          },
          {
            field: 'userType',
            title: '用户类型',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_charge_user_type,
            },
          },
          {
            field: 'assetUnitName',
            title: '资产单位',
            element: 'el-input',
          },
          {
            field: 'operatingUnitName',
            title: '运营单位',
            element: 'el-input',
          },
          {
            field: 'orderSource',
            title: '订单来源',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_order_source,
            },
          },
          {
            field: 'pileSource',
            title: '桩来源',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_pile_source,
            },
          },
          {
            field: 'pileInType',
            title: '桩接入方式',
            element: 'el-select',
            props: {
              options: this.dict.type.ls_pile_in_type,
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  mounted() {
    this.loadData();
  },
  methods: {
    async loadData() {
      const {
        stationName,
        stationNo,
        pileName,
        pileNo,
        orderNo,
        partOrderNo,
        applyMode,
        orderMethod,
        userAccountName,
        mobile,
        orderTime,
        chargeStartTimeRange,
        chargeEndTimeRange,
        settlementTime,
        chargeStatus,
        orderStatus,
        payMode,
        userType,
        assetUnitName,
        operatingUnitName,
        orderSource,
        pileSource,
        pileInType,
      } = this.params;

      let applyStartTime = '';
      let applyEndTime = '';
      if (orderTime && orderTime.length > 0) {
        applyStartTime = orderTime[0];
        applyEndTime = orderTime[1];
      }

      let bgnTimeStart = '';
      let bgnTimeEnd = '';
      if (chargeStartTimeRange && chargeStartTimeRange.length > 0) {
        bgnTimeStart = chargeStartTimeRange[0];
        bgnTimeEnd = chargeStartTimeRange[1];
      }

      let endTimeStart = '';
      let endTimeEnd = '';
      if (chargeEndTimeRange && chargeEndTimeRange.length > 0) {
        endTimeStart = chargeEndTimeRange[0];
        endTimeEnd = chargeEndTimeRange[1];
      }

      let settleTimeStart = '';
      let settleTimeEnd = '';
      if (settlementTime && settlementTime.length > 0) {
        settleTimeStart = settlementTime[0];
        settleTimeEnd = settlementTime[1];
      }

      let params = {
        stationName,
        stationNo,
        pileName,
        pileNo,
        orderNo,
        partOrderNo,
        applyMode,
        orderMethod,
        userAccountName,
        mobile,
        applyStartTime,
        applyEndTime,
        bgnTimeStart,
        bgnTimeEnd,
        endTimeStart,
        endTimeEnd,
        settleTimeStart,
        settleTimeEnd,
        chargeStatus,
        orderStatus,
        payMode,
        userType,
        assetUnitName,
        operatingUnitName,
        orderSource,
        pileSource,
        pileInType,
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
      };

      this.loading = true;
      const [err, res] = await getOrderList(params);

      this.loading = false;

      if (err) return;

      const { data, total } = res;

      this.tableData = data;
      this.tablePage.total = total;
    },

    handleCheckboxChange({ records }) {
      this.orderNumberList = records;
    },

    // 批量导出
    handleExport(row) {
      const {
        stationName,
        stationNo,
        pileName,
        pileNo,
        orderNo,
        partOrderNo,
        applyMode,
        orderMethod,
        userAccountName,
        mobile,
        orderTime,
        chargeStartTimeRange,
        chargeEndTimeRange,
        settlementTime,
        chargeStatus,
        orderStatus,
        payMode,
        userType,
        assetUnitName,
        operatingUnitName,
      } = this.params;

      let applyStartTime = '';
      let applyEndTime = '';
      if (orderTime && orderTime.length > 0) {
        applyStartTime = orderTime[0];
        applyEndTime = orderTime[1];
      }

      let bgnTimeStart = '';
      let bgnTimeEnd = '';
      if (chargeStartTimeRange && chargeStartTimeRange.length > 0) {
        bgnTimeStart = chargeStartTimeRange[0];
        bgnTimeEnd = chargeStartTimeRange[1];
      }

      let endTimeStart = '';
      let endTimeEnd = '';
      if (chargeEndTimeRange && chargeEndTimeRange.length > 0) {
        endTimeStart = chargeEndTimeRange[0];
        endTimeEnd = chargeEndTimeRange[1];
      }

      let settleTimeStart = '';
      let settleTimeEnd = '';
      if (settlementTime && settlementTime.length > 0) {
        settleTimeStart = settlementTime[0];
        settleTimeEnd = settlementTime[1];
      }

      let params = {
        stationName,
        stationNo,
        pileName,
        pileNo,
        orderNo,
        partOrderNo,
        applyMode,
        orderMethod,
        userAccountName,
        mobile,
        applyStartTime,
        applyEndTime,
        bgnTimeStart,
        bgnTimeEnd,
        endTimeStart,
        endTimeEnd,
        settleTimeStart,
        settleTimeEnd,
        chargeStatus,
        orderStatus,
        payMode,
        userType,
        assetUnitName,
        operatingUnitName,
      };
      this.download(
        '/vehicle-charging-admin/order/export',
        {
          ...params,
        },
        `订单列表.xlsx`
      );
    },

    // 标记异常
    handleMarkAbnormal(row) {
      const { orderNo } = row;
      this.$refs.markAbnormalOrderModal.orderNo = orderNo;
      this.$refs.markAbnormalOrderModal.dialogVisible = true;
    },

    // 取消订单
    cancelOrder(row) {
      const { orderNo } = row;
      this.$refs.cancelOrderModal.orderNo = orderNo;
      this.$refs.cancelOrderModal.dialogVisible = true;
    },

    // 订单详情
    hanleDetail(row) {
      const { orderNo } = row;
      this.$router.push({
        path: '/v2g-charging/operatorManage/orderManage/orderManage/detail',
        query: {
          orderNo,
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.table-wrap {
  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }

  .card-head {
    // position: relative;
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    margin-top: -20px;
    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }
  .info-wrap {
    margin-top: 16px;
    display: flex;
    flex-wrap: wrap;
    gap: 16px;

    .info-item {
      background-color: #fafbfc;
      flex: 1 1 0;
      // min-width: 180px;

      border-radius: 5px;
      padding: 8px 24px;
      box-sizing: border-box;
      // margin-right: 16px;
      display: flex;
      .info-icon {
        width: 42px;
        height: 42px;
      }
      .info-right-wrap {
        flex: 1;
        margin-left: 8px;
        .info-title {
          font-weight: 400;
          font-size: 14px;
          line-height: 14px;
          margin-bottom: 8px;
        }
        .info-number {
          font-size: 20px;
          font-weight: 500;
          .info-unit {
            font-size: 14px;
            font-weight: 400;
          }
        }
      }
    }
    .info-item:last-child {
      margin-right: 0;
    }
  }

  .top-button-wrap {
    display: flex;
    margin: 16px 0;
  }
}

.top-info-wrap {
  display: flex;
  height: 20px;
  align-items: center;
  margin-bottom: 16px;

  .top-info-icon {
    margin-left: 10px;
    margin-right: 4px;

    width: 20px;
    height: 20px;
    background-image: url('~@/assets/station/location-top.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
  .top-info-first {
    font-weight: 400;
    font-size: 16px;
    color: #505363;
  }
  .top-info-bold {
    margin-left: 4px;
    font-weight: 400;
    font-size: 16px;
    color: #12151a;
  }
}

.button-border {
  border: 0.01rem solid #217aff;
  color: #217aff;
  background-color: #fff;
}

.ellipsis-text {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}
</style>
