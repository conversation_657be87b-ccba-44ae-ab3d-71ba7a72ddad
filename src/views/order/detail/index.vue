<template>
    <div class="container container-float " style="padding: 0 0 100px 0;">
        <div class="device-head">
            <img src="@/assets/order/order-top-icon.png" class="device-head-icon">
            
            <div class="device-info-wrap">
                <div class="device-title-wrap">
                    <div class="device-title">订单编号：{{ orderNo }}</div>
                </div>
                <div class="device-info-wrap">
                    <el-row>
                        <el-col :span="6">
                            <span class="label">第三方订单编号：</span>
                            <span class="value">{{thirdPartyOrderNumber}}</span>
                        </el-col>
                        <el-col :span="6">
                            <span class="label">下单方式：</span>
                            <span class="value">{{orderMethod}}</span>
                        </el-col>
                        <el-col :span="6">
                            <span class="label">下单渠道：</span>
                            <span class="value">{{ orderChannel }}</span>
                        </el-col>
                        <el-col :span="6">
                            <span class="label">下单时间：</span>
                            <span class="value">{{ orderTime}}</span>
                        </el-col>
                    </el-row>
                </div>
            </div>
            <div class="device-status-wrap">
                    <div class="device-status-item-wrap">
                        <div class="device-status-item-title">清分状态</div>
                        <div class="device-status">{{  sortStatus}}</div>
                    </div>
                    <div class="device-status-split"></div>
                    <div class="device-status-item-wrap">
                        <div class="device-status-item-title">开票状态</div>
                        <div class="device-status">{{  invoiceStatus}}</div>
                    </div>
                    <div class="device-status-split"></div>
                    <div class="device-status-item-wrap">
                        <div class="device-status-item-title">订单状态</div>
                        <div class="device-status-success">{{  orderStatus }}</div>
                    </div>

            </div>
        </div>

        <div class="info-wrap" >
            <el-tabs v-model="activeName" @tab-click="handleClick">
                <el-tab-pane label="订单详情" name="detail">
                    <div class="info-item">
                        <div class="info-item-head">
                            <div class="info-item-before-icon"></div>
                            <div class="info-item-head-text">基本信息</div>
                        </div>

                        <div class="form-wrap">
                            <el-row :gutter="20">
                                <el-col :span="6" v-for="(item, key) in baseInfo" :key="key" style="margin-bottom: 24px;">
                                    <div style="display: flex;">
                                        <div class="info-title">{{labels.baseInfo[key]}}：</div>
                                        <div class="info-detail">{{ item }}</div>
                                    </div>
                                </el-col>
                            </el-row>
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-item-head">
                            <div class="info-item-before-icon"></div>
                            <div class="info-item-head-text">充电信息</div>
                        </div>


                        <div class="form-wrap">
                            <el-row :gutter="20">
                                <el-col :span="6" v-for="(item, key) in chargeInfo" :key="key" style="margin-bottom: 24px;">
                                    <div style="display: flex;">
                                        <div class="info-title">{{labels.chargeInfo[key]}}：</div>
                                        <div class="info-detail">{{ item }}</div>
                                    </div>
                                </el-col>
                            </el-row>
                        </div>
                    </div>
                   
                    <div class="info-item">
                        <div class="info-item-head">
                            <div class="info-item-before-icon"></div>
                            <div class="info-item-head-text">结算明细</div>
                        </div>

                        <div class="form-wrap">
                            <div class="form-title-wrap">
                                <div class="form-title-blue">
                                    电费计费模型ID：
                                    <block style="font-weight: 500; text-decoration: underline;">{{ settlementModelId }}</block>
                                </div>

                                <div class="form-title-blue">
                                    服务费用计费模型ID：
                                    <block style="font-weight: 500; text-decoration: underline;">{{ serviceModelId }}</block>
                                </div>

                                <div class="form-title">
                                    优惠券编号：
                                    <block style="font-weight: 500;">{{ couponNumber }}</block>
                                </div>

                                <div class="form-title">
                                    立减活动编号：
                                    <block style="font-weight: 500;">{{ activityNumber }}</block>
                                </div>
                            </div>

                            <BuseCrud
                                ref="settlement"
                                :tableColumn="settlementTableColumn"
                                :tableData="settlementData"
                                :modalConfig="{ addBtn: false, menu: false }"
                            >
                                
                            </BuseCrud>
                        </div>
                    </div>  

                    <div class="info-item">
                        <div class="info-item-head">
                            <div class="info-item-before-icon"></div>
                            <div class="info-item-head-text">支付信息</div>
                        </div>

                        <div class="form-wrap">
                            <BuseCrud
                                ref="payInfo"
                                :tableColumn="payInfoTableColumn"
                                :tableData="payInfoData"
                                :modalConfig="{ addBtn: false, menu: false }"
                            >
                                
                            </BuseCrud>
                        </div>
                    </div>

                    <div class="info-item">
                        <div class="info-item-head">
                            <div class="info-item-before-icon"></div>
                            <div class="info-item-head-text">退款记录</div>
                        </div>

                        <div class="form-wrap">
                            <BuseCrud
                                ref="refund"
                                :tableColumn="refundTableColumn"
                                :tableData="refundData"
                                :modalConfig="{ addBtn: false, menu: false }"
                            >
                                
                            </BuseCrud>
                        </div>
                    </div>
                </el-tab-pane>
                <el-tab-pane label="充电信息" name="chargeInfo">
                    <div class="chart-wrap" v-if="activeName === 'chargeInfo'">
                        <div class="chart-item-left-wrap"  >
                            <div class="info-item-head">
                                <div class="info-item-before-icon"></div>
                                <div class="info-item-head-text">趋势分析1</div>
                            </div>
                            <LineChart
                                id="trendChart1"
                                :data="chart1.data"
                                :xAxis="chart1.xAxis"
                                :unit="chart1.unit"
                                height="734px"
                            ></LineChart>
                        </div>
                        <div class="chart-item-right-wrap">
                            <div class="info-item-head">
                                <div class="info-item-before-icon"></div>
                                <div class="info-item-head-text">趋势分析2</div>
                            </div>
                            <LineChart
                                id="trendChart2"
                                :data="chart2.data"
                                :xAxis="chart2.xAxis"
                                :unit="chart2.unit"
                                height="734px"
                            ></LineChart>
                        </div>

                    </div>
                </el-tab-pane>
                <el-tab-pane label="控制记录" name="control">
                    <div class="approval-steps">
                        <el-steps direction="vertical" :active="controlList.length" space="100px">
                        <el-step 
                            v-for="(item, index) in controlList" 
                            :key="index"
            
                        >
                            <!-- 自定义步骤图标 -->
                            <template #icon>
                                <div :class="`step-icon `">
                                    {{ index + 1 }}
                                </div>
                            
                            </template>

                            <!-- 自定义步骤内容 -->
                            <template #title>
                               

                                <div class="remark-box" >
                                   
                                    <div class="remark-icon"> </div>

                                    <div  class="person-info">
                                        <div class="step-header">
                                            <span class="title">{{ item.chargeControlTypeName }}</span>
                                        </div>

                                        <div class="remark-time" v-if="item.controlTime">
                                            {{ item.controlTime }}
                                        </div>
                                    </div>
                                    
                                   
                                
                                </div>
                            </template>

                            
                        </el-step>
                        </el-steps>
                    </div>
                </el-tab-pane>
                <el-tab-pane label="异常记录" name="abnormal">
                    <div class="abnormal-form-wrap">
                        <BuseCrud
                            ref="abnormal"
                            :tableColumn="abnormalTableColumn"
                            :tableData="abnormalData"
                            :modalConfig="{ addBtn: false, menu: false }"
                            >     
                            <template slot="operate" slot-scope="{ row }">
                                <div class="menu-box">
                                    <el-button
                                        type="primary"
                                        plain
                                        @click="handleAbnormalDetail(row)"
                                    >
                                       处理详情
                                    </el-button>
                                </div>
                            </template>       
                        </BuseCrud>
                    </div>
                   
                </el-tab-pane>
                
                
                
            </el-tabs>
        </div>



       
    </div>
    
  </template>
  
  <script>
  import LineChart from '@/components/Charts/line';

  import {
    getOrderDetail
  } from '@/api/order/index';
  
    export default {
    components: {
        LineChart
    },
    dicts: [
        'ls_charge_status',
    ],
    data() {
      return {
            orderNo: '',
            thirdPartyOrderNumber: '',
            orderMethod: '',
            orderChannel: '',
            orderTime: '',

            sortStatus: '',
            invoiceStatus: '',
            orderStatus: '',
            
            activeName: 'detail',
            

            labels: {
                baseInfo: {
                    orderUser: '下单用户',
                    orderPhone: '用户手机号',
                    userType: '用户类型',
                    userId: '用户ID',
                    userIDNumber: '用户证件号',
                    enterpriseName: '企业名称',
                    enterpriseCode: '企业编号',
                    paymentMethod: '支付方式',
                    paymentChannel: '支付渠道',
                    isPostpaid: '是否后付费',
                    cardNumber: '卡号',
                    VIN: 'VIN码',
                    licensePlate: '车牌号',
                    settlementTime: '结算时间',
                    orderPaymentTime: '订单支付时间',
                    clearingTime: '清分时间',
                    invoiceSerialNumber: '发票序列编号',
                    detailedClearingID: '明细清分ID',
                    assetUnit: '资产单位',
                    operatingUnit: '运营单位',
                    maintenanceUnit: '运维单位',
                    clearingUnit: '清分单位',
                    isAbnormalSettlement: '是否异常结算',
                    cancelOrderTime: '取消订单时间',
                    cancelOrderUser: '取消订单人',
                    cancelOrderReason: '取消订单原因',
                },
                chargeInfo: {
                    stationName: '充电站',
                    stationId: '充电站编号',
                    chargingPile: '充电桩',
                    chargingPileId: '充电桩编号',
                    chargingGun: '充电枪',
                    chargingGunId: '充电枪编号',
                    chargingStatus: '充电状态',
                    chargingMode: '电流方式',
                    chargingAmount: '充电量(kWh)',
                    meterReading: '抄表电量(kWh)',
                    meterStartValue: '电表总起值',
                    meterEndValue: '电表总止值',
                    startTime: '充电开始时间',
                    endTime: '充电结束时间',
                    chargingDuration: '充电时长(分钟)',
                    startSOC: '开始SOC(%)',
                    endSOC: '结束SOC(%)',
                    stopReason: '结束充电原因',
                    orderSource: '订单来源',
                    pileSource: '桩来源',
                    connectionMethod: '桩接入方式'
                }
            },

            baseInfo: {
                orderUser: '',
                orderPhone: '',
                userType: '',
                userId: '',
                userIDNumber: '',
                enterpriseName: '',
                enterpriseCode: '',
                paymentMethod: '',
                paymentChannel: '',
                isPostpaid: '',
                cardNumber: '',
                VIN: '',
                licensePlate: '',
                settlementTime: '',
                orderPaymentTime: '',
                clearingTime: '',
                invoiceSerialNumber: '',
                detailedClearingID: '',
                assetUnit: '',
                operatingUnit: '',
                maintenanceUnit: '',
                clearingUnit: '',
                isAbnormalSettlement: '',
                cancelOrderTime: '',
                cancelOrderUser: '',
                cancelOrderReason: '',
            },

            chargeInfo: {
                stationName: '',
                stationId: '', // 修正图片中的完整编号
                chargingPile: '',
                chargingPileId: '',
                chargingGun: '',
                chargingGunId: '',
                chargingStatus: '',
                chargingMode: '',
                chargingAmount: '',
                meterReading: '',
                meterStartValue: '',
                meterEndValue: '',
                startTime: '',
                endTime: '',
                chargingDuration: '',
                startSOC: '',
                endSOC: '',
                stopReason: '',
                connectionMethod: ''
            },

            settlementModelId: '',
            serviceModelId: '',
            couponNumber: '',
            activityNumber: '',


            settlementTableColumn: [
                {
                    field: 'timeFlag',
                    title: '时段标识',
                    minWidth: 180,
                   
                },
                {
                    field: 'time',
                    title: '时段',
                    minWidth: 100,
                    slots: {
                        default: ({ row }) => [
                            <div style='display: flex; align-items: center;'>
                                <div>{row.beginTime}</div>
                                <div>-</div>
                                <div>{row.endTime}</div>


                            </div>
                           
                        ]
                    }
                    
                },
                {
                    field: 'periodPq',
                    title: '时段充电量(kWh)',
                    minWidth: 130,
                   
                    
                },
                {
                    field: 'periodElecPrice',
                    title: '时段电价(元/kWh)',
                    minWidth: 140,
                   
                    
                },
                {
                    field: 'periodElecFee',
                    title: '时段电费(元)',
                    minWidth: 120,
                    
                   
                },
                {
                    field: 'periodServicePrice',
                    title: '服务费单价(元/kWh)',
                    minWidth: 150,
                    
                },
                {
                    field: 'periodServiceFee',
                    title: '服务费(元)',
                    minWidth: 120,
                   
                },
                {
                    field: 'periodTotalFee',
                    title: '总金额(元)',
                    minWidth: 120,
                   
                }
            ],

            settlementData: [],

            payInfoTableColumn: [
                {
                    type: 'seq',
                    title: '序号',
                    width: 60,
                    minWidth: 60,
                    fixed: 'left',
                    align: 'center'
                },
                {
                    field: 'payMode',
                    title: '支付方式',
                    minWidth: 180,
                },
                {
                    field: 'planChargeAmt',
                    title: '预充值金额（元）',
                    minWidth: 150,
                },
                {
                    field: 'totalReceipt',
                    title: '应收金额（元）',
                    minWidth: 150,
                },
                {
                    field: 'realPrice',
                    title: '实扣金额（元）',
                    minWidth: 150,
                },
                {
                    field: 'realPrprepayRefundAmtce',
                    title: '预充退款金额（元）',
                    minWidth: 180,
                },
                {
                    field: 'refundAmount',
                    title: '订单退款金额（元）',
                    minWidth: 180,
                }
            ],

            payInfoData: [],
            refundTableColumn : [
                {
                    type: 'seq',
                    title: '序号',
                    width: 60,
                    minWidth: 60,
                },
                {
                    field: 'refundTime',
                    title: '退款时间',
                    minWidth: 180,
                },
                {
                    field: 'refundType',
                    title: '退款类型',
                    minWidth: 150,
                },
                {
                    field: 'refundAmt',
                    title: '退款金额（元）',
                    minWidth: 150,
                },
                {
                    field: 'serviceRefundAmt',
                    title: '服务费退款（元）',
                    minWidth: 160,
                },
                {
                    field: 'elecRefundAmt',
                    title: '电费退款（元）',
                    minWidth: 160,
                },
                {
                    field: 'refundReason',
                    title: '退款原因',
                    minWidth: 220,
                },
                {
                    field: 'refundStatus',
                    title: '退款状态',
                    minWidth: 180,
                }
            ],

            refundData:[],

            chart1: {
                data: [
                    {
                        name: '输出电压',
                        data: [],
                    },
                ],
                xAxis: {
                    data: [],
                },
                
            },

            chart2: {
                data: [
                    {
                        name: '电池电量',
                        data: [],
                    },
                ],
                xAxis: {
                    data: [],
                },
            },

            controlList:[],
            // controlList: [
            //     {
            //         title: '创建订单',
            //         time: '2024-11-05 09:50:34',
            //         status: '02',
            //     },
            //     {
            //         title: '下发启动充电',
            //         time: '2024-11-05 09:50:34',
            //         status: '02',
            //     },
            //     {
            //         title: 'IOE上报启动成功',
            //         time: '2024-11-05 09:50:34',
            //         status: '02',
            //     },
            //     {
            //         title: '下发停止充电',
            //         time: '2024-11-05 09:50:34',
            //         status: '02',
            //     },
            //     {
            //         title: 'IOE上报停止成功',
            //         time: '2024-11-05 09:50:34',
            //         status: '02',
            //     },
            //     {
            //         title: '交易结算',
            //         time: '2024-11-05 09:50:34',
            //         status: '05',
            //     },
            // ],

            abnormalTableColumn: [
                {
                    type: 'seq',
                    title: '序号',
                    width: 60,
                    minWidth: 60,
                },
                {
                    field: 'level',
                    title: '异常等级',
                    minWidth: 120,
                },
                {
                    field: 'type',
                    title: '异常类型',
                    minWidth: 150,
                 
                },
                {
                    field: 'name',
                    title: '异常名称',
                    minWidth: 180,
                },
                {
                    field: 'subType',
                    title: '异常原因',
                    minWidth: 200,
                },
                {
                    field: 'reportTime',
                    title: '异常上报时间',
                    minWidth: 180,
                },
                {
                    field: 'status',
                    title: '处理状态',
                    minWidth: 180,
                    
                },
                {
                    field: 'transactTime',
                    title: '处理时间',
                    minWidth: 180
                },
                {
                    title: '操作',
                    slots: { default: 'operate' },
                    width: 120,
                    align: 'center',
                    fixed: 'right',

                }
            ],

            abnormalData: []


      };
    },

    computed: {
    },
    mounted() {

        const orderNo = this.$route.query.orderNo;

        this.orderNo = orderNo;
        this.getOrderDetail();
        
    },
    methods: {
        // tab 切换
        handleClick({index}) {
            console.log(Number(index) === 0,'index')
        },

        // 处理详情
        handleAbnormalDetail(row) {
            // todo 跳转异常详情
            const {
                orderNo
            } = row;
            this.$router.push({
                path: '/v2g-charging-web/operatorManage/orderManage/abnormalOrder/handle',
                query: {
                    orderNo: orderNo,
                }
            })
        },

        // 获取详情
        async getOrderDetail() {
            const [err,res] = await getOrderDetail({
                orderNo: this.orderNo
            })

            if (err) return

            // 顶部信息
            const {
                orderNo,
                partOrderNo,
                orderMethod,
                applyModeName,
                applyTime,
                liquidationStatus,
                invoiceStatus,
                orderStatusName,
            } = res.data

            this.orderNo = orderNo
            this.thirdPartyOrderNumber = partOrderNo
            this.orderMethod  = orderMethod
            this.orderChannel = applyModeName
            this.orderTime = applyTime
            this.sortStatus = liquidationStatus
            this.invoiceStatus = this.selectDictLabel(
                    this.dict.type.ls_charge_status,
                    invoiceStatus
                  );
            this.orderStatus = orderStatusName

            // 订单信息-基本信息
            const {
                userAccountName,
                mobile,
                userType,
                userId,
                credentialNo,
                enterpriseId,
                enterpriseNo,
                payMode,
                payChannel,
                postpaidFlag,
                cardNo,
                vin,
                licencePlateNumber,
                settleTime,
                payTime,
                liquidationDate,
                invoiceId,
                liquidationId,
                assetUnitName,
                operatingUnitName,
                maintenanceUnitName,
                liquidationUnitName,
                abnoSettleFlag,
                cancelTime,
                cancelUser,
                cancelReason,
            } = res.data

            this.baseInfo =  {
                orderUser: userAccountName,
                orderPhone: mobile,
                userType: userType,
                userId: userId,
                userIDNumber: credentialNo,
                enterpriseName: enterpriseId,
                enterpriseCode: enterpriseNo,
                paymentMethod: payMode,
                paymentChannel: payChannel,
                isPostpaid: postpaidFlag,
                cardNumber: cardNo,
                VIN: vin,
                licensePlate: licencePlateNumber,
                settlementTime: settleTime,
                orderPaymentTime: payTime,
                clearingTime: liquidationDate,
                invoiceSerialNumber: invoiceId,
                detailedClearingID: liquidationId,
                assetUnit: assetUnitName,
                operatingUnit: operatingUnitName,
                maintenanceUnit: maintenanceUnitName,
                clearingUnit: liquidationUnitName,
                isAbnormalSettlement: abnoSettleFlag,
                cancelOrderTime: cancelTime,
                cancelOrderUser: cancelUser,
                cancelOrderReason:cancelReason ,
            }

            // 订单信息-充电信息
            const {
                stationName,
                stationNo,
                pileName,
                pileNo,
                gunName,
                gunNo,
                chargeStatusName,
                subTypeName,
                chargePq,
                tmr,
                beginTMr,
                endTMr,
                bgnTime,
                endTime,
                chargeTimes,
                startSoc,
                endSOC,
                stopReason,
                orderSource,
                pileSource,
                pileInType,
            } = res.data

            this.chargeInfo = {
                stationName: stationName,
                stationId: stationNo,
                chargingPile: pileName,
                chargingPileId: pileNo,
                chargingGun: gunName,
                chargingGunId: gunNo,
                chargingStatus: chargeStatusName,
                chargingMode: subTypeName,
                chargingAmount: chargePq,
                meterReading: tmr,
                meterStartValue: beginTMr,
                meterEndValue: endTMr,
                startTime: bgnTime,
                endTime: endTime,
                chargingDuration: chargeTimes,
                startSOC: startSoc,
                endSOC: endSOC,
                stopReason: stopReason,
                orderSource,
                pileSource,
                connectionMethod:  pileInType,
            }

            // 结算明细
            const {
                elecChcNo,
                serviceChcNo,
                couponCode,
                activityId,
                periodFeeBOList = [],
                totalPq,
                totalElecFee,
                totalServiceFee,
                totalFee,
                couponPriceDiscounted,
                activityDiscounted,
                electricityPriceDiscounted,
                servicePriceDiscounted,
                discountedPrice,
                elecReceipt,
                serviceReceipt,
                totalReceipt,
            } = res.data

            const list = [
                {
                    timeFlag: '小计',
                    periodPq: totalPq,
                    periodElecPrice: '-',
                    periodElecFee: totalElecFee,
                    periodServicePrice: '-',
                    periodServiceFee: totalServiceFee,
                    periodTotalFee: totalFee,
                },
                {
                    timeFlag: '优惠券优惠',
                    periodPq: '-',
                    periodElecPrice: '-',
                    periodElecFee: '-',
                    periodServicePrice: '-',
                    periodServiceFee: couponPriceDiscounted,
                    periodTotalFee: '-',
                },
                {
                    timeFlag: '立减活动优惠',
                    periodPq: '-',
                    periodElecPrice: '-',
                    periodElecFee: '-',
                    periodServicePrice: '-',
                    periodServiceFee: activityDiscounted,
                    periodTotalFee: '-',
                },
                {
                    timeFlag: '优惠总金额',
                    periodPq: '-',
                    periodElecPrice: '-',
                    periodElecFee: electricityPriceDiscounted,
                    periodServicePrice: '-',
                    periodServiceFee: servicePriceDiscounted,
                    periodTotalFee: discountedPrice,
                },
                {
                    timeFlag: '应收',
                    periodPq: '-',
                    periodElecPrice: '-',
                    periodElecFee: elecReceipt,
                    periodServicePrice: '-',
                    periodServiceFee: serviceReceipt,
                    periodTotalFee: totalReceipt,
                }
            ]

            this.settlementModelId = elecChcNo
            this.serviceModelId = serviceChcNo
            this.couponNumber = couponCode
            this.activityNumber = activityId

            if (periodFeeBOList && periodFeeBOList.length > 0) {
                this.settlementData = periodFeeBOList.concat(list)
            }
            



            // 支付信息
            const {
                // payMode,
                planChargeAmt,
                // totalReceipt,
                realPrice,
                prepayRefundAmt,
                refundAmount,
            } = res.data

            this.payInfoData = [
                {
                    payMode,
                    planChargeAmt,
                    totalReceipt,
                    realPrice,
                    prepayRefundAmt,
                    refundAmount,
                }
            ]

            // 退款记录
            const {
                refundBOList,
            } = res.data

            this.refundData = refundBOList
            


            // 充电信息 

            const {
                chargeInfoAxis,
                powerSeries,
                voltageSeries,
                voltageSeriesNeed,
                elecSeries,
                elecSeriesNeed,
                pqSeries,
                socSeries,
            } = res.data
            
            this.chart1 = {
            data: [
                {
                name: '输出电压',
                data: voltageSeries,
                yAxisIndex: 1,
                },
                // {
                // name: '需求电压',
                // data: voltageSeriesNeed,
                // yAxisIndex: 1,
                // },
                {
                name: '输出电流',
                data: elecSeries,
                yAxisIndex: 2,
                },
                // {
                // name: '需求电流',
                // data: elecSeriesNeed,
                // yAxisIndex: 2,
                // },
                {
                name: '输出功率',
                data: powerSeries,
                yAxisIndex: 0,
                }
            ],
            xAxis: {
                data: chargeInfoAxis,
            },
            yAxis: {
    
            },
            unit: ['kW', 'V', 'A'],
            }

            this.chart2 = {
            data: [
                {
                name: '电池电量',
                data: socSeries,
                yAxisIndex: 0,
                },

                {
                name: '电量',
                data: pqSeries,
                yAxisIndex: 1,
                }
            ],
            xAxis: {
                data: chargeInfoAxis,
            },
            yAxis: {
    
            },
            unit: ['%', '度']
            }

         

            // 控制记录 
            const {
                controlLog
            } = res.data

            this.controlList = controlLog



            // 异常记录

            const {
                exceptionLog
            } = res.data

            this.abnormalData = exceptionLog






        }
    },
    }

  </script>
  
  <style lang="scss" scoped>
  .container-full {
    background-color: rgba(244, 246, 249, 1);
    padding-bottom: 16px;
    min-height: calc(100% - 80px);
    overflow-y: auto;
  }
  .device-head {
    background-color: #fff;
    
    display: flex;
    height: 108px;
    display: flex;
    align-items: center;
    padding: 0 24px;
    box-sizing: border-box;
    .device-head-icon {
        width: 48px;
        height: 48px;
        margin-right: 24px;
    }
    .device-info-wrap {
        flex: 1;
        .device-title-wrap {
            height: 32px;
            display: flex;
            align-items: center;
            .device-title {
                font-weight: 500;
                font-size: 24px;
                color: #12151A;
            }
        }
        .device-info-wrap {
            height: 16px;
            margin-top: 16px;
            font-size: 16px;
            font-weight: 400;
            color: #292B33;
        }
    }
    .device-status-wrap {
        display: flex;
        align-items: center;
        .device-status-item-wrap {
            width: 150px;
            .device-status-item-title {
                font-weight: 400;
                font-size: 14px;
                line-height: 14px;
                color: #505363;
                margin: 0 auto 12px auto;
                text-align: center;
            }
            .device-status {
                width: 86px;
                height: 34px;
                border-radius: 4px;
                display: flex;
                margin: 0 auto;
                align-items: center;
                justify-content: center;
                background-color: #EBF3FF;
                font-family: PingFang SC;
                font-weight: 500;
                font-size: 18px;
                color: #217AFF;
            }
            .device-status-success {
                width: 86px;
                height: 34px;
                border-radius: 4px;
                display: flex;
                margin: 0 auto;
                align-items: center;
                justify-content: center;
                background-color: #EBFFF1;
                font-family: PingFang SC;
                font-weight: 500;
                font-size: 18px;
                color: #00C864;

            }
        }
        .device-status-split{
            width: 1px;
            height: 36px;
            background-color: #E9EBF0;
        }
    }
    
  
  }
  .info-wrap {
    margin: 16px;
    border-radius: 5px;
    border: 1px solid #fff;
    overflow: hidden;
    background-color: #fff;
    .info-item {
        margin: 16px 16px 0 16px;
        border-radius: 5px;
        border: 1px solid #E9EBF0;
        .info-item-head {
            height: 56px;
            padding: 0 16px;
            display: flex;
            align-items: center;
            .info-item-before-icon {
                width: 3px;
                height: 16px;
                background-image: url('~@/assets/station/consno-before.png');
                background-size: 100% 100%;
                background-repeat: no-repeat;
                margin-right: 8px;
            }
            .info-item-head-text {
                flex:1;
                font-weight: 500;
                font-size: 16px;
                color: #12151A;

                &::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    bottom: 0;
                    left: -3px; /* 调整这个值来改变边框的宽度 */
                    width: 0;
                    border-top: 3px solid transparent;
                    border-bottom: 3px solid transparent;
                    border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
                }        
            }

        }
        .form-wrap {
            padding: 0 24px 24px 24px;
            .form-title-wrap {
                margin: 8px 0 16px 0;
                height: 32px;
                display: flex;
                align-items: center;
                .form-title-blue {
                    line-height: 32px;
                    border-radius: 2px;
                    padding: 0 10px;
                    background-color: #EBF3FF;
                    color:#217AFF;
                    font-size: 16px;
                    font-weight: 400;
                    margin-right: 16px;
                }
                .form-title {
                    line-height: 32px;
                    border-radius: 2px;
                    padding: 0 10px;
                    background-color: #F9F9FB;
                    color:#292B33;
                    font-size: 16px;
                    font-weight: 400;
                    margin-right: 16px;
                }
            }
        }
        
    }

    .chart-wrap {
        padding: 0 24px 24px 24px;
        display: flex;
        .chart-item-left-wrap {
            margin: 16px 8px 0 0;
            border-radius: 5px;
            border: 1px solid #E9EBF0;
            width: calc(50% - 8px);
            // height: 734px;
        }
        .chart-item-right-wrap {
            margin: 16px 0 0 8px;
            border-radius: 5px;
            border: 1px solid #E9EBF0;
            width: calc(50% - 8px);
            // height: 734px;
        }

        .info-item-head {
            height: 56px;
            padding: 0 16px;
            display: flex;
            align-items: center;
            .info-item-before-icon {
                width: 3px;
                height: 16px;
                background-image: url('~@/assets/station/consno-before.png');
                background-size: 100% 100%;
                background-repeat: no-repeat;
                margin-right: 8px;
            }
            .info-item-head-text {
                flex:1;
                font-weight: 500;
                font-size: 16px;
                color: #12151A;

                &::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    bottom: 0;
                    left: -3px; /* 调整这个值来改变边框的宽度 */
                    width: 0;
                    border-top: 3px solid transparent;
                    border-bottom: 3px solid transparent;
                    border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
                }        
            }

        }
    }
  }
   


  ::v-deep  .bd3001-content{
    padding: 0 !important;
  }
 
  .approval-steps {
  padding: 20px;
  background: #fff;
  border-radius: 4px;
}

:deep(.el-step__head) {
  padding-bottom: 10px;
}

:deep(.el-step__title) {
  line-height: 1.5;
  max-width: 600px;
}
::v-deep .el-step__icon {
  border: 0px;
}

.step-icon {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #EBF3FF;
    border-radius: 50%;
    color: #217AFF
    
}
.step-icon-01 {
    background-color: #EBF3FF;
    border-radius: 50%;
    color: #217AFF
}

.step-icon-02 {
    background-color: #EBF3FF;
    border-radius: 50%;
    color: #217AFF
}
.step-icon-03 {
    background-color: #FC1E31;
    border-radius: 50%;
    color: #fff;
}
.step-icon-04 {
    background-color: #217AFF;
    border-radius: 50%;
    color: #fff;
}
.step-icon-05 {
    background-color: #F3F6FC;
    border-radius: 50%;
    color: #818496;
}
.step-header {
  display: flex;
  align-items: center;
//   margin-bottom: 8px;
}

.title {
    margin-right: 12px;
    font-weight: 500;
    font-size: 16px;
    color: #12151A;
}

.status-tag {
  margin-right: 12px;
}

.time {
  color: #999;
  font-size: 12px;
  margin-left: auto;
}



.remark-box {
  margin-top: 8px;
  padding: 16px 12px 16px 16px;
  background: #F9F9FB;
  border-radius: 5px;
  display: flex;
  margin-bottom: 32px;
  margin-right: 12px;
    .remark-icon {
            width: 48px;
            height: 48px;
            background-image: url('~@/assets/order/order-control-icon.png');
            background-size: 100% 100%;
            background-repeat: no-repeat;
            margin-right: 16px;
    }
    .person-info {
        font-weight: 400;
        font-size: 16px;
        line-height: 20px;
        flex: 1;
        .person-title {
            color: #292B33;
            .identity {
                margin: 0 8px;
            }
        }
        .person-info-status {
            font-weight: 400;
            font-size: 16px;
            line-height: 20px;
            margin: 10px 0 0 0;
        }
        .person-info-status-01 {
            color: #00C864;
        }
        .person-info-status-02 {
            color: #00C864;
        }
        .person-info-status-03 {
            color: #FC1E31;
        }
        .person-info-status-04 {
            color: #217AFF;
        }

        .remark {
            font-weight: 400;
            font-size: 16px;
            line-height: 20px;
            color: #292B33;
            margin-top: 16px;
        }
    }

    .remark-time {
        font-weight: 400;
        font-size: 16px;
        line-height: 20px;
        color: #292B33;
        margin-top: 15px;
    }

}

.remark-label {
  color: #666;
  margin-right: 6px;
}

.remark-text {
  color: #999;
}

.abnormal-form-wrap {
    margin: 16px;
}
  </style>
  