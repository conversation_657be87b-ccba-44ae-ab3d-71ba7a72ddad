<template>
    <div class="container container-float " style="padding: 0 0 100px 0;">
        <div class="table-wrap">
            <BuseCrud
                ref="crud"
                :loading="loading"
                :filterOptions="filterOptions"
                :tablePage="tablePage"
                :tableColumn="tableColumn"
                :tableData="tableData"
                :pagerProps="pagerProps"
                :modalConfig="modalConfig"
                @loadData="loadData"
            >
            <template slot="defaultHeader">
                <div>
                    <div class="card-head">
                        <div class="card-head-text">异常订单列表</div>

                    </div>
                    

                    
                </div>
                
            </template>

                <template slot="operate" slot-scope="{ row }">
                    <!-- <div class="menu-box">
                        <el-button
                            class="button-border"
                            type="primary"
                            plain
                            @click="handleDetail(row)"
                        >
                            详情
                        </el-button>

                        <el-button
                            class="button-border"
                            type="primary"
                            plain
                            @click="handleHandle(row)"
                        >
                            处理
                        </el-button>

                        <el-button
                            class="button-border"
                            type="primary"
                            plain
                            @click="handleAudit(row)"
                        >
                            审核
                        </el-button>



                    
                    </div> -->
                

                    <el-dropdown trigger="click">
                      <el-button
                        class="button-border"
                        type="primary"
                        plain
                      >
                        操作
                      </el-button>
                      <el-dropdown-menu slot="dropdown">
                        <el-dropdown-item >
                          <div     @click="handleDetail(row)">
                            详情
                          </div>
                        </el-dropdown-item>

                        <el-dropdown-item >
                          <div    @click="handleHandle(row)">
                            处理
                          </div>
                        </el-dropdown-item>

                        <el-dropdown-item  >
                          <div  @click="handleAudit(row)">
                            审核
                          </div>
                        </el-dropdown-item>


                        

                      </el-dropdown-menu>
                    </el-dropdown>
                </template>

            </BuseCrud>
        </div>
    </div>
    
  </template>
  
  <script>

import {
  getStationList,
} from '@/api/pile/index';

import {
    getAbnormalOrderList,
} from '@/api/order/index'

import StatusDot from '@/components/Business/StatusDot';

  
    export default {
    components: {
        StatusDot
    },
    dicts: [
        'ls_charge_user_type',
        'ls_charge_order_status',
        'ls_order_except_type',
        'ls_order_except_level',
        'ls_except_order_transact_satus',
        'ls_except_order_review_status',
        'ls_order_apply_mode',
        'ls_pile_in_type',
        'ls_order_source',
        'ls_pile_source',
        'ls_review_result',
    ],
    data() {
        return {
            loading: false,
            tablePage: { total: 0, currentPage: 1, pageSize: 10 },

            stationList:[],
            tableColumn:[
                {
                    type: 'seq',
                    title: '序号',
                    width: 60,
                    minWidth: 60, // 最小宽度
                    fixed: 'left',

                },
                {
                    field: 'orderId',
                    title: '订单编号',
                    minWidth: 200, // 最小宽度
                },
                {
                    field: 'partOrderNo',
                    title: '第三方订单编号',
                    minWidth: 200, // 最小宽度
                    slots: {
                        default: ({ row }) => [
                            <el-tooltip 
                                content={row.partOrderNo} 
                                placement="top" 
                                disabled={!row.partOrderNo || row.partOrderNo.length < 15}
                            >
                                <span class="ellipsis-text">{row.partOrderNo}</span>
                            </el-tooltip>
                        ]
                    }
                },
                {
                    field: 'stationName',
                    title: '充电站',
                    minWidth: 150,
                
                },
                {
                    field: 'pileName',
                    title: '充电桩',
                    minWidth: 150,
                },
                {
                    field: 'applyMode',
                    title: '下单渠道',
                    minWidth: 130,
                    formatter: ({ cellValue }) => {
                        return this.selectDictLabel(
                            this.dict.type.ls_order_apply_mode,
                            cellValue
                        );
                    },
                },
                {
                    field: 'orderSource',
                    title: '订单来源',
                    minWidth: 130,
                    formatter: ({ cellValue }) => {
                        return this.selectDictLabel(
                            this.dict.type.ls_order_source,
                            cellValue
                        );
                    },
                },
                {
                    field: 'pileInType',
                    title: '桩接入方式',
                    minWidth: 130,
                    formatter: ({ cellValue }) => {
                        return this.selectDictLabel(
                            this.dict.type.ls_pile_in_type,
                            cellValue
                        );
                    },
                },
                {
                    field: 'pileSource',
                    title: '桩来源',
                    minWidth: 130,
                    formatter: ({ cellValue }) => {
                        return this.selectDictLabel(
                            this.dict.type.ls_pile_source,
                            cellValue
                        );
                    },
                },
                {
                    field: 'userType',
                    title: '用户类型',
                    minWidth: 100,
                    formatter: ({ cellValue }) => {
                        return this.selectDictLabel(
                            this.dict.type.ls_charge_user_type,
                            cellValue
                        );
                    },
                },
                {
                    field: 'mobile',
                    title: '用户手机号',
                    minWidth: 130,
                },
                {
                    field: 'enterpriseNo',
                    title: '所属企业编号',
                    minWidth: 150,
                },
                {
                    field: 'payMode',
                    title: '支付方式',
                    minWidth: 120,
                },
                {
                    field: 'applyTime',
                    title: '下单时间',
                    minWidth: 180,
                },
                {
                    field: 'bgnTime',
                    title: '修正前充电开始时间',
                    minWidth: 180,
                },
                {
                    field: 'bgnTimeAfterC',
                    title: '修正后充电开始时间',
                    minWidth: 180,
                },
                {
                    field: 'endTime',
                    title: '修正前充电结束时间',
                    minWidth: 180,
                },
                {
                    field: 'endTimeAfterC',
                    title: '修正后充电结束时间',
                    minWidth: 180,
                },
                {
                    field: 'reportTime',
                    title: '异常上报时间',
                    minWidth: 180,
                },
                {
                    field: 'chargeStatus',
                    title: '充电状态',
                    minWidth: 120,
                },
                {
                    field: 'orderStatus',
                    title: '订单状态',
                    minWidth: 120,
                    formatter: ({ cellValue }) => {
                        return this.selectDictLabel(
                            this.dict.type.ls_charge_order_status,
                            cellValue
                        );
                    },
                },
                {
                    field: 'exceptLevel',
                    title: '异常等级',
                    minWidth: 100,
                    formatter: ({ cellValue }) => {
                        return this.selectDictLabel(
                            this.dict.type.ls_order_except_level,
                            cellValue
                        );
                    },
                },
                {
                    field: 'exceptType',
                    title: '异常类型',
                    minWidth: 200,
                    formatter: ({ cellValue }) => {
                        return this.selectDictLabel(
                            this.dict.type.ls_order_except_type,
                            cellValue
                        );
                    },
                },
                {
                    field: 'exceptId',
                    title: '异常编号',
                    minWidth: 250,
                },
                {
                    field: 'exceptName',
                    title: '异常名称',
                    minWidth: 220,
                },
                {
                    field: 'exceptDescription',
                    title: '异常描述',
                    minWidth: 300,
                },
                {
                    field: 'elecChcNo',
                    title: '电费计费模型ID',
                    minWidth: 150,
                },
                {
                    field: 'serviceChcNo',
                    title: '服务费计费模型ID',
                    minWidth: 150,
                },
                {
                    field: 'chargePq',
                    title: '修正前充电电量(kwh)',
                    minWidth: 200,
                },
                {
                    field: 'chargePqAfterC',
                    title: '修正后充电电量(kwh)',
                    minWidth: 200,
                },
                {
                    field: 'tmr',
                    title: '修正前抄表电量(kwh)',
                    minWidth: 200,
                },
                {
                    field: 'tmrAfterC',
                    title: '修正后抄表电量(kwh)',
                    minWidth: 200,
                },
                {
                    field: 'elecAmt',
                    title: '修正前充电电费(元)',
                    minWidth: 200,
                },
                {
                    field: 'elecAmtAfterC',
                    title: '修正后充电电费(元)',
                    minWidth: 200,
                },
                {
                    field: 'serviceAmt',
                    title: '修正前充电服务费(元)',
                    minWidth: 200,
                },
                {
                    field: 'serviceAmtAfterC',
                    title: '修正后充电服务费(元)',
                    minWidth: 200,
                },
                {
                    field: 'chargeAmt',
                    title: '修正前充电总金额(元)',
                    minWidth: 200,
                },
                {
                    field: 'chargeAmtAfterC',
                    title: '修正后充电总金额(元)',
                    minWidth: 200,   
                },
                {
                    field: 'electricityPriceDiscounted',
                    title: '修正前优惠电费(元)',
                    minWidth: 200,
                },
                {
                    field: 'electricityPriceDiscountedAfterC',
                    title: '修正后优惠电费(元)',
                    minWidth: 200,
                },
                {
                    field: 'servicePriceDiscounted',
                    title: '修正前优惠服务费(元)',
                    minWidth: 200,
                },
                {
                    field: 'servicePriceDiscountedAfterC',
                    title: '修正后优惠服务费(元)',
                    minWidth: 200,
                },
                {
                    field: 'discountedPrice',
                    title: '修正前优惠金额(元)',
                    minWidth: 200,
                },
                {
                    field: 'discountedPriceAfterC',
                    title: '修正后优惠金额(元)',
                    minWidth: 200,
                },
                {
                    field: 'electricityPrice',
                    title: '修正前实扣电费(元)',
                    minWidth: 200,
                },
                {
                    field: 'electricityPriceAfterC',
                    title: '修正后实扣电费(元)',
                    minWidth: 200,
                },
                {
                    field: 'servicePrice',
                    title: '修正前实扣服务费(元)',
                    minWidth: 200,
                },
                {
                    field: 'servicePriceAfterC',
                    title: '修正后实扣服务费(元)',
                    minWidth: 200,
                },
                {
                    field: 'realPrice',
                    title: '修正前实扣金额(元)',
                    minWidth: 200,
                },
                {
                    field: 'realPriceAfterC',
                    title: '修正后实扣金额(元)',
                    minWidth: 200,
                },
                {
                    field: 'status',
                    title: '处理状态',
                    minWidth: 100,
                    fixed: 'right',
                    formatter: ({ cellValue }) => {
                        return this.selectDictLabel(
                            this.dict.type.ls_except_order_transact_satus,
                            cellValue
                        );
                    },
                },
                {
                    field: 'transactor',
                    title: '处理人',
                    minWidth: 120,
                },
                {
                    field: 'transactTime',
                    title: '处理时间',
                    minWidth: 180,
                },
                {
                    field: 'reviewStatus',
                    title: '审核状态',
                    minWidth: 100,
                    fixed: 'right',
                    // formatter: ({ cellValue }) => {
                    //     return this.selectDictLabel(
                    //         this.dict.type.ls_except_order_review_status,
                    //         cellValue
                    //     );
                    // },
                    slots: {
                    // 自定义render函数
                        default: ({ row }) => {
                            return (
                            <StatusDot
                                value={row.reviewStatus}
                                dictValue={this.dict.type.ls_review_result}
                                colors={[ 'success', 'danger',]}
                            ></StatusDot>
                            );
                        },
                    },
                },
                {
                    title: '操作',
                    slots: { default: 'operate' },
                    width: 80,
                    align: 'center',
                    fixed: 'right',
                },
                

            ],
            tableData: [],
            pagerProps: {
                layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
            },
            params: {
                stationId: '',
                pileId: '',
                orderId: '',
                partOrderNo: '',
                mobile: '',
                userType: '',
                enterpriseNo: '',
                orderTime: '',
                orderStatus: '',
                exceptType: '',
                exceptLevel: '',
                transactStatus: '',
                reportTime: '',
                processingTime: '',
                reviewSatus: '',
                chargingStartTime: '',
                chargingEndTime: '',
                chargingStartTimeAfterC: '',
                chargingEndTimeAfterC: '',
                applyMode: '',
                pileInType: '',
                orderSource: '',
                pileSource: '',
            },
        };
    },

    computed: {
        filterOptions() {
            return {
                config: [
                    {
                        field: 'stationId',
                        title: '充电站',
                        element: 'el-select',
                        props: {
                            options: this.stationList,
                            filterable: true,
                            remote: true,
                            remoteMethod: this.debouncedStationSearch,
                            loading: this.stationLoading,
                        }
                    },
                    {
                        field: 'pileId',
                        title: '充电桩',
                        element: 'el-select',
                        props: {
                            // todo 新增充电桩接口
                            
                        }
                    },
                    {
                        field: 'orderId',
                        title: '订单编号',
                        element: 'el-input',
                    },
                    {
                        field: 'partOrderNo',
                        title: '第三方订单编号',
                        element: 'el-input',
                        props: {
                            placeholder: '请输入'
                        }
                    },
                    {
                        field: 'mobile',
                        title: '用户手机号',
                        element: 'el-input',
                    },
                    {
                        field: 'userType',
                        title: '用户类型',
                        element: 'el-select',
                        props: {
                            options: this.dict.type.ls_charge_user_type
                        }
                    },
                    {
                        field: 'enterpriseNo',
                        title: '企业编号',
                        element: 'el-input',
                    },
                    {
                        field: 'orderTime',
                        title: '下单时间',
                        element: 'el-date-picker',
                        props: {
                            type: 'datetimerange',
                            rangeSeparator: "至",
                            startPlaceholder: "开始日期",
                            endPlaceholder: "结束日期",
                            valueFormat: 'YYYY-MM-DD HH:mm:ss'
                        }
                    },
                    {
                        field: 'orderStatus',
                        title: '订单状态',
                        element: 'el-select',
                        props: {
                            options: this.dict.type.ls_charge_order_status
                        }
                    },
                    {
                        field: 'exceptType',
                        title: '异常类型',
                        element: 'el-select',
                        props: {
                            options: this.dict.type.ls_order_except_type
                        }
                    },
                    {
                        field: 'exceptLevel',
                        title: '异常等级',
                        element: 'el-select',
                        props: {
                            options: this.dict.type.ls_order_except_level
                        }
                    },
                    {
                        field: 'transactStatus',
                        title: '处理状态',
                        element: 'el-select',
                        props: {
                            placeholder: '请选择',
                            options: this.dict.type.ls_except_order_transact_satus
                        }
                    },
                    {
                        field: 'reportTime',
                        title: '上报时间',
                        element: 'el-date-picker',
                        props: {
                            type: 'daterange',
                            options: [],
                            valueFormat: 'yyyy-MM-dd',
                        }
                    },
                    {
                        field: 'processingTime',
                        title: '处理时间',
                        element: 'el-date-picker',
                        props: {
                            type: 'daterange',
                            options: [],
                            valueFormat: 'yyyy-MM-dd',
                        }
                    },
                    {
                        field: 'reviewSatus',
                        title: '审核状态',
                        element: 'el-select',
                        props: {
                            placeholder: '请选择',
                            options: this.dict.type.ls_except_order_review_status,
                        }
                    },
                    {
                        field: 'chargingStartTime',
                        title: '充电开始时间',
                        element: 'el-date-picker',
                        props: {
                            type: 'daterange',
                            options: [],
                            valueFormat: 'yyyy-MM-dd',
                        }
                    },
                    {
                        field: 'chargingEndTime',
                        title: '充电结束时间',
                        element: 'el-date-picker',
                        props: {
                            type: 'daterange',
                            options: [],
                            valueFormat: 'yyyy-MM-dd',
                        }
                    },
                    {
                        field: 'chargingStartTimeAfterC',
                        title: '充电开始时间(修正后)',
                        element: 'el-date-picker',
                        props: {
                            type: 'daterange',
                            options: [],
                            valueFormat: 'yyyy-MM-dd',
                        }
                    },
                    {
                        field: 'chargingEndTimeAfterC',
                        title: '充电结束时间(修正后)',
                        element: 'el-date-picker',
                        props: {
                            type: 'daterange',
                            options: [],
                            valueFormat: 'yyyy-MM-dd',
                        }
                    },
                    {
                        field: 'applyMode',
                        title: '下单渠道',
                        element: 'el-select',
                        props: {
                            placeholder: '请选择',
                            options: this.dict.type.ls_order_apply_mode,
                        }
                    },
                    {
                        field: 'pileInType',
                        title: '桩接入方式',
                        element: 'el-select',
                        props: {
                            placeholder: '请选择',
                            options: this.dict.type.ls_pile_in_type,
                        }
                    },
                    {
                        field: 'orderSource',
                        title: '订单来源',
                        element: 'el-select',
                        props: {
                            placeholder: '请选择',
                            options: this.dict.type.ls_order_source,
                        }
                    },
                    {
                        field: 'pileSource',
                        title: '桩来源',
                        element: 'el-select',
                        props: {
                            placeholder: '请选择',
                             options: this.dict.type.ls_pile_source,
                        }
                    }



                ],
                
                params: this.params,
            }
        },
        modalConfig() {
            return {
                addBtn: false,
                viewBtn: false,
                menu: false,
                editBtn: false,
                delBtn: false,
            }
        },
    },
    mounted() {
        this.loadData();

        console.log( this.dict.type.ls_review_result,' this.dict.type.ls_review_result,')
    },
    methods: {
        async loadData() {
            const {
                stationId,
                pileId,
                orderId,
                partOrderNo,
                mobile,
                userType,
                enterpriseNo,
                orderTime,
                orderStatus,
                exceptType,
                exceptLevel,
                transactStatus,
                reportTime,
                processingTime,
                reviewSatus,
                chargingStartTime,
                chargingEndTime,
                chargingStartTimeAfterC,
                chargingEndTimeAfterC,
                applyMode,
                pileInType,
                orderSource,
                pileSource,
            } = this.params;
            let applyTimeStart = '';
            let applyTimeEnd = '';
            if(orderTime && orderTime.length > 0) {
                applyTimeStart = orderTime[0];
                applyTimeEnd = orderTime[1];
            }

            let reportTimeStart = '';
            let reportTimeEnd = '';
            if(reportTime && reportTime.length > 0) {
                reportTimeStart = reportTime[0];
                reportTimeEnd = reportTime[1];
            }

            let transactTimeStart = '';
            let transactTimeEnd = '';
            if(processingTime && processingTime.length > 0) {
                transactTimeStart = processingTime[0];
                transactTimeEnd = processingTime[1];
            }

            let bgnTimeStart = '';
            let bgnTimeEnd = '';
            if(chargingStartTime &&chargingStartTime.length > 0) {
                bgnTimeStart = chargingStartTime[0];
                bgnTimeEnd = chargingStartTime[1];
            }

            let endTimeStart = '';
            let endTimeEnd = '';
            if(chargingEndTime && chargingEndTime.length > 0) {
                endTimeStart = chargingEndTime[0];
                endTimeEnd = chargingEndTime[1];
            }

            let bgnTimeAfterCStart = '';
            let bgnTimeAfterCEnd = '';
            if(chargingStartTimeAfterC && chargingStartTimeAfterC.length > 0) {
                bgnTimeAfterCStart = chargingStartTimeAfterC[0];
                bgnTimeAfterCEnd = chargingStartTimeAfterC[1];
            }

            let endTimeAfterCStart = '';
            let endTimeAfterCEnd = '';
            if(chargingEndTimeAfterC && chargingEndTimeAfterC.length > 0) {
                endTimeAfterCStart = chargingEndTimeAfterC[0];
                endTimeAfterCEnd = chargingEndTimeAfterC[1];
            }


            const params = {
                stationId,
                pileId,
                orderId,
                partOrderNo,
                mobile,
                userType,
                enterpriseNo,
                orderStatus,
                exceptType,
                exceptLevel,
                transactStatus,
                reviewSatus,
                applyMode,
                pileInType,
                orderSource,
                pileSource,
                applyTimeStart,
                applyTimeEnd,
                reportTimeStart,
                reportTimeEnd,
                transactTimeStart,
                transactTimeEnd,
                bgnTimeStart,
                bgnTimeEnd,
                endTimeStart,
                endTimeEnd,
                bgnTimeAfterCStart,
                bgnTimeAfterCEnd,
                endTimeAfterCStart,
                endTimeAfterCEnd,
                pageNum: this.tablePage.currentPage,
                pageSize: this.tablePage.pageSize,
            }

            this.loading = true;
            const [err, res] = await getAbnormalOrderList(params);

            this.loading = false;

            if (err) return 

            const { data, total } = res;

            this.tableData = data;
            this.tablePage.total = total;

        },
        async debouncedStationSearch(query) {
        if (query !== '') {
            this.stationLoading = true;
            setTimeout(async() => {
                const [err, res] = await getStationList(
                {
                    stationName: query,
                }
                );

                if (err) return;
                this.stationLoading = false;
                this.stationList = res.data.map((item) => ({
                label: item.stationName,
                value: item.stationId,
                }));
            }, 200);
            
            } else {
            this.stationList = []
        }
        
        },
         // 订单详情
         handleDetail(row) {
            const {
                orderId
            } = row;
            this.$router.push({
                path: '/v2g-charging-web/operatorManage/orderManage/orderManage/detail',
                query: {
                    orderNo: orderId
                }
            })
        },

        // 处理订单
        handleHandle(row) {
            const {
                orderId
            } = row;
            this.$router.push({
                path: '/v2g-charging-web/operatorManage/orderManage/abnormalOrder/handle',
                query: {
                    orderNo: orderId,
                }
            })
        },

        // 异常订单审核
        handleAudit(row) {
            const {
                orderId
            } = row;
            this.$router.push({
                path: '/v2g-charging-web/operatorManage/orderManage/abnormalOrder/audit',
                query: {
                    orderNo: orderId,
                }
            })
        }
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .container-full {
    background-color: rgba(244, 246, 249, 1);
    padding-bottom: 16px;
    min-height: calc(100% - 80px);
    overflow-y: auto;
  }

  .table-wrap {
    ::v-deep .bd3001-table-select-box {
        display: none;
    }
    ::v-deep .bd3001-header  {
        display: block;
    }
    ::v-deep .bd3001-button {
        display: block !important;
    }
    
    .card-head {
        // position: relative; 
        height: 56px;
        padding: 0 16px;
        display: flex;
        align-items: center;
        margin-top: -20px;
        .card-head-text {
        flex:1;
        width: 520px;
            height: 26px;
            background-image: url('~@/assets/images/bg-title.png');
            background-size: 520px 26px;
            background-repeat: no-repeat;
            font-size: 20px;
            font-style: normal;
            font-weight: 500;
            line-height: 16px;
            padding-left: 36px;
            color: #21252e;
        &::before {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            left: -3px; /* 调整这个值来改变边框的宽度 */
            width: 0;
            border-top: 3px solid transparent;
            border-bottom: 3px solid transparent;
            border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
        }
        }   

    }

    .card-head-after {
        width: 100%;
        height: 1px;
        background-color: #DCDEE2;
        margin-bottom: 16px;
    }
    .info-wrap {
        margin-top: 16px;
        display: flex;
        flex-wrap: wrap;
        gap: 16px;

        .info-item {
            background-color: #FAFBFC;
            flex: 1 1 0;
            // min-width: 180px;
            
            border-radius: 5px;
            padding: 8px 24px;
            box-sizing: border-box;
            // margin-right: 16px;
            display: flex;
            .info-icon {
                width: 42px;
                height: 42px;
            }
            .info-right-wrap {
                flex:1;
                margin-left: 8px;
                .info-title {
                    font-weight: 400;
                    font-size: 14px;
                    line-height: 14px;
                    margin-bottom: 8px;
                }
                .info-number {
                    font-size: 20px;
                    font-weight: 500;
                    .info-unit {
                        font-size: 14px;
                        font-weight: 400;
                    }
                }
            }
        }
        .info-item:last-child {
            margin-right: 0;
        }
    }

    .top-button-wrap {
        display:flex;
        margin: 16px 0;
    }
}


.button-border {
    border: 0.01rem solid #217AFF;
    color: #217AFF;
    background-color: #fff;
}

.ellipsis-text {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }
 
  </style>
  