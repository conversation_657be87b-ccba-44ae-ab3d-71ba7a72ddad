<template>
  <div class="container">
    <div class="table-wrap">
      <BuseCrud
        ref="crud"
        :loading="loading"
        :filterOptions="filterOptions"
        :tablePage="tablePage"
        :tableColumn="tableColumn"
        :tableData="tableData"
        :pagerProps="pagerProps"
        :modalConfig="modalConfig"
        @loadData="loadData"
      >
        <template slot="defaultHeader">
          <div class="card-head">
            <div class="card-head-text">场站SOC配置</div>
            <div class="top-button-wrap">
              <el-button
                type="primary"
                icon="el-icon-upload2"
                @click="handleInput"
              >
                导入
              </el-button>
              <el-button
                type="primary"
                icon="el-icon-plus"
                @click="handleAdd('01')"
              >
                新增
              </el-button>
            </div>
          </div>
          <div class="card-head-after"></div>
        </template>
        <template slot="stationSoc" slot-scope="{ row }">
          <div>{{ row.stationSoc }}%</div>
        </template>
        <template slot="validStartTime" slot-scope="{ row }">
          <div>{{ row.validStartTime }}-{{ row.validEndTime }}</div>
        </template>
        <template slot="operate" slot-scope="{ row }">
          <div class="menu-box">
            <el-button type="primary" plain @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button type="primary" plain>停用</el-button>
            <el-button type="primary" plain>启用</el-button>
            <el-button type="danger" plain @click="handleDelete(row)">
              删除
            </el-button>
          </div>
        </template>
      </BuseCrud>
    </div>
  </div>
</template>

<script>
import { getStationSOCList, getStationList } from '@/api/stationSOC/index.js';

export default {
  dicts: [
    'ls_charging_battery_type', // 电池类型
    'ls_charging_soc_config_status', // 状态
  ],
  data() {
    return {
      loading: false,
      tablePage: { total: 0, currentPage: 1, pageSize: 10 },
      tableData: [],
      tableColumn: [
        {
          type: 'seq',
          title: '序号',
          width: 60,
        },
        {
          field: 'stationName',
          title: '场站名称',
          width: 200,
        },
        {
          // field: 'stationSoc',
          title: '场站SOC',
          slots: { default: 'stationSoc' },
          width: 120,
        },
        {
          field: 'batterySocSummary',
          title: '电池SOC',
          width: 300,
        },
        {
          title: '有效时间',
          slots: { default: 'validStartTime' },
          width: 300,
        },
        {
          field: 'statusLabel',
          title: '生效状态',
          width: 120,
          //   formatter: ({ cellValue }) => {
          //     return this.selectDictLabel(
          //       this.dict.type.ls_charging_soc_config_status,
          //       cellValue
          //     );
          //   },
        },
        {
          field: 'createdBy',
          title: '创建人',
          width: 120,
        },
        {
          title: '操作',
          slots: { default: 'operate' },
          width: 300,
          align: 'center',
          fixed: 'right',
        },
      ],
      pagerProps: {
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes'],
      },
      params: {
        stationId: '',
        batteryType: '',
        status: '',
      },
      stationList: [],
      stationLoading: false,
    };
  },
  computed: {
    filterOptions() {
      return {
        config: [
          {
            field: 'stationId',
            title: '场站名称',
            element: 'el-select',
            props: {
              options: this.stationList,
              filterable: true,
              remote: true,
              remoteMethod: this.debouncedStationSearch,
              loading: this.stationLoading,
            },
          },
          {
            field: 'batteryType',
            title: '电池类型',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.dict.type.ls_charging_battery_type,
            },
          },
          {
            field: 'status',
            title: '状态',
            element: 'el-select',
            props: {
              placeholder: '请选择',
              options: this.dict.type.ls_charging_soc_config_status,
            },
          },
        ],
        params: this.params,
      };
    },
    modalConfig() {
      return {
        addBtn: false,
        viewBtn: false,
        menu: false,
        editBtn: false,
        delBtn: false,
      };
    },
  },
  mounted() {
    this.loadData();
  },
  methods: {
    // 场站名称
    async debouncedStationSearch(query) {
      console.log(query, 'query');
      if (query !== '') {
        this.stationLoading = true;
        setTimeout(async () => {
          const [err, res] = await getStationList({
            stationName: query,
          });

          if (err) return;
          this.stationLoading = false;
          this.stationList = res.data.map((item) => ({
            label: item.stationName,
            value: item.stationId,
          }));
        }, 200);
      } else {
        this.stationList = [];
      }
    },
    async loadData() {
      this.loading = true;
      let params = {
        pageNum: this.tablePage.currentPage,
        pageSize: this.tablePage.pageSize,
        stationId: this.params.stationId,
        batteryType: this.params.batteryType,
        status: this.params.status,
      };
      const [err, res] = await getStationSOCList(params);
      this.loading = false;
      if (err) {
        return;
      }
      const { data, total } = res;
      this.tableData = data;
      this.tablePage.total = total;
    },
    // 导入
    handleInput() {},
    // 新增
    handleAdd(status) {
      this.$router.push({
        path: '/v2g-charging-web/intelligence/stationSOC/apply',
        query: {
          status,
        },
      });
    },
    // 编辑
    handleEdit(val) {},
    // 删除
    handleDelete(val) {},
  },
};
</script>

<style lang="scss" scoped>
.table-wrap {
  background: #fff;
  padding: 20px;
  border-radius: 4px;

  ::v-deep .bd3001-table-select-box {
    display: none;
  }
  ::v-deep .bd3001-header {
    display: block;
  }
  ::v-deep .bd3001-button {
    display: block !important;
  }

  .card-head {
    height: 56px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .card-head-text {
      flex: 1;
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 520px 26px;
      background-repeat: no-repeat;
      font-size: 20px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
      &::before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: -3px; /* 调整这个值来改变边框的宽度 */
        width: 0;
        border-top: 3px solid transparent;
        border-bottom: 3px solid transparent;
        border-left: 3px solid rgba(33, 122, 255, 1); /* 设置边框颜色 */
      }
    }
  }

  .card-head-after {
    width: 100%;
    height: 1px;
    background-color: #dcdee2;
    margin-bottom: 16px;
  }
}
</style>
