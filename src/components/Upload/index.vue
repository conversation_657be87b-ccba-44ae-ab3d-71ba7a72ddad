<template>
  <div class="image-upload-wrapper">
    <el-upload
      ref="elUpload"
      :action="uploadUrl"
      :headers="headers"
      :file-list="innerFileList"
      :limit="limit"
      :accept="accept"
      :multiple="multiple"
      :on-exceed="handleExceed"
      :before-upload="beforeUpload"
      :on-success="handleSuccess"
      :on-error="handleError"
      :on-remove="handleRemove"
      list-type="picture-card"
      :class="{ 'upload-disabled': disabled }"
      :disabled="disabled"
    >
      <i class="el-icon-plus"></i>

      <div slot="tip" class="upload-tip" v-if="showTip">
        支持扩展名：{{ acceptTips }}，单个文件不超过 {{ maxSizeMB }}MB
      </div>

      <!-- 自定义文件列表 -->
      <div slot="file" slot-scope="{ file }" class="preview-item">
        <img
          v-if="isImage(file)"
          class="preview-image"
          :src="file.url"
          alt=""
        />

        <!-- 文档预览 -->
        <div v-else class="document-preview">
          <div class="document-icon">
            <i :class="fileIcon(file)"></i>
            <span class="file-name">{{ getFileName(file) }}</span>
          </div>
        </div>

        <div class="file-actions">
          <span
            v-if="isImage(file)"
            style="margin-right: 10px"
            class="file-delete"
            @click.stop="handlePreview(file)"
          >
            <i class="el-icon-zoom-in"></i>
          </span>
          <span
            class="file-delete"
            v-if="!disabled"
            @click.stop="handleRemove(file)"
          >
            <i class="el-icon-delete"></i>
          </span>
        </div>
      </div>
    </el-upload>

    <!-- 预览对话框 -->
    <el-dialog
      :visible.sync="previewVisible"
      title="图片预览"
      width="1200px"
      append-to-body
    >
      <img :src="previewUrl" style="width: 100%" />
    </el-dialog>
  </div>
</template>

<script>
import { getToken } from '@/utils/auth';

export default {
  name: 'ImageUpload',
  props: {
    // 上传地址（必填）
    uploadUrl: {
      type: String,
      default: '/vehicle-grid-system/upload/single',
    },
    // 当前文件列表（v-model）
    value: {
      type: Array,
      default: () => [],
    },
    // 请求头
    headers: {
      type: Object,
      default: () => ({
        Authorization: 'Bearer ' + getToken(),
      }),
    },
    // 最大文件大小（MB）
    maxSizeMB: {
      type: Number,
      default: 5,
    },
    // 允许的文件类型
    accept: {
      type: String,
      default: 'image/jpeg,image/png,image/jpg',
    },
    // 最大上传数量
    limit: {
      type: Number,
      default: 1,
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false,
    },
    // 是否显示提示文字
    showTip: {
      type: Boolean,
      default: true,
    },
  },

  data() {
    return {
      previewVisible: false,
      previewUrl: '',
      innerFileList: [],
      uploading: false,
      fullscreenLoading: null,
    };
  },

  computed: {
    // 显示友好的文件类型提示
    acceptTips() {
      // return this.accept.split(',')
      //   .map(type => type.split('/')[1].toUpperCase())
      //   .join(' / ')

      const typeMap = {
        'image/*': '图片',
        'image/jpeg': 'JPEG',
        'image/png': 'PNG',
        'image/jpg': 'JPG',
        'application/pdf': 'PDF',
        'application/msword': 'DOC',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
          'DOCX',
        'application/rar': 'RAR',
        'application/zip': 'ZIP',
      };

      return this.accept
        .split(',')
        .map((type) => typeMap[type] || type.split('/')[1].toUpperCase())
        .join(' / ');
    },

    // 是否支持多选
    multiple() {
      return this.limit > 1;
    },
  },

  watch: {
    value: {
      immediate: true,
      handler(newVal) {
        this.innerFileList = this.formatFileList(newVal);
        console.log(this.innerFileList, 'formatFileList');
      },
    },
  },

  beforeDestroy() {
    // 清理本地预览URL
    this.innerFileList.forEach((file) => {
      if (file.uid && file.url && !file.response) {
        URL.revokeObjectURL(file.url);
      }
    });
  },

  methods: {
    // 格式化文件列表
    formatFileList(fileList) {
      return fileList.map((item) => {
        if (typeof item === 'string') {
          return { url: item };
        }
        return item;
      });
    },

    // 文件上传前校验
    beforeUpload(file) {
      // const isAccept = this.accept.split(',').includes(file.type)
      const allowedTypes = this.accept.split(',');
      const isAccept = allowedTypes.some((type) => {
        if (type === 'image/*') return file.type.startsWith('image/');
        return file.type === type;
      });
      const isOverSize = file.size / 1024 / 1024 > this.maxSizeMB;

      if (!isAccept) {
        this.$message.error(`只能上传 ${this.acceptTips} 格式文件!`);
        return false;
      }

      if (isOverSize) {
        this.$message.error(`文件大小不能超过 ${this.maxSizeMB}MB!`);
        return false;
      }

      // 显示 loading
      this.showLoading();

      // 生成预览图
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        this.$set(file, 'url', reader.result);
      };

      return true;
    },

    // 处理上传成功
    handleSuccess(response, file) {
      this.hideLoading();
      console.log(response, file);
      if (response.code === '10000') {
        const fileData = {
          name: file.name,
          url: response.data.absolutePath,
          uid: file.uid,
        };
        this.$emit('input', [...this.value, fileData]);
        this.$emit('success', fileData);
      } else {
        this.$message.error(response.message || '上传失败');
      }
    },

    // 处理上传失败
    handleError(err) {
      this.hideLoading();
      console.error('上传失败:', err);
      this.$message.error('文件上传失败');
      this.$emit('error', err);
    },

    // 删除文件
    handleRemove(file) {
      console.log('删除文件:', file);
      const newList = this.innerFileList.filter((f) => f.uid !== file.uid);
      this.$emit('input', newList);
      this.$emit('remove', file);

      // 清理本地预览URL
      if (file.url && !file.response) {
        URL.revokeObjectURL(file.url);
      }
    },

    // 处理文件超出限制
    handleExceed() {
      this.$message.warning(`最多只能上传 ${this.limit} 个文件`);
    },

    // 预览图片
    handlePreview(file) {
      // if (this.disabled) return
      this.previewUrl = file.url || URL.createObjectURL(file.raw);
      this.previewVisible = true;
    },

    // 手动清空文件列表
    clearFiles() {
      this.$refs.elUpload.clearFiles();
      this.$emit('input', []);
    },

    // 文件类型判断方法
    isImage(file) {
      return (
        (file.raw && file.raw.type?.startsWith('image/')) ||
        ['jpg', 'jpeg', 'png', 'gif'].some((ext) =>
          file.url.split('?Expires=')[0]?.endsWith(ext)
        )
      );
    },

    // 获取文件图标
    fileIcon(file) {
      const typeMap = {
        pdf: 'el-icon-document',
        doc: 'el-icon-document',
        docx: 'el-icon-document',
        default: 'el-icon-document',
      };

      const extension =
        file.name?.split('.').pop().toLowerCase() ||
        file.url?.split('.').pop().toLowerCase();

      return typeMap[extension] || typeMap.default;
    },

    // 获取文件名
    getFileName(file) {
      return file.name || file.url?.split('/').pop();
    },

    showLoading() {
      this.uploading = true;
      this.fullscreenLoading = this.$loading({
        lock: true,
        text: '文件上传中，请稍候...',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.3)',
      });
    },

    hideLoading() {
      this.uploading = false;
      if (this.fullscreenLoading) {
        this.fullscreenLoading.close();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.image-upload-wrapper {
  ::v-deep {
    .el-upload-list--picture-card {
      .el-upload-list__item {
        width: 120px;
        height: 120px;
        margin: 0 8px 8px 0;
        transition: all 0.3s;

        &:hover {
          transform: scale(1.05);
          box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }

        .preview-image {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }

        .file-actions {
          position: absolute;
          width: 100%;
          height: 100%;
          left: 0;
          top: 0;
          background: rgba(0, 0, 0, 0.5);
          opacity: 0;
          transition: opacity 0.3s;
          display: flex;
          align-items: center;
          justify-content: center;

          .file-delete {
            color: #fff;
            font-size: 18px;
            cursor: pointer;

            &:hover {
              color: #409eff;
            }
          }
        }

        &:hover .file-actions {
          opacity: 1;
        }
      }
    }

    .el-upload--picture-card {
      width: 120px;
      height: 120px;
      line-height: 120px;
      border: 1px dashed #d9d9d9;
      background-color: #fafafa;

      &:hover {
        border-color: #409eff;
      }

      i {
        font-size: 24px;
        color: #666;
      }
    }
  }

  .upload-tip {
    margin-top: 8px;
    font-size: 12px;
    color: #909399;
  }
  // .preview-content {
  //   display: flex;
  //   align-items: center;
  //   justify-content: center;
  // }

  .full-preview-image {
    max-width: 100%;
    max-height: 70vh;
    display: block;
    margin: 0 auto;
  }

  .upload-disabled {
    ::v-deep .el-upload--picture-card {
      cursor: not-allowed;
      background-color: #f5f7fa;
      border-color: #e4e7ed;

      &:hover {
        border-color: #e4e7ed;
      }
    }
  }
}

.document-preview {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 10px;
  // background: #f5f7fa;

  .document-icon {
    text-align: center;
    cursor: pointer;

    i {
      font-size: 48px;
      color: #409eff;
    }

    .file-name {
      display: block;
      width: 100px;
      margin-top: 8px;
      word-break: break-all;
      font-size: 12px;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2; // 控制最多显示两行
      overflow: hidden;
      white-space: normal;
      text-overflow: ellipsis;
    }
  }
}

.document-preview-dialog {
  text-align: center;
  padding: 40px 0;

  .big-icon {
    font-size: 100px !important;
    color: #909399;
    margin-bottom: 20px;
  }

  .document-tip {
    color: #606266;
    margin: 20px 0;
  }
}
</style>
