<template>
    <div v-loading="loading">
      <div ref="chart" :id="id" :style="`height: ${height}`"></div>
    </div>
  </template>
  
  <script>
  import * as echarts from 'echarts/core';
  import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON> } from 'echarts/charts';
  import { CanvasRenderer } from 'echarts/renderers';
  import {
    GridComponent,
    TooltipComponent,
    LegendComponent,
    DataZoomComponent,
    ToolboxComponent,
    GraphicComponent,
  } from 'echarts/components';
  // import moment from 'moment';
  import { throttle } from 'lodash';
  
  echarts.use([
  <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>hart,
    CanvasRenderer,
    GridComponent,
    TooltipComponent,
    LegendComponent,
    DataZoomComponent,
    ToolboxComponent,
    GraphicComponent,
  ]);
  
  const defaultXAxis = {
    type: 'category',
    boundaryGap: true,
    triggerEvent: true,
    axisLine: {
      lineStyle: {
        color: '#E9EBF0',
      },
    },
    axisLabel: {
      color: '#292B33',
    },
    axisTick: {
      show: false,
    },
    splitLine: {
      show: false,
    },
  };
  
  export default {
    name: 'OverPieChart', // 响应率超出100%的环形图
    props: {
      id: String,
      height: {
        type: String,
        default: '200px',
      },
      data: {
        type: Array,
        default: () => [],
      },
      loading: {
        type: Boolean,
        default: false,
      },
      notMerge: {
        type: Boolean,
        default: false,
      },
      unit: {
        type: String,
        default: '%',
      },
      centText: {
        type: String,
        default: '响应完成率',
      },
      radius: {
        type: Array,
        default: () => ['60%', '70%'],
      },
      seriesCenter: {
        type: Array,
        default: () => ['50%', '50%'],
      }
    },
    data() {
      return {
        myChart: null,
        resizeFn: null,
      };
    },
    watch: {
      data: {
        handler(n) {
          if (!n || !n.length) {
            // 这里为了解决echarts图表series从有值变为空，但是图表不会更新的问题
            this.myChart &&
              this.myChart.setOption(
                {
                  series: [],
                },
                true
              );
            this.myChart &&
              this.myChart.showLoading({
                text: '暂无数据',
                showSpinner: false,
                textColor: 'rgba(0, 0, 0, 0.45)',
                maskColor: 'rgba(255, 255, 255, 1)',
                fontSize: '16px',
                fontWeight: '500',
              });
            return;
          }
  
          this.myChart && this.myChart.hideLoading();
          this.myChart &&
            this.myChart.setOption(this.getOption(n), this.unit);
        },
        deep: true,
      },
    },
    methods: {
      init() {
        this.$nextTick(() => {
          let dom = this.$refs.chart;
          if (dom) {
            this.myChart = echarts.init(dom);
            const options = this.getOption(this.data,this.unit);
            if (options) {
              this.myChart.setOption(options);
            }
            // 监听鼠标移入移出事件
            this.myChart.on('showTip', (params) => {
              this.$emit('showTip', params);
            });
            this.myChart.on('hideTip', () => {
              this.$emit('hideTip');
            });
          }
        });
      },
      getOption(data = [],unit) {
        console.log( this._props,' getOption')

        const seriesCenter = this._props.seriesCenter;
         // 计算总数
         var total = data.reduce((sum, item) => sum + item.value, 0);

         // 赋值
         let afterData = []
         if (Number(data[0].value) >= 100) {
            afterData = [
                { value: 100, name: data[0].name, itemStyle:data[0].itemStyle }, // 环形图显示 100%
                // { value: 0, name: '', itemStyle: { color: '#F4F6F9' } }, // 透明部分
            ]
         } else {
            afterData = [
                { value: Number(data[0].value), name: data[0].name, itemStyle:data[0].itemStyle }, // 环形图显示 100%
                { value: 100 - Number(data[0].value), name: '', itemStyle: { color: '#F4F6F9' } }, // 透明部分
            ]
         }

        return {
          tooltip: {
            show: false, // 禁用提示框
            //  trigger: 'item',
          },
        legend: {
                show: false,
            },
          series:[
            {
                type: 'pie',
                emphasis: {
                  disabled: true, // 禁用饼图的悬停效果
                },
                center: seriesCenter, 
                radius: this.radius,
                avoidLabelOverlap: false,
                itemStyle: {
                    borderRadius: 5,
                    borderColor: '#fff',
                    borderWidth: 1,
                    label: {
                        show: false,
                        position: 'center'
                    },
                },
                labelLine: {
                    show: false
                },
                label: {
                        // show: false,
                        // 显示在中心
                        position: 'center', 
                        // 自定义标签内容
                        formatter: `${this.centText}\n ${total}${unit} ` ,
                        // 标签样式
                        textStyle:{
                            color: '#333',
                            fontSize: 16,
                            fontWeight: 'bold',
                            lineHeight: 20,
                            textAlign: 'center'
                        }
                },
                data: afterData

            }
          ]

        };
      },
      showLoading() {
        this.myChart && this.myChart.showLoading();
      },
      resize() {
        this.myChart && this.myChart.resize && this.myChart.resize();
      },
    },
    mounted() {
      this.resizeFn = throttle(() => {
        this.resize();
      }, 1000);
      // 监听窗口变化
      window.addEventListener('resize', this.resizeFn);
      this.init();
    },
    beforeDestroy() {
      if (this.myChart) {
        this.myChart.dispose();
        this.myChart = null;
      }
      window.removeEventListener('resize', this.resizeFn);
    },
  };
  </script>
  
  <style lang="less" scoped></style>
  