<template>
  <div v-loading="loading">
    <div ref="chart" :id="id" :style="`height: ${height}`"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts/core';
import { Line<PERSON>hart, Bar<PERSON>hart } from 'echarts/charts';
import { CanvasRenderer } from 'echarts/renderers';
import {
  GridComponent,
  TooltipComponent,
  LegendComponent,
  DataZoomComponent,
  ToolboxComponent,
  GraphicComponent,
} from 'echarts/components';
// import moment from 'moment';
import { throttle } from 'lodash';

echarts.use([
  <PERSON><PERSON>hart,
  Bar<PERSON>hart,
  CanvasRenderer,
  GridComponent,
  TooltipComponent,
  LegendComponent,
  DataZoomComponent,
  ToolboxComponent,
  GraphicComponent,
]);

const defaultXAxis = {
  // type: 'time',
  // min: moment().subtract(24, 'hour').startOf('hour').valueOf(), // 设置前24小时
  // max: moment().add(1, 'hour').startOf('hour').valueOf(), // 比当前大一小时
  // interval: 3600 * 1000, // 设置刻度间隔为 1 小时
  // axisLabel: {
  //   formatter: '{HH}:{mm}',
  // },
  type: 'category',
  boundaryGap: true,
  triggerEvent: true,
  axisLine: {
    lineStyle: {
      color: '#E9EBF0',
    },
  },
  axisLabel: {
    color: '#292B33',
  },
  axisTick: {
    show: false,
  },
  splitLine: {
    show: false,
  },
};

export default {
  name: 'BarChart',
  props: {
    id: String,
    height: {
      type: String,
      default: '200px',
    },
    data: {
      type: Array,
      default: () => [],
    },
    xAxis: Object,
    unit: String,
    loading: {
      type: Boolean,
      default: false,
    },
    notMerge: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      myChart: null,
      resizeFn: null,
    };
  },
  watch: {
    data: {
      handler(n) {
        if (!n || !n.length) {
          // 这里为了解决echarts图表series从有值变为空，但是图表不会更新的问题
          this.myChart &&
            this.myChart.setOption(
              {
                series: [],
              },
              true
            );
          this.myChart &&
            this.myChart.showLoading({
              text: '暂无数据',
              showSpinner: false,
              textColor: 'rgba(0, 0, 0, 0.45)',
              maskColor: 'rgba(255, 255, 255, 1)',
              fontSize: '16px',
              fontWeight: '500',
            });
          return;
        }

        this.myChart && this.myChart.hideLoading();
        this.myChart &&
          this.myChart.setOption(this.getOption(n), this.notMerge);
      },
      deep: true,
    },
  },
  methods: {
    init() {
      this.$nextTick(() => {
        let dom = this.$refs.chart;
        if (dom) {
          this.myChart = echarts.init(dom);
          const options = this.getOption(this.data);
          if (options) {
            this.myChart.setOption(options);
          }
          // 监听鼠标移入移出事件
          this.myChart.on('showTip', (params) => {
            this.$emit('showTip', params);
          });
          this.myChart.on('hideTip', () => {
            this.$emit('hideTip');
          });
        }
      });
    },
    getOption(data = []) {
      // 设置series
      let series = [];
      data.forEach((el) => {
        series.push({
          type: 'bar',
          smooth: true,
          showSymbol: false,
          name: el.name,
          data: el.data,
          color: el.color,
          yAxisIndex: el.yAxisIndex ? el.yAxisIndex : 0,
          label: {
            show: !el?.show ? false : true,
          },
        });
      });
      const yAxis = [
        {
          type: 'value',
          name: this.unit,
          nameTextStyle: {
            color: '#292B33',
          },
          position: 'left',
          axisLine: {
            show: false,
          },
          axisLabel: {
            color: '#292B33',
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#E9EBF0',
              type: 'dashed',
            },
          },
        },
      ];
      return {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'line',
          },
          confine: true,
        },
        grid: {
          bottom: 56,
          left: 16,
          right: 16,
          containLabel: true,
        },
        xAxis: Object.assign({}, defaultXAxis, this.xAxis || {}),
        yAxis,
        legend: {
          icon: 'rect',
        },
        series,
        dataZoom: [
          {
            type: 'slider',
            start: 0,
            end: 50,
            height: 15,
            xAxisIndex: 0,
            bottom: 20,
          },
        ],
        graphic: null,
      };
    },
    showLoading() {
      this.myChart && this.myChart.showLoading();
    },
    resize() {
      this.myChart && this.myChart.resize && this.myChart.resize();
    },
  },
  mounted() {
    this.resizeFn = throttle(() => {
      this.resize();
    }, 1000);
    // 监听窗口变化
    window.addEventListener('resize', this.resizeFn);
    this.init();
  },
  beforeDestroy() {
    if (this.myChart) {
      this.myChart.dispose();
      this.myChart = null;
    }
    window.removeEventListener('resize', this.resizeFn);
  },
};
</script>

<style lang="less" scoped></style>
