<template>
  <div class="chart-container" ref="chartContainer"></div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'PieMulti',
  props: {
    data: {
      type: Array,
      default: () => [
      ]
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    data: {
      handler(val) {
        this.initChart()
      },
      deep: true
    }
  },
  mounted() {
    this.initChart()
    window.addEventListener('resize', this.resizeHandler)
  },
  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose()
      this.chart = null
    }
    window.removeEventListener('resize', this.resizeHandler)
  },
  methods: {
    initChart() {
      if (!this.$refs.chartContainer) return
      
      if (!this.chart) {
        this.chart = echarts.init(this.$refs.chartContainer)
      }

      const option = {
        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'horizontal',
          right: 'center',
          top: 'center',
        },
        series: this.data.map((item, index) => ({
          name: item.name,
          type: 'pie',
          radius: [`${26 + (index * 10)}%`, `${30 + (index * 10)}%`],
          center: ['50%', '40%'],
          startAngle: 90,
          itemStyle: {
            borderRadius: 50,
            color: item.color
          },
          label: { show: false },
          data: [
            { 
              value: Number(item.value) || 0,
            },
            { 
              value: ((100 - Number(item.percent || 0)) / 100) * Number(item.value || 0), 
              itemStyle: { color: '#F4F6F9' } 
            }
          ]
        })).reverse()
      }

      this.chart.setOption(option)
    },
    resizeHandler() {
      if (this.chart) {
        this.chart.resize()
      }
    }
  }
}
</script>

<style scoped>
.chart-container {
  width: 100%;
  height: 100%;
}
</style>
