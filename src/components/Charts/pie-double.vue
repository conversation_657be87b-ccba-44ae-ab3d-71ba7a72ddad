<template>
  <div class="pie-double-chart">
    <div class="chart-container" v-loading="loading">
      <div class="chart-item" ref="leftChart"></div>
      <div class="chart-item" ref="rightChart"></div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'

export default {
  name: 'PieDouble',
  props: {
    data: {
      type: Array,
      default: () => []
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      leftChart: null,
      rightChart: null
    }
  },
  methods: {
    initCharts() {
      if (!this.data.length) return
      
      this.$nextTick(() => {
        // 初始化左侧饼图
        if (!this.leftChart) {
          this.leftChart = echarts.init(this.$refs.leftChart)
        }
        
        // 初始化右侧饼图
        if (!this.rightChart) {
          this.rightChart = echarts.init(this.$refs.rightChart)
        }

        // 计算总数用于显示百分比
        const leftTotal = this.data.reduce((sum, item) => sum + item.value, 0)
        const rightTotal = this.data[0].children.reduce((sum, item) => sum + item.value, 0)

        // 左侧饼图配置
        const leftOption = {
          title: {
            show: false
          },
          tooltip: {
            trigger: 'item',
            formatter: '{b}: {c} ({d}%)'
          },
          color: ['#217AFF', '#FF8D24'],
          legend: {
            orient: 'horizontal',
            left: 'center',
            top: '160px',
            itemWidth: 10,
            itemHeight: 10,
            textStyle: {
              fontSize: 16
            },
            formatter: (name) => {
              const item = this.data.find(item => item.name === name)
              if(item) {
                const percentage = ((item.value / leftTotal) * 100).toFixed(0)
                return [
                  `{name|${name}}`,
                  `{value|${item.value}}`,
                  `{percent|${percentage}%}`
                ].join('')
              }
              return name
            },
            textStyle: {
              rich: {
                name: {
                  width: 80,
                  fontSize: 16
                },
                value: {
                  width: 40,
                  fontSize: 16
                },
                percent: {
                  width: 40,
                  fontSize: 16
                }
              }
            }
          },
          series: [
            {
              type: 'pie',
              radius: ['50%', '70%'],
              center: ['50%', '35%'],
              data: this.data,
              label: {
                show: false
              },
              emphasis: {
                scale: false,
              }
            }
          ]
        }

        // 右侧饼图配置
        const rightOption = {
          title: {
            show: false
          },
          tooltip: {
            trigger: 'item',
            formatter: '{b}: {c} ({d}%)'
          },
          color: ['#00C8A7', '#1AB2FF', '#FAC014'],
          legend: {
            orient: 'horizontal',
            left: 'center',
            top: '160px',
            itemWidth: 10,
            itemHeight: 10,
            textStyle: {
              fontSize: 16
            },
            formatter: (name) => {
              const item = this.data[0].children.find(item => item.name === name)
              if(item) {
                const percentage = ((item.value / rightTotal) * 100).toFixed(0)
                return [
                  `{name|${name}}`,
                  `{value|${item.value}}`,
                  `{percent|${percentage}%}`
                ].join('')
              }
              return name
            },
            textStyle: {
              rich: {
                name: {
                  width: 80,
                  fontSize: 16
                },
                value: {
                  width: 40,
                  fontSize: 16
                },
                percent: {
                  width: 40,
                  fontSize: 16
                }
              }
            }
          },
          series: [
            {
              type: 'pie',
              radius: ['40%', '60%'],
              center: ['50%', '35%'],
              data: this.data[0].children,
              label: {
                show: false
              },
              emphasis: {
                scale: false,
              }
            }
          ]
        }

        this.leftChart.setOption(leftOption)
        this.rightChart.setOption(rightOption)
      })
    },
    handleResize() {
      this.$nextTick(() => {
        if (this.leftChart) {
          this.leftChart.resize()
        }
        if (this.rightChart) {
          this.rightChart.resize()
        }
      })
    }
  },
  watch: {
    data: {
      handler(val) {
        if (val.length) {
          this.initCharts()
        }
      },
      deep: true
    }
  },
  mounted() {
    this.$nextTick(() => {
      if (this.data.length) {
        this.initCharts()
      }
      window.addEventListener('resize', this.handleResize)
    })
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
    if (this.leftChart) {
      this.leftChart.dispose()
    }
    if (this.rightChart) {
      this.rightChart.dispose()
    }
  }
}
</script>

<style lang="scss" scoped>
.pie-double-chart {
  width: 100%;
  height: 100%;
  
  .chart-container {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: space-between;
    .chart-item {
      width: 50%;
      height: 100%;
    }
  }
}
</style>
