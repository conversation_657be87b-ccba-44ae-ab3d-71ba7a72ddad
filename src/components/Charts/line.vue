<template>
  <div
    v-loading="loading"
    ref="chart"
    :id="id"
    :style="`height: ${height}`"
  ></div>
</template>

<script>
import * as echarts from 'echarts/core';
import { LineChart, BarChart } from 'echarts/charts';
import { CanvasRenderer } from 'echarts/renderers';
import {
  GridComponent,
  TooltipComponent,
  LegendComponent,
  DataZoomComponent,
  ToolboxComponent,
  GraphicComponent,
} from 'echarts/components';
// import moment from 'moment';
import { throttle } from 'lodash';

echarts.use([
  <PERSON><PERSON><PERSON>,
  Bar<PERSON>hart,
  CanvasRenderer,
  GridComponent,
  TooltipComponent,
  LegendComponent,
  DataZoomComponent,
  ToolboxComponent,
  GraphicComponent,
]);

const defaultXAxis = {
  type: 'category',
  boundaryGap: false,
  triggerEvent: true,
  axisLine: {
    lineStyle: {
      color: '#E9EBF0',
    },
  },
  axisLabel: {
    color: '#292B33',
  },
  axisTick: {
    show: false,
  },
  splitLine: {
    show: false,
  },
};

export default {
  name: '<PERSON><PERSON><PERSON>',
  props: {
    id: String,
    height: {
      type: String,
      default: '200px',
    },
    data: {
      type: Array,
      default: () => [],
    },
    xAxis: Object,
    unit: {
      type: [String, Array],
      default: '',
    },
    loading: {
      type: Boolean,
      default: false,
    },
    notMerge: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      myChart: null,
      resizeFn: null,
    };
  },
  watch: {
    data: {
      handler(n) {
        if (!n || !n.length) {
          this.myChart &&
            this.myChart.setOption(
              {
                series: [],
              },
              true
            );
          this.myChart &&
            this.myChart.showLoading({
              text: '暂无数据',
              showSpinner: false,
              textColor: 'rgba(0, 0, 0, 0.45)',
              maskColor: 'rgba(255, 255, 255, 1)',
              fontSize: '16px',
              fontWeight: '500',
            });
          return;
        }

        this.myChart && this.myChart.hideLoading();
        this.myChart &&
          this.myChart.setOption(this.getOption(n), this.notMerge);
        this.resize();
      },
      deep: true,
      immediate: true,
    },
  },
  methods: {
    init() {
      this.$nextTick(() => {
        let dom = this.$refs.chart;
        if (dom) {
          this.myChart = echarts.init(dom);
          const options = this.getOption(this.data);
          if (options) {
            this.myChart.setOption(options);
          }
          // 监听鼠标移入移出事件
          this.myChart.on('showTip', (params) => {
            this.$emit('showTip', params);
          });
          this.myChart.on('hideTip', () => {
            this.$emit('hideTip');
          });
        }
      });
    },
    getOption(data = []) {
      // 设置series
      let series = [];
      data.forEach((el) => {
        series.push({
          type: 'line',
          smooth: true,
          showSymbol: false,
          name: el.name,
          data: el.data,
          color: el.color,
          unit: el.unit,
          yAxisIndex: el.yAxisIndex ? el.yAxisIndex : 0,
          stack: el.stack ? el.stack : undefined,
          label: {
            show: !el?.show ? false : true,
          },
        });
      });

      // 计算 dataZoom 范围
      const totalDataPoints = series.reduce(
        (sum, serie) => sum + (serie.data?.length || 0),
        0
      );
      let start = 0;
      let end = 100;

      // 根据 series 数据量调整 dataZoom 范围
      if (totalDataPoints > 0) {
        // 示例逻辑：如果数据点较多，缩小显示范围
        end = Math.min(100, 10000 / totalDataPoints); // 可以根据实际需求调整此逻辑
        if (end < 2) {
          end = 2;
        }
      }

      const yAxis = [
        {
          show: true,
          type: 'value',
          name:
            '单位：' + (Array.isArray(this.unit) ? this.unit[0] : this.unit),
          nameTextStyle: {
            color: '#292B33',
          },
          position: 'left',
          axisLine: {
            show: false,
          },
          axisLabel: {
            color: '#292B33',
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#E9EBF0',
              type: 'dashed',
            },
          },
        },
        {
          show: Array.isArray(this.unit) && this.unit.length > 1,
          type: 'value',
          name:
            '单位：' + (Array.isArray(this.unit) ? this.unit[1] : this.unit),
          nameTextStyle: {
            color: '#292B33',
          },
          position: 'right',
          
          axisLine: {
            show: false,
          },
          axisLabel: {
            color: '#292B33',
          },
          splitLine: {
            show: false,
          },
        },
        {
          show: Array.isArray(this.unit) && this.unit.length > 2,
          type: 'value',
          name:
            '单位：' + (Array.isArray(this.unit) ? this.unit[2] : this.unit),
          nameTextStyle: {
            color: '#292B33',
          },
          position: 'right',
          offset: 50, // 根据需要调整偏移量
          axisLine: {
            show: false,
          },
          axisLabel: {
            color: '#292B33',
          },
          splitLine: {
            show: false,
          },
        },
      ];

      console.log(yAxis, '1111');
      const _that = this;
      return {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'line',
          },
          formatter: (params) => {
            var result = params[0].name + '<br/>';
            params.forEach(function (param) {
              var unit = '';
              // 根据系列对应的 Y 轴索引设置单位
              const yAxisIndex = _that.data[param.seriesIndex]?.yAxisIndex;
              if (yAxisIndex >= 1 && Array.isArray(_that.unit)) {
                unit = _that.unit[yAxisIndex];
              } else {
                unit = Array.isArray(_that.unit) ? _that.unit[0] : _that.unit;
              }
              // 使用 <span> 标签设置颜色
              result +=
                '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:9px;height:9px;background-color:' +
                param.color +
                '"></span>';
              result +=
                param.seriesName +
                ': ' +
                (param.value || '-') +
                ' ' +
                unit +
                '<br/>';
            });
            return result;
          },
          confine: true,
        },
        grid: {
          bottom: 56,
          // left: 16,
          // right: Array.isArray(this.unit) && this.unit.length > 1 ? 30 : 16,
          left: 30,
          right: _that.unit.length > 2 ? 60 : 30,
          containLabel: true,
        },
        xAxis: Object.assign({}, defaultXAxis, this.xAxis || {}),
        yAxis,
        legend: {
          width: '80%',
          type: 'scroll',
          pageIconSize: 8,
          icon: 'rect',
          itemWidth: 8, // 宽
          itemHeight: 2, // 高
        },
        series,
        dataZoom: [
          {
            type: 'slider',
            start: start,
            end: end,
            height: 15,
            xAxisIndex: 0,
            bottom: 20,
            show: end < 100,
          },
        ],
      };
    },
    showLoading() {
      this.myChart && this.myChart.showLoading();
    },
    resize() {
      this.myChart && this.myChart.resize && this.myChart.resize();
    },
  },
  mounted() {
    this.resizeFn = throttle(() => {
      this.resize();
    }, 1000);
    window.addEventListener('resize', this.resizeFn);
    this.init();

    console.log('12312312312312312312312');
  },
  beforeDestroy() {
    if (this.myChart) {
      this.myChart.dispose();
      this.myChart = null;
    }
    window.removeEventListener('resize', this.resizeFn);
  },
};
</script>

<style lang="less" scoped></style>
