<template>
  <el-tag v-if="title" size="medium" class="status-tag" :type="color"  :hit="false">{{ title }}</el-tag>
</template>

<script>
export default {
  name: 'StatusTag',
  props: {
    value: {
      type: String,
      default: '',
    },
    dictValue: {
      type: Array,
      default: () => [],
    },
    colors: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      title: '',
    };
  },
  watch: {
    value: {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.title = this.selectDictLabel(this.dictValue, newVal);
        } else {
          this.title = '';
        }
      },
    },
  },
  computed: {
    color() {
      return this.colors[
        this.dictValue.findIndex((item) => item.value === this.value)
      ];
    },
  },
  mounted() {},
  methods: {
    tabChange(val) {
      this.$emit('input', val);
      this.$emit('tabChange', val);
    },
  },
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.status-tag {
  border: none;
}
</style>
