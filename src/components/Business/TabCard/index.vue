<template>
  <div class="tab-card">
    <div class="card-head" :style="showPadding ? {} : { paddingLeft: '0' }">
      <div class="card-title">{{ title }}</div>
      <div class="btn-wrap">
        <slot name="button"></slot>
        <div v-if="showTabs" class="tab">
          <el-radio-group v-model="tabValue" @change="tabChange">
            <el-radio-button
              v-for="item in tabList"
              :key="item.value"
              :label="item.value"
            >
              {{ item.label }}
            </el-radio-button>
          </el-radio-group>
        </div>
      </div>
    </div>
    <div class="card-content">
      <slot />
    </div>
  </div>
</template>

<script>
export default {
  name: 'TabCard',
  props: {
    value: {
      type: String,
      default: '',
    },
    tabList: {
      type: Array,
      default: () => [
        { label: '曲线', value: '1' },
        { label: '表格', value: '2' },
      ],
    },
    title: {
      type: String,
      default: '',
    },
    showTabs: {
      type: Boolean,
      default: true,
    },
    showPadding: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      tabValue: '',
    };
  },
  watch: {
    value: {
      immediate: true,
      handler(newVal) {
        if (newVal && this.tabValue !== newVal) {
          this.tabValue = newVal;
        }
      },
    },
  },
  mounted() {},
  methods: {
    tabChange(val) {
      this.$emit('input', val);
      this.$emit('tabChange', val);
    },
  },
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.tab-card {
  border-radius: 5px;
  background-color: #fff;
  .card-head {
    padding: 8px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #e9ebf0;
    .card-title {
      width: 520px;
      height: 26px;
      background-image: url('~@/assets/images/bg-title.png');
      background-size: 100% 100%;
      font-size: 18px;
      font-style: normal;
      font-weight: 500;
      line-height: 16px;
      padding-left: 36px;
      color: #21252e;
    }
    .btn-wrap {
      display: flex;
      align-items: center;
    }
  }
}
</style>
