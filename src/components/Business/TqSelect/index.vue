<template>
    <div class="region-select">
      <div class="region-search">
        <el-input
          v-model="name"
          clearable
          size="medium"
          placeholder="请输入资源名称进行搜索"
        >
          <i slot="suffix" class="el-icon-search el-input__icon" />
        </el-input>
      </div>
      <div class="region-list">
        <el-tree
          class="filter-tree"
          :data="treeData"
          :props="defaultProps"
          default-expand-all
          highlight-current
          node-key="code"
          :expand-on-click-node="false"
          :filter-node-method="filterNode"
          @current-change="nodeClick"
          ref="nodeTree"
        >
          <template #default="{ data }">
            <div class="region-tree-item">
              <img
                v-if="data.children && data.children.length"
                src="@/assets/images/icon-folder.png"
                class="folder-icon"
                alt=""
              />
              <img
                v-else
                src="@/assets/images/icon-file.png"
                class="folder-icon"
                alt=""
              />
  
              {{ data.name }}
            </div>
          </template>
        </el-tree>
      </div>
    </div>
  </template>
  
  <script>
  import { areaTreeStation } from '@/api/vehicleGrid/common';
import {getParticipatorList,} from '@/api/settlement/settlementReview';

  export default {
    props: {
      requireId: {
          type: String,
          default: '',
      },
      requireName: {
          type: String,
          default: '',
      }
    },
    data() {
      return {
        name: '',
        treeData: [],
        defaultProps: {
          children: 'children',
          label: 'name',
        },
      };
    },
    watch: {
      name(val) {
        this.$refs.nodeTree.filter(val);
      },
    },
    async mounted() {
      await this.loadData();
      if (this.treeData.length > 0) {
        this.$nextTick(() => {
          // this.$refs.nodeTree.setCurrentKey(this.treeData[0].code);
          // // 手动触发点击事件
          // this.nodeClick(this.treeData[0]);
          this.initNode()
        });
      }
    },
    methods: {
      async loadData() {
        const [err, res] = await getParticipatorList({
          requireId: this.requireId,
        });
        if (err) return;
        // this.treeData = res.data;

        const data = {
          operators: [
            {
              operatorCode: '505050525',
              operatorName: '运营商1',
              code: '505050525',
              name: '运营商1',
              children: [
                {
                  stationId: '5050505251',
                  stationName: '站点1',
                  code: '5050505251',
                  name: '站点1',
                  children: [
                    {
                      consNo: '50505052511',
                      consName: '户号1',
                      code: '50505052511',
                      name: '户号1',
                    }
                  ]
                }
              ]
            }
          ]
        }

        // const list1 = [
        //   {
        //     requireId: '12121',
        //     requireName: '需求1',
        //     code: '12121',
        //     name: '需求1',
        //     children: [
        //       {
        //         operatorCode: '505050525',
        //         operatorName: '运营商1',
        //         code: '505050525',
        //         name: '运营商1',
        //         children: [
        //           {
        //             stationId: '5050505251',
        //             stationName: '站点1',
        //             code: '5050505251',
        //             name: '站点1',
        //             children: [
        //               {
        //                 consNo: '50505052511',
        //                 consName: '户号1',
        //                 code: '50505052511',
        //                 name: '户号1',
        //               }
        //             ]
        //           }
        //         ]
        //       }
        //     ]
        //   }
        // ]

        // const list2 = [
        //   {
        //     requireId: '12121',
        //     requireName: '需求1',
        //     code: '12121',
        //     name: '需求1',
        //     children: [
        //       {
        //         requireId: '12121',
        //         operatorCode: '505050525',
        //         operatorName: '运营商1',
        //         code: '505050525',
        //         name: '运营商1',
        //         children: [
        //           {
        //             requireId: '12121',
        //             operatorCode: '505050525',
        //             stationId: '5050505251',
        //             stationName: '站点1',
        //             code: '5050505251',
        //             name: '站点1',
        //             children: [
        //               {
        //                 requireId: '12121',
        //                 operatorCode: '505050525',
        //                 stationId: '5050505251',
        //                 consNo: '50505052511',
        //                 consName: '户号1',
        //                 code: '50505052511',
        //                 name: '户号1',
        //               }
        //             ]
        //           }
        //         ]
        //       }
        //     ]
        //   }
        // ]
        

        const list = [
          {
            requireId: this.requireId,
            requireName: this.requireName,
            code: this.requireId,
            name: this.requireName,
            children: data.operators,
          }
        ]

        this.treeData = this.processList(list)

        console.log(list,this.treeData, 'treeData');
        
        
        // this.treeData = [
        //     {
        //         code: '123',
        //         name: '聚合商1',
        //         children: [
        //             {
        //                 code: '1231',
        //                 name: '运营商1',
        //                 children: [
        //                     {
        //                         code: '12311',
        //                         name:  '场站1',
        //                         children: []
        //                     },
        //                     {
        //                         code: '12312',
        //                         name:  '场站2',
        //                         children: []
        //                     }
        //                 ]
        //             },
        //             {
        //                 code: '1232',
        //                 name: '运营商2',
        //                 children: [
        //                     {
        //                         code: '12321',
        //                         name:  '场站3',
        //                         children: []
        //                     },
        //                 ]
        //             }
        //         ]
        //     },
        //     {
        //         code: '124',
        //         name: '聚合商2',
        //         children: [
        //             {
        //                 code: '1241',
        //                 name: '运营商3',
        //                 children: [
        //                     {
        //                         code: '12411',
        //                         name:  '场站4',
        //                         children: []
        //                     },
        //                 ]
        //             },
        //         ]
        //     }
        // ]
      },
      initNode(){
          this.$refs.nodeTree.setCurrentKey(this.treeData[0].code);
          // 手动触发点击事件
          this.nodeClick(this.treeData[0]);
      },
      filterNode(value, data) {
        if (!value) return true;
        return data.name.indexOf(value) !== -1;
      },
      nodeClick(node) {
        this.$emit('nodeClick', node);
      },

      // 递归处理函数
      processList(list, parent = {}) {
        return list.map(item => {
          // 合并当前项和父级的字段
          const newItem = {
            ...item,
            requireId: parent.requireId || item.requireId || '',
            operatorCode: parent.operatorCode || item.operatorCode|| '',
            stationId: parent.stationId || item.stationId|| '',
          };

          // 如果有 children，递归处理
          if (item.children && item.children.length > 0) {
            newItem.children = this.processList(item.children, {
              requireId: newItem.requireId,
              operatorCode: newItem.operatorCode,
              stationId: newItem.stationId,
            });
          }

          return newItem;
        });
      }
    },
  };
  </script>
  
  <style rel="stylesheet/scss" lang="scss" scoped>
  .region-select {
    // max-height: 100%;
    // overflow: auto;
    float: left;
    width: 300px;
    padding: 8px 0px;
    background-color: #fff;
    border-radius: 5px;
    background: #fff;
    margin-right: 16px;
    box-shadow: 0px 5px 12px 4px rgba(0, 34, 101, 0.04),
      0px 3px 6px 0px rgba(0, 34, 101, 0.04),
      0px 1px 2px -2px rgba(0, 34, 101, 0.04);
    .region-search {
      padding: 0 16px;
      margin-bottom: 16px;
    }
  }
  .region-tree-item {
    display: flex;
    align-items: center;
    font-size: 16px;
    line-height: 40px;
    color: #12151a;
    .folder-icon {
      width: 20px;
      height: 20px;
      display: block;
      margin-right: 8px;
    }
  }
  .region-list {
    ::v-deep .el-tree-node__content {
      height: auto;
    }
    ::v-deep .el-tree-node__expand-icon {
      margin-left: 10px;
    }
  }
  </style>
  