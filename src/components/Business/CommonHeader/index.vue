<template>
  <div class="common-header">
    <img
      :style="getImgSize"
      src="@/assets/station/station-detail-top-icon.png"
    />
    <div class="content-wrap" :style="getMargin">
      <div class="title-wrap">
        <div class="title-content">{{ title }}</div>
        <div v-if="showStatus" class="status" :style="getStatusBg">
          {{ status }}
        </div>
        <slot name="tip"></slot>
      </div>
      <slot name="content"></slot>
    </div>
    <slot name="button"></slot>
  </div>
</template>
<script>
export default {
  props: {
    title: {
      type: String,
      default: '',
    },
    size: {
      type: String,
      default: 'large',
    },
    status: {
      type: String,
      default: '待提交',
    },
    statusBgColor: {
      type: String,
      default: 'linear-gradient(144.92deg, #21c8ff 8.35%, #217aff 96.07%)',
    },
    showStatus: {
      type: Boolean,
      default: true,
    },
  },
  computed: {
    getImgSize() {
      return this.size === 'large'
        ? { width: '60px', height: '60px' }
        : { width: '48px', height: '48px' };
    },
    getMargin() {
      return this.size === 'large'
        ? { margin: '0 24px' }
        : { margin: '0 16px' };
    },
    getStatusBg() {
      return { background: this.statusBgColor };
    },
  },
};
</script>
<style lang="scss" scoped>
.common-header {
  width: 100%;
  display: flex;
  align-items: center;
  box-sizing: border-box;
  .content-wrap {
    flex: 1;
    .title-wrap {
      display: flex;
      margin-bottom: 16px;
      .title-content {
        font-family: PingFang SC;
        font-size: 24px;
        font-weight: 500;
        line-height: 24px;
        color: #12151a;
        margin-right: 12px;
      }
      .status {
        width: 65px;
        height: 24px;
        border-radius: 10px 0 10px 0;
        font-family: YouSheBiaoTiHei;
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;
        text-align: center;
        color: #fff;
      }
    }
  }
}
</style>
