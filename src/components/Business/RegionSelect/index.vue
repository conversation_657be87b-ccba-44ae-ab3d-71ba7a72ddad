<template>
  <div :class="isAll?'region-select-wrap': 'show-all'">
    <div v-if="isAll" @click="onClickUp" class="show-all-buton">
        <div>收</div>
        <div>起</div>
        <div><</div>
    </div>
    <div class="region-select" v-if="isAll"> 
      <div class="region-search">
        <el-input
          v-model="areaName"
          clearable
          size="medium"
          placeholder="请输入资源名称进行搜索"
        >
          <i slot="suffix" class="el-icon-search el-input__icon" />
        </el-input>

      
      </div>
      <div class="region-list">
        <el-tree
          class="filter-tree"
          :data="treeData"
          :props="defaultProps"
          default-expand-all
          highlight-current
          node-key="areaCode"
          :expand-on-click-node="false"
          :filter-node-method="filterNode"
          @current-change="nodeClick"
          ref="nodeTree"
        >
          <template #default="{ data }">
            <div class="region-tree-item">
              <img
                v-if="data.list && data.list.length"
                src="@/assets/images/icon-folder.png"
                class="folder-icon"
                alt=""
              />
              <img
                v-else
                src="@/assets/images/icon-file.png"
                class="folder-icon"
                alt=""
              />

              {{ data.areaName }}
            </div>
          </template>
        </el-tree>
      </div>
    </div>

    <div v-if="!isAll">
      <div @click="onClickAll" class="show-all-buton">
        <div>展</div>
        <div>开</div>
        <div>></div>

      </div>
    </div>

  </div>
</template>

<script>
import { areaTreeStation } from '@/api/vehicleGrid/common';
export default {
  props: {},
  data() {
    return {
      areaName: '',
      treeData: [],
      defaultProps: {
        children: 'list',
        label: 'areaName',
      },
      isAll: true,
    };
  },
  watch: {
    areaName(val) {
      this.$refs.nodeTree.filter(val);
    },
  },
  async mounted() {
    await this.loadData();
    if (this.treeData.length > 0) {
      this.$nextTick(() => {
        this.$refs.nodeTree.setCurrentKey(this.treeData[0].areaCode);
        // 手动触发点击事件
        this.nodeClick(this.treeData[0]);
      });
    }
  },
  methods: {
    async loadData() {
      const [err, res] = await areaTreeStation({});
      if (err) return;
      this.treeData = [res.data];
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.areaName.indexOf(value) !== -1;
    },
    nodeClick(node) {
      this.$emit('nodeClick', node);
    },
    onClickUp() {
      this.isAll = false;
    },
    onClickAll() {
      this.isAll = true;
    }
  },
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.region-select-wrap {
  height: calc(100vh - 142px);
  overflow-y: auto;
  overflow-x: hidden;
  float: left;
  width: 322px;
  position: relative;
  -ms-overflow-style: none;  /* IE/Edge */
  scrollbar-width: none;     /* Firefox */

  .show-all-buton {
    z-index: 2000;
    width: 22px;
    height: 94px;
    position: absolute;
    right: 0px;
    top:50vh;
    background-image: url(~@/assets/station/show-all-wrap.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    line-height: 16px;
    font-size: 14px;
    padding-top: 24px;
    padding-left: 4px;
    box-sizing: border-box;
    color: #292B33;
  }
  .show-all-buton:hover {
    color: #217aff;
  }
}

.region-select-wrap::-webkit-scrollbar {
  display: none; /* Chrome/Safari/Edge */
}
.region-select {

  position: relative;
  height: 100%;
  // max-height: 100%;
  // overflow: auto;
  // float: left;
  width: 300px;

  // overflow-y: scroll;
  padding: 8px 0px;
  background-color: #fff;
  border-radius: 5px;
  background: #fff;
  margin-right: 16px;
  box-shadow: 0px 5px 12px 4px rgba(0, 34, 101, 0.04),
    0px 3px 6px 0px rgba(0, 34, 101, 0.04),
    0px 1px 2px -2px rgba(0, 34, 101, 0.04);
  .region-search {
    padding: 0 16px;
    margin-bottom: 16px;
  }
}
.region-tree-item {
  display: flex;
  align-items: center;
  font-size: 16px;
  line-height: 40px;
  color: #12151a;
  .folder-icon {
    width: 20px;
    height: 20px;
    display: block;
    margin-right: 8px;
  }
}
.region-list {
  ::v-deep .el-tree-node__content {
    height: auto;
  }
  ::v-deep .el-tree-node__expand-icon {
    margin-left: 10px;
  }
}

.show-all {
  background-color: #F5F6F9;
  // margin-right: 16px;
  padding: 2px;
  height: calc(100vh - 142px);
  box-sizing: border-box;
  width: 1px;
  position: relative;
  .show-all-buton {
    z-index: 1000;
    width: 22px;
    height: 94px;
    position: absolute;
    right: 0px;
    top: 50vh;
    background-image: url(~@/assets/station/show-all-wrap.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    line-height: 16px;
    font-size: 14px;
    padding-top: 24px;
    padding-left: 4px;
    box-sizing: border-box;
    color: #292B33;
  }
  .show-all-buton:hover {
    color: #217aff;
  }
}

</style>
