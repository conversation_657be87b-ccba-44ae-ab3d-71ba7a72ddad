<template>
  <div class="empty-box">
    <img class="empty-img" src="@/assets/images/bg-empty.png" alt="" />
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: '',
    },
  },
  data() {
    return {};
  },
  mounted() {},
  methods: {},
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.empty-box {
  padding: 16px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  .empty-img {
    width: 500px;
  }
}
</style>
