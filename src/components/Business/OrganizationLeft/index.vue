<template>
  <div>
    <div class="region-select" v-if="isAll">
      <div @click="onClickUp" class="show-all-buton">
        <div>收</div>
        <div>起</div>
        <div>&lt;</div>
      </div>
      <div class="region-search">
        <el-input
          v-model="searchName"
          clearable
          size="medium"
          placeholder="请输入企业名称"
          @input="topSearch"
        >
          <i slot="suffix" class="el-icon-search el-input__icon" />
        </el-input>
      </div>
      <div class="region-list">
        <el-tree
          :key="treeKey"
          :default-expanded-keys="expandKeys"
          class="filter-tree"
          :data="treeData"
          :props="defaultProps"
          highlight-current
          node-key="onlyId"
          :expand-on-click-node="false"
          :filter-node-method="filterNode"
          @current-change="nodeClick"
          @node-expand="getNext"
          ref="nodeTree"
        >
          <template #default="{ data }">
            <div class="region-tree">
              <div class="region-tree-item">
                <img
                  src="@/assets/images/icon-folder.png"
                  class="folder-icon"
                  alt=""
                />
                {{ data.enterpriseName }}
              </div>
              <div class="region-tree-icons">
                <i
                  class="el-icon-edit"
                  @click.stop="handleEdit(data, $event)"
                  v-if="data.parentDepartmentId"
                ></i>
                <i
                  class="el-icon-circle-plus-outline"
                  @click.stop="handlePlus(data, $event)"
                ></i>
                <i
                  class="el-icon-remove-outline"
                  @click.stop="handleRemove(data, $event)"
                  v-if="data.parentDepartmentId"
                ></i>
              </div>
            </div>
          </template>
        </el-tree>
      </div>
    </div>
    <div v-else class="show-all">
      <div @click="onClickAll" class="show-all-buton">
        <div>展</div>
        <div>开</div>
        <div>></div>
      </div>
    </div>
    <el-dialog
      :title="modalData.title"
      :visible.sync="modalData.open"
      width="60%"
      append-to-body
      @close="handleCancel"
    >
      <div>
        <el-form
          :model="modalData.form"
          :rules="modalData.rules"
          ref="ruleForm"
          label-position="top"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item
                label="机构名称："
                prop="name"
                :label-width="formLabelWidth"
              >
                <el-input v-model="modalData.form.name" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item
                label="机构地址："
                prop="address"
                :label-width="formLabelWidth"
              >
                <el-input v-model="modalData.form.address" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item
                label="管理员名称："
                prop="adminName"
                :label-width="formLabelWidth"
              >
                <el-input v-model="modalData.form.adminName" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item
                label="管理员联系方式："
                prop="adminMobile"
                :label-width="formLabelWidth"
              >
                <el-input v-model="modalData.form.adminMobile" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item
                label="管理员邮箱："
                prop="adminEmail"
                :label-width="formLabelWidth"
              >
                <el-input v-model="modalData.form.adminEmail" />
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="备注：" :label-width="formLabelWidth">
                <el-input
                  v-model="modalData.form.remark"
                  type="textarea"
                  :rows="3"
                  maxlength="128"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="submitFileForm">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog
      :title="modifyData.title"
      :visible.sync="modifyData.open"
      width="60%"
      append-to-body
      @close="handleModifyNameCancel"
    >
      <div>
        <el-form
          :model="modifyData.form"
          :rules="modifyData.rules"
          ref="modifyForm"
          label-position="top"
        >
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item
                label="机构名称："
                prop="newName"
                :label-width="formLabelWidth"
              >
                <el-input v-model="modifyData.form.newName" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="handleModifyNameCancel">取 消</el-button>
        <el-button type="primary" @click="submitModifyNameFileForm">
          确 定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { findAndModifyTreeNode } from '@/utils/treeUtils';
import {
  enterpriseList,
  nextLevelDepartment,
  editDepartment,
  addDepartment,
  delDepartment,
} from '@/api/user/enterpriseOrganization';

export default {
  props: {},
  data() {
    return {
      searchName: '',
      treeData: [],
      defaultProps: {
        children: 'list',
        label: 'enterpriseName',
      },
      isAll: true,
      modalData: {
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: '企业机构新增',
        form: {
          name: '', // 机构名称
          address: '', // 机构地址
          adminName: '', // 管理员名称
          adminMobile: '', // 管理员联系方式
          adminEmail: '', // 管理员邮箱
          remark: '', // 备注
        },
        id: '',
        rules: {
          name: [
            { required: true, message: '请输入机构名称', trigger: 'blur' },
          ],
          address: [
            { required: true, message: '请输入机构地址', trigger: 'blur' },
          ],
          adminName: [
            { required: true, message: '请输入管理员名称', trigger: 'blur' },
          ],
          adminMobile: [
            {
              required: true,
              message: '请输入管理员联系方式',
              trigger: 'blur',
            },
            {
              pattern: /^1[3-9]\d{9}$/,
              message: '请输入正确的手机号格式',
              trigger: 'blur',
            },
          ],
          adminEmail: [
            { required: true, message: '请输入管理员邮箱', trigger: 'blur' },
            { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' },
          ],
        },
      },
      formLabelWidth: '120px',
      showInputData: {},
      modifyData: {
        // 是否显示弹出层
        open: false,
        // 弹出层标题
        title: '企业机构名称修改',
        form: {
          newName: '',
        },
        rules: {
          newName: [
            { required: true, message: '请输入新的机构名称', trigger: 'blur' },
          ],
        },
      },
      treeKey: 0,
      expandKeys: [], // 当前展开的节点 id 列表
    };
  },
  watch: {
    enterpriseName(val) {
      this.$refs.nodeTree.filter(val);
    },
  },
  async mounted() {
    await this.loadData();
    // if (this.treeData.length > 0) {
    //   this.$nextTick(() => {
    //     this.$refs.nodeTree.setCurrentKey(this.treeData[0].onlyId);
    //     // 手动触发点击事件
    //     this.nodeClick(this.treeData[0]);
    //   });
    // }
  },
  methods: {
    async loadData() {
      const [err, res] = await enterpriseList({
        enterpriseName: this.searchName,
      });
      if (err) return;
      // console.log('企业客户不分页查询', res);
      this.treeData = res.data;
      this.treeData.forEach((item) => {
        item.list = [{}];
        item.onlyId = item.enterpriseId;
      });
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.enterpriseName.indexOf(value) !== -1;
    },
    nodeClick(node) {
      this.$emit('nodeClick', node);
    },
    async getNext(node) {
      const [err, res] = await nextLevelDepartment({
        parentDepartmentId: node.onlyId,
      });
      if (err) return;
      console.log('下级机构', res);
      let children = res.data;
      children.forEach((item) => {
        item.list = [{}];
        item.enterpriseName = item.name;
        item.onlyId = item.departmentId;
      });
      findAndModifyTreeNode(this.treeData, 'onlyId', node.onlyId, {
        list: children,
      });

      // this.$forceUpdate();
      this.treeKey++;
      this.expandKeys = [node.onlyId];
      console.log('this.treeData', this.treeData);
    },
    onClickUp() {
      this.isAll = false;
    },
    onClickAll() {
      this.isAll = true;
    },
    // 编辑
    handleEdit(row, event) {
      console.log(row);
      console.log(event);
      event.stopPropagation();
      this.showInputData = row;
      this.modifyData.form.newName = this.showInputData.enterpriseName;
      this.modifyData.open = true;
    },
    // 名称编辑弹窗关闭
    handleModifyNameCancel() {
      this.modifyData.open = false;
    },
    // 名称编辑弹窗确定
    async submitModifyNameFileForm() {
      this.$refs.modifyForm.validate(async (valid) => {
        if (valid) {
          // console.log(this.modifyData.form);
          // console.log(this.showInputData);
          let params = {
            departmentId: this.showInputData.departmentId,
            name: this.modifyData.form.newName,
            address: this.showInputData.address,
            adminName: this.showInputData.adminName,
            adminMobile: this.showInputData.adminMobile,
            adminEmail: this.showInputData.adminEmail,
            remark: this.showInputData.remark,
          };
          const [err, res] = await editDepartment(params);
          if (err) return;
          console.log(res);
          this.$message({
            message: '修改成功',
            type: 'success',
          });
          await this.loadData();
          this.treeKey++;
          this.expandKeys = [];
          this.handleModifyNameCancel();
        }
      });
    },
    // 添加
    handlePlus(row, event) {
      event.stopPropagation();
      console.log(row);
      this.showInputData = row;
      this.modalData.title = '企业机构新增';
      this.modalData.id = '';
      this.modalData.form = {
        name: '', // 机构名称
        address: '', // 机构地址
        adminName: '', // 管理员名称
        adminMobile: '', // 管理员联系方式
        adminEmail: '', // 管理员邮箱
        remark: '', // 备注
      };
      this.modalData.open = true;
    },
    // 新增取消按钮
    handleCancel() {
      this.modalData.open = false;
      this.resetForm();
    },
    // 重置表单
    resetForm() {
      this.$refs.ruleForm.resetFields();
    },
    // 新增提交表单
    submitFileForm() {
      // console.log('提交表单数据:', this.modalData.form);
      // console.log(this.showInputData);
      this.$refs.ruleForm.validate(async (valid) => {
        if (valid) {
          // 表单验证通过，执行提交逻辑
          let params = {
            ...this.modalData.form,
            level:
              (Number(this.showInputData.level)
                ? Number(this.showInputData.level)
                : 1) + 1,
            enterpriseId: this.showInputData.enterpriseId,
            parentDepartmentId: this.showInputData.departmentId
              ? this.showInputData.departmentId
              : this.showInputData.enterpriseId,
          };
          // console.log(params);
          if (this.modalData.id) {
            params.departmentId = this.modalData.id;
            const [err, res] = await editDepartment(params);
            if (err) return;
            // console.log('修改',res);
            this.$message({
              message: '修改成功',
              type: 'success',
            });
          } else {
            const [err, res] = await addDepartment(params);
            if (err) return;
            // console.log('新增res', res);
            this.$message({
              type: 'success',
              message: '提交成功!',
            });
          }
          await this.loadData();
          this.treeKey++;
          this.expandKeys = [];
          this.handleCancel();
          this.$emit('modalConfirm', this.modalData.form);
        } else {
          console.log('表单验证失败');
          return false;
        }
      });
    },
    // 删除
    handleRemove(row, event) {
      if (row.list && row.list.length > 0) {
        this.$message.info('请先删除最底层节点');
        return;
      }
      console.log(row);
      console.log(event);
      event.stopPropagation();
      this.$confirm('此操作将永久删除该节点, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          // this.deleteNode(row.onlyId);
          const [err, res] = await delDepartment({
            departmentId: row.departmentId,
          });
          if (err) return;
          this.$message({
            type: 'success',
            message: '删除成功!',
          });
          await this.loadData();
          this.treeKey++;
          this.expandKeys = [];
        })
        .catch(() => {
          this.$message({
            type: 'info',
            message: '已取消删除',
          });
        });
    },
    // 删除树状数据中的某个节点
    deleteNode(onlyId) {
      const removeNode = (tree, code) => {
        for (let i = 0; i < tree.length; i++) {
          const node = tree[i];
          if (node.onlyId === code) {
            // 找到目标节点，从数组中移除
            tree.splice(i, 1);
            return true;
          }
          if (node.list && node.list.length) {
            // 递归查找子节点
            if (removeNode(node.list, code)) {
              return true;
            }
          }
        }
        return false;
      };

      // 从 treeData 中删除节点
      removeNode(this.treeData, onlyId);
    },
    // 顶部检索
    topSearch() {
      console.log(this.searchName);
      this.loadData();
    },
  },
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.region-select {
  .show-all-buton {
    z-index: 1000;
    width: 22px;
    height: 94px;
    position: absolute;
    right: -22px;
    top: 50%;
    background-image: url(~@/assets/station/show-all-wrap.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    line-height: 16px;
    font-size: 14px;
    padding-top: 24px;
    padding-left: 4px;
    box-sizing: border-box;
    color: #292b33;
  }
  .show-all-buton:hover {
    color: #217aff;
  }
  position: relative;
  height: 100%;
  // max-height: 100%;
  // overflow: auto;
  // float: left;
  width: 300px;

  // overflow-y: scroll;
  padding: 8px 0px;
  background-color: #fff;
  border-radius: 5px;
  background: #fff;
  margin-right: 16px;
  box-shadow: 0px 5px 12px 4px rgba(0, 34, 101, 0.04),
    0px 3px 6px 0px rgba(0, 34, 101, 0.04),
    0px 1px 2px -2px rgba(0, 34, 101, 0.04);
  .region-search {
    padding: 0 16px;
    margin-bottom: 16px;
  }
}
.region-tree {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-right: 20px;
}
.region-tree-item {
  display: flex;
  align-items: center;
  font-size: 16px;
  line-height: 40px;
  color: #12151a;
  .folder-icon {
    width: 20px;
    height: 20px;
    display: block;
    margin-right: 8px;
  }
}
.region-tree-icons {
  display: flex;
  gap: 5px;
}
.region-list {
  ::v-deep .el-tree-node__content {
    height: auto;
    width: 100%;
  }
  ::v-deep .el-tree-node__expand-icon {
    margin-left: 10px;
  }
}

.show-all {
  background-color: #f5f6f9;
  // margin-right: 16px;
  padding: 2px;
  height: 100%;
  box-sizing: border-box;
  width: 1px;
  position: relative;
  .show-all-buton {
    z-index: 1000;
    width: 22px;
    height: 94px;
    position: absolute;
    right: 0px;
    top: 50%;
    background-image: url(~@/assets/station/show-all-wrap.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    line-height: 16px;
    font-size: 14px;
    padding-top: 24px;
    padding-left: 4px;
    box-sizing: border-box;
    color: #292b33;
  }
  .show-all-buton:hover {
    color: #217aff;
  }
}
</style>
