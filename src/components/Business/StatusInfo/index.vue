<template>
    <div class="status-tab" :class="[color]">
      {{ title }}
    </div>
  </template>
  
  <script>
  export default {
    name: 'StatusDot',
    props: {
      value: {
        type: String,
        default: '',
      },
      dictValue: {
        type: Array,
        default: () => [],
      },
      colors: {
        type: Array,
        default: () => [],
      },
    },
    data() {
      return {
        title: '',
      };
    },
    watch: {
      value: {
        immediate: true,
        handler(newVal) {
          if (newVal) {
            console.log(
              'this.dictValue',
              this.selectDictLabel(this.dictValue, newVal),
              this.dictValue,
              newVal
            );
            this.title = this.selectDictLabel(this.dictValue, newVal);
          } else {
            this.title = '';
          }
        },
      },
    },
    computed: {
      color() {
        return this.colors[
          this.dictValue.findIndex((item) => item.value === this.value)
        ];
      },
    },
    mounted() {},
    methods: {
      tabChange(val) {
        this.$emit('input', val);
        this.$emit('tabChange', val);
      },
    },
  };
  </script>
  
  <style rel="stylesheet/scss" lang="scss" scoped>
  .status-tab {
    display: flex;
    align-items: center;
    
  }
  .low {
    color: #1AB2FF;
  }
  .medium {
      color: #FF8D24;
  }
  .high {
    color: #FC1E31;
  }
  .success {
    color: #00C864;
  }
  .black {
    color: #292B33;
  }
  </style>
  