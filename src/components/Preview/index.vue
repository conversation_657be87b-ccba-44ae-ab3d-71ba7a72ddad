<template>
    <!-- 图片展示容器 -->
    <div 
      class="image-preview-container"
      @mouseenter="showActions = true"
      @mouseleave="showActions = false"
    >
      <img 
        :src="src" 
        class="preview-image"
      >
      
      <!-- 操作按钮 -->
      <div 
        class="preview-actions"
        v-show="showActions"
      >

        <span   @click.stop="openPreview()" class="file-delete">
            <i class="el-icon-zoom-in"></i>
        </span>
      </div>
  
      <!-- 预览弹窗 -->
      <el-dialog
        :visible.sync="dialogVisible"
        width="1200px"
        append-to-body
      >
        <img 
          :src="src" 
          style="width: 100%"
          
        >
      </el-dialog>
    </div>
  </template>
  
  <script>
  export default {
    name: 'SimpleImagePreview',
    props: {
      src: {
        type: String,
        required: true
      },
    },
    data() {
      return {
        showActions: false,
        dialogVisible: false
      }
    },
    methods: {
      openPreview() {
        this.dialogVisible = true
      }
    }
  }
  </script>
  
  <style lang="scss" scoped>
  .image-preview-container {
    position: relative;
    display: inline-block;
    transition: all 0.3s;
    overflow: hidden;
  
    &:hover {
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.35);
      background: rgba(0, 0, 0, 0.5);
      
      .preview-image {
        filter: brightness(0.9);
      }
    }

    .file-delete {
              color: #fff;
              font-size: 18px;
              cursor: pointer;
  
              &:hover {
                color: #409eff;
              }
            }
  
    .preview-image {
      display: block;
      max-width: 200px;
      max-height: 200px;

      height: auto;
      transition: all 0.3s;
      cursor: pointer;
    }
  
    .preview-actions {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      display: flex;
      gap: 8px;
  
      ::v-deep .el-button {
        width: 32px;
        height: 32px;
        padding: 6px;
      }
    }
  
    // .full-preview-image {
    //   width: 100%;
    //   height: auto;
    //   max-height: 70vh;
    //   object-fit: contain;
    // }
  }
  </style>