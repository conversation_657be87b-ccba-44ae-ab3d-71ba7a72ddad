import request from '@/utils/request'

const baseUrl = '/vehicle-charging-admin'

// 获取适用地市
export function getAreaList(data) {
  return request({
    url: baseUrl + '/area/list',
    method: 'post',
    data: data
  })
}

// 计费时段管理列表
export function getElectricPricePeriodList(data) {
  return request({
    url: baseUrl + '/billing/period/page',
    method: 'post',
    data: data
  })
}

// 新增计费时段
export function addElectricPricePeriod(data) {
  return request({
    url: baseUrl + '/billing/period/add',
    method: 'post',
    data: data
  })
}

// 获取计费时段详情
export function getElectricPricePeriodDetail(data) {
    return request({
        url: baseUrl + '/billing/period/detail',
        method: 'post',
        data: data
    })
}

// 修改计费时段
export function updateElectricPricePeriod(data) {
    return request({
        url: baseUrl + '/billing/period/edit',
        method: 'post',
        data: data
    })
}

// 删除计费时段
export function deleteElectricPricePeriod(data) {
    return request({
        url: baseUrl + '/billing/period/delete',
        method: 'post',
        data: data
    })
}