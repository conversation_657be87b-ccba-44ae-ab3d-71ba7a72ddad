import request from '@/utils/request';

const baseUrl = '/vehicle-grid-system';

// 查询销方信息
export function getSellerConfig(data) {
  return request({
    url: baseUrl + '/invSellerConfig/getSellerConfig',
    method: 'post',
    data: data,
  });
}

// 保存或修改
export function saveOrUpdateSellerConfig(data) {
  return request({
    url: baseUrl + '/invSellerConfig/saveOrUpdateSellerConfig',
    method: 'post',
    data: data,
  });
}

// 分页查询商品配置信息
export function getInvGoodsConfigByPage(data) {
  return request({
    url: baseUrl + '/invGoodsConfig/getInvGoodsConfigByPage',
    method: 'post',
    data: data,
  });
}

// 添加商品配置信息
export function addGoodsConfig(data) {
  return request({
    url: baseUrl + '/invGoodsConfig/addGoodsConfig',
    method: 'post',
    data: data,
  });
}

// 修改商品配置信息
export function updateGoodsConfig(data) {
  return request({
    url: baseUrl + '/invGoodsConfig/updateGoodsConfig',
    method: 'post',
    data: data,
  });
}

// 删除商品配置信息
export function deleteGoodsConfig(data) {
  return request({
    url: baseUrl + '/invGoodsConfig/deleteGoodsConfig',
    method: 'post',
    data: data,
  });
}
