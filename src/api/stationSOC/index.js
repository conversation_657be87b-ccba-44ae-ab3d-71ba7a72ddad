import request from '@/utils/request';

const baseUrl = '/vehicle-charging-admin';

// soc 分页列表
export function getStationSOCList(data) {
  return request({
    url: baseUrl + '/insp/soc/page',
    method: 'post',
    data: data,
  });
}

// 场站名称列表
export function getStationList(data) {
  return request({
    url: baseUrl + '/station/stationNameList',
    method: 'post',
    data: data,
  });
}

// 新增/更新 soc配置
export function addOrUpdateSoc(data) {
  return request({
    url: baseUrl + '/insp/soc/addOrUpdate',
    method: 'post',
    data: data,
  });
}

// 启用/停用
export function changeStatus(data) {
  return request({
    url: baseUrl + '/insp/soc/changeStatus',
    method: 'post',
    data: data,
  });
}

// 删除 soc配置
export function deleteSoc(data) {
  return request({
    url: baseUrl + '/insp/soc/delete',
    method: 'post',
    data: data,
  });
}

// soc 配置详情
export function socDetail(data) {
  return request({
    url: baseUrl + '/insp/soc/detail',
    method: 'post',
    data: data,
  });
}

// 站点详情
export function getStationDetail(data) {
  return request({
    url: baseUrl + '/station/detail',
    method: 'post',
    data: data,
  });
}

// 导入
export function uploadClearingResult(data) {
  return request({
    url: baseUrl + '/insp/soc/import',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}
