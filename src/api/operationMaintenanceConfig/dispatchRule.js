/**
 * @file 派单规则管理API
 */

import request from '@/utils/request';

const baseUrl = '/vehicle-charging-admin';

/**
 * @typedef {Object} RulePageRequest
 * @property {number} [pageNum=1] - 当前页码，默认为1
 * @property {number} [pageSize=10] - 每页显示条数，默认为10
 * @property {string} [ruleCode] - 规则编号
 * @property {string} [ruleName] - 规则名称
 * @property {string} [createTimeBeg] - 创建时间开始
 * @property {string} [createTimeEnd] - 创建时间结束
 * @property {number} [enableStatus] - 启用状态：0停用(规则不生效)，1启用(规则生效中)
 * @property {string} [merchantId] - 租户ID
 */

/**
 * @typedef {Object} RuleInfo
 * @property {string} ruleId - 规则ID
 * @property {string} merchantId - 租户ID
 * @property {string} ruleCode - 规则编号
 * @property {string} ruleName - 规则名称
 * @property {string} checkRule - 校验规则：VOLTAGE电压/CURRENT电流/TEMPERATURE温度
 * @property {string} compareOperator - 比较操作符：GT大于/LT小于
 * @property {number} checkPercent - 校验阈值(%)
 * @property {number} enableStatus - 启用状态：0停用/1启用
 * @property {string} remark - 备注信息
 * @property {string} createTime - 创建时间
 * @property {string} updateTime - 修改时间
 * @property {string} createBy - 创建人
 * @property {string} updateBy - 修改人
 */

/**
 * @typedef {Object} RulePageResponse
 * @property {string} code - 返回码
 * @property {string} msg - 返回信息
 * @property {string} subCode - 子返回码
 * @property {string} subMsg - 子返回信息
 * @property {Array<RuleInfo>} data - 返回数据
 * @property {number} total - 总条数
 */

/**
 * 派单规则分页查询
 * @param {RulePageRequest} params - 查询参数
 * @returns {Promise<RulePageResponse>} 返回包含规则列表的Promise
 */
export function getRulePage(params) {
  return request({
    url: baseUrl + '/ops/rule/page',
    method: 'post',
    data: params,
  });
}

/**
 * @typedef {Object} RuleDetailRequest
 * @property {string} ruleId - 规则ID
 */

/**
 * @typedef {Object} RuleDetailResponse
 * @property {string} code - 返回码
 * @property {string} msg - 返回信息
 * @property {string} subCode - 子返回码
 * @property {string} subMsg - 子返回信息
 * @property {RuleInfo} data - 返回数据
 * @property {number} total - 总条数
 */

/**
 * 派单规则详情
 * @param {RuleDetailRequest} data - 查询参数
 * @returns {Promise<RuleDetailResponse>} 返回规则详情数据的Promise
 */
export function getRuleDetail(data) {
  return request({
    url: baseUrl + '/ops/rule/detail',
    method: 'post',
    data: data,
  });
}

/**
 * @typedef {Object} RuleToggleRequest
 * @property {string} ruleId - 规则ID
 */

/**
 * @typedef {Object} RuleToggleResponse
 * @property {string} code - 返回码
 * @property {string} msg - 返回信息
 * @property {string} subCode - 子返回码
 * @property {string} subMsg - 子返回信息
 * @property {string} data - 返回数据
 * @property {number} total - 总条数
 */

/**
 * 派单规则启用/停用
 * @param {RuleToggleRequest} data - 操作参数
 * @returns {Promise<RuleToggleResponse>} 返回操作结果的Promise
 */
export function toggleRuleStatus(data) {
  return request({
    url: baseUrl + '/ops/rule/onOff',
    method: 'post',
    data: data,
  });
}

/**
 * @typedef {Object} RuleCreateRequest
 * @property {string} [ruleId] - 规则ID
 * @property {string} [merchantId] - 租户ID
 * @property {number} [enableStatus] - 启用状态：0规则停用(停止调度)，1规则启用(可被调度)
 * @property {string} ruleName - 规则名称
 * @property {string} [remark] - 备注信息
 * @property {string} checkRule - 校验规则：VOLTAGE电压/CURRENT电流/TEMPERATURE温度
 * @property {string} compareOperator - 比较操作符：GT大于/LT小于
 * @property {number} checkPercent - 校验阈值(%)
 */

/**
 * @typedef {Object} RuleCreateResponse
 * @property {string} code - 返回码
 * @property {string} msg - 返回信息
 * @property {string} subCode - 子返回码
 * @property {string} subMsg - 子返回信息
 * @property {string} data - 返回数据
 * @property {number} total - 总条数
 */

/**
 * 派单规则新增
 * @param {RuleCreateRequest} data - 规则数据
 * @returns {Promise<RuleCreateResponse>} 返回创建结果的Promise
 */
export function createRule(data) {
  return request({
    url: baseUrl + '/ops/rule/create',
    method: 'post',
    data: data,
  });
}

/**
 * @typedef {Object} RuleUpdateRequest
 * @property {string} ruleId - 规则ID
 * @property {string} [merchantId] - 租户ID
 * @property {number} [enableStatus] - 启用状态：0规则停用(停止调度)，1规则启用(可被调度)
 * @property {string} ruleName - 规则名称
 * @property {string} [remark] - 备注信息
 * @property {string} checkRule - 校验规则：VOLTAGE电压/CURRENT电流/TEMPERATURE温度
 * @property {string} compareOperator - 比较操作符：GT大于/LT小于
 * @property {number} checkPercent - 校验阈值(%)
 */

/**
 * @typedef {Object} RuleUpdateResponse
 * @property {string} code - 返回码
 * @property {string} msg - 返回信息
 * @property {string} subCode - 子返回码
 * @property {string} subMsg - 子返回信息
 * @property {string} data - 返回数据
 * @property {number} total - 总条数
 */

/**
 * 派单规则修改
 * @param {RuleUpdateRequest} data - 规则数据
 * @returns {Promise<RuleUpdateResponse>} 返回修改结果的Promise
 */
export function updateRule(data) {
  return request({
    url: baseUrl + '/ops/rule/update',
    method: 'post',
    data: data,
  });
}

/**
 * 批量删除派单规则
 * @param {Object} params - 删除参数
 * @param {Array<string>} params.ruleIds - 规则ID数组
 * @returns {Promise} 返回删除结果的Promise
 */
export function deleteRules(params) {
  return request({
    url: baseUrl + '/rule/delete',
    method: 'post',
    data: params,
  });
}

/**
 * 导出派单规则
 * @param {Object} params - 导出参数
 * @param {string} [params.ruleCode] - 规则编号
 * @param {string} [params.ruleName] - 规则名称
 * @param {string} [params.createTimeBeg] - 创建时间开始
 * @param {string} [params.createTimeEnd] - 创建时间结束
 * @param {number} [params.enableStatus] - 启用状态
 * @returns {Promise} 返回导出文件的Promise
 */
export function exportRules(params) {
  return request({
    url: baseUrl + '/rule/export',
    method: 'post',
    data: params,
    responseType: 'blob',
  });
}

/**
 * 获取规则统计信息
 * @param {Object} params - 查询参数
 * @param {string} [params.merchantId] - 租户ID
 * @returns {Promise} 返回统计数据的Promise
 */
export function getRuleStatistics(params) {
  return request({
    url: baseUrl + '/rule/statistics',
    method: 'post',
    data: params,
  });
}

/**
 * 获取规则执行日志
 * @param {Object} params - 查询参数
 * @param {string} params.ruleId - 规则ID
 * @param {number} [params.pageNum=1] - 当前页码，默认为1
 * @param {number} [params.pageSize=10] - 每页显示条数，默认为10
 * @param {string} [params.startTime] - 开始时间
 * @param {string} [params.endTime] - 结束时间
 * @returns {Promise} 返回执行日志的Promise
 */
export function getRuleExecutionLogs(params) {
  return request({
    url: baseUrl + '/rule/logs',
    method: 'post',
    data: params,
  });
}

/**
 * 复制派单规则
 * @param {Object} data - 复制参数
 * @param {string} data.ruleId - 源规则ID
 * @param {string} data.newRuleName - 新规则名称
 * @returns {Promise} 返回复制结果的Promise
 */
export function copyRule(data) {
  return request({
    url: baseUrl + '/rule/copy',
    method: 'post',
    data: data,
  });
}

/**
 * 批量启用/停用规则
 * @param {Object} data - 操作参数
 * @param {Array<string>} data.ruleIds - 规则ID数组
 * @param {number} data.enableStatus - 启用状态：0停用，1启用
 * @returns {Promise} 返回操作结果的Promise
 */
export function batchToggleRuleStatus(data) {
  return request({
    url: baseUrl + '/rule/batchToggle',
    method: 'post',
    data: data,
  });
}

/**
 * 获取规则模板列表
 * @param {Object} params - 查询参数
 * @returns {Promise} 返回模板列表的Promise
 */
export function getRuleTemplates(params) {
  return request({
    url: baseUrl + '/rule/templates',
    method: 'post',
    data: params,
  });
}

/**
 * 根据模板创建规则
 * @param {Object} data - 创建参数
 * @param {string} data.templateId - 模板ID
 * @param {string} data.ruleName - 规则名称
 * @param {Object} data.customParams - 自定义参数
 * @returns {Promise} 返回创建结果的Promise
 */
export function createRuleFromTemplate(data) {
  return request({
    url: baseUrl + '/rule/createFromTemplate',
    method: 'post',
    data: data,
  });
}

/**
 * 验证规则配置
 * @param {Object} data - 验证参数
 * @param {string} data.checkRule - 校验规则
 * @param {string} data.compareOperator - 比较操作符
 * @param {number} data.checkPercent - 校验阈值
 * @returns {Promise} 返回验证结果的Promise
 */
export function validateRuleConfig(data) {
  return request({
    url: baseUrl + '/rule/validate',
    method: 'post',
    data: data,
  });
}
