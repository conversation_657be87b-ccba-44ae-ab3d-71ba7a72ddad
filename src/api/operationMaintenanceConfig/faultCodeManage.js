/**
 * @file 故障代码管理API
 */

import request from '@/utils/request';

const baseUrl = '/vehicle-charging-admin';

/**
 * @typedef {Object} FaultCodePageRequest
 * @property {number} [pageNum=1] - 当前页码，默认为1
 * @property {number} [pageSize=10] - 每页显示条数，默认为10
 * @property {string} [faultCode] - 故障代码
 * @property {string} [faultName] - 故障名称
 * @property {string} [faultLevel] - 故障级别：1一般/2严重/3紧急
 * @property {string} [deviceType] - 设备类型：1充电桩/2配电柜/3监控设备
 * @property {string} [createTimeBeg] - 创建时间开始
 * @property {string} [createTimeEnd] - 创建时间结束
 * @property {number} [enableStatus] - 启用状态：0停用，1启用
 * @property {string} [merchantId] - 租户ID
 */

/**
 * @typedef {Object} FaultCodeInfo
 * @property {string} faultCodeId - 故障代码ID
 * @property {string} merchantId - 租户ID
 * @property {string} faultCode - 故障代码
 * @property {string} faultName - 故障名称
 * @property {string} faultLevel - 故障级别：1一般/2严重/3紧急
 * @property {string} deviceType - 设备类型：1充电桩/2配电柜/3监控设备
 * @property {number} enableStatus - 启用状态：0停用/1启用
 * @property {string} remark - 备注信息
 * @property {string} createTime - 创建时间
 * @property {string} updateTime - 修改时间
 * @property {string} createBy - 创建人
 * @property {string} updateBy - 修改人
 */

/**
 * @typedef {Object} FaultCodePageResponse
 * @property {string} code - 返回码
 * @property {string} msg - 返回信息
 * @property {string} subCode - 子返回码
 * @property {string} subMsg - 子返回信息
 * @property {Array<FaultCodeInfo>} data - 返回数据
 * @property {number} total - 总条数
 */

// 模拟数据
const mockData = [
  {
    faultCodeId: '1',
    merchantId: 'default',
    faultCode: 'FC001',
    faultName: '充电桩通信故障',
    faultLevel: '2',
    deviceType: '1',
    enableStatus: 1,
    createTime: '2024-12-01 10:30:00',
    updateTime: '2024-12-01 10:30:00',
    createBy: '张三',
    updateBy: '张三',
    remark: '充电桩与后台通信中断',
  },
  {
    faultCodeId: '2',
    merchantId: 'default',
    faultCode: 'FC002',
    faultName: '配电柜过载保护',
    faultLevel: '3',
    deviceType: '2',
    enableStatus: 1,
    createTime: '2024-12-01 11:15:00',
    updateTime: '2024-12-01 11:15:00',
    createBy: '李四',
    updateBy: '李四',
    remark: '配电柜负载超过额定值',
  },
  {
    faultCodeId: '3',
    merchantId: 'default',
    faultCode: 'FC003',
    faultName: '监控设备离线',
    faultLevel: '1',
    deviceType: '3',
    enableStatus: 0,
    createTime: '2024-12-01 12:00:00',
    updateTime: '2024-12-01 12:00:00',
    createBy: '王五',
    updateBy: '王五',
    remark: '监控摄像头网络连接异常',
  },
];

/**
 * 故障代码分页查询
 * @param {FaultCodePageRequest} params - 查询参数
 * @returns {Promise<FaultCodePageResponse>} 返回包含故障代码列表的Promise
 */
export function getFaultCodePage(params) {
  // 开发环境使用模拟数据
  if (process.env.NODE_ENV === 'development') {
    return new Promise((resolve) => {
      setTimeout(() => {
        const {
          pageNum = 1,
          pageSize = 10,
          faultCode,
          faultName,
          faultLevel,
          deviceType,
          enableStatus,
        } = params;

        // 模拟筛选逻辑
        let filteredData = [...mockData];
        if (faultCode) {
          filteredData = filteredData.filter((item) =>
            item.faultCode.includes(faultCode)
          );
        }
        if (faultName) {
          filteredData = filteredData.filter((item) =>
            item.faultName.includes(faultName)
          );
        }
        if (faultLevel) {
          filteredData = filteredData.filter(
            (item) => item.faultLevel === faultLevel
          );
        }
        if (deviceType) {
          filteredData = filteredData.filter(
            (item) => item.deviceType === deviceType
          );
        }
        if (enableStatus !== undefined && enableStatus !== '') {
          filteredData = filteredData.filter(
            (item) => item.enableStatus === Number(enableStatus)
          );
        }

        const start = (pageNum - 1) * pageSize;
        const end = start + pageSize;
        const result = filteredData.slice(start, end);

        resolve([
          null,
          {
            code: '200',
            msg: '操作成功',
            data: result,
            total: filteredData.length,
          },
        ]);
      }, 500);
    });
  }

  return request({
    url: baseUrl + '/ops/faultCode/page',
    method: 'post',
    data: params,
  });
}

// 兼容旧方法名
export const getFaultCodeList = getFaultCodePage;

/**
 * @typedef {Object} FaultCodeDetailRequest
 * @property {string} faultCodeId - 故障代码ID
 */

/**
 * @typedef {Object} FaultCodeDetailResponse
 * @property {string} code - 返回码
 * @property {string} msg - 返回信息
 * @property {string} subCode - 子返回码
 * @property {string} subMsg - 子返回信息
 * @property {FaultCodeInfo} data - 返回数据
 * @property {number} total - 总条数
 */

/**
 * 故障代码详情
 * @param {FaultCodeDetailRequest} data - 查询参数
 * @returns {Promise<FaultCodeDetailResponse>} 返回故障代码详情数据的Promise
 */
export function getFaultCodeDetail(data) {
  // 开发环境使用模拟数据
  if (process.env.NODE_ENV === 'development') {
    return new Promise((resolve) => {
      setTimeout(() => {
        const item = mockData.find(
          (item) => item.faultCodeId === data.faultCodeId
        );
        resolve([
          null,
          {
            code: '200',
            msg: '操作成功',
            data: item || null,
            total: item ? 1 : 0,
          },
        ]);
      }, 300);
    });
  }

  return request({
    url: baseUrl + '/ops/faultCode/detail',
    method: 'post',
    data: data,
  });
}

/**
 * @typedef {Object} FaultCodeToggleRequest
 * @property {string} faultCodeId - 故障代码ID
 */

/**
 * @typedef {Object} FaultCodeToggleResponse
 * @property {string} code - 返回码
 * @property {string} msg - 返回信息
 * @property {string} subCode - 子返回码
 * @property {string} subMsg - 子返回信息
 * @property {string} data - 返回数据
 * @property {number} total - 总条数
 */

/**
 * 故障代码启用/停用
 * @param {FaultCodeToggleRequest} data - 操作参数
 * @returns {Promise<FaultCodeToggleResponse>} 返回操作结果的Promise
 */
export function toggleFaultCodeStatus(data) {
  // 开发环境使用模拟数据
  if (process.env.NODE_ENV === 'development') {
    return new Promise((resolve) => {
      setTimeout(() => {
        const index = mockData.findIndex(
          (item) => item.faultCodeId === data.faultCodeId
        );
        if (index !== -1) {
          mockData[index].enableStatus =
            mockData[index].enableStatus === 1 ? 0 : 1;
          mockData[index].updateTime = new Date().toLocaleString();
        }
        resolve([
          null,
          {
            code: '200',
            msg: '操作成功',
            data: 'success',
            total: 0,
          },
        ]);
      }, 300);
    });
  }

  return request({
    url: baseUrl + '/ops/faultCode/onOff',
    method: 'post',
    data: data,
  });
}

/**
 * @typedef {Object} FaultCodeCreateRequest
 * @property {string} [faultCodeId] - 故障代码ID
 * @property {string} [merchantId] - 租户ID
 * @property {number} [enableStatus] - 启用状态：0停用，1启用
 * @property {string} faultCode - 故障代码
 * @property {string} faultName - 故障名称
 * @property {string} faultLevel - 故障级别：1一般/2严重/3紧急
 * @property {string} deviceType - 设备类型：1充电桩/2配电柜/3监控设备
 * @property {string} [remark] - 备注信息
 */

/**
 * @typedef {Object} FaultCodeCreateResponse
 * @property {string} code - 返回码
 * @property {string} msg - 返回信息
 * @property {string} subCode - 子返回码
 * @property {string} subMsg - 子返回信息
 * @property {string} data - 返回数据
 * @property {number} total - 总条数
 */

/**
 * 故障代码新增
 * @param {FaultCodeCreateRequest} data - 故障代码数据
 * @returns {Promise<FaultCodeCreateResponse>} 返回创建结果的Promise
 */
export function createFaultCode(data) {
  // 开发环境使用模拟数据
  if (process.env.NODE_ENV === 'development') {
    return new Promise((resolve) => {
      setTimeout(() => {
        const newItem = {
          faultCodeId: Date.now().toString(),
          merchantId: 'default',
          enableStatus: data.enableStatus || 1,
          createTime: new Date().toLocaleString(),
          updateTime: new Date().toLocaleString(),
          createBy: '当前用户',
          updateBy: '当前用户',
          ...data,
        };
        mockData.push(newItem);
        resolve([
          null,
          {
            code: '200',
            msg: '操作成功',
            data: newItem.faultCodeId,
            total: 0,
          },
        ]);
      }, 300);
    });
  }

  return request({
    url: baseUrl + '/ops/faultCode/create',
    method: 'post',
    data: data,
  });
}

// 兼容旧方法名
export const addFaultCode = createFaultCode;

/**
 * @typedef {Object} FaultCodeUpdateRequest
 * @property {string} faultCodeId - 故障代码ID
 * @property {string} [merchantId] - 租户ID
 * @property {number} [enableStatus] - 启用状态：0停用，1启用
 * @property {string} faultCode - 故障代码
 * @property {string} faultName - 故障名称
 * @property {string} faultLevel - 故障级别：1一般/2严重/3紧急
 * @property {string} deviceType - 设备类型：1充电桩/2配电柜/3监控设备
 * @property {string} [remark] - 备注信息
 */

/**
 * @typedef {Object} FaultCodeUpdateResponse
 * @property {string} code - 返回码
 * @property {string} msg - 返回信息
 * @property {string} subCode - 子返回码
 * @property {string} subMsg - 子返回信息
 * @property {string} data - 返回数据
 * @property {number} total - 总条数
 */

/**
 * 故障代码修改
 * @param {FaultCodeUpdateRequest} data - 故障代码数据
 * @returns {Promise<FaultCodeUpdateResponse>} 返回修改结果的Promise
 */
export function updateFaultCode(data) {
  // 开发环境使用模拟数据
  if (process.env.NODE_ENV === 'development') {
    return new Promise((resolve) => {
      setTimeout(() => {
        const index = mockData.findIndex(
          (item) => item.faultCodeId === data.faultCodeId
        );
        if (index !== -1) {
          mockData[index] = {
            ...mockData[index],
            ...data,
            updateTime: new Date().toLocaleString(),
            updateBy: '当前用户',
          };
        }
        resolve([
          null,
          {
            code: '200',
            msg: '操作成功',
            data: 'success',
            total: 0,
          },
        ]);
      }, 300);
    });
  }

  return request({
    url: baseUrl + '/ops/faultCode/update',
    method: 'post',
    data: data,
  });
}

/**
 * 批量删除故障代码
 * @param {Object} params - 删除参数
 * @param {Array<string>} params.faultCodeIds - 故障代码ID数组
 * @returns {Promise} 返回删除结果的Promise
 */
export function deleteFaultCodes(params) {
  // 开发环境使用模拟数据
  if (process.env.NODE_ENV === 'development') {
    return new Promise((resolve) => {
      setTimeout(() => {
        const { faultCodeIds } = params;
        faultCodeIds.forEach((id) => {
          const index = mockData.findIndex((item) => item.faultCodeId === id);
          if (index !== -1) {
            mockData.splice(index, 1);
          }
        });
        resolve([
          null,
          {
            code: '200',
            msg: '操作成功',
            data: 'success',
            total: 0,
          },
        ]);
      }, 300);
    });
  }

  return request({
    url: baseUrl + '/ops/faultCode/delete',
    method: 'post',
    data: params,
  });
}

/**
 * 删除单个故障代码
 * @param {Object} data - 删除参数
 * @param {string} data.faultCodeId - 故障代码ID
 * @returns {Promise} 返回删除结果的Promise
 */
export function deleteFaultCode(data) {
  return deleteFaultCodes({ faultCodeIds: [data.faultCodeId || data.id] });
}

/**
 * 导出故障代码
 * @param {Object} params - 导出参数
 * @param {string} [params.faultCode] - 故障代码
 * @param {string} [params.faultName] - 故障名称
 * @param {string} [params.faultLevel] - 故障级别
 * @param {string} [params.deviceType] - 设备类型
 * @param {string} [params.createTimeBeg] - 创建时间开始
 * @param {string} [params.createTimeEnd] - 创建时间结束
 * @param {number} [params.enableStatus] - 启用状态
 * @returns {Promise} 返回导出文件的Promise
 */
export function exportFaultCodes(params) {
  return request({
    url: baseUrl + '/ops/faultCode/export',
    method: 'post',
    data: params,
    responseType: 'blob',
  });
}

/**
 * 获取故障代码统计信息
 * @param {Object} params - 查询参数
 * @param {string} [params.merchantId] - 租户ID
 * @returns {Promise} 返回统计数据的Promise
 */
export function getFaultCodeStatistics(params) {
  return request({
    url: baseUrl + '/ops/faultCode/statistics',
    method: 'post',
    data: params,
  });
}

/**
 * 批量启用/停用故障代码
 * @param {Object} data - 操作参数
 * @param {Array<string>} data.faultCodeIds - 故障代码ID数组
 * @param {number} data.enableStatus - 启用状态：0停用，1启用
 * @returns {Promise} 返回操作结果的Promise
 */
export function batchToggleFaultCodeStatus(data) {
  // 开发环境使用模拟数据
  if (process.env.NODE_ENV === 'development') {
    return new Promise((resolve) => {
      setTimeout(() => {
        const { faultCodeIds, enableStatus } = data;
        faultCodeIds.forEach((id) => {
          const index = mockData.findIndex((item) => item.faultCodeId === id);
          if (index !== -1) {
            mockData[index].enableStatus = enableStatus;
            mockData[index].updateTime = new Date().toLocaleString();
            mockData[index].updateBy = '当前用户';
          }
        });
        resolve([
          null,
          {
            code: '200',
            msg: '操作成功',
            data: 'success',
            total: 0,
          },
        ]);
      }, 300);
    });
  }

  return request({
    url: baseUrl + '/ops/faultCode/batchToggle',
    method: 'post',
    data: data,
  });
}

/**
 * 复制故障代码
 * @param {Object} data - 复制参数
 * @param {string} data.faultCodeId - 源故障代码ID
 * @param {string} data.newFaultCode - 新故障代码
 * @param {string} data.newFaultName - 新故障名称
 * @returns {Promise} 返回复制结果的Promise
 */
export function copyFaultCode(data) {
  // 开发环境使用模拟数据
  if (process.env.NODE_ENV === 'development') {
    return new Promise((resolve) => {
      setTimeout(() => {
        const sourceItem = mockData.find(
          (item) => item.faultCodeId === data.faultCodeId
        );
        if (sourceItem) {
          const newItem = {
            ...sourceItem,
            faultCodeId: Date.now().toString(),
            faultCode: data.newFaultCode,
            faultName: data.newFaultName,
            createTime: new Date().toLocaleString(),
            updateTime: new Date().toLocaleString(),
            createBy: '当前用户',
            updateBy: '当前用户',
          };
          mockData.push(newItem);
        }
        resolve([
          null,
          {
            code: '200',
            msg: '操作成功',
            data: 'success',
            total: 0,
          },
        ]);
      }, 300);
    });
  }

  return request({
    url: baseUrl + '/ops/faultCode/copy',
    method: 'post',
    data: data,
  });
}
