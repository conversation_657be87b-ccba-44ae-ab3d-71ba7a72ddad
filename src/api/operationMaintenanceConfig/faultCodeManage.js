import request from '@/utils/request';

const baseUrl = '/vehicle-charging-admin';

// 模拟数据
const mockData = [
  {
    id: '1',
    faultCode: 'FC001',
    faultName: '充电桩通信故障',
    faultLevel: '2',
    deviceType: '1',
    faultStatus: '1',
    createTime: '2024-12-01 10:30:00',
    createUser: '张三',
    remark: '充电桩与后台通信中断',
  },
  {
    id: '2',
    faultCode: 'FC002',
    faultName: '配电柜过载保护',
    faultLevel: '3',
    deviceType: '2',
    faultStatus: '1',
    createTime: '2024-12-01 11:15:00',
    createUser: '李四',
    remark: '配电柜负载超过额定值',
  },
  {
    id: '3',
    faultCode: 'FC003',
    faultName: '监控设备离线',
    faultLevel: '1',
    deviceType: '3',
    faultStatus: '0',
    createTime: '2024-12-01 12:00:00',
    createUser: '王五',
    remark: '监控摄像头网络连接异常',
  },
];

// 故障代码管理分页查询
export function getFaultCodeList(data) {
  // 开发环境使用模拟数据
  if (process.env.NODE_ENV === 'development') {
    return new Promise((resolve) => {
      setTimeout(() => {
        const { pageNum = 1, pageSize = 10 } = data;
        const start = (pageNum - 1) * pageSize;
        const end = start + pageSize;
        const result = mockData.slice(start, end);

        resolve([
          null,
          {
            data: result,
            total: mockData.length,
          },
        ]);
      }, 500);
    });
  }

  return request({
    url: baseUrl + '/ops/faultCode/page',
    method: 'post',
    data: data,
  });
}

// 故障代码管理新增
export function addFaultCode(data) {
  // 开发环境使用模拟数据
  if (process.env.NODE_ENV === 'development') {
    return new Promise((resolve) => {
      setTimeout(() => {
        const newItem = {
          id: Date.now().toString(),
          ...data,
          createTime: new Date().toLocaleString(),
          createUser: '当前用户',
        };
        mockData.push(newItem);
        resolve([null, { success: true }]);
      }, 300);
    });
  }

  return request({
    url: baseUrl + '/ops/faultCode/create',
    method: 'post',
    data: data,
  });
}

// 故障代码管理编辑
export function updateFaultCode(data) {
  // 开发环境使用模拟数据
  if (process.env.NODE_ENV === 'development') {
    return new Promise((resolve) => {
      setTimeout(() => {
        const index = mockData.findIndex((item) => item.id === data.id);
        if (index !== -1) {
          mockData[index] = { ...mockData[index], ...data };
        }
        resolve([null, { success: true }]);
      }, 300);
    });
  }

  return request({
    url: baseUrl + '/ops/faultCode/update',
    method: 'post',
    data: data,
  });
}

// 故障代码管理删除
export function deleteFaultCode(data) {
  // 开发环境使用模拟数据
  if (process.env.NODE_ENV === 'development') {
    return new Promise((resolve) => {
      setTimeout(() => {
        const index = mockData.findIndex((item) => item.id === data.id);
        if (index !== -1) {
          mockData.splice(index, 1);
        }
        resolve([null, { success: true }]);
      }, 300);
    });
  }

  return request({
    url: baseUrl + '/ops/faultCode/delete',
    method: 'post',
    data: data,
  });
}

// 获取故障代码详情
export function getFaultCodeDetail(data) {
  return request({
    url: baseUrl + '/ops/faultCode/detail',
    method: 'post',
    data: data,
  });
}
