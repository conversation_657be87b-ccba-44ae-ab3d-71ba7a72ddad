import request from '@/utils/request'

const baseUrl = '/vehicle-charging-admin'

const deptUrl = '/market/api/system'

// 班组管理分页查询
export function  getOperationMaintenanceTeamList(data) {
  return request({
    url: baseUrl + '/ops/team/page',
    method: 'post',
    data: data
  })
}

// 获取部门列表
export function getDeptList() {
  return request({
      url: deptUrl+ '/dept/list',
      method: 'get',
    })
}

// 获取用户列表
export function getUserList(query) {
  return request({
        url: deptUrl+ '/user/list',
        method: 'get',
        params: query
    })
}

// 班组管理新增
export function addOperationMaintenanceTeam(data) {
  return request({
    url: baseUrl + '/ops/team/create',
    method: 'post',
    data: data
  })
}

// 班组启用停用
export function updateOperationMaintenanceTeamStatus(data) {
    return request({
        url: baseUrl + '/ops/team/onOff',
        method: 'post',
        data: data
      })
}

// 获取班组详情
export function getOperationMaintenanceTeamDetail(data) {
    return request({
        url: baseUrl + '/ops/team/detail',
        method: 'post',
        data: data
    })
}