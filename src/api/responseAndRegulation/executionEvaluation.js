import request from '@/utils/request'

const baseUrl = '/vehicle-grid-admin'

// 参与运营商/场站/户号
export function getOperatorList(data) {
  return request({
    url: baseUrl + '/adjustment/calculate/queryParticipator',
    method: 'post',
    data: data
  })
}

// 需求维度收益及收益明细
export function getRequireIncome(data) {
  return request({
    url: baseUrl + '/adjustment/calculate/queryRequireIncome',
    method: 'post',
    data: data
  })
}

// 运营商维度收益及收益明细
export function getOperatorIncome(data) {
  return request({
    url: baseUrl + '/adjustment/calculate/queryOperatorIncome',
    method: 'post',
    data: data
  })
}

// 场站维度收益及收益明细
export function getStationIncome(data) {
  return request({
    url: baseUrl + '/adjustment/calculate/queryStationIncome',
    method: 'post',
    data: data
  })
}