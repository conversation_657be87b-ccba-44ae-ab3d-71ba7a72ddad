import request from '@/utils/request'

const baseUrl = '/vehicle-grid-admin'

// 申报详情
export function getDeclareDetail(data) {
  return request({
    url: baseUrl + '/adjustment/plan/record/declareDetail',
    method: 'post',
    data: data
  })
}

// 申报记录列表
export function getDeclareList(data) {
  return request({
    url: baseUrl + '/adjustment/plan/record/declareList',
    method: 'post',
    data: data
  })
}

// 申报计划详情
export function getDeclarePlanDetail(data) {
  return request({
    url: baseUrl + '/adjustment/plan/record/declarePlanDetail',
    method: 'post',
    data: data
  })
}

// 申报计划详情-有序充电
export function declarePlanOrderCharging(data) {
  return request({
    url: baseUrl + '/adjustment/plan/record/declarePlanOrderCharging',
    method: 'post',
    data: data
  })
}

// 申报计划详情-V2G
export function declarePlanV2G(data) {
  return request({
    url: baseUrl + '/adjustment/plan/record/declarePlanV2G',
    method: 'post',
    data: data
  })
}