import request from '@/utils/request'

const baseUrl = '/vehicle-charging-admin'

// 计费模版分页查询
export function getBillModelList(data) {
  return request({
    url: baseUrl + '/billing/template/page',
    method: 'post',
    data: data
  })
}

// 时段时段列表
export function getElectricPricePeriodList(data) {
  return request({
    url: baseUrl + '/billing/period/list/simple',
    method: 'post',
    data: data
  })
}

// 新增计费模版
export function addBillModel(data) {
  return request({
    url: baseUrl + '/billing/template/create',
    method: 'post',
    data: data
  })
}

// 计费模版详情
export function getBillModelDetail(data) {
  return request({
    url: baseUrl + '/billing/template/detail',
    method: 'post',
    data: data
  })
}

// 修改计费模版详情
export function updateBillModel(data) {
  return request({
    url: baseUrl + '/billing/template/update',
    method: 'post',
    data: data
  })
}

// 删除计费模版
export function deleteBillModel(data) {
  return request({
    url: baseUrl + '/billing/template/remove',
    method: 'post',
    data: data
  })
}