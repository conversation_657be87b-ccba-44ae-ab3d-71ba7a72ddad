/**
 * @file 早期预警管理API
 */

import request from '@/utils/request';

const baseUrl = '/vehicle-charging-admin';

/**
 * 获取价格阈值分页列表
 * @param {Object} params - 请求参数
 * @param {number} [params.pageNum=1] - 当前页码，默认为1
 * @param {number} [params.pageSize=10] - 每页显示条数，默认为10
 * @param {string} [params.stationId] - 站点ID
 * @param {string} [params.stationNo] - 站点编号
 * @param {string} [params.stationName] - 站点名称
 * @param {string} [params.city] - 城市
 * @param {string} [params.assetProperty] - 资产属性
 * @param {string} [params.stationType] - 站点类型
 * @returns {Promise} 返回包含价格阈值分页列表的Promise
 */
export function getPriceThresholdPage(params) {
  return request({
    url: baseUrl + '/warning/billing/priceThreshold/page',
    method: 'post',
    data: params,
  });
}

/**
 * 创建价格阈值
 * @param {Object} data - 请求数据
 * @param {Array<string>} [data.ids] - 主键id集合（批量更新时使用）
 * @param {Array<string>} [data.stationIds] - 关联场站ID集合（新增时使用）
 * @param {number} data.serviceTopThreshold - 尖 - 服务费阈值
 * @param {number} data.servicePeakThreshold - 峰 - 服务费阈值
 * @param {number} data.serviceFlatThreshold - 平 - 服务费阈值
 * @param {number} data.serviceValleyThreshold - 谷 - 服务费阈值
 * @returns {Promise} 返回创建结果的Promise
 */
export function createPriceThreshold(data) {
  return request({
    url: baseUrl + '/warning/billing/priceThreshold/create',
    method: 'post',
    data: data,
  });
}

/**
 * 批量修改价格阈值
 * @param {Object} data - 请求数据
 * @param {Array<string>} [data.ids] - 主键id集合（批量更新时使用）
 * @param {Array<string>} [data.stationIds] - 关联场站ID集合（新增时使用）
 * @param {number} data.serviceTopThreshold - 尖 - 服务费阈值
 * @param {number} data.servicePeakThreshold - 峰 - 服务费阈值
 * @param {number} data.serviceFlatThreshold - 平 - 服务费阈值
 * @param {number} data.serviceValleyThreshold - 谷 - 服务费阈值
 * @returns {Promise} 返回批量修改结果的Promise
 */
export function batchUpdatePriceThreshold(data) {
  return request({
    url: baseUrl + '/warning/billing/priceThreshold/batchUpdate',
    method: 'post',
    data: data,
  });
}

/**
 * 配置详情
 * @returns {Promise} 返回配置详情的Promise
 */
export function getBillingConfigDetail() {
  return request({
    url: baseUrl + '/warning/billing/detail',
    method: 'get',
  });
}

/**
 * 获取预警记录分页列表
 * @param {Object} params - 请求参数
 * @param {number} [params.pageNum=1] - 当前页码，默认为1
 * @param {number} [params.pageSize=10] - 每页显示条数，默认为10
 * @param {string} [params.notifyRecordBillingId] - 计费预警流水编号
 * @param {string} [params.pileNo] - 桩编号
 * @param {string} [params.pileName] - 桩名称
 * @param {string} [params.stationId] - 站点id
 * @param {string} [params.chcNo] - 充电计费编号
 * @param {string} [params.issueUser] - 下发人
 * @param {string} [params.warningTimeLeft] - 预警时间左区间
 * @param {string} [params.warningTimeRight] - 预警时间右区间
 * @returns {Promise} 返回包含预警记录分页列表的Promise
 */
export function getWarningRecordPage(params) {
  return request({
    url: baseUrl + '/warning/billing/record/page',
    method: 'post',
    data: params,
  });
}

/**
 * 导出预警记录
 * @param {Object} params - 请求参数
 * @param {number} [params.pageNum=1] - 当前页码，默认为1
 * @param {number} [params.pageSize=10] - 每页显示条数，默认为10
 * @param {string} [params.notifyRecordBillingId] - 计费预警流水编号
 * @param {string} [params.pileNo] - 桩编号
 * @param {string} [params.pileName] - 桩名称
 * @param {string} [params.stationId] - 站点id
 * @param {string} [params.chcNo] - 充电计费编号
 * @param {string} [params.issueUser] - 下发人
 * @param {string} [params.warningTimeLeft] - 预警时间左区间
 * @param {string} [params.warningTimeRight] - 预警时间右区间
 * @returns {Promise} 返回导出结果的Promise
 */
export function exportWarningRecord(params) {
  return request({
    url: baseUrl + '/warning/billing/record/export',
    method: 'post',
    data: params,
  });
}

// 模版列表
export function templateList(params) {
  return request({
    url: baseUrl + '/warning/message/template/list',
    method: 'post',
    data: params,
  });
}

// 规则信息配置 （新增/修改）
export function billingConfig(params) {
  return request({
    url: baseUrl + '/warning/billing/config',
    method: 'post',
    data: params,
  });
}

// 区域查询
export function areaList(params) {
  return request({
    url: baseUrl + '/area/list',
    method: 'post',
    data: params,
  });
}

// 计费预警记录 - 分页列表
export function billingPage(params) {
  return request({
    url: baseUrl + '/warning/record/billing/page',
    method: 'post',
    data: params,
  });
}

// 计费预警记录详情
export function recordDetail(params) {
  return request({
    url: baseUrl + '/warning/record/detail',
    method: 'post',
    data: params,
  });
}

// 订单预警记录 - 导出
export function orderExport(params) {
  return request({
    url: baseUrl + '/warning/record/order/export',
    method: 'post',
    data: params,
  });
}
