import request from '@/utils/request';
import { re } from 'mathjs';

const baseUrl = '/vehicle-charging-admin';

// 异常规则分页查询
export function getExruleList(data) {
  return request({
    url: baseUrl + '/exRule/pageQuery',
    method: 'post',
    data: data,
  });
}

// 新增异常规则
export function addExrule(data) {
  return request({
    url: baseUrl + '/exRule/add',
    method: 'post',
    data: data,
  });
}

// 获取异常规则详情
export function getExruleDetail(data) {
  return request({
    url: baseUrl + '/exRule/detail',
    method: 'post',
    data: data,
  });
}

// 编辑异常规则
export function editExrule(data) {
  return request({
    url: baseUrl + '/exRule/edit',
    method: 'post',
    data: data,
  });
}

// 异常规则删除
export function exruleDelete(data) {
  return request({
    url: baseUrl + '/exRule/delete',
    method: 'post',
    data,
  });
}

// 异常规则启用
export function exruleEnable(data) {
  return request({
    url: baseUrl + '/exRule/enable',
    method: 'post',
    data,
  });
}

// 异常规则停用
export function exruleDisable(data) {
  return request({
    url: baseUrl + '/exRule/disable',
    method: 'post',
    data,
  });
}

// 异常规则审核分页查询
export function getRuleReviewList(data) {
  return request({
    url: baseUrl + '/rule/review/pageQuery',
    method: 'post',
    data: data,
  });
}

// 异常规则详情
export function getRuleDetail(data) {
  return request({
    url: baseUrl + '/rule/review/detail',
    method: 'post',
    data: data,
  });
}

// 异常规则审核
export function ruleApprove(data) {
  return request({
    url: baseUrl + '/rule/review/approve',
    method: 'post',
    data: data,
  });
}

// 查询充电订单列表（分页）
export function getOrderList(data) {
  return request({
    url: baseUrl + '/order/page',
    method: 'post',
    data: data,
  });
}

// 获取订单详情
export function getOrderDetail(data) {
  return request({
    url: baseUrl + '/order/detail',
    method: 'post',
    data: data,
  });
}

// 获取异常规则列表 不分页
export function getAbnormalRuleList(data) {
  return request({
    url: baseUrl + '/exRule/listQuery',
    method: 'post',
    data: data,
  });
}

// 标记异常订单
export function markAbnormal(data) {
  return request({
    url: baseUrl + '/exception/orderRecord/markException',
    method: 'post',
    data: data,
  });
}

// 取消订单
export function cancelOrder(data) {
  return request({
    url: baseUrl + '/exception/orderRecord/cancel',
    method: 'post',
    data: data,
  });
}

// 获取异常订单详情
export function getAbnormalOrderDetail(data) {
  return request({
    url: baseUrl + '/exception/orderRecord/detail',
    method: 'post',
    data: data,
  });
}

// 异常订单处理
export function abnormalOrderHandle(data) {
  return request({
    url: baseUrl + '/exception/orderRecord/handle',
    method: 'post',
    data: data,
  });
}

// 异常订单处理审核
export function abnormalOrderHandleAudit(data) {
  return request({
    url: baseUrl + '/exception/orderRecord/review',
    method: 'post',
    data: data,
  });
}

// 获取异常订单列表
export function getAbnormalOrderList(data) {
  return request({
    url: baseUrl + '/exception/orderRecord/page',
    method: 'post',
    data: data,
  });
}
