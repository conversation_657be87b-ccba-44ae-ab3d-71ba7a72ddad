import request from '@/utils/request';

const baseUrl = '/vehicle-charging-admin';

// 充电卡管理-分页查询
export function getCardPage(data) {
  return request({
    url: baseUrl + '/card/page',
    method: 'post',
    data: data,
  });
}

// 充电卡管理-导出
export function getCardExport(data) {
  return request({
    url: baseUrl + '/card/export',
    method: 'post',
    data: data,
  });
}

// 充电卡管理-退卡
export function cardRefundCard(data) {
  return request({
    url: baseUrl + '/card/refundCard',
    method: 'post',
    data: data,
  });
}

// 充电卡管理-绑定用户
export function cardBindUser(data) {
  return request({
    url: baseUrl + '/card/bindUser',
    method: 'post',
    data: data,
  });
}

// 充电卡管理-解除绑定
export function cardRelieve(data) {
  return request({
    url: baseUrl + '/card/relieve',
    method: 'post',
    data: data,
  });
}

// 充电卡管理-充值
export function cardRecharge(data) {
  return request({
    url: baseUrl + '/card/recharge',
    method: 'post',
    data: data,
  });
}

// 充电卡管理-挂失
export function cardLoss(data) {
  return request({
    url: baseUrl + '/card/loss',
    method: 'post',
    data: data,
  });
}

// 充电卡管理-补卡
export function cardReplacementCard(data) {
  return request({
    url: baseUrl + '/card/replacementCard',
    method: 'post',
    data: data,
  });
}

// 充电卡管理-注销
export function cardLogout(data) {
  return request({
    url: baseUrl + '/card/logout',
    method: 'post',
    data: data,
  });
}

// 充电卡管理-取消挂失
export function cardCancel(data) {
  return request({
    url: baseUrl + '/card/cancel',
    method: 'post',
    data: data,
  });
}

// 充电卡管理-电卡流水
export function cardWater(data) {
  return request({
    url: baseUrl + '/card/cardWater',
    method: 'post',
    data: data,
  });
}

// 充电卡管理-电卡流水导出
export function cardWaterExport(data) {
  return request({
    url: baseUrl + '/card/cardWater/export',
    method: 'post',
    data: data,
  });
}

// 充电卡业务记录-分页查询
export function cardBizTypePage(data) {
  return request({
    url: baseUrl + '/card/bizType/page',
    method: 'post',
    data: data,
  });
}

// 充电卡业务记录-导出
export function cardBizTypeExport(data) {
  return request({
    url: baseUrl + '/card/bizType/export',
    method: 'post',
    data: data,
  });
}

// 充电卡购卡记录分页查询
export function queryCardPurchasePage(data) {
  return request({
    url: baseUrl + '/card/record/purchase/page',
    method: 'post',
    data: data,
  });
}

// 充电卡购卡记录-购卡
export function cardPurchaseCard(data) {
  return request({
    url: baseUrl + '/card/record/purchase/purchaseCard',
    method: 'post',
    data: data,
  });
}

// 充电卡购卡记录-退卡详情
export function cardRefundCardDetail(chargeCardId) {
  return request({
    url: baseUrl + `/card/record/purchase/refundCard/${chargeCardId}`,
    method: 'get',
    params: {},
  });
}

// 充电卡购卡记录-退卡
export function cardPurchaseRefundCard(data) {
  return request({
    url: baseUrl + '/card/record/purchase/refundCard',
    method: 'post',
    data: data,
  });
}

// 充电卡购卡记录-导出
export function cardPurchaseCardExport(data) {
  return request({
    url: baseUrl + '/card/record/purchase/export',
    method: 'post',
    data: data,
  });
}

// 充电卡退卡记录-分页
export function cardReturnPage(data) {
  return request({
    url: baseUrl + '/card/record/return/page',
    method: 'post',
    data: data,
  });
}

// 充电卡退卡记录-导出
export function cardReturnExport(data) {
  return request({
    url: baseUrl + '/card/record/return/export',
    method: 'post',
    data: data,
  });
}

// 充电卡购买人列表
export function enterpriseList(data) {
  return request({
    url: baseUrl + '/card/record/enterprise/list',
    method: 'post',
    data: data,
  });
}

// 企业员工不分页查询
export function queryEnterpriseUser(data) {
  return request({
    url: baseUrl + '/enterpriseUser/queryList',
    method: 'post',
    data: data,
  });
}
