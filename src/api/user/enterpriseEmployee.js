import request from '@/utils/request';

const baseUrl = '/vehicle-charging-admin';

// 企业用户分页查询
export function queryPage(data) {
  return request({
    url: baseUrl + '/enterpriseUser/queryPage',
    method: 'post',
    data: data,
  });
}

// 企业客户不分页查询
export function queryEnterpriseList(data) {
  return request({
    url: baseUrl + '/enterprise/enterpriseList',
    method: 'post',
    data: data,
  });
}

// 企业用户状态批量修改
export function batchModifyStatus(data) {
  return request({
    url: baseUrl + '/enterpriseUser/batchModifyStatus',
    method: 'post',
    data: data,
  });
}

// 企业客户删除
export function enterpriseUserRemove(data) {
  return request({
    url: baseUrl + '/enterpriseUser/remove',
    method: 'post',
    data: data,
  });
}

// 企业用户新增
export function enterpriseUserAdd(data) {
  return request({
    url: baseUrl + '/enterpriseUser/add',
    method: 'post',
    data: data,
  });
}

// 企业用户编辑
export function enterpriseUserEdit(data) {
  return request({
    url: baseUrl + '/enterpriseUser/edit',
    method: 'post',
    data: data,
  });
}

// 企业用户明细
export function queryDetail(data) {
  return request({
    url: baseUrl + '/enterpriseUser/queryDetail',
    method: 'post',
    data: data,
  });
}

// 企业用户批量导入
export function batchImport(data) {
  return request({
    url: baseUrl + '/enterpriseUser/batchImport',
    method: 'post',
    data: data,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}


// 员工账户转帐
export function transferAccount(data) {
  return request({
    url: baseUrl + '/account/employee/transfer',
    method: 'post',
    data: data,
  });
} 