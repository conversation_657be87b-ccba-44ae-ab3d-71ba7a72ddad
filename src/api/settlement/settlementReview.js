import request from '@/utils/request'

const baseUrl = '/vehicle-grid-admin'

// 复核需求列表
export function getReviewList(data) {
  return request({
    url: baseUrl + '/review/list',
    method: 'post',
    data: data
  })
}


// 参与需求的运营商/场站/户号
export function getParticipatorList(data) {
  return request({
    url: baseUrl + '/review/queryParticipator',
    method: 'post',
    data: data
  })
}

// 需求维度收益
export function queryRequireIncome(data) {
  return request({
    url: baseUrl + '/review/queryRequireIncome',
    method: 'post',
    data: data
  })
}

// 复核
export function review(data) {
  return request({
    url: baseUrl + '/review/reviewed',
    method: 'post',
    data: data
  })
}

// 一键矫正
export function correct(data) {
  return request({
    url: baseUrl + '/review/corrected',
    method: 'post',
    data: data
  })
}