import request from '@/utils/request'

const baseUrl = '/vehicle-grid-admin'

// 运营商列表
export function getOperatorList(data) {
  return request({
    url: baseUrl + '/operators/list',
    method: 'post',
    data: data
  })
}

// 获取站点列表
export function getStationList(data) {
    return request({
      url: baseUrl + '/station/stationList',
      method: 'post',
      data: data
    })
}

// 获取全部结算规则模板
export function getSettleTemplateList(data) {
    return request({
        url: baseUrl + '/settleTemplate/listQuery',
        method: 'post',
        data: data
    })
}

// 结算配置-新增关联配置
export function createSettleBind(data) {
    return request({
        url: baseUrl + '/settleBind/create',
        method: 'post',
        data: data
    })
}

// 结算配置-分页查询
export function getSettleList(data) {
    return request({
        url: baseUrl + '/settleBind/pageQuery',
        method: 'post',
        data: data
    })
}