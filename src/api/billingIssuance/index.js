import request from '@/utils/request'
import { re } from 'mathjs'

const baseUrl = '/vehicle-charging-admin'

// 计费下发列表分页查询
export function getBillBindList(data) {
  return request({
    url: baseUrl + '/billing/binding/page',
    method: 'post',
    data: data
  })
}

// 计费模板下拉列表
export function getBillModelSelectList(data) {
  return request({
    url: baseUrl + '/billing/template/selectList',
    method: 'post',
    data: data
  })
}

// 计费模板下发
export function createBillIssuance(data) {
    return request({
        url: baseUrl + '/billing/binding/create',
        method: 'post',
        data: data
    })
}

// 计费模版单独下发
export function billTemplateIssuance(data) {
    return request({
        url: baseUrl + '/billing/template/issue',
        method: 'post',
        data: data
    })
}

// 取消下发
export function cancelBillIssuance(data) {
    return request({
        url: baseUrl + '/billing/binding/cancelIssue',
        method: 'post',
        data: data
    })
}   

// 重新下发计费模版
export function reBillIssuance(data) {
    return request({
        url: baseUrl + '/billing/binding/reissue',
        method: 'post',
        data: data
    })
}

// 获取计费下发详情
export function getBillIssuanceDetail(data) {
    return request({
        url: baseUrl + '/billing/binding/detail',
        method: 'post',
        data: data
    })
}

// 详情 -下发桩列表
export function getBillIssuancePileList(data) {
    return request({
        url: baseUrl + '/billing/binding/detail/issuePile',
        method: 'post',
        data: data
    })  
}

// 详情- 下发桩统计信息
export function getBillIssuancePileCount(data) {
    return request({
        url: baseUrl + '/billing/binding/detail/issuePile/statistic',
        method: 'post',
        data: data
    })
}

// 计费下发删除
export function deleteBillIssuance(data) {
    return request({
        url: baseUrl + '/billing/binding/delete',
        method: 'post',
        data: data
    })
}