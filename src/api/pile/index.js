import request from '@/utils/request'

const baseUrl = '/vehicle-charging-admin'

// 所属充电站列表
export function getStationList(data) {
  return request({
    url: baseUrl + '/station/stationNameList',
    method: 'post',
    data: data
  })
}

// 设备品牌
export function getBrandList(data) {
  return request({
    url: baseUrl + '/brand/list',
    method: 'post',
    data: data
  })
}

// 设备型号
export function getModelList(data) {
  return request({
    url: baseUrl + '/equipModelPile/list ',
    method: 'post',
    data: data
  })
}

// 产权单位
export function getAssetUnit(data) {
  return request({
    url: baseUrl + '/common/getAssetUnit',
    method: 'post',
    data: data
  })
}

// 充电桩分页查询
export function getPileList(data) {
  return request({
    url: baseUrl + '/pile/page',
    method: 'post',
    data: data
  })
}

// 充电桩数据汇总
export function getPileSummary(data) {
  return request({
    url: baseUrl + '/pile/pileCount',
    method: 'post',
    data: data
  })
}

// 充电桩详情
export function getPileDetail(data) {
  return request({
    url: baseUrl + '/pile/detail',
    method: 'post',
    data: data
  })    
}

// 充电枪品牌列表
export function getGunBrandList(data) {
    return request({
      url: baseUrl + '/brand/list',
      method: 'post',
      data: data
    })
}

// 调控配置详情
export function getPileControlDetail(data) {
    return request({
      url: baseUrl + '/pile/pileControlDetail',
      method: 'post',
      data: data
    })
}

// 编辑调控配置
export function editPileControl(data) {
    return request({
      url: baseUrl + '/pile/pileControl',
      method: 'post',
      data: data
    })
}

// 枪状态记录
export function getPileLog(data) {
  return request({
    url: baseUrl + '/pile/log',
    method: 'post',
    data: data
  })
}

// 充电枪品牌型号
export function getGunBrandModel(data) {
  return request({
    url: baseUrl + '/brand/listByType',
    method: 'post',
    data: data
  })
}

// 通讯模块协议
export function getProtocolList(data) {
  return request({
    url: baseUrl + '/protocol/list',
    method: 'post',
    data: data
  })
}

// 通讯模块协议
export function getComiiProtocolList(data) {
  return request({
    url: baseUrl + '/comiiProtocol/list',
    method: 'post',
    data: data
  })
}

// 协议规则新增
export function addProtocolRule(data) {
  return request({
    url: baseUrl + '/protocol/create',
    method: 'post',
    data: data
  })
}

// 新增充电桩
export function createPile(data) {
  return request({
    url: baseUrl + '/pile/create',
    method: 'post',
    data: data
  })
}

// 编辑充电桩
export function updatePile(data) {
  return request({
    url: baseUrl + '/pile/update',
    method: 'post',
    data: data
  })
}

// 获取营销户号列表
export function getConsList(data) {
  return request({
    url: baseUrl + '/station/consList',
    method: 'post',
    data: data
  })
}


// 桩状态变更管理-分页查询
export function getPileStatusList(data) {
  return request({
    url: baseUrl + '/pile/pileStatusPage',
    method: 'post',
    data: data
  })
}

// 桩状态变更
export function updatePileStatus(data) {
  return request({
    url: baseUrl + '/pile/pileStatusChange',
    method: 'post',
    data: data
  })
}

// 获取审批详情
export function getAuditDetail(data) {
  return request({
    url: baseUrl + '/pile/pileApprovalDetail',
    method: 'post',
    data: data
  })
} 

// 充电桩状态审批
export function pileAudit(data) {
  return request({
    url: baseUrl + '/approval/pile/audit',
    method: 'post',
    data: data
  })
}

// 批量删除充电桩
export function batchDeletePile(data) {
  return request({
    url: baseUrl + '/pile/remove',
    method: 'post',
    data: data
  })
} 

// 批量取消删除
export function batchCancelDelete(data) {
  return request({
    url: baseUrl + '/pile/cancelRemove',
    method: 'post',
    data: data
  })
}

// 批量下发
export function batchIssue(data) {
  return request({
    url: baseUrl + '/pile/issueQrCode',
    method: 'post',
    data: data
  })
}