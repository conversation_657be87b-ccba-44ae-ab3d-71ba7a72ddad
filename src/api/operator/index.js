import request from '@/utils/request'

const baseUrl = '/vehicle-charging-admin'

// 运营商列表
export function getOperatorsList(data) {
  return request({
    url: baseUrl + '/operators/page',
    method: 'post',
    data: data
  })
}

// 新增
export function createOperators(data) {
  return request({
    url: baseUrl + '/operators/create',
    method: 'post',
    data: data
  })
}

// 更新
export function updateOperators(data) {
  return request({
    url: baseUrl + '/operators/update',
    method: 'post',
    data: data
  })
}

// 详情
export function getOperatorsDetail(query) {
  return request({
    url: baseUrl + '/operators/detail/'+ query.operatorId,
    method: 'get',
    // params: query
  })
}

// 启用
export function enableOperators(query) {
  return request({
    url: baseUrl + '/operators/enable',
    method: 'get',
    params: query
  })
}

// 禁用
export function disableOperators(query) {
  return request({
    url: baseUrl + '/operators/disable',
    method: 'get',
    params: query
  })
}

// 删除
export function deleteOperators(query) {
  return request({
    url: baseUrl + '/operators/delete',
    method: 'get',
    params: query
  })
}

