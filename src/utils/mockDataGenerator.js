/**
 * Mock数据生成工具
 * @description 提供各种类型的Mock数据生成功能
 * <AUTHOR>
 * @since 2024-12-01
 */

/**
 * 基础数据生成器
 */
export class MockDataGenerator {
  constructor(options = {}) {
    this.locale = options.locale || 'zh_CN';
    this.seed = options.seed || 12345;
    this.random = this.createSeededRandom(this.seed);
  }

  /**
   * 创建带种子的随机数生成器
   * @param {number} seed - 随机种子
   * @returns {Function} 随机数生成函数
   */
  createSeededRandom(seed) {
    let currentSeed = seed;
    return function() {
      currentSeed = (currentSeed * 9301 + 49297) % 233280;
      return currentSeed / 233280;
    };
  }

  /**
   * 生成随机整数
   * @param {number} min - 最小值
   * @param {number} max - 最大值
   * @returns {number} 随机整数
   */
  randomInt(min = 0, max = 100) {
    return Math.floor(this.random() * (max - min + 1)) + min;
  }

  /**
   * 生成随机浮点数
   * @param {number} min - 最小值
   * @param {number} max - 最大值
   * @param {number} precision - 精度
   * @returns {number} 随机浮点数
   */
  randomFloat(min = 0, max = 100, precision = 2) {
    const value = this.random() * (max - min) + min;
    return parseFloat(value.toFixed(precision));
  }

  /**
   * 从数组中随机选择元素
   * @param {Array} array - 数组
   * @returns {any} 随机元素
   */
  randomChoice(array) {
    return array[Math.floor(this.random() * array.length)];
  }

  /**
   * 生成随机字符串
   * @param {number} length - 长度
   * @param {string} chars - 字符集
   * @returns {string} 随机字符串
   */
  randomString(length = 8, chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789') {
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(this.random() * chars.length));
    }
    return result;
  }

  /**
   * 生成UUID
   * @returns {string} UUID
   */
  uuid() {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
      const r = Math.floor(this.random() * 16);
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }

  /**
   * 生成中文姓名
   * @returns {string} 中文姓名
   */
  chineseName() {
    const surnames = ['王', '李', '张', '刘', '陈', '杨', '赵', '黄', '周', '吴', '徐', '孙', '胡', '朱', '高', '林', '何', '郭', '马', '罗'];
    const names = ['伟', '芳', '娜', '秀英', '敏', '静', '丽', '强', '磊', '军', '洋', '勇', '艳', '杰', '娟', '涛', '明', '超', '秀兰', '霞'];
    
    const surname = this.randomChoice(surnames);
    const name = this.randomChoice(names);
    return surname + name;
  }

  /**
   * 生成手机号
   * @returns {string} 手机号
   */
  phoneNumber() {
    const prefixes = ['130', '131', '132', '133', '134', '135', '136', '137', '138', '139', '150', '151', '152', '153', '155', '156', '157', '158', '159', '180', '181', '182', '183', '184', '185', '186', '187', '188', '189'];
    const prefix = this.randomChoice(prefixes);
    const suffix = this.randomString(8, '0123456789');
    return prefix + suffix;
  }

  /**
   * 生成邮箱
   * @returns {string} 邮箱
   */
  email() {
    const domains = ['qq.com', '163.com', '126.com', 'gmail.com', 'sina.com', 'sohu.com'];
    const username = this.randomString(6, 'abcdefghijklmnopqrstuvwxyz0123456789');
    const domain = this.randomChoice(domains);
    return `${username}@${domain}`;
  }

  /**
   * 生成公司名称
   * @returns {string} 公司名称
   */
  companyName() {
    const prefixes = ['湖南', '长沙', '株洲', '湘潭', '衡阳', '岳阳', '常德', '张家界', '益阳', '郴州'];
    const types = ['科技', '电力', '能源', '交通', '建设', '发展', '投资', '实业', '集团'];
    const suffixes = ['有限公司', '股份有限公司', '集团有限公司', '科技有限公司'];
    
    const prefix = this.randomChoice(prefixes);
    const type = this.randomChoice(types);
    const suffix = this.randomChoice(suffixes);
    
    return `${prefix}${type}${suffix}`;
  }

  /**
   * 生成地址
   * @returns {string} 地址
   */
  address() {
    const cities = ['长沙市', '株洲市', '湘潭市', '衡阳市', '岳阳市', '常德市', '张家界市', '益阳市', '郴州市', '永州市'];
    const districts = ['芙蓉区', '天心区', '岳麓区', '开福区', '雨花区', '望城区', '长沙县', '浏阳市', '宁乡市'];
    const streets = ['五一大道', '中山路', '解放路', '人民路', '建设路', '文艺路', '劳动路', '韶山路', '湘江路', '橘子洲大道'];
    
    const city = this.randomChoice(cities);
    const district = this.randomChoice(districts);
    const street = this.randomChoice(streets);
    const number = this.randomInt(1, 999);
    
    return `${city}${district}${street}${number}号`;
  }

  /**
   * 生成日期
   * @param {Date} start - 开始日期
   * @param {Date} end - 结束日期
   * @returns {string} 日期字符串
   */
  date(start = new Date(2024, 0, 1), end = new Date()) {
    const startTime = start.getTime();
    const endTime = end.getTime();
    const randomTime = startTime + this.random() * (endTime - startTime);
    return new Date(randomTime).toISOString().split('T')[0];
  }

  /**
   * 生成日期时间
   * @param {Date} start - 开始日期
   * @param {Date} end - 结束日期
   * @returns {string} 日期时间字符串
   */
  datetime(start = new Date(2024, 0, 1), end = new Date()) {
    const startTime = start.getTime();
    const endTime = end.getTime();
    const randomTime = startTime + this.random() * (endTime - startTime);
    return new Date(randomTime).toLocaleString('zh-CN');
  }

  /**
   * 生成订单编号
   * @returns {string} 订单编号
   */
  orderNumber() {
    const prefix = 'ORD';
    const timestamp = Date.now().toString().slice(-8);
    const random = this.randomString(4, '0123456789');
    return `${prefix}${timestamp}${random}`;
  }

  /**
   * 生成充电站编号
   * @returns {string} 充电站编号
   */
  stationCode() {
    const prefix = 'ST';
    const number = this.randomString(6, '0123456789');
    return `${prefix}${number}`;
  }

  /**
   * 生成充电桩编号
   * @returns {string} 充电桩编号
   */
  pileCode() {
    const prefix = 'CP';
    const number = this.randomString(8, '0123456789');
    return `${prefix}${number}`;
  }

  /**
   * 生成金额
   * @param {number} min - 最小值
   * @param {number} max - 最大值
   * @returns {number} 金额
   */
  amount(min = 10, max = 1000) {
    return this.randomFloat(min, max, 2);
  }

  /**
   * 生成电量
   * @param {number} min - 最小值
   * @param {number} max - 最大值
   * @returns {number} 电量
   */
  power(min = 5, max = 100) {
    return this.randomFloat(min, max, 2);
  }

  /**
   * 生成百分比
   * @param {number} min - 最小值
   * @param {number} max - 最大值
   * @returns {number} 百分比
   */
  percentage(min = 0, max = 100) {
    return this.randomInt(min, max);
  }
}

/**
 * 业务数据生成器
 */
export class BusinessDataGenerator extends MockDataGenerator {
  /**
   * 生成派单规则数据
   * @param {number} count - 生成数量
   * @returns {Array} 派单规则数据数组
   */
  generateDispatchRules(count = 20) {
    const checkRules = ['VOLTAGE', 'CURRENT', 'TEMPERATURE'];
    const operators = ['GT', 'LT', 'EQ'];
    const creators = ['系统管理员', '运维人员', '技术主管', '安全主管', '电气工程师'];

    return Array.from({ length: count }, (_, i) => ({
      ruleId: `rule_${String(i + 1).padStart(3, '0')}`,
      ruleCode: `RULE${String(i + 1).padStart(3, '0')}`,
      ruleName: `${this.randomChoice(['电压', '电流', '温度'])}${this.randomChoice(['异常', '过载', '监控', '保护', '检测'])}规则`,
      checkRule: this.randomChoice(checkRules),
      compareOperator: this.randomChoice(operators),
      checkPercent: this.randomInt(5, 98),
      enableStatus: this.randomChoice([0, 1]),
      merchantId: 'default',
      remark: `${this.randomChoice(['监控', '检测', '保护', '控制'])}${this.randomChoice(['设备', '系统', '环境'])}${this.randomChoice(['状态', '参数', '指标'])}`,
      createTime: this.datetime(new Date(2024, 10, 1), new Date()),
      updateTime: this.datetime(new Date(2024, 10, 1), new Date()),
      createBy: this.randomChoice(creators),
      updateBy: this.randomChoice(creators),
    }));
  }

  /**
   * 生成故障代码数据
   * @param {number} count - 生成数量
   * @returns {Array} 故障代码数据数组
   */
  generateFaultCodes(count = 20) {
    const deviceTypes = ['1', '2', '3']; // 1充电桩/2配电柜/3监控设备
    const faultLevels = ['1', '2', '3']; // 1一般/2严重/3紧急
    const prefixes = { '1': 'E', '2': 'C', '3': 'M' };
    const creators = ['系统管理员', '安全工程师', '维护人员', '技术人员', '电气工程师'];

    return Array.from({ length: count }, (_, i) => {
      const deviceType = this.randomChoice(deviceTypes);
      const prefix = prefixes[deviceType];
      
      return {
        faultCodeId: `fault_${String(i + 1).padStart(3, '0')}`,
        merchantId: 'default',
        faultCode: `${prefix}${String(i + 1).padStart(3, '0')}`,
        faultName: `${this.randomChoice(['通信', '电压', '电流', '温度', '连接', '传感器'])}${this.randomChoice(['异常', '故障', '过载', '超限', '失效'])}`,
        faultLevel: this.randomChoice(faultLevels),
        deviceType: deviceType,
        enableStatus: this.randomChoice([0, 1]),
        remark: `${this.randomChoice(['设备', '系统', '传感器', '线路'])}${this.randomChoice(['异常', '故障', '损坏', '失效'])}${this.randomChoice(['检测', '处理', '维修', '更换'])}`,
        createTime: this.datetime(new Date(2024, 10, 1), new Date()),
        updateTime: this.datetime(new Date(2024, 10, 1), new Date()),
        createBy: this.randomChoice(creators),
        updateBy: this.randomChoice(creators),
      };
    });
  }

  /**
   * 生成问题反馈数据
   * @param {number} count - 生成数量
   * @returns {Array} 问题反馈数据数组
   */
  generateProblemFeedbacks(count = 20) {
    const cities = ['长沙市', '株洲市', '湘潭市', '衡阳市', '岳阳市'];
    const stationTypes = ['火车站', '汽车站', '商业中心', '工业园', '住宅区'];
    const objTypes = ['公交', '城际公交', '出租车', '物流'];
    const creators = ['王五', '赵六', '孙七', '周八', '吴九'];

    return Array.from({ length: count }, (_, i) => {
      const city = this.randomChoice(cities);
      const stationType = this.randomChoice(stationTypes);
      
      return {
        proId: `prob_${String(i + 1).padStart(3, '0')}`,
        feedbackNo: `FB${new Date().getFullYear()}${String(new Date().getMonth() + 1).padStart(2, '0')}${String(new Date().getDate()).padStart(2, '0')}${String(i + 1).padStart(4, '0')}`,
        clearNo: `CLR${new Date().getFullYear()}${String(new Date().getMonth() + 1).padStart(2, '0')}${String(new Date().getDate()).padStart(2, '0')}${String(i + 1).padStart(4, '0')}`,
        stationName: `${city.replace('市', '')}${stationType}充电站`,
        cityName: city,
        toObjTypeName: this.randomChoice(objTypes),
        toObjName: `${city.replace('市', '')}${this.randomChoice(['公交集团', '城际公交', '出租车公司', '物流公司'])}`,
        feedbackCount: this.randomInt(1, 10),
        totalAmount: this.amount(50, 500),
        checkStatus: this.randomChoice(['0', '1']),
        createBy: this.randomChoice(creators),
        createTime: this.datetime(new Date(2024, 11, 1), new Date()),
        checkBy: this.randomChoice(['', '管理员', '审核员']),
        checkTime: this.randomChoice(['', this.datetime(new Date(2024, 11, 1), new Date())]),
      };
    });
  }
}

/**
 * 创建Mock数据文件
 * @param {string} filename - 文件名
 * @param {Object} data - 数据对象
 * @returns {string} JSON字符串
 */
export function createMockDataFile(filename, data) {
  const mockData = {
    ...data,
    _meta: {
      filename,
      generatedAt: new Date().toISOString(),
      version: '1.0.0',
    },
  };

  return JSON.stringify(mockData, null, 2);
}

/**
 * 生成标准Mock数据模板
 * @param {string} moduleName - 模块名称
 * @param {Array} list - 数据列表
 * @param {Object} options - 选项
 * @returns {Object} Mock数据模板
 */
export function createMockDataTemplate(moduleName, list, options = {}) {
  return {
    list,
    statistics: options.statistics || {},
    options: options.options || {},
    templates: options.templates || [],
    defaultResponse: 'success',
  };
}

// 创建默认实例
export const mockDataGenerator = new MockDataGenerator();
export const businessDataGenerator = new BusinessDataGenerator();

export default {
  MockDataGenerator,
  BusinessDataGenerator,
  createMockDataFile,
  createMockDataTemplate,
  mockDataGenerator,
  businessDataGenerator,
};
