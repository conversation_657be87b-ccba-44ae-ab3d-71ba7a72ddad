import JSEncrypt from 'jsencrypt/bin/jsencrypt.min'

// 密钥对生成 http://web.chacuo.net/netrsakeypair

const publicKey = 'MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCUXesXJnxER20v4V8T1f0t/6A0Kmyoc+VFMejuawEF93vfpdor+sEdtI2Yr+LaW6Tp+F8yFJnhweJlyPeFTZWq5v5WYLswZykZIkn5SNIOkKyYx1FihV1YWyrlx8/ngqFkNzgqlS0sOjKBPkug/TGOwZM8SDNYRVW7DE8xWjhEwwIDAQAB'

const privateKey = 'MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAJRd6xcmfERHbS/hXxPV/S3/oDQqbKhz5UUx6O5rAQX3e9+l2iv6wR20jZiv4tpbpOn4XzIUmeHB4mXI94VNlarm/lZguzBnKRkiSflI0g6QrJjHUWKFXVhbKuXHz+eCoWQ3OCqVLSw6MoE+S6D9MY7BkzxIM1hFVbsMTzFaOETDAgMBAAECgYEAgI0ftN9TrJV2tZXAm589zy090fUwUISakmm8OVfdex4D5rP9ZT3PNb3LMhCzrh7ZtR/O28Wdz03+q9KFJKskkawTJwJmokE9TOrik2mzTu9754frRVIn+QB7BJf84KLHlMTBUKbnh7IwjcjWkJypZfiF0O/1RZ5dGz+jiVBSJ4ECQQDYsQ/7GatV8kCkOU5pezjVTGSXoiapPZDOwKiNRRxth9m4HZPx5R5ztPPxSfr3aO4nlDqR+5mbSY243Hi+z89HAkEAr0fsWZho+NXVyV/OFxFU9kJ0FDBvvC5xvoeAllxfdidbRBJXRJsoRscgssQ4gpnNGVRxDyE20kkWCqvh2Yj0pQJACQABq+o7BYzURn3i1dq6V5Ges/WWKX1XIF88IPH35ky4LD9+P5PGmgOwBhuOguwmyBoN/LY4gQyjCESP90zmTQJBAKhmPVjG6PNI3iCefu4CpNIDfmp3LqJpLLaUkKHlFYH6xeBcjqNHPibh4TRe+Lvg7//J9OVIT9cwiJCwGkhZIFECQQCsH9e2WTw3QAM7TvyM7U8OYUi/VapDUK57j87A7ws1j3VMUuE2KIZLHVyh695HLx1U7m6DET1/+P/QxAc4jIV0'

// 加密
export function encrypt(txt) {
  const encryptor = new JSEncrypt()
  encryptor.setPublicKey(publicKey) // 设置公钥
  return encryptor.encrypt(txt) // 对数据进行加密
}

// 解密
export function decrypt(txt) {
  const encryptor = new JSEncrypt()
  encryptor.setPrivateKey(privateKey) // 设置私钥
  return encryptor.decrypt(txt) // 对数据进行解密
}

