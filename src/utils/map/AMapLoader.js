// 高德地图SDK加载器
export const Map_Key = 'd36a13128cb9b8ee92bcd0567098dfa6';
// 高德地图安全密钥
export const Security_Jscode = 'cba362edb71c5c1f2806ddd1297c544a';

class AMapLoader {
    static load(plugins = []) {
        return new Promise((resolve, reject) => {
            // 如果已经加载过，直接返回
            if (window.AMap) {
                resolve(window.AMap);
                return;
            }

            // 配置安全密钥
            window._AMapSecurityConfig = {
                securityJsCode: Security_Jscode,
            };

            // 动态创建script标签
            const script = document.createElement('script');
            script.type = 'text/javascript';
            script.async = true;
            script.src = 'https://webapi.amap.com/loader.js';

            script.onerror = () => {
                reject(new Error('高德地图SDK加载失败'));
            };

            script.onload = () => {
                window.AMapLoader.load({
                    key: Map_Key,
                    version: '2.0',
                    plugins: plugins,
                })
                .then(AMap => {
                    resolve(AMap);
                })
                .catch(error => {
                    reject(error);
                });
            };

            document.head.appendChild(script);
        });
    }
}

export default AMapLoader;