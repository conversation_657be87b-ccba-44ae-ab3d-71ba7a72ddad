class MapEventBus {
    constructor() {
      // 存储事件及其对应的回调函数
      this.events = {};
    }

  
    $on(eventName, callback) {
      // 检查该事件是否已经存在，如果不存在则创建一个空数组
      if (!this.events[eventName]) {
        this.events[eventName] = [];
      }
      // 将回调函数添加到该事件的回调函数数组中
      this.events[eventName].push(callback);
    }
  
    $emit(eventName,...args) {
      // 检查该事件是否存在
      if (this.events[eventName]) {
        // 遍历该事件的所有回调函数并执行
        this.events[eventName].forEach(callback => {
          callback(...args);
        });
      }
    }
  
    $off(eventName, callback) {
      // 检查该事件是否存在
      if (this.events[eventName]) {
        // 过滤掉要移除的回调函数
        this.events[eventName] = this.events[eventName].filter(cb => cb!== callback);
      }
    }
  }

  export default new MapEventBus();