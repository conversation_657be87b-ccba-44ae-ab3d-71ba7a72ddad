import <PERSON><PERSON><PERSON>_Json from '@/assets/bigScreen/hunan.json';
import HuNan_City_Json from '@/assets/bigScreen/hunan_city.json';
import HuNan_District_Json from '@/assets/bigScreen/hunan_district.json';
import MapBaseContext from './MapBaseContext';
import MapMarker from '@/views/stationInteraction/components/MapMarker.vue';
import StationMarker from '@/views/stationInteraction/components/StationMarker.vue';

import * as THREE from "three";
import { MapDisplayLevel } from './constants';

import MapEvent from './MapEvent';


const ExtrudeHeight = 40000;

export default class ThreeMapContext extends MapBaseContext {

    // _map = null;
    // _mapOptions = null;
    // _Amap = null;
    _gllayer = null; // 自定义的threejs图层
    camera = null;
    renderer = null;
    scene = null;
    paths = [];
    _satelliteLayer = null;
    // 二级图层，如果展示的省地图，这个图层则是城市，如果展示的是城市地图，这个图层则是区
    _secondLayer = null;
    // 当前地图层级
    currentLevel = MapDisplayLevel.Province;
    // 行政区对应的经纬度中心点
    codeCenterMap = new Map();
    // 地图是否允许下钻到市级
    allowDownMap = true;

     // 行政中心地图列表
     mapCodeList = [];

    constructor(id, options, config = {}) {
        super({ id, options });
        this.allowDownMap = config.allowDownMap ?? true;
        this.mapCodeList = config.mapCodeList ?? [];

        console.log('ThreeMapContext',config)
        this.init();
    }

    async init() {
        await this.initMap((AMap) => {
            this._satelliteLayer = new AMap.TileLayer.Satellite({
                zIndex: 2,
            });
            return {
                layers: [this._satelliteLayer],
            };
        });
        this.initSearch();
        const { _Amap, _map } = this;
        // 绘制省份边界线
        this.drawBoundaryLine(HuNan_Json);
        // 绘制城市边界线, 并添加marker
        this._secondLayer = this.drawBoundaryLine(HuNan_City_Json, {
            strokeColor: "#ffffff",
            strokeWeight: 2,
            fillColor: 'transparent',
            cursor: 'pointer',
        }, (geojson, polygon) => {
            const { adcode: currentAdcode, name, center } = geojson.properties;


            let stationNum = ''

            this.mapCodeList.forEach(item => {
                if (Number(item.areaCode) === currentAdcode) {
                    stationNum = item.stationNum
                }
            })

            this.addVueMarker(center, MapMarker, {
                content: stationNum|| 0,
            });
            // 添加文本标记
            const label = new _Amap.Text({
                text: name,
                position: center,
                anchor: 'top-center', // 设置文本标记锚点
                style: {
                    'background-color': 'transparent',
                    'border': 'none',
                    'color': '#fff'
                },
                map: _map
            });

            // 添加鼠标移入事件
            polygon.on('mouseover', function (event) {
                // 鼠标移入时改变样式
                polygon.setOptions({
                    fillColor: 'rgba(104, 148, 251, 0.47)',
                    fillOpacity: 0.47,
                    strokeWeight: 3
                });
            });

            // 添加鼠标移出事件
            polygon.on('mouseout', function (event) {
                // 鼠标移出时恢复样式
                polygon.setOptions({
                    fillColor: "transparent",
                    strokeWeight: 2
                });
            });

            polygon.on('click', () => {
                if(!this.allowDownMap) return;
                if (this.currentLevel === MapDisplayLevel.Province) {
                    const temLevel = this.currentLevel;
                    this.currentLevel = MapDisplayLevel.City;

                    let temMapCodeList = []
                    const upMapCodeList = this.mapCodeList
                    let upAdcode = ''
                    this.mapCodeList.forEach(item => {
                        if (Number(item.areaCode) === currentAdcode) {
                           temMapCodeList = item.children
                            upAdcode = item.provinceCode
                        }
                    })
                    this.mapCodeList = temMapCodeList

                    console.log('geojson',geojson)

                    this.downMapToCity(geojson);

                    MapEvent.$emit('areaClick',{areaCode: currentAdcode, level:temLevel, upAdcode, upMapCodeList,geojson});
                } else if (this.currentLevel === MapDisplayLevel.City) {
                    const temLevel = this.currentLevel;
                    this.currentLevel = MapDisplayLevel.District;

                    let temMapCodeList = []
                    const upMapCodeList = this.mapCodeList
                    let upAdcode = ''
                    this.mapCodeList.forEach(item => {
                        if (Number(item.areaCode) === currentAdcode) {
                            temMapCodeList = item.children
                            upAdcode = item.cityCode
                        }
                    })
                    this.mapCodeList = temMapCodeList

                    this.downMapToCity(geojson);

                    MapEvent.$emit('areaClick',{areaCode: currentAdcode, level:temLevel, upAdcode, upMapCodeList });

                    // 下钻到区级, 此时要展示2D地图
                    // 3D地图不支持切换到2D,目前方案是用第二个地图进行展示
                    MapEvent.$emit('cityClick', geojson);

                    if (this._gllayer) {
                        this._gllayer.setMap(null);
                    }

                    if (this._satelliteLayer) {
                        this._satelliteLayer.setMap(null);
                    }

                    // const layers = this._map.getLayers();
                    // layers.forEach(layer => layer.setMap(null));
                    this.removeAllMarkers();
                    // this._map.setMask(null);
                    // this._map.add([new _Amap.TileLayer()]);
                    // this._map.setZoomAndCenter(12, center);
                    // this._map.setPitch(0, false, 100);
                    // this._map.setMapStyle('amap://styles/grey');
                    // this._map.setFitView();


                    this._map = new _Amap.Map(this._mapOptions.id, {
                        center,
                        showLabel: true,
                        labelzIndex: 130,
                        zoom: 14.4,
                        mapStyle: "amap://styles/darkblue",
                    });
                    

                    // this.addVueMarker([112.559369, 29.524107], StationMarker, {
                    //     info: {
                    //         stationAddress: '西雅酒店停车场',
                    //         pileNum: 0.019869366043830716,
                    //     },
                    //     dark: true,
                    //     stationClick(){
                    //         MapEvent.$emit('stationClick', '哈哈哈');
                    //     },
                    // });
                    this.mapCodeList.forEach((item) => {
                        this.addVueMarker([item.lon,item.lat], StationMarker, {
                            info: {
                                stationAddress: item.stationAddress,
                                income: Number(item.income).toFixed(2),
                                fastNum: item.fastNum,
                                slowNum: item.slowNum,
                            },
                            dark: true,
                            stationClick(){
                                console.log( ' mapCodeList stationClick');
                                MapEvent.$emit('stationClick', item.stationNo);
                            },
                        });
                    })

                    // this.addVueMarker(['120.364340','31.490550'], StationMarker, {
                    //     info: {
                    //         stationAddress: "西雅酒店停车场",
                    //         pileNum: 0.70,
                    //         fastNum: 4094,
                    //         slowNum: 6312,
                    //     },
                    //     dark: true,
                    //     stationClick(){
                    //         MapEvent.$emit('stationClick', "0001");
                    //     },
                    // });

                }
            });
        });
        this.initPaths();
        // // 开始初始化threejs
        await this.initThreeLayer();
        this.drawMapShape();
        // this.drawMapBackground();
    }

    initPaths(coords) {
        const customCoords = this._map.customCoords;
        this.paths = customCoords.lngLatsToCoords(coords || this._mapOptions.options.mask)[0];
    }

    /**
     * 地图下钻到城市级别
     * 1、更新地图的mask为当前城市
     * 2、重新绘制城市边界线
     * 3、重新绘制threejs
     */
    downMapToCity(geojson) {
        // 清除threejs中的元素
        this.scene.clear();
        this.removeAllMarkers();
        // 更新地图的mask为当前城市
        const newMask = geojson.geometry.coordinates[0];
        this._map.setMask(newMask);
        // 获取当前所选城市的边界
        const cityBoundary = {
            type: 'FeatureCollection',
            features: [geojson],
        }
        this.drawBoundaryLine(cityBoundary);
        this.drawCityMap(HuNan_District_Json, geojson.properties.adcode);
        // // 重新绘制threejs
        this.initPaths(newMask);
        this.drawMapShape();
        // // this._map.setZoomAndCenter(8.5, geojson.properties.center);
        this._map.setFitView();
        this._map.setZoom(8.4);
    }

    initThreeLayer() {
        return new Promise(resolve => {
            const { _map, _Amap } = this;
            // 创建 GL 图层
            const gllayer = new _Amap.GLCustomLayer({
                // 图层的层级
                zIndex: 1,
                // 初始化的操作，创建图层过程中执行一次。
                init: (gl) => {
                    const container = document.getElementById(this._mapOptions.id);
                    this.camera = new THREE.PerspectiveCamera(60, container.clientWidth / container.clientHeight, 1, 1 << 30);
                    this.renderer = new THREE.WebGLRenderer({
                        context: gl,
                        // canvas: document.querySelector('.amap-layer'), //也可以直接用canvas初始化
                        antialias: true, // 抗锯齿，默认false 耗性能
                    });
                    // 自动清空画布这里必须设置为 false，否则地图底图将无法显示
                    this.renderer.autoClear = false;
                    this.renderer.outputEncoding = THREE.sRGBEncoding;

                    this.scene = new THREE.Scene();
                    // 增加环境光
                    const aLight = new THREE.AmbientLight(0xffffff, 0.3);
                    this.scene.add(aLight);
                    resolve();
                },
                render: () => {
                    const { _map, camera, renderer, scene } = this;
                    const params = _map.customCoords.getCameraParams();
                    const { near, far, fov, up, lookAt, position } = params;
                    camera.near = near; // 近平面
                    camera.far = far; // 远平面
                    camera.fov = fov; // 视野范围
                    camera.position.set(...position);
                    camera.up.set(...up);
                    camera.lookAt(...lookAt);
                    // 更新相机坐标系
                    camera.updateProjectionMatrix();
                    renderer.render(scene, camera);
                    // 这里必须执行！重新设置 three 的 gl 上下文状态
                    renderer.resetState();
                },
            });
            _map.add(gllayer);
            this._gllayer = gllayer;
        })
    }

    drawMapShape() {
        const { paths, scene } = this;
        const pointsArr = paths.map(item => {
            return new THREE.Vector2(item[0], item[1], 0)
        });
        const shape = new THREE.Shape(pointsArr);
        const extrudeSettings = {
            steps: 1, // 用于沿着挤压样条的深度细分段的点数。默认值为1。
            depth: ExtrudeHeight, // 挤压形状的深度
            bevelEnabled: true,
            bevelThickness: 1,
            bevelSize: 1,
            bevelOffset: 0,
            bevelSegments: 1,
        };
        const geometry = new THREE.ExtrudeGeometry(shape, extrudeSettings);
        const material = new THREE.MeshBasicMaterial({
            vertexColors: true,  // 启用顶点颜色
            wireframe: false,
            transparent: false,
        });

        const positions = geometry.attributes.position.array;
        const colors = [];
        // 最大高度和最小高度
        const maxHeight = ExtrudeHeight;  // 最大高度
        const planeVertices = [];
        // 遍历所有顶点并计算颜色
        for (let i = 0; i < positions.length; i += 3) {
            const z = positions[i + 2]; // 获取 z 坐标（高度）
            const color = new THREE.Color();
            const isTopLevel = z >= maxHeight;
            if (isTopLevel) {
                planeVertices.push(new THREE.Vector3(positions[i], positions[i + 1], z));
            }
            // HSL 色调值根据高度变化，1为饱和度，0.5为亮度
            color.setHSL(isTopLevel ? 0.45 : 0.48, 1, isTopLevel ? 0.4 : 0.01);
            // 将颜色存入颜色数组
            colors.push(color.r, color.g, color.b);
        }

        // // 将颜色数组设置到几何体的颜色属性中
        geometry.setAttribute('color', new THREE.BufferAttribute(new Float32Array(colors), 3));
        const mesh = new THREE.Mesh(geometry, material);
        mesh.position.z = -ExtrudeHeight;
        scene.add(mesh)
    }


    // 绘制背景图
    drawMapBackground() {
        const { paths, scene } = this;
        const pointsArr = paths.map(item => {
            return new THREE.Vector2(item[0], item[1], 0)
        });
        const shape = new THREE.Shape(pointsArr);
        // 创建平面几何体，设置贴图
        const planeGeometry = new THREE.ShapeGeometry(shape);
        // 创建材质
        var planeMaterial = new THREE.MeshBasicMaterial({
            // color: 0x00ff00, 
            map: new THREE.TextureLoader().load(require('@/assets/bigScreen/map.png')),
            transparent: false,
        });

        // 创建网格对象
        var plane = new THREE.Mesh(planeGeometry, planeMaterial);
        // plane.setPosition(positions[0], positions[1], positions[2]);

        const uvArray = [];
        const vertices = planeGeometry.attributes.position.array;
        // 计算几何体的中心点并居中
        planeGeometry.computeBoundingBox();
        // 计算平面几何体的中心点
        const { min, max } = planeGeometry.boundingBox;
        for (let i = 0; i < vertices.length; i += 3) {
            const vertex = {
                x: vertices[i],
                y: vertices[i + 1],
            };
            // 将x坐标归一化到0-1范围作为U坐标
            const u = 1 - (vertex.x - min.x) / (max.x - min.x);
            // 将y坐标归一化到0-1范围作为V坐标
            const v = (vertex.y - min.y) / (max.y - min.y);
            uvArray.push(u, v);
        }
        // 创建UV属性并设置到几何体上
        const uvAttribute = new THREE.BufferAttribute(new Float32Array(uvArray), 2);
        planeGeometry.setAttribute('uv', uvAttribute);
        plane.position.z = 4;
        scene.add(plane);
    }



    // 地图下钻，adcode 为父级行政区的adcode  展示区级
    drawCityMap(json, adcode) {
        if (!this._map || !this._secondLayer) return;
        let filterJson = json;
        if (adcode) {
            filterJson = {
                type: 'FeatureCollection',
                features: json.features.filter(item => item.properties.parent.adcode == adcode),
            };
        }
        this._secondLayer.importData(filterJson);
    }

    // 地图下钻，adcode 为父级行政区的adcode
    showAreaMap(json, adcode) {
        console.log('showAreaMap');
        if (!this._map || !this._secondLayer) return;
        // 清除地图上的所有覆盖物
        this.removeAllMarkers();
        this.init();

        // let filterJson = json;
        // if (adcode) {
        //     filterJson = {
        //         type: 'FeatureCollection',
        //         features: json.features.filter(item => item.properties.parent.adcode == adcode),
        //     };
        // }
        // this._secondLayer.importData(filterJson);
        // this._map.setFitView();//地图自适应
    }

    upToProvince() {
        this.currentLevel = MapDisplayLevel.Province;
        this.removeAllMarkers();
        this.init();
    }

    // 地图切换至区级
    upToDistrict(upLevel,upAdcode,currentAdcode,geojson,upMapCodeList,nowMapCodeList) {
        const temLevel = upLevel;
        this.currentLevel = MapDisplayLevel.District;
        
        this.mapCodeList = nowMapCodeList.children;

        console.log(this.mapCodeList, 'this.mapCodeList')

        this.downMapToCity(geojson);

        MapEvent.$emit('areaClick',{areaCode: currentAdcode, level:temLevel, upAdcode, upMapCodeList: upMapCodeList.children});

        // 下钻到区级, 此时要展示2D地图
        // 3D地图不支持切换到2D,目前方案是用第二个地图进行展示
        MapEvent.$emit('cityClick', geojson);

        if (this._gllayer) {
            this._gllayer.setMap(null);
        }

        if (this._satelliteLayer) {
            this._satelliteLayer.setMap(null);
        }
                    
        this.removeAllMarkers();

        const { _Amap, _map } = this;

        const {  center } = geojson.properties;

        this._map = new _Amap.Map(this._mapOptions.id, {
            center,
            showLabel: true,
            labelzIndex: 130,
            zoom: 14.4,
            mapStyle: "amap://styles/darkblue",
        });

        this.mapCodeList.forEach((item) => {
            this.addVueMarker([item.lon,item.lat], StationMarker, {
                info: {
                    stationAddress: item.stationAddress,
                    income: Number(item.income).toFixed(2),
                    fastNum: item.fastNum,
                    slowNum: item.slowNum,
                },
                dark: true,
                stationClick(){
                    console.log( ' mapCodeList stationClick');
                    MapEvent.$emit('stationClick', item.stationNo);
                },
            });
        })
    }

       // 地图切换到指定层级
       async  upMapToLevel(level, adcode,geojson,provinceList) {
            console.log(level,'upMapToLevel1');
            console.log(adcode,'upMapToLevel2');
            console.log(geojson,'upMapToLevel3')
            console.log(provinceList,'upMapToLevel4')
        
            if (!Object.values(MapDisplayLevel).includes(level)) {
                throw new Error('level is not valid');
            }
            if (level === MapDisplayLevel.City) {
                // this.showAreaMap(HuNan_District_Json, adcode);
                // 返回至市级
                // this.upToCity(adcode);
                await this.init();
                setTimeout(() => {
                    this.downMapToCity(geojson);
                    MapEvent.$emit('areaClick',{areaCode: adcode, level:MapDisplayLevel.Province, upAdcode:'430000', upMapCodeList: provinceList });
                }, 50)
            } else if (level === MapDisplayLevel.Province) {
                // 返回至省级
                // this.showAreaMap(HuNan_City_Json);
                this.upToProvince();
            } else if (level === MapDisplayLevel.District) {
                // 展示区级
                this.hanldeStationList()
            }
            this.currentLevel = level;
        }

        // 重画省级市级 站点数据
        updataMarker(level,areaCode) {
            console.log('updataMarker 2', level)
            this.removeAllMarkers();
            if(level === '02'){
                this.init();
            } else if(level === '03'){
                // 更新城市下每个区的数据
                this.drawCityMap(HuNan_District_Json,areaCode );
            } else if(level === '04'){
                // 更新区级下每个镇的数据
                this.mapCodeList.forEach((item) => {
                    this.addVueMarker([item.lon,item.lat], StationMarker, {
                        info: {
                            stationAddress: item.stationAddress,
                            income: Number(item.income).toFixed(2),
                            fastNum: item.fastNum,
                            slowNum: item.slowNum,
                        },
                        dark: true,
                        stationClick(){
                            console.log( ' mapCodeList stationClick');
                            MapEvent.$emit('stationClick', item.stationNo);
                        },
                    });
                })
            }
        }
}