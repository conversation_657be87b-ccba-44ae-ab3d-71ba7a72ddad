/**
 * ServiceWorker Mock系统管理器
 * @description 负责ServiceWorker的注册、配置更新和状态管理
 * <AUTHOR>
 * @since 2024-12-01
 */

/**
 * Mock ServiceWorker管理类
 */
class MockServiceWorkerManager {
  constructor() {
    this.isRegistered = false;
    this.registration = null;
    this.config = null;
    this.metrics = {
      requestCount: 0,
      errorCount: 0,
      averageResponseTime: 0,
    };
    
    // 加载配置
    this.loadConfig();
  }

  /**
   * 加载Mock配置
   */
  async loadConfig() {
    try {
      // 动态导入配置
      if (window.MockConfig) {
        this.config = window.MockConfig.config;
      } else {
        // 如果全局配置不存在，尝试加载配置文件
        const configModule = await import('../../mock.config.js');
        this.config = configModule.mockConfig;
      }
      
      console.log('[Mock SW Manager] Configuration loaded:', this.config);
    } catch (error) {
      console.error('[Mock SW Manager] Failed to load configuration:', error);
      this.config = { enabled: false };
    }
  }

  /**
   * 检查ServiceWorker支持
   * @returns {boolean} 是否支持ServiceWorker
   */
  isServiceWorkerSupported() {
    return 'serviceWorker' in navigator;
  }

  /**
   * 注册ServiceWorker
   * @returns {Promise<boolean>} 注册是否成功
   */
  async register() {
    if (!this.isServiceWorkerSupported()) {
      console.warn('[Mock SW Manager] ServiceWorker not supported');
      return false;
    }

    if (!this.config || !this.config.enabled) {
      console.log('[Mock SW Manager] Mock system disabled');
      return false;
    }

    if (!this.config.serviceWorker?.enabled) {
      console.log('[Mock SW Manager] ServiceWorker mock disabled');
      return false;
    }

    try {
      const scriptPath = this.config.serviceWorker.scriptPath || '/sw-mock-manager.js';
      const scope = this.config.serviceWorker.scope || '/';
      
      this.registration = await navigator.serviceWorker.register(scriptPath, {
        scope,
        updateViaCache: this.config.serviceWorker.updateViaCache || 'none',
      });

      console.log('[Mock SW Manager] ServiceWorker registered:', this.registration);

      // 监听ServiceWorker状态变化
      this.setupEventListeners();

      // 等待ServiceWorker激活
      await this.waitForActivation();

      // 发送初始配置
      await this.updateServiceWorkerConfig();

      this.isRegistered = true;
      return true;

    } catch (error) {
      console.error('[Mock SW Manager] ServiceWorker registration failed:', error);
      return false;
    }
  }

  /**
   * 设置事件监听器
   */
  setupEventListeners() {
    if (!this.registration) return;

    // 监听ServiceWorker更新
    this.registration.addEventListener('updatefound', () => {
      console.log('[Mock SW Manager] ServiceWorker update found');
      const newWorker = this.registration.installing;
      
      newWorker.addEventListener('statechange', () => {
        if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
          console.log('[Mock SW Manager] New ServiceWorker installed, reloading...');
          window.location.reload();
        }
      });
    });

    // 监听ServiceWorker消息
    navigator.serviceWorker.addEventListener('message', (event) => {
      this.handleServiceWorkerMessage(event);
    });

    // 监听控制器变化
    navigator.serviceWorker.addEventListener('controllerchange', () => {
      console.log('[Mock SW Manager] ServiceWorker controller changed');
      this.updateServiceWorkerConfig();
    });
  }

  /**
   * 等待ServiceWorker激活
   * @returns {Promise<void>}
   */
  async waitForActivation() {
    if (!this.registration) return;

    return new Promise((resolve) => {
      if (this.registration.active) {
        resolve();
        return;
      }

      const worker = this.registration.installing || this.registration.waiting;
      if (worker) {
        worker.addEventListener('statechange', () => {
          if (worker.state === 'activated') {
            resolve();
          }
        });
      } else {
        resolve();
      }
    });
  }

  /**
   * 处理ServiceWorker消息
   * @param {MessageEvent} event - 消息事件
   */
  handleServiceWorkerMessage(event) {
    const { type, data } = event.data;

    switch (type) {
      case 'METRICS_RESPONSE':
        this.metrics = data;
        this.notifyMetricsUpdate();
        break;

      case 'REQUEST_LOG':
        this.handleRequestLog(data);
        break;

      case 'ERROR_LOG':
        this.handleErrorLog(data);
        break;

      default:
        console.log('[Mock SW Manager] Unknown message from ServiceWorker:', event.data);
    }
  }

  /**
   * 处理请求日志
   * @param {Object} logData - 日志数据
   */
  handleRequestLog(logData) {
    if (this.config?.devTools?.enabled && this.config.devTools.console?.logRequests) {
      console.log('[Mock SW Manager] Request:', logData);
    }
  }

  /**
   * 处理错误日志
   * @param {Object} errorData - 错误数据
   */
  handleErrorLog(errorData) {
    if (this.config?.devTools?.enabled && this.config.devTools.console?.logErrors) {
      console.error('[Mock SW Manager] Error:', errorData);
    }
  }

  /**
   * 更新ServiceWorker配置
   * @returns {Promise<void>}
   */
  async updateServiceWorkerConfig() {
    if (!navigator.serviceWorker.controller) return;

    try {
      navigator.serviceWorker.controller.postMessage({
        type: 'CONFIG_UPDATE',
        config: this.config,
      });
      
      console.log('[Mock SW Manager] Configuration sent to ServiceWorker');
    } catch (error) {
      console.error('[Mock SW Manager] Failed to update ServiceWorker config:', error);
    }
  }

  /**
   * 清除ServiceWorker缓存
   * @returns {Promise<void>}
   */
  async clearCache() {
    if (!navigator.serviceWorker.controller) return;

    try {
      navigator.serviceWorker.controller.postMessage({
        type: 'CLEAR_CACHE',
      });
      
      console.log('[Mock SW Manager] Cache clear request sent');
    } catch (error) {
      console.error('[Mock SW Manager] Failed to clear cache:', error);
    }
  }

  /**
   * 获取性能指标
   * @returns {Promise<Object>} 性能指标
   */
  async getMetrics() {
    if (!navigator.serviceWorker.controller) return this.metrics;

    return new Promise((resolve) => {
      const channel = new MessageChannel();
      
      channel.port1.onmessage = (event) => {
        if (event.data.type === 'METRICS_RESPONSE') {
          this.metrics = event.data.data;
          resolve(this.metrics);
        }
      };

      navigator.serviceWorker.controller.postMessage(
        { type: 'GET_METRICS' },
        [channel.port2]
      );

      // 超时处理
      setTimeout(() => resolve(this.metrics), 1000);
    });
  }

  /**
   * 通知指标更新
   */
  notifyMetricsUpdate() {
    window.dispatchEvent(new CustomEvent('mockMetricsUpdate', {
      detail: this.metrics,
    }));
  }

  /**
   * 注销ServiceWorker
   * @returns {Promise<boolean>} 注销是否成功
   */
  async unregister() {
    if (!this.registration) return false;

    try {
      const result = await this.registration.unregister();
      this.isRegistered = false;
      this.registration = null;
      
      console.log('[Mock SW Manager] ServiceWorker unregistered:', result);
      return result;
    } catch (error) {
      console.error('[Mock SW Manager] Failed to unregister ServiceWorker:', error);
      return false;
    }
  }

  /**
   * 动态更新配置
   * @param {Object} updates - 配置更新
   */
  updateConfig(updates) {
    if (!this.config) return;

    Object.assign(this.config, updates);
    this.updateServiceWorkerConfig();
    
    console.log('[Mock SW Manager] Configuration updated:', updates);
  }

  /**
   * 启用/禁用Mock功能
   * @param {boolean} enabled - 是否启用
   */
  setEnabled(enabled) {
    this.updateConfig({ enabled });
  }

  /**
   * 启用/禁用特定模块
   * @param {string} moduleName - 模块名称
   * @param {boolean} enabled - 是否启用
   */
  setModuleEnabled(moduleName, enabled) {
    if (!this.config.modules[moduleName]) return;

    this.config.modules[moduleName].enabled = enabled;
    this.updateServiceWorkerConfig();
  }

  /**
   * 启用/禁用特定路由
   * @param {string} path - 路由路径
   * @param {boolean} enabled - 是否启用
   */
  setRouteEnabled(path, enabled) {
    if (!this.config.routes[path]) {
      this.config.routes[path] = {};
    }

    this.config.routes[path].enabled = enabled;
    this.updateServiceWorkerConfig();
  }

  /**
   * 获取当前状态
   * @returns {Object} 当前状态
   */
  getStatus() {
    return {
      isSupported: this.isServiceWorkerSupported(),
      isRegistered: this.isRegistered,
      isEnabled: this.config?.enabled || false,
      registration: this.registration,
      metrics: this.metrics,
    };
  }
}

// 创建全局实例
const mockServiceWorkerManager = new MockServiceWorkerManager();

/**
 * 初始化Mock系统
 * @returns {Promise<boolean>} 初始化是否成功
 */
export async function initMockSystem() {
  try {
    const success = await mockServiceWorkerManager.register();
    
    if (success) {
      console.log('[Mock System] Initialized successfully');
      
      // 开发环境下添加调试工具
      if (process.env.NODE_ENV === 'development') {
        addDevTools();
      }
    }
    
    return success;
  } catch (error) {
    console.error('[Mock System] Initialization failed:', error);
    return false;
  }
}

/**
 * 添加开发工具
 */
function addDevTools() {
  // 添加全局调试方法
  window.MockDebug = {
    getStatus: () => mockServiceWorkerManager.getStatus(),
    getMetrics: () => mockServiceWorkerManager.getMetrics(),
    clearCache: () => mockServiceWorkerManager.clearCache(),
    setEnabled: (enabled) => mockServiceWorkerManager.setEnabled(enabled),
    setModuleEnabled: (module, enabled) => mockServiceWorkerManager.setModuleEnabled(module, enabled),
    setRouteEnabled: (path, enabled) => mockServiceWorkerManager.setRouteEnabled(path, enabled),
    updateConfig: (updates) => mockServiceWorkerManager.updateConfig(updates),
  };

  // 添加快捷键支持
  document.addEventListener('keydown', (event) => {
    if (event.ctrlKey && event.shiftKey && event.key === 'M') {
      event.preventDefault();
      showMockDebugPanel();
    }
  });

  console.log('[Mock System] Debug tools loaded. Use Ctrl+Shift+M to open debug panel or window.MockDebug for API access.');
}

/**
 * 显示Mock调试面板
 */
function showMockDebugPanel() {
  // 创建简单的调试面板
  const panel = document.createElement('div');
  panel.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    width: 300px;
    background: white;
    border: 1px solid #ccc;
    border-radius: 8px;
    padding: 16px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 10000;
    font-family: monospace;
    font-size: 12px;
  `;

  const status = mockServiceWorkerManager.getStatus();
  
  panel.innerHTML = `
    <div style="font-weight: bold; margin-bottom: 8px;">Mock System Debug</div>
    <div>Supported: ${status.isSupported}</div>
    <div>Registered: ${status.isRegistered}</div>
    <div>Enabled: ${status.isEnabled}</div>
    <div style="margin: 8px 0;">
      <button onclick="window.MockDebug.clearCache()">Clear Cache</button>
      <button onclick="window.MockDebug.setEnabled(!${status.isEnabled})">${status.isEnabled ? 'Disable' : 'Enable'}</button>
    </div>
    <div style="margin-top: 8px;">
      <button onclick="this.parentElement.parentElement.remove()">Close</button>
    </div>
  `;

  document.body.appendChild(panel);

  // 5秒后自动关闭
  setTimeout(() => {
    if (panel.parentElement) {
      panel.remove();
    }
  }, 5000);
}

// 导出管理器实例和工具函数
export default mockServiceWorkerManager;
export {
  mockServiceWorkerManager,
  initMockSystem,
  addDevTools,
  showMockDebugPanel,
};
