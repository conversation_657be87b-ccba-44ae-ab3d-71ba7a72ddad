# 网络接口封装使用说明文档

## 概述

本项目基于 Axios 构建了完整的网络请求封装体系，提供了统一的接口调用方式、错误处理、请求拦截、响应拦截等功能。

## 核心架构

### 请求封装结构
```
src/utils/request.js        # 核心请求封装
src/api/                    # API接口定义
├── operationMaintenanceConfig/  # 运维配置相关接口
├── user/                   # 用户相关接口
├── charging/               # 充电相关接口
└── ...                     # 其他业务模块接口
```

## 基础配置

### 1. Axios 实例配置
```javascript
// src/utils/request.js
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API,  // 基础URL
  timeout: 20000,                         // 超时时间20秒
});

// 默认请求头
axios.defaults.headers['Content-Type'] = 'application/json;charset=utf-8';
```

### 2. 环境变量配置
```bash
# .env.development
VUE_APP_BASE_API = '/dev-api'

# .env.production  
VUE_APP_BASE_API = '/prod-api'

# .env.staging
VUE_APP_BASE_API = '/stage-api'
```

## 请求拦截器

### 1. 认证处理
```javascript
// 自动添加Token
if (getToken() && !isToken) {
  config.headers['Authorization'] = 'Bearer ' + getToken();
}
```

### 2. 防重复提交
```javascript
// 防止重复提交配置
const isRepeatSubmit = (config.headers || {}).repeatSubmit === false;

// 1秒内相同请求视为重复提交
const interval = 1000;
```

### 3. 参数处理
```javascript
// GET请求参数处理
if (config.method === 'get' && config.params) {
  let url = config.url + '?' + tansParams(config.params);
  config.params = {};
  config.url = url;
}

// 清理空值参数
Object.keys(config.data).forEach((key) => {
  if (config.data[key] === '' || 
      config.data[key] === undefined || 
      config.data[key] === null) {
    delete config.data[key];
  }
});
```

## 响应拦截器

### 1. 状态码处理
```javascript
// 成功状态码
if (code === 200 || code === '10000') {
  return res.data;
}

// 认证失效
if (code === 401 || code === '6003') {
  // 自动跳转登录
}

// 服务器错误
if (code === 500) {
  Message({ message: msg, type: 'error' });
}
```

### 2. 错误处理
```javascript
// 网络错误处理
if (message == 'Network Error') {
  message = '后端接口连接异常';
} else if (message.includes('timeout')) {
  message = '系统接口请求超时';
} else if (message.includes('Request failed with status code')) {
  message = '系统接口' + message.substr(message.length - 3) + '异常';
}
```

## 统一请求方法

### 1. 基础请求方法
```javascript
// src/utils/request.js
const request = async function (options) {
  try {
    const response = await service(options);
    return [null, response];  // [error, data] 格式
  } catch (error) {
    return [error || true, null];
  }
};

export default request;
```

### 2. 使用示例
```javascript
// 在API文件中使用
import request from '@/utils/request';

export function getUserList(params) {
  return request({
    url: '/user/list',
    method: 'post',
    data: params
  });
}

// 在组件中使用
async loadData() {
  const [err, res] = await getUserList(this.params);
  if (err) return;
  
  this.tableData = res.data;
  this.total = res.total;
}
```

## 文件下载封装

### 1. 通用下载方法
```javascript
// src/utils/request.js
export function download(url, params, filename = '', method = 'post', config) {
  // 显示下载进度
  downloadLoadingInstance = Loading.service({
    text: '正在下载数据，请稍候',
    spinner: 'el-icon-loading',
    background: 'rgba(0, 0, 0, 0.7)',
  });

  return service.post(url, params, {
    responseType: 'blob',
    ...config,
  }).then(async (res) => {
    // 处理文件名
    const hs = res.headers['content-disposition'];
    if (hs) {
      const reg = /filename=(.*);/;
      const match = reg.exec(hs);
      if (match) {
        filename = decodeURIComponent(match[1].trim());
      }
    }

    // 验证并下载文件
    const isBlob = blobValidate(res.data);
    if (isBlob) {
      const blob = new Blob([res.data]);
      saveAs(blob, filename);
    }
  });
}
```

### 2. 下载使用示例
```javascript
// 导出Excel
async handleExport() {
  const params = { ...this.queryParams };
  await download('/user/export', params, '用户列表.xlsx');
}
```

## API接口规范

### 1. 接口文件结构
```javascript
// src/api/user/index.js
import request from '@/utils/request';

const baseUrl = '/vehicle-charging-admin';

/**
 * 用户分页查询
 * @param {Object} params - 查询参数
 * @returns {Promise} 返回用户列表数据
 */
export function getUserPage(params) {
  return request({
    url: baseUrl + '/user/page',
    method: 'post',
    data: params
  });
}

/**
 * 新增用户
 * @param {Object} data - 用户数据
 * @returns {Promise} 返回创建结果
 */
export function createUser(data) {
  return request({
    url: baseUrl + '/user/create',
    method: 'post',
    data: data
  });
}
```

### 2. TypeScript类型定义
```javascript
/**
 * @typedef {Object} UserPageRequest
 * @property {number} [pageNum=1] - 当前页码
 * @property {number} [pageSize=10] - 每页显示条数
 * @property {string} [userName] - 用户名
 * @property {string} [userPhone] - 手机号
 * @property {number} [status] - 状态：0停用，1启用
 */

/**
 * @typedef {Object} UserInfo
 * @property {string} userId - 用户ID
 * @property {string} userName - 用户名
 * @property {string} userPhone - 手机号
 * @property {number} status - 状态
 * @property {string} createTime - 创建时间
 */
```

## 请求配置选项

### 1. 特殊请求头配置
```javascript
// 跳过Token验证
export function publicApi(data) {
  return request({
    url: '/public/api',
    method: 'post',
    data: data,
    headers: {
      isToken: false  // 不携带Token
    }
  });
}

// 允许重复提交
export function urgentApi(data) {
  return request({
    url: '/urgent/api',
    method: 'post',
    data: data,
    headers: {
      repeatSubmit: false  // 允许重复提交
    }
  });
}

// 防抖配置
export function debounceApi(data) {
  return request({
    url: '/debounce/api',
    method: 'post',
    data: data,
    isDebounce: true  // 启用防抖
  });
}
```

### 2. 文件上传配置
```javascript
export function uploadFile(file) {
  const formData = new FormData();
  formData.append('file', file);
  
  return request({
    url: '/upload',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}
```

## 错误处理策略

### 1. 全局错误处理
```javascript
// 响应拦截器中的错误处理
service.interceptors.response.use(
  (res) => {
    const code = res.data.code || 200;
    const msg = errorCode[code] || res.data.msg || errorCode['default'];
    
    if (code === 200) {
      return res.data;
    } else {
      Notification.error({ title: msg });
      return Promise.reject('error');
    }
  },
  (error) => {
    // 网络错误、超时等处理
    Message({ message: message, type: 'error', duration: 5000 });
    return Promise.reject(error);
  }
);
```

### 2. 业务错误处理
```javascript
// 在组件中处理特定错误
async loadData() {
  const [err, res] = await getUserList(this.params);
  
  if (err) {
    // 处理特定错误
    if (err.code === 'USER_NOT_FOUND') {
      this.$message.warning('用户不存在');
    }
    return;
  }
  
  // 处理成功响应
  this.tableData = res.data;
}
```

## 性能优化

### 1. 请求缓存
```javascript
// 使用缓存避免重复请求
import cache from '@/plugins/cache';

export function getCachedData(params) {
  const cacheKey = `data_${JSON.stringify(params)}`;
  const cached = cache.local.getJSON(cacheKey);
  
  if (cached) {
    return Promise.resolve([null, cached]);
  }
  
  return request({
    url: '/data',
    method: 'post',
    data: params
  }).then(([err, res]) => {
    if (!err) {
      cache.local.setJSON(cacheKey, res, 300); // 缓存5分钟
    }
    return [err, res];
  });
}
```

### 2. 请求取消
```javascript
// 取消请求
const CancelToken = axios.CancelToken;
let cancel;

export function cancelableRequest(params) {
  // 取消之前的请求
  if (cancel) {
    cancel('Operation canceled by the user.');
  }
  
  return request({
    url: '/api',
    method: 'post',
    data: params,
    cancelToken: new CancelToken(function executor(c) {
      cancel = c;
    })
  });
}
```

## 最佳实践

### 1. 接口命名规范
```javascript
// 推荐的命名方式
getUserPage()      // 分页查询
getUserDetail()    // 详情查询
createUser()       // 新增
updateUser()       // 修改
deleteUser()       // 删除
toggleUserStatus() // 状态切换
exportUsers()      // 导出
```

### 2. 错误处理模式
```javascript
// 统一的错误处理模式
async handleSubmit() {
  this.loading = true;
  
  const [err, res] = await createUser(this.form);
  
  this.loading = false;
  
  if (err) return; // 错误已在拦截器中处理
  
  this.$message.success('操作成功');
  this.dialogVisible = false;
  this.loadData();
}
```

### 3. 接口文档注释
```javascript
/**
 * 用户管理相关接口
 * @file src/api/user/index.js
 * <AUTHOR>
 * @since 2024-12-01
 */

/**
 * 获取用户列表
 * @description 支持分页查询和条件筛选
 * @param {UserPageRequest} params - 查询参数
 * @returns {Promise<[Error|null, UserPageResponse|null]>} 返回用户列表数据
 * @example
 * const [err, res] = await getUserPage({ pageNum: 1, pageSize: 10 });
 */
```
