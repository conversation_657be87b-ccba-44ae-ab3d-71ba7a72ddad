{"list": [{"ruleId": "rule_001", "ruleCode": "RULE001", "ruleName": "电压异常检测规则", "checkRule": "VOLTAGE", "compareOperator": "GT", "checkPercent": 85, "enableStatus": 1, "merchantId": "default", "remark": "当电压超过85%阈值时触发派单", "createTime": "2024-11-15 09:30:00", "updateTime": "2024-11-15 09:30:00", "createBy": "系统管理员", "updateBy": "系统管理员"}, {"ruleId": "rule_002", "ruleCode": "RULE002", "ruleName": "电流过载保护规则", "checkRule": "CURRENT", "compareOperator": "GT", "checkPercent": 90, "enableStatus": 1, "merchantId": "default", "remark": "当电流超过90%阈值时触发派单", "createTime": "2024-11-15 10:15:00", "updateTime": "2024-11-15 10:15:00", "createBy": "系统管理员", "updateBy": "系统管理员"}, {"ruleId": "rule_003", "ruleCode": "RULE003", "ruleName": "温度监控规则", "checkRule": "TEMPERATURE", "compareOperator": "GT", "checkPercent": 75, "enableStatus": 0, "merchantId": "default", "remark": "当温度超过75%阈值时触发派单", "createTime": "2024-11-15 11:20:00", "updateTime": "2024-11-15 11:20:00", "createBy": "运维人员", "updateBy": "运维人员"}, {"ruleId": "rule_004", "ruleCode": "RULE004", "ruleName": "低电压预警规则", "checkRule": "VOLTAGE", "compareOperator": "LT", "checkPercent": 20, "enableStatus": 1, "merchantId": "default", "remark": "当电压低于20%阈值时触发派单", "createTime": "2024-11-15 14:30:00", "updateTime": "2024-11-15 14:30:00", "createBy": "技术主管", "updateBy": "技术主管"}, {"ruleId": "rule_005", "ruleCode": "RULE005", "ruleName": "电流异常下限规则", "checkRule": "CURRENT", "compareOperator": "LT", "checkPercent": 10, "enableStatus": 1, "merchantId": "default", "remark": "当电流低于10%阈值时触发派单", "createTime": "2024-11-15 15:45:00", "updateTime": "2024-11-15 15:45:00", "createBy": "运维人员", "updateBy": "运维人员"}, {"ruleId": "rule_006", "ruleCode": "RULE006", "ruleName": "高温紧急处理规则", "checkRule": "TEMPERATURE", "compareOperator": "GT", "checkPercent": 95, "enableStatus": 1, "merchantId": "default", "remark": "当温度超过95%阈值时立即派单处理", "createTime": "2024-11-16 08:20:00", "updateTime": "2024-11-16 08:20:00", "createBy": "安全主管", "updateBy": "安全主管"}, {"ruleId": "rule_007", "ruleCode": "RULE007", "ruleName": "电压波动监控规则", "checkRule": "VOLTAGE", "compareOperator": "GT", "checkPercent": 80, "enableStatus": 0, "merchantId": "default", "remark": "监控电压波动情况，超过80%阈值时派单检查", "createTime": "2024-11-16 09:15:00", "updateTime": "2024-11-16 09:15:00", "createBy": "系统管理员", "updateBy": "系统管理员"}, {"ruleId": "rule_008", "ruleCode": "RULE008", "ruleName": "设备过载综合规则", "checkRule": "CURRENT", "compareOperator": "GT", "checkPercent": 85, "enableStatus": 1, "merchantId": "default", "remark": "综合监控设备负载情况，超过85%时派单维护", "createTime": "2024-11-16 10:30:00", "updateTime": "2024-11-16 10:30:00", "createBy": "运维主管", "updateBy": "运维主管"}, {"ruleId": "rule_009", "ruleCode": "RULE009", "ruleName": "低温环境适应规则", "checkRule": "TEMPERATURE", "compareOperator": "LT", "checkPercent": 15, "enableStatus": 0, "merchantId": "default", "remark": "低温环境下的设备运行监控规则", "createTime": "2024-11-16 11:45:00", "updateTime": "2024-11-16 11:45:00", "createBy": "技术人员", "updateBy": "技术人员"}, {"ruleId": "rule_010", "ruleCode": "RULE010", "ruleName": "电压稳定性检测规则", "checkRule": "VOLTAGE", "compareOperator": "LT", "checkPercent": 25, "enableStatus": 1, "merchantId": "default", "remark": "检测电压稳定性，低于25%时需要检查", "createTime": "2024-11-16 13:20:00", "updateTime": "2024-11-16 13:20:00", "createBy": "质量主管", "updateBy": "质量主管"}, {"ruleId": "rule_011", "ruleCode": "RULE011", "ruleName": "夜间温度监控规则", "checkRule": "TEMPERATURE", "compareOperator": "GT", "checkPercent": 70, "enableStatus": 1, "merchantId": "default", "remark": "夜间时段温度监控，超过70%阈值时派单", "createTime": "2024-11-16 14:10:00", "updateTime": "2024-11-16 14:10:00", "createBy": "夜班主管", "updateBy": "夜班主管"}, {"ruleId": "rule_012", "ruleCode": "RULE012", "ruleName": "电流峰值控制规则", "checkRule": "CURRENT", "compareOperator": "GT", "checkPercent": 95, "enableStatus": 1, "merchantId": "default", "remark": "控制电流峰值，超过95%时立即派单处理", "createTime": "2024-11-16 15:30:00", "updateTime": "2024-11-16 15:30:00", "createBy": "电气工程师", "updateBy": "电气工程师"}, {"ruleId": "rule_013", "ruleCode": "RULE013", "ruleName": "设备启动电压检测", "checkRule": "VOLTAGE", "compareOperator": "LT", "checkPercent": 30, "enableStatus": 0, "merchantId": "default", "remark": "设备启动时电压检测，低于30%时需要检查", "createTime": "2024-11-16 16:15:00", "updateTime": "2024-11-16 16:15:00", "createBy": "设备工程师", "updateBy": "设备工程师"}, {"ruleId": "rule_014", "ruleCode": "RULE014", "ruleName": "环境温度适应规则", "checkRule": "TEMPERATURE", "compareOperator": "LT", "checkPercent": 10, "enableStatus": 1, "merchantId": "default", "remark": "极低温环境下的设备保护规则", "createTime": "2024-11-17 08:45:00", "updateTime": "2024-11-17 08:45:00", "createBy": "环境工程师", "updateBy": "环境工程师"}, {"ruleId": "rule_015", "ruleCode": "RULE015", "ruleName": "负载均衡监控规则", "checkRule": "CURRENT", "compareOperator": "LT", "checkPercent": 5, "enableStatus": 1, "merchantId": "default", "remark": "监控负载均衡情况，电流过低时检查设备状态", "createTime": "2024-11-17 09:30:00", "updateTime": "2024-11-17 09:30:00", "createBy": "系统工程师", "updateBy": "系统工程师"}, {"ruleId": "rule_016", "ruleCode": "RULE016", "ruleName": "高压保护规则", "checkRule": "VOLTAGE", "compareOperator": "GT", "checkPercent": 98, "enableStatus": 1, "merchantId": "default", "remark": "高压保护规则，超过98%阈值时紧急派单", "createTime": "2024-11-17 10:20:00", "updateTime": "2024-11-17 10:20:00", "createBy": "安全工程师", "updateBy": "安全工程师"}, {"ruleId": "rule_017", "ruleCode": "RULE017", "ruleName": "设备散热监控规则", "checkRule": "TEMPERATURE", "compareOperator": "GT", "checkPercent": 80, "enableStatus": 0, "merchantId": "default", "remark": "监控设备散热情况，温度过高时派单检查", "createTime": "2024-11-17 11:15:00", "updateTime": "2024-11-17 11:15:00", "createBy": "维护工程师", "updateBy": "维护工程师"}, {"ruleId": "rule_018", "ruleCode": "RULE018", "ruleName": "电流稳定性规则", "compareOperator": "LT", "checkPercent": 15, "enableStatus": 1, "merchantId": "default", "remark": "监控电流稳定性，过低时检查线路连接", "createTime": "2024-11-17 12:30:00", "updateTime": "2024-11-17 12:30:00", "createBy": "电气技师", "updateBy": "电气技师"}, {"ruleId": "rule_019", "ruleCode": "RULE019", "ruleName": "电压质量监控规则", "checkRule": "VOLTAGE", "compareOperator": "LT", "checkPercent": 35, "enableStatus": 1, "merchantId": "default", "remark": "监控电压质量，低于标准时派单检查", "createTime": "2024-11-17 13:45:00", "updateTime": "2024-11-17 13:45:00", "createBy": "质量检测员", "updateBy": "质量检测员"}, {"ruleId": "rule_020", "ruleCode": "RULE020", "ruleName": "综合性能监控规则", "checkRule": "TEMPERATURE", "compareOperator": "GT", "checkPercent": 88, "enableStatus": 1, "merchantId": "default", "remark": "综合监控设备性能，温度异常时派单维护", "createTime": "2024-11-17 14:20:00", "updateTime": "2024-11-17 14:20:00", "createBy": "性能工程师", "updateBy": "性能工程师"}], "templates": [{"templateId": "template_001", "templateName": "电压监控模板", "checkRule": "VOLTAGE", "compareOperator": "GT", "checkPercent": 80, "description": "标准电压监控模板，适用于大部分设备"}, {"templateId": "template_002", "templateName": "电流保护模板", "checkRule": "CURRENT", "compareOperator": "GT", "checkPercent": 90, "description": "电流过载保护模板，防止设备损坏"}, {"templateId": "template_003", "templateName": "温度控制模板", "checkRule": "TEMPERATURE", "compareOperator": "GT", "checkPercent": 75, "description": "温度控制模板，保证设备正常运行"}], "statistics": {"totalRules": 20, "enabledRules": 14, "disabledRules": 6, "voltageRules": 8, "currentRules": 6, "temperatureRules": 6, "recentExecutions": 156, "successRate": 98.5}, "executionLogs": [{"logId": "log_001", "ruleId": "rule_001", "ruleName": "电压异常检测规则", "executeTime": "2024-11-17 15:30:00", "result": "success", "triggerValue": 87.5, "threshold": 85, "action": "派单已生成", "operator": "系统自动"}, {"logId": "log_002", "ruleId": "rule_002", "ruleName": "电流过载保护规则", "executeTime": "2024-11-17 15:25:00", "result": "success", "triggerValue": 92.3, "threshold": 90, "action": "派单已生成", "operator": "系统自动"}], "defaultResponse": "success"}