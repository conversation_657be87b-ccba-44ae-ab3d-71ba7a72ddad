{"list": [{"faultCodeId": "fault_001", "merchantId": "default", "faultCode": "E001", "faultName": "充电桩通信异常", "faultLevel": "2", "deviceType": "1", "enableStatus": 1, "remark": "充电桩与后台通信中断", "createTime": "2024-11-15 09:30:00", "updateTime": "2024-11-15 09:30:00", "createBy": "系统管理员", "updateBy": "系统管理员"}, {"faultCodeId": "fault_002", "merchantId": "default", "faultCode": "E002", "faultName": "电压过高故障", "faultLevel": "3", "deviceType": "1", "enableStatus": 1, "remark": "充电桩输出电压超过安全范围", "createTime": "2024-11-15 10:15:00", "updateTime": "2024-11-15 10:15:00", "createBy": "安全工程师", "updateBy": "安全工程师"}, {"faultCodeId": "fault_003", "merchantId": "default", "faultCode": "E003", "faultName": "温度传感器故障", "faultLevel": "2", "deviceType": "1", "enableStatus": 0, "remark": "温度传感器读数异常", "createTime": "2024-11-15 11:20:00", "updateTime": "2024-11-15 11:20:00", "createBy": "维护人员", "updateBy": "维护人员"}, {"faultCodeId": "fault_004", "merchantId": "default", "faultCode": "C001", "faultName": "配电柜主开关故障", "faultLevel": "3", "deviceType": "2", "enableStatus": 1, "remark": "配电柜主开关无法正常开合", "createTime": "2024-11-15 14:30:00", "updateTime": "2024-11-15 14:30:00", "createBy": "电气工程师", "updateBy": "电气工程师"}, {"faultCodeId": "fault_005", "merchantId": "default", "faultCode": "C002", "faultName": "配电柜过载保护", "faultLevel": "2", "deviceType": "2", "enableStatus": 1, "remark": "配电柜负载超过额定容量", "createTime": "2024-11-15 15:45:00", "updateTime": "2024-11-15 15:45:00", "createBy": "运维人员", "updateBy": "运维人员"}, {"faultCodeId": "fault_006", "merchantId": "default", "faultCode": "M001", "faultName": "监控设备离线", "faultLevel": "1", "deviceType": "3", "enableStatus": 1, "remark": "监控设备失去网络连接", "createTime": "2024-11-16 08:20:00", "updateTime": "2024-11-16 08:20:00", "createBy": "网络管理员", "updateBy": "网络管理员"}, {"faultCodeId": "fault_007", "merchantId": "default", "faultCode": "M002", "faultName": "摄像头故障", "faultLevel": "1", "deviceType": "3", "enableStatus": 0, "remark": "监控摄像头图像异常", "createTime": "2024-11-16 09:15:00", "updateTime": "2024-11-16 09:15:00", "createBy": "安防人员", "updateBy": "安防人员"}, {"faultCodeId": "fault_008", "merchantId": "default", "faultCode": "E004", "faultName": "充电枪连接异常", "faultLevel": "2", "deviceType": "1", "enableStatus": 1, "remark": "充电枪与车辆连接检测异常", "createTime": "2024-11-16 10:30:00", "updateTime": "2024-11-16 10:30:00", "createBy": "技术人员", "updateBy": "技术人员"}, {"faultCodeId": "fault_009", "merchantId": "default", "faultCode": "E005", "faultName": "电流传感器故障", "faultLevel": "2", "deviceType": "1", "enableStatus": 1, "remark": "电流传感器读数不准确", "createTime": "2024-11-16 11:45:00", "updateTime": "2024-11-16 11:45:00", "createBy": "检测人员", "updateBy": "检测人员"}, {"faultCodeId": "fault_010", "merchantId": "default", "faultCode": "C003", "faultName": "配电柜温度过高", "faultLevel": "3", "deviceType": "2", "enableStatus": 1, "remark": "配电柜内部温度超过安全阈值", "createTime": "2024-11-16 13:20:00", "updateTime": "2024-11-16 13:20:00", "createBy": "安全主管", "updateBy": "安全主管"}], "statistics": {"totalFaultCodes": 10, "enabledCodes": 8, "disabledCodes": 2, "chargingPileCodes": 6, "distributionCabinetCodes": 3, "monitoringDeviceCodes": 2, "level1Codes": 2, "level2Codes": 5, "level3Codes": 3}, "deviceTypes": [{"value": "1", "label": "充电桩"}, {"value": "2", "label": "配电柜"}, {"value": "3", "label": "监控设备"}], "faultLevels": [{"value": "1", "label": "一般"}, {"value": "2", "label": "严重"}, {"value": "3", "label": "紧急"}], "enableStatusOptions": [{"value": 0, "label": "停用"}, {"value": 1, "label": "启用"}], "defaultResponse": "success"}