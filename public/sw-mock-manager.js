/**
 * ServiceWorker Mock管理器
 * @description 核心ServiceWorker模拟请求处理模块
 * <AUTHOR>
 * @since 2024-12-01
 */

// 导入配置（在ServiceWorker中需要通过importScripts）
let mockConfig = null;
let dataCache = new Map();
let performanceMetrics = {
  requestCount: 0,
  totalResponseTime: 0,
  errorCount: 0,
  cacheHits: 0,
};

/**
 * ServiceWorker安装事件
 */
self.addEventListener('install', (event) => {
  console.log('[SW Mock Manager] Installing...');

  // 跳过等待，立即激活
  self.skipWaiting();

  event.waitUntil(
    // 预加载配置和数据
    initializeMockSystem()
  );
});

/**
 * ServiceWorker激活事件
 */
self.addEventListener('activate', (event) => {
  console.log('[SW Mock Manager] Activating...');

  // 立即控制所有客户端
  event.waitUntil(self.clients.claim());
});

/**
 * 初始化Mock系统
 */
async function initializeMockSystem() {
  try {
    // 加载配置文件
    const configResponse = await fetch('/mock.config.js');
    const configText = await configResponse.text();

    // 解析配置（简单的eval，生产环境应使用更安全的方式）
    const configMatch = configText.match(/const mockConfig = ({[\s\S]*?});/);
    if (configMatch) {
      mockConfig = eval('(' + configMatch[1] + ')');
      console.log('[SW Mock Manager] Configuration loaded:', mockConfig);
    }

    // 预加载常用数据文件
    await preloadMockData();
  } catch (error) {
    console.error('[SW Mock Manager] Initialization failed:', error);
  }
}

/**
 * 预加载Mock数据
 */
async function preloadMockData() {
  if (!mockConfig || !mockConfig.enabled) return;

  const dataFiles = new Set();

  // 收集所有数据文件路径
  for (const [moduleName, moduleConfig] of Object.entries(
    mockConfig.modules || {}
  )) {
    if (!moduleConfig.enabled) continue;

    for (const [subModuleName, subModuleConfig] of Object.entries(
      moduleConfig.routes || {}
    )) {
      if (subModuleConfig.enabled && subModuleConfig.dataFile) {
        dataFiles.add(subModuleConfig.dataFile);
      }
    }
  }

  // 预加载数据文件
  for (const dataFile of dataFiles) {
    try {
      await loadMockData(dataFile);
    } catch (error) {
      console.warn(`[SW Mock Manager] Failed to preload ${dataFile}:`, error);
    }
  }
}

/**
 * 加载Mock数据
 * @param {string} dataFile - 数据文件名
 * @returns {Promise<Object>} Mock数据
 */
async function loadMockData(dataFile) {
  const cacheKey = `mock-data-${dataFile}`;

  // 检查缓存
  if (mockConfig?.mockData?.cacheEnabled && dataCache.has(cacheKey)) {
    const cached = dataCache.get(cacheKey);
    if (
      Date.now() - cached.timestamp <
      (mockConfig.mockData.cacheTimeout || 300000)
    ) {
      performanceMetrics.cacheHits++;
      return cached.data;
    }
  }

  try {
    const dataPath = `${mockConfig.mockData.basePath}/${dataFile}`;
    const response = await fetch(dataPath);

    if (!response.ok) {
      throw new Error(`Failed to load ${dataPath}: ${response.status}`);
    }

    const data = await response.json();

    // 缓存数据
    if (mockConfig?.mockData?.cacheEnabled) {
      dataCache.set(cacheKey, {
        data,
        timestamp: Date.now(),
      });
    }

    return data;
  } catch (error) {
    console.error(
      `[SW Mock Manager] Failed to load mock data ${dataFile}:`,
      error
    );
    return null;
  }
}

/**
 * 检查是否应该模拟请求
 * @param {Request} request - 请求对象
 * @returns {boolean} 是否模拟
 */
function shouldMockRequest(request) {
  if (!mockConfig || !mockConfig.enabled) return false;

  const url = new URL(request.url);
  const path = url.pathname;
  const method = request.method;

  // 检查路径级别配置
  if (mockConfig.routes && mockConfig.routes[path]) {
    const routeConfig = mockConfig.routes[path];
    return (
      routeConfig.enabled &&
      (!routeConfig.method ||
        routeConfig.method.toUpperCase() === method.toUpperCase())
    );
  }

  // 检查模块级别配置
  for (const [moduleName, moduleConfig] of Object.entries(
    mockConfig.modules || {}
  )) {
    if (!moduleConfig.enabled) continue;

    for (const [subModuleName, subModuleConfig] of Object.entries(
      moduleConfig.routes || {}
    )) {
      if (!subModuleConfig.enabled) continue;

      if (subModuleConfig.routes && subModuleConfig.routes[path]) {
        return true;
      }
    }
  }

  return false;
}

/**
 * 获取路由配置
 * @param {string} path - 路径
 * @returns {Object} 路由配置
 */
function getRouteConfig(path) {
  // 优先检查路径级别配置
  if (mockConfig.routes && mockConfig.routes[path]) {
    return mockConfig.routes[path];
  }

  // 检查模块级别配置
  for (const [moduleName, moduleConfig] of Object.entries(
    mockConfig.modules || {}
  )) {
    if (!moduleConfig.enabled) continue;

    for (const [subModuleName, subModuleConfig] of Object.entries(
      moduleConfig.routes || {}
    )) {
      if (!subModuleConfig.enabled) continue;

      if (subModuleConfig.routes && subModuleConfig.routes[path]) {
        return {
          enabled: true,
          dataFile: subModuleConfig.dataFile,
          method: 'POST',
          timeout: mockConfig.timeout || 1000,
        };
      }
    }
  }

  return { enabled: false };
}

/**
 * 模拟网络延迟
 * @param {number} timeout - 延迟时间（毫秒）
 * @returns {Promise} 延迟Promise
 */
function simulateDelay(timeout) {
  return new Promise((resolve) => setTimeout(resolve, timeout));
}

/**
 * 生成错误响应
 * @param {number} status - 状态码
 * @param {string} message - 错误消息
 * @returns {Response} 错误响应
 */
function createErrorResponse(status, message) {
  const errorData = {
    code: status.toString(),
    msg: message,
    data: null,
    total: 0,
    timestamp: new Date().toISOString(),
  };

  return new Response(JSON.stringify(errorData), {
    status: 200, // 业务错误仍返回200状态码
    headers: mockConfig?.response?.headers || {
      'Content-Type': 'application/json;charset=utf-8',
    },
  });
}

/**
 * 处理Mock请求
 * @param {Request} request - 请求对象
 * @returns {Promise<Response>} 响应对象
 */
async function handleMockRequest(request) {
  const startTime = Date.now();
  performanceMetrics.requestCount++;

  try {
    const url = new URL(request.url);
    const path = url.pathname;
    const method = request.method;

    // 获取路由配置
    const routeConfig = getRouteConfig(path);
    if (!routeConfig.enabled) {
      throw new Error('Route not enabled for mocking');
    }

    // 模拟网络延迟
    const timeout = routeConfig.timeout || mockConfig.timeout || 1000;
    await simulateDelay(timeout);

    // 错误模拟
    if (mockConfig.errorSimulation?.enabled) {
      const errorRate =
        routeConfig.errorRate ||
        mockConfig.errorSimulation.globalErrorRate ||
        0;
      if (Math.random() < errorRate) {
        const errorCodes = Object.keys(
          mockConfig.errorSimulation.errorCodes || { 500: '服务器错误' }
        );
        const randomErrorCode =
          errorCodes[Math.floor(Math.random() * errorCodes.length)];
        const errorMessage =
          mockConfig.errorSimulation.errorCodes[randomErrorCode];

        performanceMetrics.errorCount++;
        return createErrorResponse(parseInt(randomErrorCode), errorMessage);
      }
    }

    // 加载Mock数据
    const mockData = await loadMockData(routeConfig.dataFile);
    if (!mockData) {
      throw new Error(`Mock data not found for ${routeConfig.dataFile}`);
    }

    // 解析请求参数
    let requestData = {};
    if (
      method === 'POST' &&
      request.headers.get('content-type')?.includes('application/json')
    ) {
      try {
        requestData = await request.json();
      } catch (error) {
        console.warn('[SW Mock Manager] Failed to parse request JSON:', error);
      }
    }

    // 处理不同类型的请求
    const responseData = await processMockRequest(
      path,
      method,
      requestData,
      mockData
    );

    // 记录性能指标
    const responseTime = Date.now() - startTime;
    performanceMetrics.totalResponseTime += responseTime;

    // 开发工具日志
    if (
      mockConfig.devTools?.enabled &&
      mockConfig.devTools.console?.logRequests
    ) {
      console.log(`[SW Mock Manager] ${method} ${path}`, {
        request: requestData,
        response: responseData,
        responseTime: `${responseTime}ms`,
      });
    }

    // 创建响应
    const headers = {
      ...(mockConfig?.response?.headers || {}),
      'Content-Type': 'application/json;charset=utf-8',
      'X-Mock-Source': 'service-worker',
      'X-Mock-File': routeConfig.dataFile || 'unknown',
      'X-Mock-Response-Time': `${responseTime}ms`,
    };

    return new Response(JSON.stringify(responseData), {
      status: 200,
      headers,
    });
  } catch (error) {
    performanceMetrics.errorCount++;

    if (
      mockConfig.devTools?.enabled &&
      mockConfig.devTools.console?.logErrors
    ) {
      console.error('[SW Mock Manager] Request handling failed:', error);
    }

    return createErrorResponse(500, error.message || '系统错误');
  }
}

/**
 * 处理具体的Mock请求逻辑
 * @param {string} path - 请求路径
 * @param {string} method - 请求方法
 * @param {Object} requestData - 请求数据
 * @param {Object} mockData - Mock数据
 * @returns {Object} 响应数据
 */
async function processMockRequest(path, method, requestData, mockData) {
  // 根据路径确定处理逻辑
  if (path.includes('/page')) {
    // 分页查询
    return handlePageRequest(requestData, mockData);
  } else if (path.includes('/detail')) {
    // 详情查询
    return handleDetailRequest(requestData, mockData);
  } else if (path.includes('/create')) {
    // 新增
    return handleCreateRequest(requestData, mockData);
  } else if (path.includes('/update')) {
    // 更新
    return handleUpdateRequest(requestData, mockData);
  } else if (path.includes('/delete')) {
    // 删除
    return handleDeleteRequest(requestData, mockData);
  } else if (path.includes('/onOff')) {
    // 启用/停用
    return handleToggleRequest(requestData, mockData);
  } else if (path.includes('/export')) {
    // 导出
    return handleExportRequest(requestData, mockData);
  } else {
    // 默认处理
    return {
      code: mockConfig?.response?.successCode || '200',
      msg: mockConfig?.response?.defaultMessage || '操作成功',
      data: mockData.defaultResponse || 'success',
      total: 0,
    };
  }
}

/**
 * 处理分页查询请求
 * @param {Object} requestData - 请求数据
 * @param {Object} mockData - Mock数据
 * @returns {Object} 响应数据
 */
function handlePageRequest(requestData, mockData) {
  const { pageNum = 1, pageSize = 10, ...filters } = requestData;
  let data = mockData.list || [];

  // 应用过滤条件
  if (Object.keys(filters).length > 0) {
    data = data.filter((item) => {
      return Object.entries(filters).every(([key, value]) => {
        if (value === undefined || value === null || value === '') {
          return true;
        }

        const itemValue = item[key];
        if (typeof value === 'string') {
          return String(itemValue).includes(value);
        }

        return itemValue === value;
      });
    });
  }

  // 分页处理
  const start = (pageNum - 1) * pageSize;
  const end = start + pageSize;
  const pageData = data.slice(start, end);

  return {
    code: mockConfig?.response?.successCode || '200',
    msg: mockConfig?.response?.defaultMessage || '操作成功',
    data: pageData,
    total: data.length,
  };
}

/**
 * 处理详情查询请求
 * @param {Object} requestData - 请求数据
 * @param {Object} mockData - Mock数据
 * @returns {Object} 响应数据
 */
function handleDetailRequest(requestData, mockData) {
  const { id, ruleId, infoId } = requestData;
  const targetId = id || ruleId || infoId;

  const data = mockData.list || [];
  const item = data.find(
    (item) =>
      item.id === targetId ||
      item.ruleId === targetId ||
      item.infoId === targetId
  );

  return {
    code: mockConfig?.response?.successCode || '200',
    msg: mockConfig?.response?.defaultMessage || '操作成功',
    data: item || null,
    total: item ? 1 : 0,
  };
}

/**
 * 处理新增请求
 * @param {Object} requestData - 请求数据
 * @param {Object} mockData - Mock数据
 * @returns {Object} 响应数据
 */
function handleCreateRequest(requestData, mockData) {
  // 模拟新增操作
  const newId = `mock_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  return {
    code: mockConfig?.response?.successCode || '200',
    msg: '创建成功',
    data: newId,
    total: 0,
  };
}

/**
 * 处理更新请求
 * @param {Object} requestData - 请求数据
 * @param {Object} mockData - Mock数据
 * @returns {Object} 响应数据
 */
function handleUpdateRequest(requestData, mockData) {
  return {
    code: mockConfig?.response?.successCode || '200',
    msg: '更新成功',
    data: 'success',
    total: 0,
  };
}

/**
 * 处理删除请求
 * @param {Object} requestData - 请求数据
 * @param {Object} mockData - Mock数据
 * @returns {Object} 响应数据
 */
function handleDeleteRequest(requestData, mockData) {
  return {
    code: mockConfig?.response?.successCode || '200',
    msg: '删除成功',
    data: 'success',
    total: 0,
  };
}

/**
 * 处理启用/停用请求
 * @param {Object} requestData - 请求数据
 * @param {Object} mockData - Mock数据
 * @returns {Object} 响应数据
 */
function handleToggleRequest(requestData, mockData) {
  return {
    code: mockConfig?.response?.successCode || '200',
    msg: '操作成功',
    data: 'success',
    total: 0,
  };
}

/**
 * 处理导出请求
 * @param {Object} requestData - 请求数据
 * @param {Object} mockData - Mock数据
 * @returns {Object} 响应数据
 */
function handleExportRequest(requestData, mockData) {
  // 模拟导出操作，实际应返回文件流
  return {
    code: mockConfig?.response?.successCode || '200',
    msg: '导出成功',
    data: 'export_file_url',
    total: 0,
  };
}

/**
 * 主要的fetch事件处理器
 */
self.addEventListener('fetch', (event) => {
  const request = event.request;

  // 只处理API请求
  if (!request.url.includes('/vehicle-charging-admin/')) {
    return;
  }

  // 检查是否应该模拟此请求
  if (shouldMockRequest(request)) {
    event.respondWith(handleMockRequest(request));
  }
  // 否则让请求正常通过
});

/**
 * 消息处理器
 */
self.addEventListener('message', (event) => {
  const { type, data } = event.data;

  switch (type) {
    case 'CONFIG_UPDATE':
      mockConfig = data.config;
      console.log('[SW Mock Manager] Configuration updated:', mockConfig);
      break;

    case 'CLEAR_CACHE':
      dataCache.clear();
      console.log('[SW Mock Manager] Cache cleared');
      break;

    case 'GET_METRICS':
      event.ports[0].postMessage({
        type: 'METRICS_RESPONSE',
        data: {
          ...performanceMetrics,
          averageResponseTime:
            performanceMetrics.requestCount > 0
              ? performanceMetrics.totalResponseTime /
                performanceMetrics.requestCount
              : 0,
          cacheHitRate:
            performanceMetrics.requestCount > 0
              ? performanceMetrics.cacheHits / performanceMetrics.requestCount
              : 0,
          errorRate:
            performanceMetrics.requestCount > 0
              ? performanceMetrics.errorCount / performanceMetrics.requestCount
              : 0,
        },
      });
      break;

    default:
      console.warn('[SW Mock Manager] Unknown message type:', type);
  }
});

console.log('[SW Mock Manager] Service Worker loaded and ready');
