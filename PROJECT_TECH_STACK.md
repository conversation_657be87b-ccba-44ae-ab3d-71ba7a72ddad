# 车网互动管理平台 - 技术栈文档

## 项目基本信息

### 项目概述
- **项目名称**: v2g-charging-web (车网互动管理平台)
- **项目版本**: 3.8.9
- **项目描述**: 车网互动管理平台
- **开发团队**: 若依
- **许可证**: MIT

### 技术架构
```
前端架构: Vue 2.x + Element UI + Vuex + Vue Router
组件库: Element UI + BuseCrud + VXE Table
构建工具: Vue CLI 4.x + Webpack
样式预处理: SCSS/Sass
状态管理: Vuex + vuex-persistedstate
网络请求: Axios
```

## 核心技术栈

### 🎯 前端框架
| 技术 | 版本 | 用途 | 说明 |
|------|------|------|------|
| **Vue.js** | 2.6.12 | 前端框架 | 渐进式JavaScript框架 |
| **Vue Router** | 3.4.9 | 路由管理 | 官方路由管理器 |
| **Vuex** | 3.6.0 | 状态管理 | 集中式状态管理模式 |
| **Vue CLI** | 4.4.6 | 构建工具 | 标准工具链 |

### 🎨 UI组件库
| 技术 | 版本 | 用途 | 说明 |
|------|------|------|------|
| **Element UI** | 2.15.14 | 基础组件库 | 桌面端组件库 |
| **BuseCrud** | 1.0.0 | 业务组件库 | 自定义CRUD组件 |
| **VXE Table** | 3.8.25 | 表格组件 | 高性能表格组件 |

### 📊 数据可视化
| 技术 | 版本 | 用途 | 说明 |
|------|------|------|------|
| **ECharts** | 5.4.0 | 图表库 | 数据可视化图表 |
| **ECharts GL** | 2.0.9 | 3D图表 | 3D可视化扩展 |
| **Three.js** | 0.142.0 | 3D引擎 | 3D图形库 |

### 🛠️ 工具库
| 技术 | 版本 | 用途 | 说明 |
|------|------|------|------|
| **Axios** | 0.28.1 | HTTP客户端 | 网络请求库 |
| **Moment.js** | 2.30.1 | 时间处理 | 日期时间库 |
| **Lodash** | - | 工具函数 | JavaScript工具库 |
| **Math.js** | 14.2.1 | 数学计算 | 数学表达式解析器 |
| **Decimal.js** | 10.5.0 | 精确计算 | 任意精度十进制类型 |

### 🔧 开发工具
| 技术 | 版本 | 用途 | 说明 |
|------|------|------|------|
| **ESLint** | 7.15.0 | 代码检查 | JavaScript代码检查工具 |
| **Sass** | 1.32.13 | CSS预处理 | CSS预处理器 |
| **Babel** | - | JS编译 | JavaScript编译器 |
| **Webpack** | - | 模块打包 | 模块打包工具 |

### 📱 功能增强
| 技术 | 版本 | 用途 | 说明 |
|------|------|------|------|
| **vue-meta** | 2.4.0 | SEO优化 | 管理页面元信息 |
| **screenfull** | 5.0.2 | 全屏功能 | 全屏API封装 |
| **nprogress** | 0.2.0 | 进度条 | 页面加载进度条 |
| **js-cookie** | 3.0.1 | Cookie管理 | Cookie操作库 |
| **clipboard** | 2.0.8 | 剪贴板 | 剪贴板操作 |

### 🔐 安全相关
| 技术 | 版本 | 用途 | 说明 |
|------|------|------|------|
| **jsencrypt** | 3.0.0-rc.1 | RSA加密 | RSA加密解密 |
| **js-md5** | 0.8.3 | MD5加密 | MD5哈希算法 |
| **js-base64** | 3.7.7 | Base64编码 | Base64编码解码 |

### 📄 文件处理
| 技术 | 版本 | 用途 | 说明 |
|------|------|------|------|
| **file-saver** | 2.0.5 | 文件下载 | 客户端文件保存 |
| **vue-cropper** | 0.5.5 | 图片裁剪 | 图片裁剪组件 |
| **quill** | 2.0.2 | 富文本编辑 | 富文本编辑器 |

### 🎮 交互增强
| 技术 | 版本 | 用途 | 说明 |
|------|------|------|------|
| **sortablejs** | 1.10.2 | 拖拽排序 | 拖拽排序库 |
| **vuedraggable** | 2.24.3 | Vue拖拽 | Vue拖拽组件 |
| **vue-count-to** | 1.0.13 | 数字动画 | 数字滚动动画 |
| **gsap** | 3.12.5 | 动画库 | 高性能动画库 |

## 项目结构

### 目录结构
```
v2g-charging-web/
├── public/                 # 静态资源
├── src/                    # 源代码
│   ├── api/               # API接口
│   ├── assets/            # 静态资源
│   ├── components/        # 全局组件
│   ├── directive/         # 自定义指令
│   ├── layout/            # 布局组件
│   ├── plugins/           # 插件
│   ├── router/            # 路由配置
│   ├── store/             # Vuex状态管理
│   ├── styles/            # 全局样式
│   ├── utils/             # 工具函数
│   ├── views/             # 页面组件
│   ├── App.vue            # 根组件
│   └── main.js            # 入口文件
├── package.json           # 依赖配置
└── vue.config.js          # Vue CLI配置
```

### 核心模块
- **用户管理**: 企业用户、员工、充电卡管理
- **充电管理**: 充电站、充电桩、订单管理
- **运维配置**: 故障代码、派单规则、运维班组
- **智能调度**: 车网互动、SOC配置
- **营销管理**: 优惠券、活动、发票管理

## 开发环境

### 环境要求
- **Node.js**: >= 8.9
- **NPM**: >= 3.0.0
- **浏览器**: > 1%, last 2 versions

### 开发命令
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 构建测试版本
npm run build:test

# 构建预发布版本
npm run build:stage

# 代码检查
npm run lint

# 预览构建结果
npm run preview
```

### 环境配置
- **开发环境**: development
- **测试环境**: test
- **预发布环境**: staging
- **生产环境**: production

## 特色功能

### 1. 响应式设计
- 支持多种屏幕尺寸
- 移动端适配
- 自适应布局

### 2. 权限控制
- 基于角色的权限管理
- 路由级权限控制
- 按钮级权限控制

### 3. 国际化支持
- 多语言切换
- 动态语言包加载
- 本地化配置

### 4. 主题定制
- 多主题切换
- 自定义主题色
- 暗黑模式支持

### 5. 性能优化
- 路由懒加载
- 组件按需加载
- 图片懒加载
- 代码分割

## 浏览器兼容性

### 支持的浏览器
- **Chrome**: >= 60
- **Firefox**: >= 60
- **Safari**: >= 12
- **Edge**: >= 79
- **IE**: 不支持

### 兼容性策略
- 使用Babel进行ES6+转换
- Polyfill支持
- CSS前缀自动添加
- 渐进式增强

## 部署说明

### 构建配置
- **开发环境**: 热重载、Source Map
- **生产环境**: 代码压缩、Tree Shaking
- **CDN支持**: 静态资源CDN部署
- **Gzip压缩**: 资源压缩优化

### 部署流程
1. 代码构建: `npm run build`
2. 静态资源上传CDN
3. 服务器部署
4. 域名配置
5. SSL证书配置

## 维护说明

### 代码规范
- ESLint代码检查
- Prettier代码格式化
- Git Hooks预提交检查
- 组件命名规范

### 版本管理
- 语义化版本控制
- Git Flow工作流
- 变更日志维护
- 发布流程规范

### 性能监控
- 页面加载性能
- 接口响应时间
- 错误日志收集
- 用户行为分析
