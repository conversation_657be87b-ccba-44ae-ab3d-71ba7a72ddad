# 接口模拟系统使用说明

## 系统概述

本项目基于ServiceWorker技术实现了一套完整的接口模拟系统，能够在开发环境下无缝替代真实API接口，提供高度真实的模拟数据和完整的CRUD操作支持。

## 系统架构

### 核心组件架构
```
┌─────────────────────────────────────────────────────────────┐
│                    前端应用 (Vue.js)                        │
├─────────────────────────────────────────────────────────────┤
│                 Axios请求层 (request.js)                    │
├─────────────────────────────────────────────────────────────┤
│              ServiceWorker拦截层 (sw-mock-manager.js)        │
├─────────────────────────────────────────────────────────────┤
│                Mock配置管理 (mock.config.js)                │
├─────────────────────────────────────────────────────────────┤
│              Mock数据存储 (public/mock/*.json)              │
└─────────────────────────────────────────────────────────────┘
```

### 工作流程
1. **应用启动** → 根据配置注册ServiceWorker
2. **API请求** → Axios发起请求
3. **请求拦截** → ServiceWorker检查是否需要模拟
4. **数据处理** → 从JSON文件读取数据并处理
5. **响应返回** → 模拟网络延迟后返回标准格式响应

## 安装配置

### 1. 基础配置

#### 环境变量设置
在项目根目录的 `.env.development` 文件中添加：
```bash
# 启用Mock功能
VUE_APP_ENABLE_MOCK=true

# API基础路径
VUE_APP_BASE_API=/dev-api
```

#### 全局配置文件
项目根目录的 `mock.config.js` 是核心配置文件：
```javascript
const mockConfig = {
  // 基础配置
  enabled: true,                    // 是否启用Mock功能
  baseURL: '/dev-api',             // API基础路径
  timeout: 1000,                   // 模拟网络延迟（毫秒）

  // ServiceWorker配置
  serviceWorker: {
    enabled: true,                 // 是否启用ServiceWorker模拟
    scriptPath: '/sw-mock-manager.js',
    scope: '/',
  },

  // Mock数据配置
  mockData: {
    basePath: '/mock',             // Mock数据文件基础路径
    fileExtension: '.json',        // 数据文件扩展名
    cacheEnabled: true,            // 是否启用数据缓存
  },

  // 模块级别控制
  modules: {
    operationMaintenanceConfig: {
      enabled: true,
      routes: {
        dispatchRule: {
          enabled: true,
          dataFile: 'operationMaintenanceConfig-dispatchRule.json',
          routes: {
            '/vehicle-charging-admin/ops/rule/page': true,
            '/vehicle-charging-admin/ops/rule/detail': true,
            // ... 更多路由配置
          },
        },
      },
    },
  },
};
```

### 2. 项目集成

#### 在main.js中初始化
```javascript
import { initMockSystem } from '@/utils/mockServiceWorker';

// 初始化Mock系统
if (process.env.NODE_ENV === 'development') {
  initMockSystem().then(success => {
    if (success) {
      console.log('🚀 Mock系统已启动');
    } else {
      console.log('⚠️ Mock系统启动失败或已禁用');
    }
  });
}
```

#### ServiceWorker文件部署
确保 `public/sw-mock-manager.js` 文件存在且可访问。

## Mock数据文件编写

### 1. 文件组织结构
```
public/mock/
├── operationMaintenanceConfig-dispatchRule.json    # 派单规则管理
├── operationMaintenanceConfig-faultCode.json       # 故障代码管理
├── sortingManage-problemFeedback.json              # 问题反馈管理
└── user-manage.json                                # 用户管理
```

### 2. 数据文件格式规范

#### 标准数据文件模板
```json
{
  "list": [
    {
      "id": "unique_id",
      "field1": "value1",
      "field2": "value2",
      "createTime": "2024-12-01 10:30:00",
      "updateTime": "2024-12-01 10:30:00",
      "createBy": "创建人",
      "updateBy": "更新人"
    }
  ],
  "statistics": {
    "total": 100,
    "enabled": 80,
    "disabled": 20
  },
  "options": {
    "statusOptions": [
      { "value": 0, "label": "停用" },
      { "value": 1, "label": "启用" }
    ]
  },
  "templates": [],
  "defaultResponse": "success"
}
```

#### 派单规则数据示例
```json
{
  "list": [
    {
      "ruleId": "rule_001",
      "ruleCode": "RULE001",
      "ruleName": "电压异常检测规则",
      "checkRule": "VOLTAGE",
      "compareOperator": "GT",
      "checkPercent": 85,
      "enableStatus": 1,
      "merchantId": "default",
      "remark": "当电压超过85%阈值时触发派单",
      "createTime": "2024-11-15 09:30:00",
      "updateTime": "2024-11-15 09:30:00",
      "createBy": "系统管理员",
      "updateBy": "系统管理员"
    }
  ],
  "statistics": {
    "totalRules": 20,
    "enabledRules": 14,
    "disabledRules": 6
  },
  "defaultResponse": "success"
}
```

### 3. 数据生成工具使用

#### 使用内置数据生成器
```javascript
import { businessDataGenerator } from '@/utils/mockDataGenerator';

// 生成派单规则数据
const dispatchRules = businessDataGenerator.generateDispatchRules(20);

// 生成故障代码数据
const faultCodes = businessDataGenerator.generateFaultCodes(15);

// 生成问题反馈数据
const problemFeedbacks = businessDataGenerator.generateProblemFeedbacks(10);
```

#### 自定义数据生成
```javascript
import { MockDataGenerator } from '@/utils/mockDataGenerator';

const generator = new MockDataGenerator({ seed: 12345 });

// 生成自定义数据
const customData = Array.from({ length: 10 }, (_, i) => ({
  id: generator.uuid(),
  name: generator.chineseName(),
  phone: generator.phoneNumber(),
  email: generator.email(),
  company: generator.companyName(),
  address: generator.address(),
  amount: generator.amount(100, 5000),
  createTime: generator.datetime(),
}));
```

## 为新页面添加接口模拟

### 1. 分析页面接口需求

以用户管理页面为例，需要支持以下接口：
- `GET /api/user/page` - 分页查询
- `POST /api/user/create` - 新增用户
- `PUT /api/user/update` - 更新用户
- `DELETE /api/user/delete` - 删除用户
- `GET /api/user/detail` - 用户详情

### 2. 配置路由映射

在 `mock.config.js` 中添加模块配置：
```javascript
modules: {
  user: {
    enabled: true,
    routes: {
      userManage: {
        enabled: true,
        dataFile: 'user-manage.json',
        routes: {
          '/vehicle-charging-admin/user/page': true,
          '/vehicle-charging-admin/user/detail': true,
          '/vehicle-charging-admin/user/create': true,
          '/vehicle-charging-admin/user/update': true,
          '/vehicle-charging-admin/user/delete': true,
        },
      },
    },
  },
}
```

### 3. 创建Mock数据文件

创建 `public/mock/user-manage.json`：
```json
{
  "list": [
    {
      "userId": "user_001",
      "userName": "张三",
      "userPhone": "13800138001",
      "userEmail": "<EMAIL>",
      "status": 1,
      "createTime": "2024-11-15 09:30:00",
      "updateTime": "2024-11-15 09:30:00",
      "createBy": "管理员",
      "updateBy": "管理员"
    }
  ],
  "statistics": {
    "totalUsers": 100,
    "activeUsers": 85,
    "inactiveUsers": 15
  },
  "statusOptions": [
    { "value": 0, "label": "停用" },
    { "value": 1, "label": "启用" }
  ],
  "defaultResponse": "success"
}
```

### 4. 验证接口模拟

在浏览器开发者工具中：
1. 打开Network面板
2. 执行页面操作
3. 查看请求是否被ServiceWorker拦截
4. 检查响应数据格式是否正确

## 调试和故障排除

### 1. 开发工具

#### 控制台调试命令
```javascript
// 查看Mock系统状态
window.MockDebug.getStatus();

// 获取性能指标
window.MockDebug.getMetrics();

// 清除缓存
window.MockDebug.clearCache();

// 启用/禁用Mock功能
window.MockDebug.setEnabled(true/false);

// 启用/禁用特定模块
window.MockDebug.setModuleEnabled('operationMaintenanceConfig', true);

// 启用/禁用特定路由
window.MockDebug.setRouteEnabled('/api/user/page', false);
```

#### 快捷键支持
- `Ctrl + Shift + M` - 打开Mock调试面板

### 2. 常见问题排查

#### ServiceWorker未注册
**症状**: 请求直接发送到真实API
**解决方案**:
1. 检查浏览器是否支持ServiceWorker
2. 确认 `mock.config.js` 中 `enabled: true`
3. 检查控制台是否有注册错误

#### Mock数据未加载
**症状**: 接口返回404或空数据
**解决方案**:
1. 确认数据文件路径正确
2. 检查JSON格式是否有效
3. 查看Network面板确认文件是否成功加载

#### 路由配置不生效
**症状**: 特定接口未被拦截
**解决方案**:
1. 检查路由路径是否完全匹配
2. 确认模块和路由都已启用
3. 重新注册ServiceWorker

### 3. 性能监控

#### 查看性能指标
```javascript
window.MockDebug.getMetrics().then(metrics => {
  console.log('请求总数:', metrics.requestCount);
  console.log('平均响应时间:', metrics.averageResponseTime);
  console.log('错误率:', metrics.errorRate);
  console.log('缓存命中率:', metrics.cacheHitRate);
});
```

#### 监控事件
```javascript
// 监听指标更新
window.addEventListener('mockMetricsUpdate', (event) => {
  console.log('Mock指标更新:', event.detail);
});
```

## 与现有技术栈集成

### 1. Axios集成

Mock系统与现有的Axios封装完全兼容：
```javascript
// 现有的API调用方式不需要任何修改
import { getUserPage } from '@/api/user';

async function loadUsers() {
  const [err, res] = await getUserPage({
    pageNum: 1,
    pageSize: 10
  });

  if (err) return;

  // 开发环境下会自动使用Mock数据
  // 生产环境下会调用真实API
  this.userList = res.data;
}
```

### 2. BuseCrud组件集成

Mock数据格式完全匹配BuseCrud组件要求：
```javascript
// 表格配置
tableColumn: [
  { field: 'ruleId', title: 'ID' },
  { field: 'ruleName', title: '规则名称' },
  { field: 'enableStatus', title: '状态' },
],

// 筛选配置
filterOptions: {
  config: [
    { field: 'ruleName', title: '规则名称', element: 'el-input' },
    { field: 'enableStatus', title: '状态', element: 'el-select' },
  ],
  params: this.queryParams
}
```

### 3. 环境切换

系统支持无缝的环境切换：
- **开发环境**: 自动使用Mock数据
- **测试环境**: 可配置使用Mock或真实API
- **生产环境**: 自动禁用Mock功能

### 4. 构建集成

在 `vue.config.js` 中可以添加构建时配置：
```javascript
module.exports = {
  // 开发服务器配置
  devServer: {
    // Mock系统不需要额外的代理配置
    // ServiceWorker会自动处理API请求
  },

  // 生产构建时排除Mock文件
  configureWebpack: (config) => {
    if (process.env.NODE_ENV === 'production') {
      config.externals = {
        ...config.externals,
        '@/utils/mockServiceWorker': 'void 0'
      };
    }
  }
};
```

## 最佳实践

### 1. 数据一致性
- 保持Mock数据与真实API数据结构一致
- 定期更新Mock数据以反映业务变化
- 使用相同的字段命名和数据类型

### 2. 性能优化
- 启用数据缓存减少文件读取
- 合理设置网络延迟模拟
- 避免生成过大的数据文件

### 3. 团队协作
- 统一Mock数据格式规范
- 建立数据文件版本管理
- 及时同步接口变更

### 4. 测试支持
- 为不同测试场景准备不同的数据集
- 支持错误场景模拟
- 提供数据重置功能

## 总结

本接口模拟系统提供了完整的开发环境API模拟解决方案，具有以下优势：

1. **无侵入性**: 与现有代码完全兼容，无需修改业务逻辑
2. **高度真实**: 支持完整的CRUD操作和复杂业务逻辑模拟
3. **易于配置**: 灵活的配置系统，支持细粒度控制
4. **开发友好**: 丰富的调试工具和性能监控
5. **生产安全**: 生产环境自动禁用，无安全风险

通过合理使用本系统，可以显著提高前端开发效率，降低对后端接口的依赖，提升整体开发体验。

## 高级功能

### 1. 动态数据生成

#### 参数化数据生成
```javascript
// 在Mock数据文件中支持动态参数
{
  "list": "{{generateDispatchRules(20)}}",
  "statistics": "{{calculateStatistics(list)}}"
}
```

#### 关联数据生成
```javascript
// 生成具有关联关系的数据
const generator = new BusinessDataGenerator();

// 生成用户数据
const users = generator.generateUsers(10);

// 基于用户生成订单数据
const orders = users.flatMap(user =>
  generator.generateOrdersForUser(user.userId, 3)
);
```

### 2. 错误场景模拟

#### 配置错误模拟
```javascript
// 在mock.config.js中配置
errorSimulation: {
  enabled: true,
  globalErrorRate: 0.05,  // 5%全局错误率
  errorTypes: {
    network: 0.3,    // 30%网络错误
    server: 0.4,     // 40%服务器错误
    timeout: 0.2,    // 20%超时错误
    business: 0.1,   // 10%业务错误
  },
  errorCodes: {
    400: '请求参数错误',
    401: '未授权访问',
    500: '服务器内部错误',
  },
}
```

#### 特定接口错误配置
```javascript
routes: {
  '/api/user/create': {
    enabled: true,
    errorRate: 0.1,  // 10%错误率
    errorCode: 400,
    errorMessage: '用户名已存在'
  }
}
```

### 3. 数据模板系统

#### 创建数据模板
```javascript
// 在数据文件中定义模板
{
  "templates": [
    {
      "name": "standardUser",
      "template": {
        "userId": "{{uuid()}}",
        "userName": "{{chineseName()}}",
        "userPhone": "{{phoneNumber()}}",
        "status": 1,
        "createTime": "{{datetime()}}"
      }
    }
  ],
  "list": "{{generateFromTemplate('standardUser', 20)}}"
}
```

### 4. 实时数据更新

#### WebSocket模拟支持
```javascript
// 模拟实时数据推送
class MockWebSocket {
  constructor(url) {
    this.url = url;
    this.readyState = WebSocket.CONNECTING;

    setTimeout(() => {
      this.readyState = WebSocket.OPEN;
      this.onopen && this.onopen();

      // 定期推送模拟数据
      this.startDataPush();
    }, 100);
  }

  startDataPush() {
    setInterval(() => {
      if (this.readyState === WebSocket.OPEN) {
        const mockData = generateRealtimeData();
        this.onmessage && this.onmessage({
          data: JSON.stringify(mockData)
        });
      }
    }, 5000);
  }
}

// 在开发环境下替换WebSocket
if (process.env.NODE_ENV === 'development') {
  window.WebSocket = MockWebSocket;
}
```

## 扩展开发

### 1. 自定义数据处理器

#### 创建自定义处理器
```javascript
// src/utils/customMockHandlers.js
export class CustomMockHandler {
  static handleComplexQuery(requestData, mockData) {
    // 实现复杂查询逻辑
    const { filters, sorts, aggregations } = requestData;

    let result = mockData.list;

    // 应用过滤器
    if (filters) {
      result = this.applyFilters(result, filters);
    }

    // 应用排序
    if (sorts) {
      result = this.applySorts(result, sorts);
    }

    // 应用聚合
    if (aggregations) {
      result = this.applyAggregations(result, aggregations);
    }

    return result;
  }

  static applyFilters(data, filters) {
    return data.filter(item => {
      return filters.every(filter => {
        const { field, operator, value } = filter;
        const itemValue = item[field];

        switch (operator) {
          case 'eq': return itemValue === value;
          case 'ne': return itemValue !== value;
          case 'gt': return itemValue > value;
          case 'lt': return itemValue < value;
          case 'contains': return String(itemValue).includes(value);
          case 'startsWith': return String(itemValue).startsWith(value);
          case 'endsWith': return String(itemValue).endsWith(value);
          default: return true;
        }
      });
    });
  }
}
```

#### 在ServiceWorker中使用
```javascript
// 在sw-mock-manager.js中导入并使用
importScripts('/src/utils/customMockHandlers.js');

function processMockRequest(path, method, requestData, mockData) {
  if (path.includes('/complexQuery')) {
    const result = CustomMockHandler.handleComplexQuery(requestData, mockData);
    return {
      code: '200',
      msg: '查询成功',
      data: result,
      total: result.length
    };
  }

  // 其他处理逻辑...
}
```

### 2. 插件系统

#### 创建Mock插件
```javascript
// src/plugins/mockPlugin.js
export class MockPlugin {
  constructor(name, options = {}) {
    this.name = name;
    this.options = options;
    this.hooks = {};
  }

  // 注册钩子
  addHook(hookName, handler) {
    if (!this.hooks[hookName]) {
      this.hooks[hookName] = [];
    }
    this.hooks[hookName].push(handler);
  }

  // 执行钩子
  async executeHook(hookName, ...args) {
    const handlers = this.hooks[hookName] || [];
    for (const handler of handlers) {
      await handler(...args);
    }
  }

  // 请求前处理
  beforeRequest(request) {
    return this.executeHook('beforeRequest', request);
  }

  // 响应后处理
  afterResponse(response) {
    return this.executeHook('afterResponse', response);
  }
}

// 数据验证插件
export class DataValidationPlugin extends MockPlugin {
  constructor(options) {
    super('DataValidation', options);

    this.addHook('beforeRequest', this.validateRequest.bind(this));
  }

  validateRequest(request) {
    const { method, data } = request;

    if (method === 'POST' && this.options.schemas) {
      const schema = this.options.schemas[request.url];
      if (schema) {
        this.validateData(data, schema);
      }
    }
  }

  validateData(data, schema) {
    // 实现数据验证逻辑
    for (const [field, rules] of Object.entries(schema)) {
      if (rules.required && !data[field]) {
        throw new Error(`字段 ${field} 是必填的`);
      }

      if (rules.type && typeof data[field] !== rules.type) {
        throw new Error(`字段 ${field} 类型错误`);
      }
    }
  }
}
```

### 3. 性能优化

#### 数据分片加载
```javascript
// 大数据集分片处理
class DataChunkLoader {
  constructor(dataFile, chunkSize = 1000) {
    this.dataFile = dataFile;
    this.chunkSize = chunkSize;
    this.chunks = new Map();
  }

  async loadChunk(chunkIndex) {
    if (this.chunks.has(chunkIndex)) {
      return this.chunks.get(chunkIndex);
    }

    const response = await fetch(`/mock/chunks/${this.dataFile}_${chunkIndex}.json`);
    const chunk = await response.json();

    this.chunks.set(chunkIndex, chunk);
    return chunk;
  }

  async getPageData(pageNum, pageSize) {
    const startIndex = (pageNum - 1) * pageSize;
    const endIndex = startIndex + pageSize;

    const startChunk = Math.floor(startIndex / this.chunkSize);
    const endChunk = Math.floor(endIndex / this.chunkSize);

    const chunks = [];
    for (let i = startChunk; i <= endChunk; i++) {
      chunks.push(await this.loadChunk(i));
    }

    const allData = chunks.flat();
    const localStart = startIndex % this.chunkSize;
    const localEnd = localStart + pageSize;

    return allData.slice(localStart, localEnd);
  }
}
```

#### 缓存优化
```javascript
// 智能缓存管理
class SmartCache {
  constructor(maxSize = 100, ttl = 300000) {
    this.cache = new Map();
    this.accessTimes = new Map();
    this.maxSize = maxSize;
    this.ttl = ttl;
  }

  set(key, value) {
    // 如果缓存已满，清理最少使用的项
    if (this.cache.size >= this.maxSize) {
      this.evictLeastUsed();
    }

    this.cache.set(key, {
      value,
      timestamp: Date.now()
    });
    this.accessTimes.set(key, Date.now());
  }

  get(key) {
    const item = this.cache.get(key);
    if (!item) return null;

    // 检查是否过期
    if (Date.now() - item.timestamp > this.ttl) {
      this.cache.delete(key);
      this.accessTimes.delete(key);
      return null;
    }

    // 更新访问时间
    this.accessTimes.set(key, Date.now());
    return item.value;
  }

  evictLeastUsed() {
    let leastUsedKey = null;
    let leastUsedTime = Date.now();

    for (const [key, time] of this.accessTimes) {
      if (time < leastUsedTime) {
        leastUsedTime = time;
        leastUsedKey = key;
      }
    }

    if (leastUsedKey) {
      this.cache.delete(leastUsedKey);
      this.accessTimes.delete(leastUsedKey);
    }
  }
}
```

## 故障排除指南

### 1. 常见错误及解决方案

#### ServiceWorker注册失败
```
错误: Failed to register service worker
原因: HTTPS要求或文件路径错误
解决:
1. 确保在localhost或HTTPS环境下运行
2. 检查sw-mock-manager.js文件路径
3. 查看浏览器控制台详细错误信息
```

#### Mock数据加载失败
```
错误: Failed to load mock data
原因: JSON格式错误或文件不存在
解决:
1. 验证JSON文件格式
2. 检查文件路径配置
3. 确认文件权限
```

#### 路由匹配失败
```
错误: Route not matched for mocking
原因: 路由配置不正确
解决:
1. 检查路径是否完全匹配
2. 确认HTTP方法配置
3. 验证模块启用状态
```

### 2. 调试技巧

#### 启用详细日志
```javascript
// 在mock.config.js中启用调试
devTools: {
  enabled: true,
  console: {
    logRequests: true,
    logResponses: true,
    logErrors: true,
  },
}
```

#### 使用浏览器调试工具
```javascript
// 在ServiceWorker中添加断点
self.addEventListener('fetch', (event) => {
  debugger; // 在这里添加断点

  if (shouldMockRequest(event.request)) {
    event.respondWith(handleMockRequest(event.request));
  }
});
```

#### 网络面板分析
1. 打开开发者工具 → Network面板
2. 查看请求是否显示"(from ServiceWorker)"
3. 检查响应时间和数据格式
4. 分析失败请求的错误信息

### 3. 性能问题排查

#### 响应时间过长
```javascript
// 检查数据文件大小
console.log('Data file size:', JSON.stringify(mockData).length);

// 优化建议:
// 1. 减少单个文件的数据量
// 2. 启用数据缓存
// 3. 使用数据分片
```

#### 内存使用过高
```javascript
// 监控内存使用
setInterval(() => {
  if (performance.memory) {
    console.log('Memory usage:', {
      used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) + 'MB',
      total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024) + 'MB',
      limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024) + 'MB'
    });
  }
}, 5000);
```

## 总结

本接口模拟系统是一个功能完整、高度可配置的开发工具，为前端开发提供了强大的API模拟能力。通过ServiceWorker技术实现的无侵入式设计，确保了与现有项目的完美集成。

系统的主要价值：
1. **提高开发效率** - 无需等待后端接口即可进行前端开发
2. **降低开发成本** - 减少前后端协调成本和环境依赖
3. **增强测试能力** - 支持各种边界情况和错误场景测试
4. **改善开发体验** - 提供丰富的调试工具和性能监控

通过本文档的指导，开发团队可以快速掌握系统的使用方法，充分发挥其在项目开发中的价值。
