# 全局组件说明文档

## 概述

本项目包含丰富的全局组件，分为基础组件、业务组件、图表组件等多个类别，提供了完整的UI解决方案。

## 组件分类

### 📊 核心业务组件

#### 1. BuseCrud 组件
**位置**: `buse-components-element` (第三方组件库)
**用途**: 统一的CRUD操作组件，集成表格、分页、筛选、操作等功能

**基础用法**:
```vue
<template>
  <BuseCrud
    ref="crud"
    :loading="loading"
    :filterOptions="filterOptions"
    :tablePage="tablePage"
    :tableColumn="tableColumn"
    :tableData="tableData"
    :pagerProps="pagerProps"
    :modalConfig="modalConfig"
    @loadData="loadData"
  >
    <template slot="defaultHeader">
      <div class="card-head">
        <div class="card-head-text">页面标题</div>
        <div class="top-button-wrap">
          <el-button type="primary" @click="handleAdd">新增</el-button>
        </div>
      </div>
    </template>
    
    <template slot="operate" slot-scope="{ row }">
      <el-button @click="handleEdit(row)">编辑</el-button>
      <el-button @click="handleDelete(row)">删除</el-button>
    </template>
  </BuseCrud>
</template>
```

**主要属性**:
```javascript
// 表格配置
tableColumn: [
  {
    field: 'name',           // 字段名
    title: '名称',           // 列标题
    minWidth: 120,          // 最小宽度
    slots: { default: 'customSlot' }  // 自定义插槽
  }
]

// 筛选配置
filterOptions: {
  config: [
    {
      field: 'name',
      title: '名称',
      element: 'el-input',   // 组件类型
      props: {
        placeholder: '请输入名称'
      }
    }
  ],
  params: this.queryParams
}

// 分页配置
tablePage: {
  total: 0,
  currentPage: 1,
  pageSize: 10
}

// 模态框配置
modalConfig: {
  addBtn: false,    // 隐藏新增按钮
  editBtn: false,   // 隐藏编辑按钮
  delBtn: false,    // 隐藏删除按钮
  menu: false       // 隐藏操作菜单
}
```

**事件处理**:
```javascript
// 表格事件
tableOn: {
  'checkbox-change': handleCheckboxChange,
  'checkbox-all': handleCheckboxChange
}

// 加载数据事件
@loadData="loadData"
```

#### 2. VXE Table 组件
**位置**: `vxe-table` (第三方表格组件)
**用途**: 高性能表格组件，支持虚拟滚动、树形结构等

**全局配置**:
```javascript
// src/register.js
import VXETable from 'vxe-table';
Vue.use(VXETable);
```

### 🎨 业务组件库

#### 1. StatusDot 状态点组件
**位置**: `src/components/Business/StatusDot/index.vue`
**用途**: 显示状态的圆点组件

```vue
<StatusDot
  :value="row.status"
  :dictValue="statusOptions"
  :colors="['success', 'danger']"
/>
```

#### 2. StatusTag 状态标签组件
**位置**: `src/components/Business/StatusTag/index.vue`
**用途**: 显示状态的标签组件

```vue
<StatusTag
  :value="row.status"
  :options="statusOptions"
/>
```

#### 3. RegionSelect 地区选择组件
**位置**: `src/components/Business/RegionSelect/index.vue`
**用途**: 省市区三级联动选择

```vue
<RegionSelect
  v-model="form.region"
  :level="3"
  placeholder="请选择地区"
/>
```

#### 4. OperatorSelect 运营商选择组件
**位置**: `src/components/Business/OperatorSelect/index.vue`
**用途**: 运营商选择下拉框

```vue
<OperatorSelect
  v-model="form.operatorId"
  placeholder="请选择运营商"
/>
```

#### 5. OrganizationLeft 组织架构组件
**位置**: `src/components/Business/OrganizationLeft/index.vue`
**用途**: 左侧组织架构树形组件

```vue
<OrganizationLeft
  ref="OrganizationLeft"
  @nodeClick="nodeClick"
  @modalConfirm="modalConfirm"
/>
```

#### 6. EmptyBox 空状态组件
**位置**: `src/components/Business/EmptyBox/index.vue`
**用途**: 数据为空时的占位组件

```vue
<EmptyBox
  :show="tableData.length === 0"
  description="暂无数据"
/>
```

### 📈 图表组件库

#### 1. Charts 图表组件
**位置**: `src/components/Charts/`
**用途**: 基于ECharts的图表组件集合

**可用图表类型**:
```vue
<!-- 柱状图 -->
<bar-chart :data="chartData" :options="chartOptions" />

<!-- 折线图 -->
<line-chart :data="chartData" :options="chartOptions" />

<!-- 饼图 -->
<pie-chart :data="chartData" :options="chartOptions" />

<!-- 仪表盘 -->
<gauge-chart :data="chartData" :options="chartOptions" />

<!-- 多重饼图 -->
<pie-multi-chart :data="chartData" :options="chartOptions" />
```

### 🛠️ 工具组件

#### 1. FileUpload 文件上传组件
**位置**: `src/components/FileUpload/index.vue`
**用途**: 文件上传功能

```vue
<FileUpload
  v-model="form.fileList"
  :limit="5"
  :fileSize="10"
  :fileType="['doc', 'docx', 'pdf']"
  @success="handleUploadSuccess"
/>
```

#### 2. ImageUpload 图片上传组件
**位置**: `src/components/ImageUpload/index.vue`
**用途**: 图片上传功能

```vue
<ImageUpload
  v-model="form.avatar"
  :limit="1"
  :fileSize="2"
/>
```

#### 3. ImagePreview 图片预览组件
**位置**: `src/components/ImagePreview/index.vue`
**用途**: 图片预览功能

```vue
<ImagePreview
  :src="imageSrc"
  :visible.sync="previewVisible"
/>
```

#### 4. Editor 富文本编辑器
**位置**: `src/components/Editor/index.vue`
**用途**: 基于Quill的富文本编辑器

```vue
<Editor
  v-model="form.content"
  :min-height="300"
  :placeholder="请输入内容"
/>
```

### 🎯 表单组件

#### 1. TreeSelect 树形选择组件
**位置**: `src/components/TreeSelect/index.vue`
**用途**: 树形结构选择器

```vue
<TreeSelect
  v-model="form.deptId"
  :options="deptOptions"
  :normalizer="normalizer"
  placeholder="请选择部门"
/>
```

#### 2. IconSelect 图标选择组件
**位置**: `src/components/IconSelect/index.vue`
**用途**: 图标选择器

```vue
<IconSelect
  v-model="form.icon"
  placeholder="请选择图标"
/>
```

#### 3. DictTag 字典标签组件
**位置**: `src/components/DictTag/index.vue`
**用途**: 字典值显示标签

```vue
<DictTag
  :options="dict.type.sys_user_sex"
  :value="scope.row.sex"
/>
```

### 🎨 布局组件

#### 1. Pagination 分页组件
**位置**: `src/components/Pagination/index.vue`
**用途**: 分页导航组件

```vue
<Pagination
  v-show="total > 0"
  :total="total"
  :page.sync="queryParams.pageNum"
  :limit.sync="queryParams.pageSize"
  @pagination="getList"
/>
```

#### 2. RightToolbar 右侧工具栏
**位置**: `src/components/RightToolbar/index.vue`
**用途**: 表格右侧工具栏

```vue
<RightToolbar
  :showSearch.sync="showSearch"
  @queryTable="getList"
  @resetQuery="resetQuery"
/>
```

#### 3. Breadcrumb 面包屑导航
**位置**: `src/components/Breadcrumb/index.vue`
**用途**: 页面路径导航

```vue
<Breadcrumb />
```

### 🔧 功能组件

#### 1. Screenfull 全屏组件
**位置**: `src/components/Screenfull/index.vue`
**用途**: 全屏切换功能

```vue
<Screenfull />
```

#### 2. HeaderSearch 头部搜索
**位置**: `src/components/HeaderSearch/index.vue`
**用途**: 头部搜索功能

```vue
<HeaderSearch />
```

#### 3. SizeSelect 尺寸选择
**位置**: `src/components/SizeSelect/index.vue`
**用途**: 组件尺寸切换

```vue
<SizeSelect />
```

#### 4. ThemePicker 主题选择器
**位置**: `src/components/ThemePicker/index.vue`
**用途**: 主题色选择

```vue
<ThemePicker />
```

## 组件注册

### 全局注册
```javascript
// src/main.js
import Vue from 'vue';

// 全局组件自动注册
const requireComponent = require.context(
  './components',
  true,
  /\.(vue|js)$/
);

requireComponent.keys().forEach(fileName => {
  const componentConfig = requireComponent(fileName);
  const componentName = fileName.replace(/^\.\/(.*)\.\w+$/, '$1');
  Vue.component(componentName, componentConfig.default || componentConfig);
});
```

### 按需注册
```javascript
// 在组件中按需引入
import StatusDot from '@/components/Business/StatusDot';
import FileUpload from '@/components/FileUpload';

export default {
  components: {
    StatusDot,
    FileUpload
  }
}
```

## 组件开发规范

### 1. 组件命名
- 使用PascalCase命名
- 组件名应该具有描述性
- 避免与HTML元素冲突

### 2. 组件结构
```vue
<template>
  <!-- 模板内容 -->
</template>

<script>
export default {
  name: 'ComponentName',
  props: {
    // 属性定义
  },
  data() {
    return {
      // 数据定义
    };
  },
  computed: {
    // 计算属性
  },
  methods: {
    // 方法定义
  }
};
</script>

<style lang="scss" scoped>
/* 样式定义 */
</style>
```

### 3. 属性定义
```javascript
props: {
  value: {
    type: [String, Number, Array],
    default: '',
    required: true,
    validator: function (value) {
      return value.length > 0;
    }
  }
}
```

### 4. 事件定义
```javascript
// 触发事件
this.$emit('change', newValue);
this.$emit('update:value', newValue);

// 事件文档注释
/**
 * @event change
 * @description 值改变时触发
 * @param {any} value - 新值
 */
```

## 最佳实践

### 1. 组件复用
- 提取公共逻辑到mixins
- 使用插槽提供灵活性
- 通过props控制组件行为

### 2. 性能优化
- 使用v-show代替v-if（频繁切换）
- 合理使用computed和watch
- 避免在模板中使用复杂表达式

### 3. 可维护性
- 添加详细的注释
- 使用TypeScript类型定义
- 编写单元测试

### 4. 样式管理
- 使用scoped样式避免污染
- 提取公共样式到全局
- 支持主题定制
