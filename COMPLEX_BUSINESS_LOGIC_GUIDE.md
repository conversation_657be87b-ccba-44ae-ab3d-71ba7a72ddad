# 复杂业务逻辑说明文档

## 概述

本文档详细说明项目中的复杂业务逻辑实现，包括工作流程、数据处理逻辑、验证规则、业务规则等核心功能的实现方式。

## 业务模块分类

### 🔄 工作流程管理

#### 1. 异常订单处理流程
**位置**: `src/views/order/abnormalOrder/`
**业务场景**: 充电订单异常处理的完整工作流

**核心流程**:
```javascript
// 异常订单处理工作流
const abnormalOrderWorkflow = {
  // 1. 异常检测
  detectAbnormal: async (orderData) => {
    const rules = await getAbnormalRules();
    return validateOrderAgainstRules(orderData, rules);
  },
  
  // 2. 异常标记
  markAbnormal: async (orderNo, abnormalInfo) => {
    const params = {
      orderNos: [orderNo],
      exceptionFlag: abnormalInfo.isAbnormal,
      level: abnormalInfo.level,
      type: abnormalInfo.type,
      exceptId: abnormalInfo.ruleId
    };
    return await markAbnormal(params);
  },
  
  // 3. 异常处理
  handleAbnormal: async (orderNo, handleInfo) => {
    // 费用重新计算
    const recalculatedFees = calculateFees(handleInfo);
    // 更新订单状态
    return await updateOrderStatus(orderNo, recalculatedFees);
  }
};
```

**费用计算逻辑**:
```javascript
// 精确费用计算（避免浮点数精度问题）
calculateFee(quantity, price) {
  return Math.round(quantity * price * 100) / 100;
},

// 精确加法运算
sumValues(a, b) {
  return Math.round((Number(a) + Number(b)) * 100) / 100;
},

// 单行数据更新逻辑
updateRow(row) {
  // 重新计算电费
  row.periodElecFeeAfterC = this.calculateFee(
    row.periodPqAfterC, 
    row.periodElecPrice
  );
  
  // 重新计算服务费
  row.periodServiceFeeAfterC = this.calculateFee(
    row.periodPqAfterC, 
    row.periodServicePrice
  );
  
  // 计算总费用
  row.periodTotalFeeAfterC = this.sumValues(
    row.periodElecFeeAfterC, 
    row.periodServiceFeeAfterC
  );
  
  // 触发响应式更新
  this.changeData = [...this.changeData];
}
```

#### 2. 调控计划下发流程
**位置**: `src/api/responseAndRegulation/planDispatch.js`
**业务场景**: 电网调控计划的制定、审核、下发流程

**流程步骤**:
```javascript
// 调控计划完整流程
const adjustmentPlanWorkflow = {
  // 1. 需求分析
  analyzeRequirement: async (requirementData) => {
    const options = await getRequirementOptions(requirementData);
    return processRequirementOptions(options);
  },
  
  // 2. 计划制定
  createPlan: async (planData) => {
    // 站点分配逻辑
    const stationAllocation = await allocateStations(planData);
    // 时间段分配
    const timeSlotAllocation = allocateTimeSlots(planData);
    return { stationAllocation, timeSlotAllocation };
  },
  
  // 3. 批量下发
  batchSendPlan: async (planIds) => {
    const results = [];
    for (const planId of planIds) {
      const result = await sendAdjustmentPlan({ planId });
      results.push(result);
    }
    return results;
  },
  
  // 4. 执行监控
  monitorExecution: async (planId) => {
    const stationList = await getPlanStationList({ planId });
    return monitorStationExecution(stationList);
  }
};
```

### 📊 数据处理与计算

#### 1. 结算规则引擎
**位置**: `src/api/settlement/`
**业务场景**: 复杂的费用结算规则计算

**结算规则模板**:
```javascript
// 结算规则模板系统
class SettlementRuleEngine {
  constructor() {
    this.templates = new Map();
    this.operators = new Map();
  }
  
  // 创建结算模板
  async createTemplate(templateData) {
    const template = {
      id: generateId(),
      name: templateData.name,
      rules: this.parseRules(templateData.rules),
      effectiveDate: templateData.effectiveDate,
      status: 'DRAFT'
    };
    
    await createSettleTemplate(template);
    return template;
  }
  
  // 规则解析
  parseRules(rulesConfig) {
    return rulesConfig.map(rule => ({
      condition: this.parseCondition(rule.condition),
      calculation: this.parseCalculation(rule.calculation),
      priority: rule.priority || 0
    }));
  }
  
  // 执行结算计算
  async calculateSettlement(orderData, templateId) {
    const template = await this.getTemplate(templateId);
    const applicableRules = this.filterApplicableRules(orderData, template.rules);
    
    let totalAmount = 0;
    for (const rule of applicableRules) {
      const amount = this.executeCalculation(orderData, rule.calculation);
      totalAmount += amount;
    }
    
    return {
      totalAmount,
      breakdown: this.generateBreakdown(applicableRules, orderData)
    };
  }
}
```

#### 2. 动态表单验证引擎
**位置**: `src/views/order/abnormalRule/creat/index.vue`
**业务场景**: 复杂的动态表单验证规则

**验证规则系统**:
```javascript
// 自定义验证规则映射
const validationRuleMap = {
  // 复合字段验证
  validationRuleMap(rule, value, callback) {
    const requiredFields = ['checkItem', 'checkMethod', 'threshold', 'unit'];
    const missingFields = requiredFields.filter(field => !value?.[field]);
    
    if (missingFields.length > 0) {
      callback(new Error(`请填写完整: ${missingFields.join(', ')}`));
      return;
    }
    
    // 业务逻辑验证
    if (!this.validateBusinessLogic(value)) {
      callback(new Error('业务规则验证失败'));
      return;
    }
    
    callback();
  },
  
  // 业务逻辑验证
  validateBusinessLogic(value) {
    const { checkItem, checkMethod, threshold, unit } = value;
    
    // 根据检查项目验证阈值范围
    const thresholdRanges = {
      'VOLTAGE': { min: 0, max: 1000, units: ['V', 'kV'] },
      'CURRENT': { min: 0, max: 500, units: ['A', 'mA'] },
      'POWER': { min: 0, max: 10000, units: ['W', 'kW', 'MW'] }
    };
    
    const range = thresholdRanges[checkItem];
    if (!range) return false;
    
    // 验证单位
    if (!range.units.includes(unit)) return false;
    
    // 验证阈值范围
    const numThreshold = parseFloat(threshold);
    if (numThreshold < range.min || numThreshold > range.max) return false;
    
    return true;
  }
};
```

### 🔐 权限与安全控制

#### 1. 多级权限验证
**位置**: `src/store/modules/permission.js`
**业务场景**: 基于角色的多级权限控制

**权限控制逻辑**:
```javascript
// 权限验证引擎
class PermissionEngine {
  constructor(userRoles, userPermissions) {
    this.userRoles = userRoles;
    this.userPermissions = userPermissions;
  }
  
  // 路由权限验证
  hasRoutePermission(route) {
    if (!route.meta?.roles) return true;
    return route.meta.roles.some(role => this.userRoles.includes(role));
  }
  
  // 操作权限验证
  hasOperationPermission(permission) {
    return this.userPermissions.includes(permission);
  }
  
  // 数据权限验证
  hasDataPermission(dataScope, userId) {
    const scopeMap = {
      'ALL': () => true,
      'DEPT': () => this.checkDeptPermission(userId),
      'SELF': () => this.checkSelfPermission(userId)
    };
    
    return scopeMap[dataScope]?.() || false;
  }
  
  // 动态权限过滤
  filterPermittedData(dataList, permissionKey) {
    return dataList.filter(item => {
      return this.hasDataPermission(item.dataScope, item.userId);
    });
  }
}
```

#### 2. 数据脱敏处理
**位置**: `src/utils/dataMask.js`
**业务场景**: 敏感数据的脱敏显示

```javascript
// 数据脱敏工具
class DataMaskUtil {
  // 手机号脱敏
  static maskPhone(phone) {
    if (!phone || phone.length !== 11) return phone;
    return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
  }
  
  // 身份证脱敏
  static maskIdCard(idCard) {
    if (!idCard) return idCard;
    return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2');
  }
  
  // 银行卡脱敏
  static maskBankCard(cardNo) {
    if (!cardNo) return cardNo;
    return cardNo.replace(/(\d{4})\d+(\d{4})/, '$1****$2');
  }
  
  // 根据权限级别脱敏
  static maskByPermission(data, permissionLevel) {
    const maskRules = {
      'LOW': ['phone', 'email'],
      'MEDIUM': ['phone', 'email', 'idCard'],
      'HIGH': ['phone', 'email', 'idCard', 'bankCard']
    };
    
    const fieldsToMask = maskRules[permissionLevel] || [];
    const maskedData = { ...data };
    
    fieldsToMask.forEach(field => {
      if (maskedData[field]) {
        maskedData[field] = this.getMaskFunction(field)(maskedData[field]);
      }
    });
    
    return maskedData;
  }
}
```

### 🔄 状态机与流程控制

#### 1. 订单状态机
**位置**: `src/utils/stateMachine.js`
**业务场景**: 订单状态的复杂流转控制

```javascript
// 订单状态机
class OrderStateMachine {
  constructor() {
    this.states = {
      'CREATED': {
        name: '已创建',
        allowedTransitions: ['PAID', 'CANCELLED']
      },
      'PAID': {
        name: '已支付',
        allowedTransitions: ['CHARGING', 'REFUNDED']
      },
      'CHARGING': {
        name: '充电中',
        allowedTransitions: ['COMPLETED', 'ABNORMAL']
      },
      'COMPLETED': {
        name: '已完成',
        allowedTransitions: ['SETTLED']
      },
      'ABNORMAL': {
        name: '异常',
        allowedTransitions: ['CHARGING', 'COMPLETED', 'CANCELLED']
      }
    };
  }
  
  // 验证状态转换
  canTransition(fromState, toState) {
    const state = this.states[fromState];
    return state?.allowedTransitions.includes(toState) || false;
  }
  
  // 执行状态转换
  async transition(orderId, fromState, toState, context) {
    if (!this.canTransition(fromState, toState)) {
      throw new Error(`不允许从 ${fromState} 转换到 ${toState}`);
    }
    
    // 执行转换前置条件检查
    await this.checkPreConditions(orderId, toState, context);
    
    // 执行状态转换
    await this.updateOrderStatus(orderId, toState);
    
    // 执行后置操作
    await this.executePostActions(orderId, toState, context);
    
    return { success: true, newState: toState };
  }
  
  // 前置条件检查
  async checkPreConditions(orderId, toState, context) {
    const preConditions = {
      'PAID': () => this.validatePayment(context.paymentInfo),
      'CHARGING': () => this.validateChargingConditions(orderId),
      'COMPLETED': () => this.validateCompletionConditions(orderId)
    };
    
    const checker = preConditions[toState];
    if (checker && !(await checker())) {
      throw new Error(`状态转换到 ${toState} 的前置条件不满足`);
    }
  }
}
```

### 📈 实时数据处理

#### 1. 实时监控数据流
**位置**: `src/utils/realTimeMonitor.js`
**业务场景**: 充电桩实时状态监控

```javascript
// 实时监控系统
class RealTimeMonitor {
  constructor() {
    this.websocket = null;
    this.dataBuffer = new Map();
    this.alertRules = new Map();
  }
  
  // 初始化监控
  initMonitor(stationIds) {
    this.websocket = new WebSocket(process.env.VUE_APP_WS_URL);
    
    this.websocket.onmessage = (event) => {
      const data = JSON.parse(event.data);
      this.processRealTimeData(data);
    };
    
    // 订阅站点数据
    this.subscribeStations(stationIds);
  }
  
  // 处理实时数据
  processRealTimeData(data) {
    const { stationId, timestamp, metrics } = data;
    
    // 数据缓存
    this.updateDataBuffer(stationId, timestamp, metrics);
    
    // 异常检测
    this.detectAnomalies(stationId, metrics);
    
    // 触发界面更新
    this.emitDataUpdate(stationId, metrics);
  }
  
  // 异常检测算法
  detectAnomalies(stationId, metrics) {
    const rules = this.alertRules.get(stationId) || [];
    
    rules.forEach(rule => {
      const isAnomalous = this.evaluateRule(metrics, rule);
      if (isAnomalous) {
        this.triggerAlert(stationId, rule, metrics);
      }
    });
  }
  
  // 规则评估
  evaluateRule(metrics, rule) {
    const { field, operator, threshold, duration } = rule;
    const value = metrics[field];
    
    switch (operator) {
      case 'GT': return value > threshold;
      case 'LT': return value < threshold;
      case 'EQ': return value === threshold;
      case 'RANGE': return value < threshold.min || value > threshold.max;
      default: return false;
    }
  }
}
```

### 🧮 复杂计算引擎

#### 1. 电费计算引擎
**位置**: `src/utils/electricityCalculator.js`
**业务场景**: 分时电价、阶梯电价等复杂计费规则

```javascript
// 电费计算引擎
class ElectricityCalculator {
  constructor() {
    this.priceRules = new Map();
    this.discountRules = new Map();
  }
  
  // 分时电价计算
  calculateTimeBasedPrice(usage, timeSlots) {
    let totalCost = 0;
    
    timeSlots.forEach(slot => {
      const { startTime, endTime, price, usageInSlot } = slot;
      totalCost += usageInSlot * price;
    });
    
    return this.roundToTwoDecimals(totalCost);
  }
  
  // 阶梯电价计算
  calculateTieredPrice(totalUsage, tiers) {
    let totalCost = 0;
    let remainingUsage = totalUsage;
    
    for (const tier of tiers) {
      const { threshold, price } = tier;
      const usageInTier = Math.min(remainingUsage, threshold);
      
      totalCost += usageInTier * price;
      remainingUsage -= usageInTier;
      
      if (remainingUsage <= 0) break;
    }
    
    return this.roundToTwoDecimals(totalCost);
  }
  
  // 优惠计算
  applyDiscounts(baseCost, discountRules) {
    let finalCost = baseCost;
    
    discountRules.forEach(rule => {
      switch (rule.type) {
        case 'PERCENTAGE':
          finalCost *= (1 - rule.value / 100);
          break;
        case 'FIXED_AMOUNT':
          finalCost -= rule.value;
          break;
        case 'FREE_QUOTA':
          // 免费额度逻辑
          break;
      }
    });
    
    return Math.max(0, this.roundToTwoDecimals(finalCost));
  }
  
  // 精确计算（避免浮点数问题）
  roundToTwoDecimals(value) {
    return Math.round(value * 100) / 100;
  }
}
```

## 最佳实践

### 1. 错误处理策略
```javascript
// 统一错误处理
class BusinessErrorHandler {
  static async handleBusinessOperation(operation) {
    try {
      const result = await operation();
      return { success: true, data: result };
    } catch (error) {
      console.error('业务操作失败:', error);
      
      // 根据错误类型进行分类处理
      if (error.code === 'VALIDATION_ERROR') {
        return { success: false, error: '数据验证失败', details: error.details };
      } else if (error.code === 'PERMISSION_DENIED') {
        return { success: false, error: '权限不足' };
      } else {
        return { success: false, error: '系统错误，请稍后重试' };
      }
    }
  }
}
```

### 2. 性能优化
```javascript
// 防抖处理
const debouncedSave = _.debounce(function() {
  this.handleSave();
}, 300);

// 数据缓存
const dataCache = new Map();
const getCachedData = (key, fetcher) => {
  if (dataCache.has(key)) {
    return Promise.resolve(dataCache.get(key));
  }
  
  return fetcher().then(data => {
    dataCache.set(key, data);
    return data;
  });
};
```

### 3. 代码可维护性
```javascript
// 配置驱动的业务逻辑
const businessConfig = {
  orderStates: {
    transitions: { /* 状态转换配置 */ },
    validations: { /* 验证规则配置 */ }
  },
  calculationRules: {
    electricity: { /* 电费计算规则 */ },
    service: { /* 服务费计算规则 */ }
  }
};

// 使用配置驱动业务逻辑
class ConfigDrivenBusinessLogic {
  constructor(config) {
    this.config = config;
  }
  
  executeBusinessRule(ruleType, data) {
    const rule = this.config[ruleType];
    return this.applyRule(rule, data);
  }
}
```
