# 车网互动管理平台 - 项目文档总览

## 📋 文档目录

本项目包含完整的技术文档体系，涵盖技术栈、开发指南、组件说明、业务逻辑等各个方面。

### 📚 核心文档列表

| 文档名称 | 文件路径 | 描述 | 更新时间 |
|---------|---------|------|----------|
| **项目技术栈文档** | `PROJECT_TECH_STACK.md` | 项目基本信息、技术架构、依赖说明 | 2024-12-01 |
| **网络接口封装指南** | `NETWORK_API_GUIDE.md` | HTTP请求封装、API规范、错误处理 | 2024-12-01 |
| **全局组件说明** | `GLOBAL_COMPONENTS_GUIDE.md` | 组件库使用、自定义组件、最佳实践 | 2024-12-01 |
| **状态管理指南** | `STATE_MANAGEMENT_GUIDE.md` | Vuex使用、数据流管理、状态持久化 | 2024-12-01 |
| **复杂业务逻辑说明** | `COMPLEX_BUSINESS_LOGIC_GUIDE.md` | 工作流程、计算引擎、业务规则 | 2024-12-01 |

### 🎯 专项文档

| 文档名称 | 文件路径 | 描述 |
|---------|---------|------|
| **故障代码管理文档** | `src/views/operationMaintenanceConfig/faultCodeManage/README.md` | 故障代码管理功能说明 |
| **接口联调指南** | `src/views/operationMaintenanceConfig/faultCodeManage/API_INTEGRATION_GUIDE.md` | 接口规范和联调说明 |
| **项目总结报告** | `FAULT_CODE_MANAGEMENT_SUMMARY.md` | 故障代码管理开发总结 |
| **接口联调总结** | `FAULT_CODE_API_INTEGRATION_SUMMARY.md` | 接口联调完成总结 |

## 🏗️ 项目架构概览

### 技术栈
- **前端框架**: Vue.js 2.6.12 + Vue Router + Vuex
- **UI组件库**: Element UI 2.15.14 + BuseCrud + VXE Table
- **构建工具**: Vue CLI 4.x + Webpack
- **样式预处理**: SCSS/Sass
- **网络请求**: Axios 0.28.1
- **状态管理**: Vuex + vuex-persistedstate
- **图表库**: ECharts 5.4.0 + Three.js

### 项目结构
```
v2g-charging-web/
├── 📁 public/                 # 静态资源
├── 📁 src/                    # 源代码
│   ├── 📁 api/               # API接口定义
│   ├── 📁 assets/            # 静态资源
│   ├── 📁 components/        # 全局组件
│   │   ├── 📁 Business/      # 业务组件
│   │   ├── 📁 Charts/        # 图表组件
│   │   └── 📁 ...            # 其他组件
│   ├── 📁 store/             # Vuex状态管理
│   ├── 📁 utils/             # 工具函数
│   ├── 📁 views/             # 页面组件
│   │   ├── 📁 user/          # 用户管理
│   │   ├── 📁 order/         # 订单管理
│   │   ├── 📁 operationMaintenanceConfig/ # 运维配置
│   │   └── 📁 ...            # 其他业务模块
│   └── 📄 main.js            # 入口文件
├── 📄 package.json           # 依赖配置
└── 📄 vue.config.js          # Vue CLI配置
```

## 🔧 开发指南

### 快速开始
```bash
# 1. 安装依赖
npm install

# 2. 启动开发服务器
npm run dev

# 3. 构建生产版本
npm run build

# 4. 代码检查
npm run lint
```

### 环境配置
- **开发环境**: `npm run dev`
- **测试环境**: `npm run build:test`
- **预发布环境**: `npm run build:stage`
- **生产环境**: `npm run build`

## 📖 文档使用指南

### 1. 新手入门
建议按以下顺序阅读文档：
1. **项目技术栈文档** - 了解项目整体技术架构
2. **全局组件说明** - 熟悉可用的组件库
3. **网络接口封装指南** - 学习API调用规范
4. **状态管理指南** - 掌握数据流管理

### 2. 开发实践
- **组件开发**: 参考全局组件说明文档
- **API开发**: 参考网络接口封装指南
- **状态管理**: 参考状态管理指南
- **业务逻辑**: 参考复杂业务逻辑说明

### 3. 问题排查
- **接口问题**: 查看网络接口封装指南的错误处理部分
- **组件问题**: 查看全局组件说明的最佳实践部分
- **状态问题**: 查看状态管理指南的调试部分
- **业务逻辑**: 查看复杂业务逻辑说明的相关模块

## 🎨 核心功能模块

### 用户管理模块
- **企业用户管理**: 企业注册、认证、管理
- **企业员工管理**: 员工账号、权限管理
- **充电卡管理**: 充电卡发放、充值、业务记录

### 充电管理模块
- **订单管理**: 充电订单查询、导出、异常处理
- **计费下发**: 计费策略配置和下发
- **异常处理**: 异常订单检测、标记、处理流程

### 运维配置模块
- **故障代码管理**: 故障代码维护、分类管理
- **派单规则管理**: 运维派单规则配置
- **运维班组管理**: 运维团队组织架构

### 智能调度模块
- **车网互动**: V2G调度策略配置
- **SOC配置**: 站点SOC参数设置
- **调控计划**: 电网调控计划制定和执行

### 营销管理模块
- **优惠券管理**: 优惠券发放、使用、统计
- **活动管理**: 营销活动策划、执行、效果分析
- **发票管理**: 发票开具、查询、管理

## 🔍 技术特色

### 1. 组件化架构
- **BuseCrud组件**: 统一的CRUD操作组件
- **业务组件库**: 丰富的业务场景组件
- **图表组件**: 基于ECharts的可视化组件

### 2. 状态管理
- **模块化Store**: 按业务模块划分的状态管理
- **状态持久化**: 关键状态的本地持久化
- **权限控制**: 基于角色的权限管理

### 3. 网络层封装
- **统一请求封装**: 基于Axios的请求拦截和响应处理
- **错误处理**: 完善的错误分类和处理机制
- **防重复提交**: 自动防重复提交保护

### 4. 业务逻辑
- **工作流引擎**: 复杂业务流程的状态机管理
- **计算引擎**: 精确的费用计算和业务规则引擎
- **实时监控**: WebSocket实时数据处理

## 📈 性能优化

### 1. 构建优化
- **代码分割**: 路由级别的懒加载
- **Tree Shaking**: 无用代码消除
- **资源压缩**: Gzip压缩和资源优化

### 2. 运行时优化
- **组件缓存**: 合理使用keep-alive
- **数据缓存**: API响应缓存机制
- **防抖节流**: 用户操作优化

### 3. 用户体验
- **加载状态**: 完善的loading状态管理
- **错误提示**: 友好的错误信息展示
- **响应式设计**: 多设备适配

## 🛠️ 开发规范

### 1. 代码规范
- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化
- **命名规范**: 统一的命名约定

### 2. 组件规范
- **单一职责**: 组件功能单一明确
- **可复用性**: 提取公共逻辑
- **文档注释**: 完善的组件文档

### 3. API规范
- **RESTful**: 遵循REST API设计原则
- **错误码**: 统一的错误码体系
- **类型定义**: 完整的TypeScript类型定义

## 📞 技术支持

### 文档维护
- **更新频率**: 随功能迭代实时更新
- **版本控制**: 文档版本与代码版本同步
- **反馈机制**: 欢迎提出文档改进建议

### 开发支持
- **技术咨询**: 技术问题可参考相关文档
- **最佳实践**: 文档中包含丰富的实践案例
- **问题排查**: 提供详细的问题排查指南

---

**注意**: 本文档体系会随着项目的发展持续更新，建议开发者定期查看最新版本的文档内容。
