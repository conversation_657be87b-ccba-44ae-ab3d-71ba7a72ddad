<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mock系统测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button.success {
            background: #28a745;
        }
        .test-button.error {
            background: #dc3545;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .status {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
        }
        .info-box {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 5px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .info-box h4 {
            margin-top: 0;
            color: #0066cc;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Mock系统测试页面</h1>
        
        <div class="info-box">
            <h4>📋 测试说明</h4>
            <p>本页面用于测试ServiceWorker接口模拟系统的功能。请按照以下步骤进行测试：</p>
            <ol>
                <li>首先检查Mock系统状态</li>
                <li>测试派单规则管理相关接口</li>
                <li>验证数据格式和响应时间</li>
                <li>检查浏览器Network面板确认请求被拦截</li>
            </ol>
        </div>

        <div class="test-section">
            <h3>🔧 系统状态检查</h3>
            <button class="test-button" onclick="checkSystemStatus()">检查Mock系统状态</button>
            <button class="test-button" onclick="checkServiceWorker()">检查ServiceWorker状态</button>
            <button class="test-button" onclick="checkConfiguration()">检查配置状态</button>
            <div id="system-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📊 派单规则管理接口测试</h3>
            <button class="test-button" onclick="testDispatchRulePage()">测试分页查询</button>
            <button class="test-button" onclick="testDispatchRuleDetail()">测试详情查询</button>
            <button class="test-button" onclick="testDispatchRuleCreate()">测试新增接口</button>
            <button class="test-button" onclick="testDispatchRuleOnOff()">测试启用/停用</button>
            <div id="dispatch-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🧪 综合功能测试</h3>
            <button class="test-button" onclick="runAllTests()">运行所有测试</button>
            <button class="test-button" onclick="testErrorScenarios()">测试错误场景</button>
            <button class="test-button" onclick="testPerformance()">性能测试</button>
            <div id="comprehensive-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📈 实时监控</h3>
            <button class="test-button" onclick="startMonitoring()">开始监控</button>
            <button class="test-button" onclick="stopMonitoring()">停止监控</button>
            <button class="test-button" onclick="clearLogs()">清除日志</button>
            <div id="monitoring-result" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        let monitoringInterval = null;
        
        // 工具函数
        function showResult(elementId, content, type = 'info') {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.innerHTML = content;
            element.className = `result ${type}`;
        }

        function logMessage(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const statusClass = type === 'success' ? 'success' : type === 'error' ? 'error' : 'warning';
            return `[${timestamp}] <span class="status ${statusClass}">${type.toUpperCase()}</span> ${message}\n`;
        }

        // 系统状态检查
        async function checkSystemStatus() {
            let result = logMessage('开始检查Mock系统状态...', 'info');
            
            try {
                // 检查ServiceWorker支持
                if ('serviceWorker' in navigator) {
                    result += logMessage('✅ 浏览器支持ServiceWorker', 'success');
                } else {
                    result += logMessage('❌ 浏览器不支持ServiceWorker', 'error');
                    showResult('system-result', result);
                    return;
                }

                // 检查ServiceWorker注册状态
                const registration = await navigator.serviceWorker.getRegistration();
                if (registration) {
                    result += logMessage(`✅ ServiceWorker已注册: ${registration.scope}`, 'success');
                    result += logMessage(`状态: ${registration.active ? '激活' : '未激活'}`, registration.active ? 'success' : 'warning');
                } else {
                    result += logMessage('❌ ServiceWorker未注册', 'error');
                }

                // 检查Mock配置
                if (window.MockConfig) {
                    result += logMessage('✅ Mock配置已加载', 'success');
                    result += logMessage(`Mock系统启用状态: ${window.MockConfig.config.enabled}`, window.MockConfig.config.enabled ? 'success' : 'warning');
                } else {
                    result += logMessage('❌ Mock配置未加载', 'error');
                }

                // 检查调试工具
                if (window.MockDebug) {
                    result += logMessage('✅ Mock调试工具可用', 'success');
                } else {
                    result += logMessage('❌ Mock调试工具不可用', 'error');
                }

                if (window.MockSystem) {
                    result += logMessage('✅ Mock测试工具可用', 'success');
                } else {
                    result += logMessage('❌ Mock测试工具不可用', 'error');
                }

            } catch (error) {
                result += logMessage(`❌ 检查过程中发生错误: ${error.message}`, 'error');
            }

            showResult('system-result', result);
        }

        async function checkServiceWorker() {
            let result = logMessage('检查ServiceWorker详细状态...', 'info');
            
            try {
                const registration = await navigator.serviceWorker.getRegistration();
                if (registration) {
                    result += logMessage(`Scope: ${registration.scope}`, 'info');
                    result += logMessage(`Update via cache: ${registration.updateViaCache}`, 'info');
                    
                    if (registration.installing) {
                        result += logMessage('ServiceWorker正在安装...', 'warning');
                    }
                    if (registration.waiting) {
                        result += logMessage('ServiceWorker等待激活...', 'warning');
                    }
                    if (registration.active) {
                        result += logMessage('ServiceWorker已激活并运行', 'success');
                        result += logMessage(`脚本URL: ${registration.active.scriptURL}`, 'info');
                        result += logMessage(`状态: ${registration.active.state}`, 'info');
                    }
                }

                // 检查控制器
                if (navigator.serviceWorker.controller) {
                    result += logMessage('✅ ServiceWorker正在控制当前页面', 'success');
                } else {
                    result += logMessage('⚠️ ServiceWorker未控制当前页面', 'warning');
                }

            } catch (error) {
                result += logMessage(`❌ 检查ServiceWorker时发生错误: ${error.message}`, 'error');
            }

            showResult('system-result', result);
        }

        async function checkConfiguration() {
            let result = logMessage('检查Mock配置详情...', 'info');
            
            try {
                if (window.MockConfig) {
                    const config = window.MockConfig.config;
                    result += logMessage(`基础配置:`, 'info');
                    result += logMessage(`  - 启用状态: ${config.enabled}`, 'info');
                    result += logMessage(`  - 基础URL: ${config.baseURL}`, 'info');
                    result += logMessage(`  - 超时时间: ${config.timeout}ms`, 'info');
                    
                    result += logMessage(`模块配置:`, 'info');
                    for (const [moduleName, moduleConfig] of Object.entries(config.modules || {})) {
                        result += logMessage(`  - ${moduleName}: ${moduleConfig.enabled ? '启用' : '禁用'}`, moduleConfig.enabled ? 'success' : 'warning');
                    }

                    result += logMessage(`路由配置数量: ${Object.keys(config.routes || {}).length}`, 'info');
                } else {
                    result += logMessage('❌ 无法获取Mock配置', 'error');
                }
            } catch (error) {
                result += logMessage(`❌ 检查配置时发生错误: ${error.message}`, 'error');
            }

            showResult('system-result', result);
        }

        // 派单规则管理接口测试
        async function testDispatchRulePage() {
            let result = logMessage('测试派单规则分页查询接口...', 'info');
            
            try {
                const startTime = Date.now();
                const response = await fetch('/vehicle-charging-admin/ops/rule/page', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        pageNum: 1,
                        pageSize: 10,
                        ruleName: '',
                        enableStatus: ''
                    })
                });

                const endTime = Date.now();
                const responseTime = endTime - startTime;

                result += logMessage(`响应状态: ${response.status}`, response.ok ? 'success' : 'error');
                result += logMessage(`响应时间: ${responseTime}ms`, 'info');
                result += logMessage(`来源: ${response.headers.get('X-Mock-Source') || '真实API'}`, 'info');

                if (response.ok) {
                    const data = await response.json();
                    result += logMessage(`响应数据:`, 'success');
                    result += logMessage(`  - 状态码: ${data.code}`, 'info');
                    result += logMessage(`  - 消息: ${data.msg}`, 'info');
                    result += logMessage(`  - 数据条数: ${data.data?.length || 0}`, 'info');
                    result += logMessage(`  - 总数: ${data.total}`, 'info');
                    
                    if (data.data && data.data.length > 0) {
                        const firstItem = data.data[0];
                        result += logMessage(`  - 第一条数据示例:`, 'info');
                        result += logMessage(`    规则ID: ${firstItem.ruleId}`, 'info');
                        result += logMessage(`    规则名称: ${firstItem.ruleName}`, 'info');
                        result += logMessage(`    启用状态: ${firstItem.enableStatus}`, 'info');
                    }
                } else {
                    result += logMessage(`❌ 请求失败: ${response.statusText}`, 'error');
                }

            } catch (error) {
                result += logMessage(`❌ 测试过程中发生错误: ${error.message}`, 'error');
            }

            showResult('dispatch-result', result);
        }

        async function testDispatchRuleDetail() {
            let result = logMessage('测试派单规则详情查询接口...', 'info');
            
            try {
                const response = await fetch('/vehicle-charging-admin/ops/rule/detail', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        ruleId: 'rule_001'
                    })
                });

                result += logMessage(`响应状态: ${response.status}`, response.ok ? 'success' : 'error');
                result += logMessage(`来源: ${response.headers.get('X-Mock-Source') || '真实API'}`, 'info');

                if (response.ok) {
                    const data = await response.json();
                    result += logMessage(`✅ 详情查询成功`, 'success');
                    result += logMessage(`数据: ${JSON.stringify(data.data, null, 2)}`, 'info');
                } else {
                    result += logMessage(`❌ 详情查询失败`, 'error');
                }

            } catch (error) {
                result += logMessage(`❌ 测试过程中发生错误: ${error.message}`, 'error');
            }

            showResult('dispatch-result', result);
        }

        async function testDispatchRuleCreate() {
            let result = logMessage('测试派单规则新增接口...', 'info');
            
            try {
                const response = await fetch('/vehicle-charging-admin/ops/rule/create', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        ruleName: '测试规则',
                        checkRule: 'VOLTAGE',
                        compareOperator: 'GT',
                        checkPercent: 80,
                        remark: '这是一个测试规则'
                    })
                });

                result += logMessage(`响应状态: ${response.status}`, response.ok ? 'success' : 'error');
                result += logMessage(`来源: ${response.headers.get('X-Mock-Source') || '真实API'}`, 'info');

                if (response.ok) {
                    const data = await response.json();
                    result += logMessage(`✅ 新增成功`, 'success');
                    result += logMessage(`返回数据: ${JSON.stringify(data, null, 2)}`, 'info');
                } else {
                    result += logMessage(`❌ 新增失败`, 'error');
                }

            } catch (error) {
                result += logMessage(`❌ 测试过程中发生错误: ${error.message}`, 'error');
            }

            showResult('dispatch-result', result);
        }

        async function testDispatchRuleOnOff() {
            let result = logMessage('测试派单规则启用/停用接口...', 'info');
            
            try {
                const response = await fetch('/vehicle-charging-admin/ops/rule/onOff', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        ruleId: 'rule_001',
                        enableStatus: 1
                    })
                });

                result += logMessage(`响应状态: ${response.status}`, response.ok ? 'success' : 'error');
                result += logMessage(`来源: ${response.headers.get('X-Mock-Source') || '真实API'}`, 'info');

                if (response.ok) {
                    const data = await response.json();
                    result += logMessage(`✅ 启用/停用操作成功`, 'success');
                    result += logMessage(`返回数据: ${JSON.stringify(data, null, 2)}`, 'info');
                } else {
                    result += logMessage(`❌ 启用/停用操作失败`, 'error');
                }

            } catch (error) {
                result += logMessage(`❌ 测试过程中发生错误: ${error.message}`, 'error');
            }

            showResult('dispatch-result', result);
        }

        // 综合测试
        async function runAllTests() {
            let result = logMessage('开始运行所有测试...', 'info');
            
            const tests = [
                { name: '分页查询', func: testDispatchRulePage },
                { name: '详情查询', func: testDispatchRuleDetail },
                { name: '新增接口', func: testDispatchRuleCreate },
                { name: '启用停用', func: testDispatchRuleOnOff }
            ];

            for (const test of tests) {
                result += logMessage(`\n=== 测试: ${test.name} ===`, 'info');
                try {
                    await test.func();
                    result += logMessage(`✅ ${test.name} 测试完成`, 'success');
                } catch (error) {
                    result += logMessage(`❌ ${test.name} 测试失败: ${error.message}`, 'error');
                }
                
                // 等待一秒再进行下一个测试
                await new Promise(resolve => setTimeout(resolve, 1000));
            }

            result += logMessage('\n🎉 所有测试完成！', 'success');
            showResult('comprehensive-result', result);
        }

        async function testErrorScenarios() {
            let result = logMessage('测试错误场景...', 'info');
            
            try {
                // 测试不存在的接口
                const response1 = await fetch('/vehicle-charging-admin/ops/rule/nonexistent', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({})
                });
                
                result += logMessage(`不存在接口测试: ${response1.status}`, 'info');
                
                // 测试错误的请求方法
                const response2 = await fetch('/vehicle-charging-admin/ops/rule/page', {
                    method: 'GET'
                });
                
                result += logMessage(`错误方法测试: ${response2.status}`, 'info');
                
            } catch (error) {
                result += logMessage(`❌ 错误场景测试失败: ${error.message}`, 'error');
            }

            showResult('comprehensive-result', result);
        }

        async function testPerformance() {
            let result = logMessage('开始性能测试...', 'info');
            
            const testCount = 10;
            const times = [];
            
            for (let i = 0; i < testCount; i++) {
                const startTime = Date.now();
                
                try {
                    const response = await fetch('/vehicle-charging-admin/ops/rule/page', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ pageNum: 1, pageSize: 10 })
                    });
                    
                    const endTime = Date.now();
                    const responseTime = endTime - startTime;
                    times.push(responseTime);
                    
                    result += logMessage(`第${i + 1}次请求: ${responseTime}ms`, 'info');
                    
                } catch (error) {
                    result += logMessage(`第${i + 1}次请求失败: ${error.message}`, 'error');
                }
            }
            
            if (times.length > 0) {
                const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
                const minTime = Math.min(...times);
                const maxTime = Math.max(...times);
                
                result += logMessage(`\n📊 性能统计:`, 'success');
                result += logMessage(`  - 平均响应时间: ${avgTime.toFixed(2)}ms`, 'info');
                result += logMessage(`  - 最快响应时间: ${minTime}ms`, 'info');
                result += logMessage(`  - 最慢响应时间: ${maxTime}ms`, 'info');
                result += logMessage(`  - 成功率: ${(times.length / testCount * 100).toFixed(1)}%`, 'info');
            }

            showResult('comprehensive-result', result);
        }

        // 实时监控
        function startMonitoring() {
            if (monitoringInterval) {
                clearInterval(monitoringInterval);
            }

            let result = logMessage('开始实时监控...', 'info');
            showResult('monitoring-result', result);

            monitoringInterval = setInterval(async () => {
                try {
                    if (window.MockDebug) {
                        const metrics = await window.MockDebug.getMetrics();
                        const timestamp = new Date().toLocaleTimeString();
                        
                        result += logMessage(`[${timestamp}] 请求总数: ${metrics.requestCount}, 错误数: ${metrics.errorCount}, 平均响应时间: ${metrics.averageResponseTime?.toFixed(2)}ms`, 'info');
                        showResult('monitoring-result', result);
                    }
                } catch (error) {
                    result += logMessage(`监控错误: ${error.message}`, 'error');
                    showResult('monitoring-result', result);
                }
            }, 5000);
        }

        function stopMonitoring() {
            if (monitoringInterval) {
                clearInterval(monitoringInterval);
                monitoringInterval = null;
                
                const result = document.getElementById('monitoring-result').innerHTML + 
                              logMessage('监控已停止', 'warning');
                showResult('monitoring-result', result);
            }
        }

        function clearLogs() {
            showResult('monitoring-result', '');
            document.getElementById('monitoring-result').style.display = 'none';
        }

        // 页面加载完成后自动检查系统状态
        window.addEventListener('load', () => {
            setTimeout(checkSystemStatus, 1000);
        });
    </script>
</body>
</html>
