/**
 * Mock接口模拟系统配置文件
 * @description 全局配置管理，支持环境变量控制和细粒度控制
 * <AUTHOR>
 * @since 2024-12-01
 */

// 环境变量配置
const isDevelopment = process.env.NODE_ENV === 'development';
const isProduction = process.env.NODE_ENV === 'production';
const enableMock = process.env.VUE_APP_ENABLE_MOCK !== 'false';

/**
 * Mock系统全局配置
 */
const mockConfig = {
  // 基础配置
  enabled: true, // 是否启用Mock功能
  baseURL: process.env.VUE_APP_BASE_API, // API基础路径
  timeout: 1000, // 模拟网络延迟（毫秒）

  // ServiceWorker配置
  serviceWorker: {
    enabled: true, // 是否启用ServiceWorker模拟
    scriptPath: '/sw-mock-manager.js', // ServiceWorker脚本路径
    scope: '/', // ServiceWorker作用域
    updateViaCache: 'none', // 缓存策略
  },

  // Mock数据配置
  mockData: {
    basePath: '/mock', // Mock数据文件基础路径
    fileExtension: '.json', // 数据文件扩展名
    encoding: 'utf-8', // 文件编码
    cacheEnabled: true, // 是否启用数据缓存
    cacheTimeout: 5 * 60 * 1000, // 缓存超时时间（5分钟）
  },

  // 响应配置
  response: {
    successCode: '200', // 成功状态码
    errorCode: '500', // 错误状态码
    defaultMessage: '操作成功', // 默认成功消息
    errorMessage: '系统错误', // 默认错误消息
    headers: {
      'Content-Type': 'application/json;charset=utf-8',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  },

  // 模块级别控制
  modules: {
    // 运维配置模块
    operationMaintenanceConfig: {
      enabled: true,
      routes: {
        // 派单规则管理
        dispatchRule: {
          enabled: true,
          dataFile: 'operationMaintenanceConfig-dispatchRule.json',
          routes: {
            '/vehicle-charging-admin/ops/rule/page': true, // 分页查询
            '/vehicle-charging-admin/ops/rule/detail': true, // 详情查询
            '/vehicle-charging-admin/ops/rule/create': true, // 新增
            '/vehicle-charging-admin/ops/rule/update': true, // 更新
            '/vehicle-charging-admin/ops/rule/onOff': true, // 启用/停用
            '/vehicle-charging-admin/ops/rule/delete': true, // 删除
            '/vehicle-charging-admin/ops/rule/export': true, // 导出
          },
        },
        // 故障代码管理
        faultCode: {
          enabled: true,
          dataFile: 'operationMaintenanceConfig-faultCode.json',
          routes: {
            '/vehicle-charging-admin/ops/faultCode/page': true,
            '/vehicle-charging-admin/ops/faultCode/detail': true,
            '/vehicle-charging-admin/ops/faultCode/create': true,
            '/vehicle-charging-admin/ops/faultCode/update': true,
            '/vehicle-charging-admin/ops/faultCode/onOff': true,
            '/vehicle-charging-admin/ops/faultCode/delete': true,
          },
        },
      },
    },

    // 分拣管理模块
    sortingManage: {
      enabled: true,
      routes: {
        // 问题反馈管理
        problemFeedback: {
          enabled: true,
          dataFile: 'sortingManage-problemFeedback.json',
          routes: {
            '/vehicle-charging-admin/clearProblemFeedback/page': true,
            '/vehicle-charging-admin/clearInfo/detail': true,
            '/vehicle-charging-admin/clearDetail/page': true,
            '/vehicle-charging-admin/clearProblemFeedbackDetail/page': true,
            '/vehicle-charging-admin/clearProblemFeedbackDetail/insert': true,
            '/vehicle-charging-admin/clearProblemFeedback/check': true,
          },
        },
      },
    },

    // 用户管理模块
    user: {
      enabled: true,
      routes: {
        userManage: {
          enabled: true,
          dataFile: 'user-manage.json',
          routes: {
            '/vehicle-charging-admin/user/page': true,
            '/vehicle-charging-admin/user/detail': true,
            '/vehicle-charging-admin/user/create': true,
            '/vehicle-charging-admin/user/update': true,
            '/vehicle-charging-admin/user/delete': true,
          },
        },
      },
    },
  },

  // 路径级别控制（优先级最高）
  routes: {
    // 派单规则管理接口
    '/vehicle-charging-admin/ops/rule/page': {
      enabled: true,
      method: 'POST',
      dataFile: 'operationMaintenanceConfig-dispatchRule.json',
      timeout: 500,
      errorRate: 0, // 错误率（0-1）
    },
    '/vehicle-charging-admin/ops/rule/detail': {
      enabled: true,
      method: 'POST',
      dataFile: 'operationMaintenanceConfig-dispatchRule.json',
      timeout: 300,
    },
    '/vehicle-charging-admin/ops/rule/create': {
      enabled: true,
      method: 'POST',
      dataFile: 'operationMaintenanceConfig-dispatchRule.json',
      timeout: 800,
    },
    '/vehicle-charging-admin/ops/rule/update': {
      enabled: true,
      method: 'POST',
      dataFile: 'operationMaintenanceConfig-dispatchRule.json',
      timeout: 800,
    },
    '/vehicle-charging-admin/ops/rule/onOff': {
      enabled: true,
      method: 'POST',
      dataFile: 'operationMaintenanceConfig-dispatchRule.json',
      timeout: 600,
    },
    '/vehicle-charging-admin/ops/rule/delete': {
      enabled: true,
      method: 'POST',
      dataFile: 'operationMaintenanceConfig-dispatchRule.json',
      timeout: 600,
    },
    '/vehicle-charging-admin/ops/rule/export': {
      enabled: true,
      method: 'POST',
      dataFile: 'operationMaintenanceConfig-dispatchRule.json',
      timeout: 1000,
    },
    // 可以禁用特定路径的模拟
    '/vehicle-charging-admin/auth/login': {
      enabled: false, // 登录接口不使用模拟
    },
  },

  // 开发工具配置
  devTools: {
    enabled: isDevelopment, // 是否启用开发工具
    console: {
      logRequests: true, // 是否在控制台记录请求
      logResponses: true, // 是否在控制台记录响应
      logErrors: true, // 是否在控制台记录错误
    },
    ui: {
      enabled: true, // 是否启用可视化界面
      position: 'bottom-right', // 界面位置
      hotkey: 'ctrl+shift+m', // 快捷键
    },
  },

  // 数据生成配置
  dataGeneration: {
    locale: 'zh_CN', // 数据本地化
    seed: 12345, // 随机种子，确保数据一致性
    defaultPageSize: 10, // 默认分页大小
    maxPageSize: 100, // 最大分页大小
    dateFormat: 'YYYY-MM-DD HH:mm:ss', // 日期格式
    numberPrecision: 2, // 数字精度
  },

  // 错误模拟配置
  errorSimulation: {
    enabled: false, // 是否启用错误模拟
    globalErrorRate: 0.05, // 全局错误率（5%）
    errorTypes: {
      network: 0.3, // 网络错误比例
      server: 0.4, // 服务器错误比例
      timeout: 0.2, // 超时错误比例
      business: 0.1, // 业务错误比例
    },
    errorCodes: {
      400: '请求参数错误',
      401: '未授权访问',
      403: '禁止访问',
      404: '资源不存在',
      500: '服务器内部错误',
      502: '网关错误',
      503: '服务不可用',
      504: '网关超时',
    },
  },

  // 性能监控配置
  performance: {
    enabled: isDevelopment,
    metrics: {
      requestCount: true, // 请求计数
      responseTime: true, // 响应时间
      errorRate: true, // 错误率
      cacheHitRate: true, // 缓存命中率
    },
    reporting: {
      interval: 60000, // 报告间隔（1分钟）
      console: true, // 控制台输出
      storage: false, // 本地存储
    },
  },
};

/**
 * 获取模块配置
 * @param {string} moduleName - 模块名称
 * @returns {Object} 模块配置
 */
function getModuleConfig(moduleName) {
  return mockConfig.modules[moduleName] || { enabled: false };
}

/**
 * 获取路由配置
 * @param {string} path - 路由路径
 * @returns {Object} 路由配置
 */
function getRouteConfig(path) {
  // 优先检查路径级别配置
  if (mockConfig.routes[path]) {
    return mockConfig.routes[path];
  }

  // 检查模块级别配置
  for (const [moduleName, moduleConfig] of Object.entries(mockConfig.modules)) {
    if (!moduleConfig.enabled) continue;

    for (const [subModuleName, subModuleConfig] of Object.entries(
      moduleConfig.routes || {}
    )) {
      if (!subModuleConfig.enabled) continue;

      if (subModuleConfig.routes && subModuleConfig.routes[path]) {
        return {
          enabled: true,
          dataFile: subModuleConfig.dataFile,
          method: 'POST',
          timeout: mockConfig.timeout,
        };
      }
    }
  }

  return { enabled: false };
}

/**
 * 检查路径是否启用模拟
 * @param {string} path - 路由路径
 * @param {string} method - HTTP方法
 * @returns {boolean} 是否启用模拟
 */
function isRouteEnabled(path, method = 'POST') {
  if (!mockConfig.enabled) return false;

  const routeConfig = getRouteConfig(path);
  return (
    routeConfig.enabled &&
    (!routeConfig.method ||
      routeConfig.method.toUpperCase() === method.toUpperCase())
  );
}

/**
 * 获取数据文件路径
 * @param {string} path - 路由路径
 * @returns {string} 数据文件路径
 */
function getDataFilePath(path) {
  const routeConfig = getRouteConfig(path);
  if (routeConfig.dataFile) {
    return `${mockConfig.mockData.basePath}/${routeConfig.dataFile}`;
  }
  return null;
}

/**
 * 运行时配置更新
 * @param {Object} updates - 配置更新
 */
function updateConfig(updates) {
  Object.assign(mockConfig, updates);

  // 通知ServiceWorker配置已更新
  if ('serviceWorker' in navigator && navigator.serviceWorker.controller) {
    navigator.serviceWorker.controller.postMessage({
      type: 'CONFIG_UPDATE',
      config: mockConfig,
    });
  }
}

// 导出配置和工具函数
if (typeof module !== 'undefined' && module.exports) {
  // Node.js环境
  module.exports = {
    mockConfig,
    getModuleConfig,
    getRouteConfig,
    isRouteEnabled,
    getDataFilePath,
    updateConfig,
  };
} else {
  // 浏览器环境
  window.MockConfig = {
    config: mockConfig,
    getModuleConfig,
    getRouteConfig,
    isRouteEnabled,
    getDataFilePath,
    updateConfig,
  };
}
